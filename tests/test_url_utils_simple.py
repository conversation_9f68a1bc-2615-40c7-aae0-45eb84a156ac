"""Simple test for URL utility functions without pytest."""

from local_deep_research.utilities.url_utils import normalize_url


def test_url_normalization():
    """Test the normalize_url function."""

    # Test localhost without scheme
    assert normalize_url("localhost:11434") == "http://localhost:11434"
    assert normalize_url("127.0.0.1:11434") == "http://127.0.0.1:11434"

    # Test external host without scheme
    assert normalize_url("example.com:11434") == "https://example.com:11434"

    # Test malformed URL with scheme
    assert normalize_url("http:localhost:11434") == "http://localhost:11434"
    assert (
        normalize_url("https:example.com:11434") == "https://example.com:11434"
    )

    # Test well-formed URLs
    assert normalize_url("http://localhost:11434") == "http://localhost:11434"
    assert (
        normalize_url("https://example.com:11434")
        == "https://example.com:11434"
    )

    # Test URLs with double slash prefix
    assert normalize_url("//localhost:11434") == "http://localhost:11434"
    assert normalize_url("//example.com:11434") == "https://example.com:11434"

    # Test empty URL (should raise ValueError)
    try:
        normalize_url("")
        print("FAIL: Expected ValueError for empty URL")
    except ValueError:
        pass  # Expected

    print("All tests passed!")


if __name__ == "__main__":
    test_url_normalization()
