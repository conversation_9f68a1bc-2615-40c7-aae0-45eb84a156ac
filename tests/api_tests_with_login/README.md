# API Tests with Authentication

⚠️ **IMPORTANT: THESE ARE REAL INTEGRATION TESTS** ⚠️

These tests run against a REAL running LDR server instance and perform ACTUAL operations including:
- Creating real user accounts in the database
- Making real API calls that may create/modify data
- Consuming real server resources

**DO NOT convert these to mock tests** - they are specifically designed to test the full integration stack.

This test suite provides comprehensive API testing for Local Deep Research with proper authentication handling. It combines Puppeteer for browser-based authentication and curl for direct API testing.

## Purpose

These tests are designed to:

1. **Verify API endpoints with authentication** - Ensure all protected endpoints properly require and validate authentication
2. **Test API functionality** - Validate that API endpoints return correct data and handle errors appropriately
3. **Debug authentication issues** - Help diagnose problems with session handling, cookies, and CSRF tokens
4. **Regression testing** - Catch breaking changes in API endpoints or authentication flow
5. **Integration testing** - Test the full stack from browser login to API access

## Test Structure

### Files

Each API endpoint has its own dedicated test file:

- `test_report_api.js` - Tests `/api/report/<id>` endpoint for fetching research reports
- `test_settings_api.js` - Tests `/api/settings` endpoints for user settings management
- `test_history_api.js` - Tests `/api/history` endpoints for research history
- `test_models_api.js` - Tests `/api/models` endpoints for AI model management
- `test_search_engines_api.js` - Tests `/api/search-engines` endpoints
- `test_research_api.js` - Tests `/api/research/*` endpoints for starting/managing research
- `test_auth_api.js` - Tests `/auth/*` endpoints for authentication and user management
- `test_metrics_api.js` - Tests `/metrics/*` endpoints for analytics and performance metrics
- `test_benchmark_api.js` - Tests `/benchmark/*` endpoints for benchmark management
- `test_queue_api.js` - Tests `/api/queue/*` endpoints for research queue status
- `test_apiv1_api.js` - Tests `/api/v1/*` REST API endpoints

Supporting files:
- `base_api_test.js` - Base class providing common functionality for all tests
- `test_helpers.js` - Utility functions for cookie handling and curl execution
- `package.json` - Dependencies and test scripts

### Key Features

1. **Automatic user registration** - Each test run creates a new test user to avoid conflicts
2. **Cookie extraction** - Extracts session cookies from Puppeteer and formats them for curl
3. **CSRF token handling** - Properly handles CSRF protection for POST requests
4. **Error validation** - Tests both success and error cases (401, 404, 500, etc.)

## Running the Tests

### Prerequisites

1. Start the LDR server:
   ```bash
   scripts/dev/restart_server.sh
   ```

2. Ensure Ollama is running and has the required model:
   ```bash
   # Check if Ollama is running
   ollama list

   # If gemma3n:e2b is not available, pull it:
   ollama pull gemma3n:e2b
   ```

   **Note**: The tests use the `gemma3n:e2b` model for all AI operations. This is a small, fast model suitable for testing.

3. Install test dependencies:
   ```bash
   cd tests/api_tests_with_login
   npm install
   ```

### Run All Tests

```bash
npm test
```

### Run Specific Endpoint Tests

```bash
# Individual endpoint tests
npm run test:report      # Test /api/report endpoints
npm run test:settings    # Test /api/settings endpoints
npm run test:history     # Test /api/history endpoints
npm run test:models      # Test /api/models endpoints
npm run test:search      # Test /api/search-engines endpoints
npm run test:research    # Test /api/research endpoints
npm run test:auth        # Test /auth endpoints
npm run test:metrics     # Test /metrics endpoints
npm run test:benchmark   # Test /benchmark endpoints
npm run test:queue       # Test /api/queue endpoints
npm run test:apiv1       # Test /api/v1 REST API endpoints

# Or directly with mocha
npx mocha test_report_api.js
npx mocha test_settings_api.js
# etc...
```

### Debug Mode

```bash
# Run with full browser visibility
HEADLESS=false npm test

# Run with Node.js debugging
npm run test:debug
```

## Test Coverage

### Authentication Tests
- User registration with password validation
- Login with correct/incorrect credentials
- Session cookie management
- Logout functionality

### API Endpoint Tests
- `/api/report/<id>` - Research report retrieval
- `/api/settings` - User settings management
- `/api/history` - Research history
- `/api/models` - Available AI models
- `/api/search-engines` - Available search engines
- `/api/research/*` - Start and manage research tasks
- `/auth/*` - Authentication (login, register, logout)
- `/metrics/*` - Analytics and performance metrics
- `/benchmark/*` - Benchmark management
- `/api/queue/*` - Research queue status
- `/api/v1/*` - REST API endpoints

### Security Tests
- Authentication requirement (401 without cookies)
- CSRF token validation
- Session expiration
- Cross-user access prevention

## Debugging Failed Tests

### Common Issues

1. **Server not running**
   - Error: `ECONNREFUSED`
   - Solution: Start the server with `scripts/dev/restart_server.sh`

2. **Authentication failure**
   - Error: `Login failed - still on login page`
   - Check: Database permissions, user exists, correct password

3. **Settings context error**
   - Error: `No settings context available`
   - Check: User settings initialization, database state

4. **CSRF token missing**
   - Error: `The CSRF token is missing`
   - Check: Include CSRF token in POST requests

5. **Model not found**
   - Error: `Model gemma3n:e2b not found`
   - Solution: Ensure Ollama is running and pull the model:
     ```bash
     ollama pull gemma3n:e2b
     ```

6. **Ollama not running**
   - Error: Connection refused on Ollama endpoints
   - Solution: Start Ollama service:
     ```bash
     # On Linux/Mac
     ollama serve

     # Or check if it's already running
     ps aux | grep ollama
     ```

### Debugging Tools

1. **View server logs**:
   ```bash
   tail -f /tmp/ldr_server.log
   ```

2. **Check test cookies**:
   ```bash
   cat test_cookies.txt
   ```

3. **Manual curl test**:
   ```bash
   # Get cookies from test output and test manually
   curl -H "Cookie: session=..." http://127.0.0.1:5000/api/settings
   ```

## Adding New Tests

To add a new API test:

1. Add test case to `test_api_with_curl.js`:
   ```javascript
   it('should test new endpoint', async () => {
       const response = makeAuthenticatedRequest(
           `${baseUrl}/api/new-endpoint`,
           cookieString,
           { headers: { 'Accept': 'application/json' } }
       );

       expect(response.status).to.equal(200);
       // Add more assertions
   });
   ```

2. For complex flows, use Puppeteer directly:
   ```javascript
   it('should handle complex UI flow', async () => {
       await page.goto(`${baseUrl}/some-page`);
       // Interact with page
       const result = await page.evaluate(() => {
           // Extract data from page
       });
       expect(result).to.exist;
   });
   ```

## CI/CD Integration

These tests can be integrated into CI/CD pipelines:

```yaml
# Example GitHub Actions
- name: Start LDR Server
  run: scripts/dev/restart_server.sh

- name: Run API Tests
  run: |
    cd tests/api_tests_with_login
    npm install
    npm test
```

## Troubleshooting

### Test Timeouts
- Increase timeout in test files: `this.timeout(60000);`
- Check for slow database operations

### Flaky Tests
- Add retry logic for network requests
- Ensure proper cleanup between tests
- Use unique usernames for each test run

### Browser Issues
- Update Puppeteer: `npm update puppeteer`
- Use different Chrome args for CI environments

## Contributing

When adding new tests:
1. Follow existing patterns for consistency
2. Add proper error messages for debugging
3. Clean up test data after completion
4. Document any new test utilities
5. Ensure tests are idempotent
