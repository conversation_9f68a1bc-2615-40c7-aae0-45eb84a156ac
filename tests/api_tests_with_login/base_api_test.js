/**
 * Base API Test Class
 *
 * ⚠️ IMPORTANT: THIS IS A REAL INTEGRATION TEST ⚠️
 *
 * This test runs against a REAL running LDR server and performs ACTUAL operations.
 * DO NOT convert this to a mock test - it's designed to test the full integration stack.
 *
 * This base class provides common functionality for all API endpoint tests.
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('../ui_tests/auth_helper');
const {
    getCookieStringFromPage,
    saveCookieJar,
    makeAuthenticatedRequest
} = require('./test_helpers');
const fs = require('fs');
const path = require('path');

class BaseApiTest {
    constructor(testName) {
        this.testName = testName;
        this.baseUrl = 'http://127.0.0.1:5000';
        this.testUsername = `${testName}_${Date.now()}`;
        this.testPassword = 'T3st!Secure#2024$LDR';
        this.cookieJarPath = path.join(__dirname, `${testName}_cookies.txt`);
    }

    async setup() {
        this.browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        this.page = await this.browser.newPage();
        this.authHelper = new AuthHelper(this.page, this.baseUrl);

        // Register and login
        await this.authHelper.ensureAuthenticated(this.testUsername, this.testPassword);

        // Save cookies for curl
        this.cookieString = await getCookieStringFromPage(this.page);
        await saveCookieJar(this.page, this.cookieJarPath);

        console.log(`✅ Test user authenticated: ${this.testUsername}`);
    }

    async teardown() {
        if (this.browser) {
            await this.browser.close();
        }
        // Clean up cookie jar
        if (fs.existsSync(this.cookieJarPath)) {
            fs.unlinkSync(this.cookieJarPath);
        }
    }

    makeRequest(endpoint, options = {}) {
        return makeAuthenticatedRequest(
            `${this.baseUrl}${endpoint}`,
            this.cookieString,
            options
        );
    }

    async getCSRFToken() {
        await this.page.goto(`${this.baseUrl}/`);
        return await this.page.evaluate(() => {
            const meta = document.querySelector('meta[name="csrf-token"]');
            return meta ? meta.content : null;
        });
    }
}

module.exports = BaseApiTest;
