#!/usr/bin/env python3
"""
Standalone test runner for pytest API tests
"""

import sys
import subprocess
from pathlib import Path

# Add the pytest_tests directory to Python path
test_dir = str(Path(__file__).parent / "pytest_tests")
sys.path.insert(0, test_dir)

# Run pytest with the tests
cmd = [
    sys.executable,
    "-m",
    "pytest",
    test_dir,
    "-v",
    "-s",
    "--tb=short",
    "--no-header",
]

# Add any command line arguments
if len(sys.argv) > 1:
    cmd.extend(sys.argv[1:])

# Run the tests
result = subprocess.run(cmd)
sys.exit(result.returncode)
