/**
 * Puppeteer configuration helper for CI/Docker environments
 */

function getPuppeteerLaunchOptions(additionalOptions = {}) {
    const defaultOptions = {
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu'
        ]
    };

    // In Docker/CI environments, explicitly skip Chrome download and use system Chrome/Chromium
    if (process.env.CI || process.env.DOCKER_ENV) {
        // Try to use <PERSON><PERSON>'s Chromium if available
        const possiblePaths = [
            '/root/.cache/ms-playwright/chromium-*/chrome-linux/chrome',
            '/ms-playwright/chromium-*/chrome-linux/chrome',
            '/usr/lib/chromium/chromium',
            '/usr/bin/chromium-browser',
            '/usr/bin/chromium'
        ];

        const { execSync } = require('child_process');
        for (const pathPattern of possiblePaths) {
            try {
                const chromiumPath = execSync(`ls ${pathPattern} 2>/dev/null | head -1`, { encoding: 'utf8' }).trim();
                if (chromiumPath && chromiumPath.length > 0) {
                    console.log(`Using Chromium at: ${chromiumPath}`);
                    defaultOptions.executablePath = chromiumPath;
                    break;
                }
            } catch (e) {
                // Continue to next path
            }
        }

        if (!defaultOptions.executablePath) {
            console.warn('Could not find Chromium/Chrome binary, Puppeteer will try to use its own');
        }
    }

    return { ...defaultOptions, ...additionalOptions };
}

module.exports = { getPuppeteerLaunchOptions };
