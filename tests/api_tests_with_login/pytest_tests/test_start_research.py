"""
Test starting research with model validation
"""

import os
import pytest
import time

# Skip entire module in CI
pytestmark = pytest.mark.skipif(
    os.environ.get("CI") == "true"
    or os.environ.get("GITHUB_ACTIONS") == "true",
    reason="API integration tests require running server - skipped in CI",
)


def test_start_research_with_model(auth_session, base_url):
    """Test starting research with specific model and verify it's actually used"""
    session, csrf_token = auth_session

    # Start research
    research_data = {
        "query": f"Test research with model validation {time.time()}",
        "search_engine": "auto",
        "model": "gemma3n:e2b",
        "model_provider": "OLLAMA",
        "mode": "quick",
        "iterations": 1,
        "questions_per_iteration": 1,
    }

    response = session.post(
        f"{base_url}/api/start_research", json=research_data
    )

    # Basic assertions
    assert response.status_code in [200, 201, 202], (
        f"Failed to start research: {response.text}"
    )

    data = response.json()
    assert "research_id" in data, "No research_id in response"

    research_id = data["research_id"]
    assert len(research_id) == 36, "Invalid research_id format"

    print(f"Started research with ID: {research_id}")

    # Wait for research to start processing
    time.sleep(2)

    # Check research status and logs
    status_response = session.get(
        f"{base_url}/api/research/{research_id}/status"
    )
    assert status_response.status_code == 200, (
        f"Failed to get status: {status_response.text}"
    )

    status_data = status_response.json()
    assert "status" in status_data
    print(f"Research status: {status_data['status']}")

    # Get logs to verify model is being used
    logs_response = session.get(f"{base_url}/api/research/{research_id}/logs")
    if logs_response.status_code == 200:
        logs_data = logs_response.json()
        logs = logs_data.get("logs", [])

        # Check if model appears in logs
        model_found = False
        for log in logs:
            if "gemma3n:e2b" in str(log) or "model" in str(log).lower():
                print(f"Log entry mentioning model: {log}")
                model_found = True

        # This should fail if model isn't being passed!
        assert model_found, (
            "Model 'gemma3n:e2b' not found in research logs - model parameter may not be passed correctly!"
        )
