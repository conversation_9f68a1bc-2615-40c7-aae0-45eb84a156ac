"""
Test research API validation
"""


def test_research_without_required_fields(auth_session, base_url):
    """Test validation of required fields"""
    session, csrf_token = auth_session

    # Only test truly invalid requests
    # Note: Empty model is now allowed and uses defaults from database
    invalid_requests = [
        {},  # Empty request - missing query
        {"model": "gemma3n:e2b"},  # Missing query
        {"query": "", "model": "gemma3n:e2b"},  # Empty query
    ]

    for data in invalid_requests:
        response = session.post(f"{base_url}/api/start_research", json=data)

        assert response.status_code in [400, 422], (
            f"Expected validation error for {data}, got {response.status_code}"
        )
        print(f"✓ Correctly rejected invalid request: {data}")

    # Test that requests with query but no model are accepted (uses defaults)
    valid_requests_with_defaults = [
        {"query": "test"},  # Missing model - uses default
        {"query": "test", "model": ""},  # Empty model - uses default
    ]

    for data in valid_requests_with_defaults:
        response = session.post(f"{base_url}/api/start_research", json=data)

        # Requests with query but missing/empty model should succeed (200 or research started)
        assert response.status_code == 200, (
            f"Expected success for {data}, got {response.status_code}"
        )
        print(f"✓ Correctly accepted request with default model: {data}")


def test_research_requires_authentication(base_url):
    """Test that research API requires authentication"""
    import requests

    # Try without any auth
    response = requests.post(
        f"{base_url}/api/start_research",
        json={
            "query": "test",
            "model": "gemma3n:e2b",
            "model_provider": "OLLAMA",
        },
    )

    # Accept both 400 (bad request) and 401 (unauthorized) as both indicate the request was rejected
    assert response.status_code in [400, 401], (
        f"Expected 400 or 401 for unauthenticated request, got {response.status_code}"
    )
    print("✓ API correctly requires authentication")
