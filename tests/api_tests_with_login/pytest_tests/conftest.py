"""
Pytest configuration and shared fixtures for API tests
"""

import json
import subprocess
import time
import pytest
import requests
from pathlib import Path
import tempfile
import os

# Base URL for tests
BASE_URL = os.environ.get("LDR_TEST_BASE_URL", "http://127.0.0.1:5000")
TEST_USERNAME = f"testuser_{int(time.time())}"
TEST_PASSWORD = "testpass123"


class AuthHelper:
    """Helper class to handle Puppeteer authentication"""

    @staticmethod
    def get_auth_cookies():
        """Use Puppeteer to authenticate and get cookies"""
        # Create a temporary file for cookie storage
        cookie_file = tempfile.NamedTemporaryFile(
            mode="w", suffix=".json", delete=False
        )
        cookie_file.close()

        # Run the Node.js auth helper
        auth_script = Path(__file__).parent.parent / "auth_helper.js"
        cmd = [
            "node",
            str(auth_script),
            BASE_URL,
            TEST_USERNAME,
            TEST_PASSWORD,
            cookie_file.name,
        ]

        try:
            result = subprocess.run(
                cmd, capture_output=True, text=True, timeout=90
            )
            if result.returncode != 0:
                raise Exception(f"Auth failed: {result.stderr}")

            # Read cookies from file
            with open(cookie_file.name, "r") as f:
                cookies = json.load(f)

            # Convert to requests format
            cookie_dict = {c["name"]: c["value"] for c in cookies}

            # Extract CSRF token
            csrf_token = None
            for cookie in cookies:
                if cookie["name"] == "csrf_token":
                    csrf_token = cookie["value"]
                    break

            return cookie_dict, csrf_token

        finally:
            # Clean up temp file
            if Path(cookie_file.name).exists():
                os.unlink(cookie_file.name)


@pytest.fixture(scope="session")
def auth_session():
    """Session-wide fixture for authenticated requests"""
    cookies, csrf_token = AuthHelper.get_auth_cookies()

    session = requests.Session()
    session.cookies.update(cookies)
    session.headers.update(
        {"X-CSRFToken": csrf_token, "Accept": "application/json"}
    )

    yield session, csrf_token

    session.close()


@pytest.fixture
def base_url():
    """Fixture to provide base URL"""
    return BASE_URL
