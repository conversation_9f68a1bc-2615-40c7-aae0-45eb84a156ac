"""
Test research completion and results
"""

import time
import pytest


def test_research_completion_time(auth_session, base_url):
    """Test that research actually completes within reasonable time"""
    session, csrf_token = auth_session

    # Start research
    research_data = {
        "query": "Quick test for completion time",
        "search_engine": "auto",
        "model": "gemma3n:e2b",
        "model_provider": "OLLAMA",
        "mode": "quick",
        "iterations": 1,
        "questions_per_iteration": 1,
    }

    response = session.post(
        f"{base_url}/api/start_research", json=research_data
    )

    assert response.status_code in [200, 201, 202]
    research_id = response.json()["research_id"]

    # Poll for completion
    start_time = time.time()
    max_wait = 60  # 1 minute max
    completed = False

    while time.time() - start_time < max_wait:
        status_response = session.get(
            f"{base_url}/api/research/{research_id}/status"
        )
        if status_response.status_code == 200:
            status = status_response.json().get("status")
            print(f"Status after {int(time.time() - start_time)}s: {status}")

            if status == "completed":
                completed = True
                break
            elif status == "failed":
                # Get logs to understand failure
                logs_response = session.get(
                    f"{base_url}/api/research/{research_id}/logs"
                )
                if logs_response.status_code == 200:
                    print("Research failed. Logs:")
                    for log in logs_response.json().get("logs", [])[
                        -10:
                    ]:  # Last 10 logs
                        print(f"  {log}")
                pytest.fail("Research failed - check logs above")

        time.sleep(2)

    assert completed, f"Research did not complete within {max_wait} seconds"
    print(f"✓ Research completed in {int(time.time() - start_time)} seconds")
