"""
Test research results and report generation
"""

import time
import pytest


def test_research_produces_valid_report(auth_session, base_url):
    """Test that research produces a valid report with content"""
    session, csrf_token = auth_session

    # Start research
    research_data = {
        "query": "Test query for report validation",
        "search_engine": "auto",
        "model": "gemma3n:e2b",
        "model_provider": "OLLAMA",
        "mode": "quick",
        "iterations": 1,
        "questions_per_iteration": 1,
    }

    response = session.post(
        f"{base_url}/api/start_research", json=research_data
    )

    assert response.status_code in [200, 201, 202]
    research_id = response.json()["research_id"]

    # Wait for completion
    max_attempts = 30
    for i in range(max_attempts):
        status_response = session.get(
            f"{base_url}/api/research/{research_id}/status"
        )
        if status_response.status_code == 200:
            status = status_response.json().get("status")
            if status == "completed":
                break
            elif status == "failed":
                pytest.fail("Research failed")
        time.sleep(2)

    # Get the report
    report_response = session.get(f"{base_url}/api/report/{research_id}")
    assert report_response.status_code == 200, (
        f"Failed to get report: {report_response.text}"
    )

    report_data = report_response.json()

    # Verify report structure
    assert "title" in report_data or "content" in report_data, (
        "Report missing basic structure"
    )

    # If model isn't being used, report will be minimal/empty
    if "content" in report_data:
        content_length = len(str(report_data["content"]))
        assert content_length > 100, (
            f"Report content too short ({content_length} chars) - model may not be running"
        )
        print(f"✓ Report generated with {content_length} characters of content")

    # Check for expected report sections
    report_str = str(report_data)
    expected_indicators = ["sources", "references", "summary", "findings"]
    found_indicators = [
        ind for ind in expected_indicators if ind in report_str.lower()
    ]

    assert len(found_indicators) > 0, (
        f"Report lacks expected sections. Found: {found_indicators}"
    )
    print(f"✓ Report contains sections: {found_indicators}")
