"""
Specific tests to verify model parameter is passed correctly
"""

import time


def test_model_parameter_in_logs(auth_session, base_url):
    """Verify the model parameter appears in research logs"""
    session, csrf_token = auth_session

    # Start research with specific model
    research_data = {
        "query": "Test model parameter passing",
        "search_engine": "auto",
        "model": "gemma3n:e2b",
        "model_provider": "OLLAMA",
        "mode": "quick",
        "iterations": 1,
        "questions_per_iteration": 1,
    }

    response = session.post(
        f"{base_url}/api/start_research", json=research_data
    )

    assert response.status_code in [200, 201, 202]
    research_id = response.json()["research_id"]

    # Give it time to start
    time.sleep(3)

    # Check logs for model parameter
    logs_response = session.get(f"{base_url}/api/research/{research_id}/logs")
    assert logs_response.status_code == 200, "Failed to get logs"

    logs = logs_response.json()  # The response is already a list

    # Look for model in logs
    model_mentions = []
    for log in logs:
        log_str = str(log).lower()
        if "model" in log_str or "gemma3n" in log_str or "ollama" in log_str:
            model_mentions.append(log)
            print(f"Found model-related log: {log}")

    assert len(model_mentions) > 0, (
        "No mention of model in logs - parameter not being passed!"
    )

    # Specifically check for the model name
    model_name_found = any("gemma3n:e2b" in str(log) for log in logs)
    assert model_name_found, "Model name 'gemma3n:e2b' not found in any logs!"


def test_empty_model_parameter_allowed(auth_session, base_url):
    """Test that empty model parameter is allowed (uses default)"""
    session, csrf_token = auth_session

    research_data = {
        "query": "Test with empty model",
        "search_engine": "auto",
        "model": "",  # Empty model should use default
        "model_provider": "OLLAMA",
        "mode": "quick",
    }

    response = session.post(
        f"{base_url}/api/start_research", json=research_data
    )

    assert response.status_code in [200, 201, 202], (
        f"Empty model should be allowed (uses default), got {response.status_code}"
    )
    print("✓ Empty model parameter correctly allowed (uses default)")
