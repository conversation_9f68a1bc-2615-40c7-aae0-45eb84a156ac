/**
 * Minimal test for export functionality
 */

const { expect } = require('chai');
const BaseApiTest = require('./base_api_test');

describe('Export Functionality Tests', function() {
    this.timeout(60000);

    const apiTest = new BaseApiTest('export_tests');
    let testResearchId = null;

    before(async () => {
        await apiTest.setup();
    });

    after(async () => {
        await apiTest.cleanup();
    });

    it('should start research and wait for completion', async () => {
        const researchData = {
            query: `Test export functionality ${Date.now()}`,
            search_engine: 'auto',
            model: 'gemma3n:e2b',
            model_provider: 'OLLAMA',
            mode: 'quick',
            iterations: 1,
            questions_per_iteration: 1
        };

        const response = await apiTest.makeAuthenticatedRequest('/api/start_research', {
            method: 'POST',
            body: JSON.stringify(researchData)
        });

        expect(response.status).to.be.oneOf([200, 201, 202]);
        const data = await response.json();
        expect(data).to.have.property('research_id');

        testResearchId = data.research_id;
        console.log(`Started research: ${testResearchId}`);

        // Wait for research to complete
        console.log('Waiting for research to complete...');
        await new Promise(resolve => setTimeout(resolve, 15000));
    });

    it('should have report content available', async () => {
        const response = await apiTest.makeAuthenticatedRequest(`/api/report/${testResearchId}`);
        expect(response.status).to.equal(200);

        const data = await response.json();
        console.log(`Report keys: ${Object.keys(data).join(', ')}`);

        const content = data.content || data.markdown || '';
        expect(content).to.have.length.greaterThan(100);
        console.log(`Report has ${content.length} characters`);
    });

    it('should export to LaTeX format', async () => {
        const response = await apiTest.makeAuthenticatedRequest(
            `/api/v1/research/${testResearchId}/export/latex`,
            { method: 'POST' }
        );

        console.log(`LaTeX export status: ${response.status}`);
        console.log(`Content-Type: ${response.headers.get('content-type')}`);
        console.log(`Content-Disposition: ${response.headers.get('content-disposition')}`);

        const content = await response.text();
        console.log(`LaTeX content length: ${content.length} chars`);

        if (content.length === 0) {
            console.error('ERROR: LaTeX export returned empty content!');

            // Try to get more debug info
            const debugResponse = await apiTest.makeAuthenticatedRequest(`/api/research/${testResearchId}/status`);
            const debugData = await debugResponse.json();
            console.log('Research status:', JSON.stringify(debugData, null, 2));
        }

        expect(response.status).to.equal(200);
        expect(content).to.have.length.greaterThan(0);
        expect(content).to.include('\\documentclass').or.include('\\begin{document}');
    });

    it('should export to Quarto format', async () => {
        const response = await apiTest.makeAuthenticatedRequest(
            `/api/v1/research/${testResearchId}/export/quarto`,
            { method: 'POST' }
        );

        console.log(`Quarto export status: ${response.status}`);
        const content = await response.text();
        console.log(`Quarto content length: ${content.length} chars`);

        expect(response.status).to.equal(200);
        expect(content).to.have.length.greaterThan(0);
    });

    it('should export to RIS format', async () => {
        const response = await apiTest.makeAuthenticatedRequest(
            `/api/v1/research/${testResearchId}/export/ris`,
            { method: 'POST' }
        );

        console.log(`RIS export status: ${response.status}`);
        const content = await response.text();
        console.log(`RIS content length: ${content.length} chars`);

        expect(response.status).to.equal(200);
        expect(content).to.have.length.greaterThan(0);
        expect(content).to.include('TY  -');
        expect(content).to.include('ER  -');
    });
});
