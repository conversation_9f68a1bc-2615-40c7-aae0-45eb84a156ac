"""
Test authentication routes including login, register, and logout.
"""

import os
import shutil
import tempfile
from pathlib import Path

import pytest

from src.local_deep_research.database.auth_db import (
    get_auth_db_session,
    init_auth_database,
)
from src.local_deep_research.database.encrypted_db import db_manager
from src.local_deep_research.database.models.auth import User
from src.local_deep_research.web.app_factory import create_app


@pytest.fixture
def temp_data_dir():
    """Create a temporary data directory for testing."""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def app(temp_data_dir, monkeypatch):
    """Create a Flask app configured for testing."""
    # Override data directory
    monkeypatch.setenv("LDR_DATA_DIR", str(temp_data_dir))

    # Clear database manager state
    db_manager.connections.clear()

    # Reset db_manager's data directory to temp directory
    db_manager.data_dir = temp_data_dir / "encrypted_databases"
    db_manager.data_dir.mkdir(parents=True, exist_ok=True)

    # Create app with testing config
    app, _ = create_app()
    app.config["TESTING"] = True
    app.config["WTF_CSRF_ENABLED"] = False
    app.config["SESSION_COOKIE_SECURE"] = False  # For testing without HTTPS

    # Initialize auth database
    init_auth_database()

    # Clean up any existing test users
    auth_db = get_auth_db_session()
    auth_db.query(User).filter(User.username.like("testuser%")).delete()
    auth_db.commit()
    auth_db.close()

    yield app

    # Cleanup after test
    db_manager.connections.clear()


@pytest.fixture
def client(app):
    """Create a test client."""
    return app.test_client()


class TestAuthRoutes:
    """Test authentication routes."""

    def test_root_redirects_to_login(self, client):
        """Test that unauthenticated users are redirected to login."""
        response = client.get("/")
        assert response.status_code == 302
        assert "/auth/login" in response.location

    def test_login_page_loads(self, client):
        """Test that login page loads successfully."""
        response = client.get("/auth/login")
        assert response.status_code == 200
        assert b"Local Deep Research" in response.data
        assert b"Your data is encrypted" in response.data

    def test_register_page_loads(self, client):
        """Test that register page loads successfully."""
        response = client.get("/auth/register")
        assert response.status_code == 200
        assert b"Create Account" in response.data
        assert b"NO way to recover your data" in response.data

    def test_successful_registration(self, client):
        """Test successful user registration."""
        response = client.post(
            "/auth/register",
            data={
                "username": "testuser",
                "password": "testpassword123",
                "confirm_password": "testpassword123",
                "acknowledge": "true",
            },
            follow_redirects=True,
        )

        assert response.status_code == 200

        # Check user was created in auth database
        auth_db = get_auth_db_session()
        user = auth_db.query(User).filter_by(username="testuser").first()
        assert user is not None
        assert user.username == "testuser"
        auth_db.close()

        # Check user database exists
        assert db_manager.user_exists("testuser")

    def test_registration_validation(self, client):
        """Test registration form validation."""
        # Test missing fields
        response = client.post("/auth/register", data={})
        assert response.status_code == 400
        assert b"Username is required" in response.data

        # Test short username
        response = client.post(
            "/auth/register",
            data={
                "username": "ab",
                "password": "testpassword123",
                "confirm_password": "testpassword123",
                "acknowledge": "true",
            },
        )
        assert response.status_code == 400
        assert b"Username must be at least 3 characters" in response.data

        # Test password mismatch
        response = client.post(
            "/auth/register",
            data={
                "username": "testuser",
                "password": "password1",
                "confirm_password": "password2",
                "acknowledge": "true",
            },
        )
        assert response.status_code == 400
        assert b"Passwords do not match" in response.data

        # Test missing acknowledgment
        response = client.post(
            "/auth/register",
            data={
                "username": "testuser",
                "password": "testpassword123",
                "confirm_password": "testpassword123",
                "acknowledge": "false",
            },
        )
        assert response.status_code == 400
        assert b"You must acknowledge" in response.data

    def test_duplicate_username(self, client):
        """Test that duplicate usernames are rejected."""
        # Register first user
        client.post(
            "/auth/register",
            data={
                "username": "testuser",
                "password": "testpassword123",
                "confirm_password": "testpassword123",
                "acknowledge": "true",
            },
        )

        # Try to register same username
        response = client.post(
            "/auth/register",
            data={
                "username": "testuser",
                "password": "otherpassword123",
                "confirm_password": "otherpassword123",
                "acknowledge": "true",
            },
        )
        assert response.status_code == 400
        assert b"Username already exists" in response.data

    def test_successful_login(self, client):
        """Test successful login."""
        # Register user first
        client.post(
            "/auth/register",
            data={
                "username": "testuser",
                "password": "testpassword123",
                "confirm_password": "testpassword123",
                "acknowledge": "true",
            },
        )

        # Logout
        client.post("/auth/logout")

        # Login
        response = client.post(
            "/auth/login",
            data={"username": "testuser", "password": "testpassword123"},
            follow_redirects=True,
        )

        assert response.status_code == 200

        # Check session
        with client.session_transaction() as sess:
            assert "username" in sess
            assert sess["username"] == "testuser"

    def test_invalid_login(self, client):
        """Test login with invalid credentials."""
        response = client.post(
            "/auth/login",
            data={"username": "nonexistent", "password": "wrongpassword"},
        )

        assert response.status_code == 401
        assert b"Invalid username or password" in response.data

    def test_logout(self, client):
        """Test logout functionality."""
        # Register and login
        client.post(
            "/auth/register",
            data={
                "username": "testuser",
                "password": "testpassword123",
                "confirm_password": "testpassword123",
                "acknowledge": "true",
            },
        )

        # Verify logged in
        with client.session_transaction() as sess:
            assert sess.get("username") == "testuser"

        # Logout
        response = client.post("/auth/logout", follow_redirects=False)
        assert response.status_code == 302
        assert "/auth/login" in response.location

        # Check session is cleared
        with client.session_transaction() as sess:
            assert "username" not in sess

    @pytest.mark.skipif(
        os.environ.get("CI") == "true"
        or os.environ.get("GITHUB_ACTIONS") == "true",
        reason="Password change with encrypted DB re-keying is complex to test in CI",
    )
    def test_change_password(self, client):
        """Test password change functionality."""
        # Register user
        client.post(
            "/auth/register",
            data={
                "username": "testuser",
                "password": "oldpassword123",
                "confirm_password": "oldpassword123",
                "acknowledge": "true",
            },
        )

        # Change password - don't follow redirects to check status
        response = client.post(
            "/auth/change-password",
            data={
                "current_password": "oldpassword123",
                "new_password": "newpassword456",
                "confirm_password": "newpassword456",
            },
            follow_redirects=False,
        )

        # Should redirect to login after successful password change
        assert response.status_code == 302
        assert "/auth/login" in response.location

        # Now follow the redirect to login page
        response = client.get("/auth/login")
        assert response.status_code == 200

        # Try to login with new password
        response = client.post(
            "/auth/login",
            data={"username": "testuser", "password": "newpassword456"},
            follow_redirects=True,
        )

        assert response.status_code == 200

    def test_remember_me(self, client):
        """Test remember me functionality."""
        # Register user
        client.post(
            "/auth/register",
            data={
                "username": "testuser",
                "password": "testpassword123",
                "confirm_password": "testpassword123",
                "acknowledge": "true",
            },
        )

        # Logout
        client.post("/auth/logout")

        # Login with remember me
        client.post(
            "/auth/login",
            data={
                "username": "testuser",
                "password": "testpassword123",
                "remember": "true",
            },
        )

        with client.session_transaction() as sess:
            assert sess.permanent is True

    def test_auth_check_endpoint(self, client):
        """Test the authentication check endpoint."""
        # Not logged in
        response = client.get("/auth/check")
        assert response.status_code == 401
        data = response.get_json()
        assert data["authenticated"] is False

        # Register and check
        client.post(
            "/auth/register",
            data={
                "username": "testuser",
                "password": "testpassword123",
                "confirm_password": "testpassword123",
                "acknowledge": "true",
            },
        )

        response = client.get("/auth/check")
        assert response.status_code == 200
        data = response.get_json()
        assert data["authenticated"] is True
        assert data["username"] == "testuser"
