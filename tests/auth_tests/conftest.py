"""
Pytest configuration for authentication tests.
"""

import os
import sys
from pathlib import Path

import pytest

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))

# Disable HTTPS for testing
os.environ["LDR_HTTPS_TESTING"] = "1"


@pytest.fixture(autouse=True)
def reset_singletons():
    """Reset singleton instances between tests."""
    # Clear database manager connections
    from src.local_deep_research.database.encrypted_db import db_manager

    db_manager.connections.clear()

    # Clear auth session manager
    from src.local_deep_research.web.auth.routes import session_manager

    session_manager.sessions.clear()

    yield

    # Cleanup after test
    db_manager.connections.clear()
    session_manager.sessions.clear()


@pytest.fixture
def mock_settings(monkeypatch):
    """Mock settings for testing."""
    # Settings mocking is no longer needed since the deprecated function was removed
    # The app will use default values from environment or settings files
    pass
