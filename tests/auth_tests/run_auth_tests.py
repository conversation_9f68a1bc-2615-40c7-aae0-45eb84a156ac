#!/usr/bin/env python3
"""
Run authentication tests with proper configuration.
"""

import os
import subprocess
import sys
from pathlib import Path

# Add project root to Python path
project_root = str(Path(__file__).parent.parent.parent.resolve())
sys.path.insert(0, project_root)

# Set test environment
os.environ["TESTING"] = "1"
os.environ["LDR_HTTPS_TESTING"] = "1"


def main():
    """Run authentication tests."""
    print("Running LDR Authentication Tests...")
    print("=" * 60)

    # Change to project root
    os.chdir(project_root)

    # Run tests with pytest
    cmd = [
        sys.executable,
        "-m",
        "pytest",
        "tests/auth_tests/",
        "-v",  # Verbose output
        "--tb=short",  # Short traceback format
        "--strict-markers",  # Strict marker usage
        "-p",
        "no:warnings",  # Disable warnings
    ]

    # Add coverage if requested
    if "--coverage" in sys.argv:
        cmd.extend(
            [
                "--cov=src/local_deep_research/web/auth",
                "--cov=src/local_deep_research/database/encrypted_db",
                "--cov=src/local_deep_research/database/auth_db",
                "--cov-report=term-missing",
                "--cov-report=html:htmlcov",
            ]
        )
        print("Running with coverage reporting...")

    # Run the tests
    result = subprocess.run(cmd)

    if result.returncode == 0:
        print("\n" + "=" * 60)
        print("✅ All authentication tests passed!")
        if "--coverage" in sys.argv:
            print("📊 Coverage report generated in htmlcov/index.html")
    else:
        print("\n" + "=" * 60)
        print("❌ Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
