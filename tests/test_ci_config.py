"""
CI-specific test configuration for API tests.
Sets up environment for tests to run without external LLM services.
"""

import os

# Set environment variables for CI testing
os.environ["LDR_TESTING_MODE"] = "true"
os.environ["LDR_USE_FALLBACK_LLM"] = "true"

# Disable all external LLM providers
os.environ["LDR_DISABLE_OLLAMA"] = "true"
os.environ["LDR_DISABLE_OPENAI"] = "true"
os.environ["LDR_DISABLE_ANTHROPIC"] = "true"

# Set test-specific configurations
os.environ["LDR_LLM_TIMEOUT"] = "5"  # Shorter timeout for tests
os.environ["LDR_SEARCH_TIMEOUT"] = "5"  # Shorter timeout for searches
