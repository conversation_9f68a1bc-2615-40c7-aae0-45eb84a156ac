"""
Test script to demonstrate the duplicate links issue and our fix for issue #301.

This test demonstrates the problem where the search system duplicates links in detailed report mode
by unconditionally extending the all_links_of_system list with itself.

GitHub issue: https://github.com/LearningCircuit/local-deep-research/issues/301

The fix is to check if the lists are the same object (have the same id()) before extending.
"""


class Strategy:
    def __init__(self, all_links=None):
        # Create a new list if None is provided
        self.all_links_of_system = [] if all_links is None else all_links
        print(
            f"Strategy initialized with list id: {id(self.all_links_of_system)}"
        )

    def analyze_topic(self, query):
        # Add some links
        self.all_links_of_system.extend(
            [
                {"title": "Link 1", "link": "http://example.com/1"},
                {"title": "Link 2", "link": "http://example.com/2"},
            ]
        )
        print(f"Strategy now has {len(self.all_links_of_system)} links")
        return {"content": "Analysis results"}


class AdvancedSearchSystem:
    def __init__(self):
        # Initialize with empty list
        self.all_links_of_system = []
        print(
            f"Search system initialized with list id: {id(self.all_links_of_system)}"
        )

        # Create strategy with our list reference
        self.strategy = Strategy(all_links=self.all_links_of_system)

        # Check if they're the same object
        print(
            f"Are lists the same object? {id(self.all_links_of_system) == id(self.strategy.all_links_of_system)}"
        )

    def analyze_topic_with_bug(self, query):
        # Run the strategy
        result = self.strategy.analyze_topic(query)

        # BUG: Unconditionally extend our list with the strategy's list
        # This is problematic because they're the same list
        print("\nBUG DEMO: Extending unconditionally")
        before_count = len(self.all_links_of_system)
        print(f"Before extending: {before_count} links")

        self.all_links_of_system.extend(self.strategy.all_links_of_system)

        after_count = len(self.all_links_of_system)
        print(f"After extending: {after_count} links")
        print(f"Added {after_count - before_count} links (duplicates)")

        # Return results
        return {
            "all_links_of_system": self.all_links_of_system,
            "content": result["content"],
        }

    def analyze_topic_with_fix(self, query):
        # Run the strategy
        result = self.strategy.analyze_topic(query)

        # FIX: Only extend if they're different objects
        print("\nFIX DEMO: Only extending if lists are different")
        before_count = len(self.all_links_of_system)
        print(f"Before fix check: {before_count} links")

        if id(self.all_links_of_system) != id(
            self.strategy.all_links_of_system
        ):
            print("Lists are different objects - extending")
            self.all_links_of_system.extend(self.strategy.all_links_of_system)
        else:
            print(
                "Lists are the same object - not extending (avoiding duplicates)"
            )

        after_count = len(self.all_links_of_system)
        print(f"After fix check: {after_count} links")
        print(f"Added {after_count - before_count} links")

        # Return results
        return {
            "all_links_of_system": self.all_links_of_system,
            "content": result["content"],
        }


def test_bug_and_fix():
    print("=== Testing Bug and Fix ===\n")

    print("1. Creating first search system to demonstrate the bug:")
    search_system1 = AdvancedSearchSystem()
    result1 = search_system1.analyze_topic_with_bug(
        "What is quantum computing?"
    )
    print(f"\nFinal link count with bug: {len(result1['all_links_of_system'])}")

    print("\n2. Creating second search system to demonstrate the fix:")
    search_system2 = AdvancedSearchSystem()
    result2 = search_system2.analyze_topic_with_fix(
        "What is quantum computing?"
    )
    print(f"\nFinal link count with fix: {len(result2['all_links_of_system'])}")

    print("\nTest complete!")


if __name__ == "__main__":
    test_bug_and_fix()
