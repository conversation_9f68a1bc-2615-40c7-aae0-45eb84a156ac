# Fix Tests

This directory contains tests for specific bug fixes in the codebase.

## Tests

### test_duplicate_links_fix.py

Demonstrates and tests the fix for GitHub issue [#301](https://github.com/LearningCircuit/local-deep-research/issues/301) - "too many links in detailed report mode".

The test simulates the bug where links are duplicated in the `all_links_of_system` list when using detailed report mode. It compares the behavior before and after implementing the fix.

#### Bug Description

The bug occurred because `search_system.py` was unconditionally extending `self.all_links_of_system` with `self.strategy.all_links_of_system` in the `analyze_topic` method, even though they were the same list object (when initialized with the same reference).

#### Fix

The fix checks if the lists are the same object (have the same `id()`) before extending:

```python
# Only extend if they're different objects in memory to avoid duplication
if id(self.all_links_of_system) != id(self.strategy.all_links_of_system):
    self.all_links_of_system.extend(self.strategy.all_links_of_system)
```

This prevents duplicating the content when both lists are actually the same object.

#### Running the Test

```bash
cd tests/fix_tests
python test_duplicate_links_fix.py
```

The test demonstrates both the bug and the fixed behavior side-by-side.
