# Test Fixtures

This directory contains reusable test fixtures and mock utilities for the Local Deep Research project.

## Overview

These fixtures were created based on s<PERSON>vr's testing proposal (PR #361) but have been updated to address review feedback:

1. **Removed outdated config file references** - The original MockConfigFiles class has been removed as the project no longer uses config files
2. **Added security validation** - Includes proper URL validation for Wikipedia to address security concerns
3. **Improved modularity** - Split fixtures into logical modules for better organization

## Files

### `search_engine_mocks.py`
Contains mock responses for various search engines:
- Wikipedia API responses
- arXiv XML responses
- PubMed search and article responses
- Semantic Scholar API responses
- Google Programmable Search Engine responses
- DuckDuckGo search responses
- Error response collections for testing error handling

Also includes:
- `validate_wikipedia_url()` - Security function to validate Wikipedia URLs
- Mock API response fixtures for successful and error cases

### Usage Example

```python
from tests.fixtures.search_engine_mocks import SearchEngineMocks, validate_wikipedia_url

def test_wikipedia_search(search_engine_mocks):
    mock_response = search_engine_mocks.wikipedia_response()
    # Use mock_response in your test

def test_url_security():
    assert validate_wikipedia_url("https://en.wikipedia.org/wiki/Test") == True
    assert validate_wikipedia_url("https://evil.com/wiki/Test") == False
```

## Security Considerations

The `validate_wikipedia_url()` function addresses the security issue mentioned in PR #361 review by:
- Properly parsing URLs using `urllib.parse`
- Checking that the hostname ends with `.wikipedia.org` or is exactly `wikipedia.org`
- Handling exceptions gracefully
- Rejecting malformed or malicious URLs

## Integration with conftest.py

The main `conftest.py` file imports these fixtures to make them available throughout the test suite. The fixtures can be used directly in tests via dependency injection.
