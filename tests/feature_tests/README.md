# Feature Tests

This directory contains test scripts for potential features and improvements before they are fully implemented.

## Available Tests

### test_custom_context.py

Tests the implementation of a custom context window size setting for LLMs (issue #241).

**Purpose:**
- Verify that we can correctly apply a custom context window size to various LLM providers
- Test the calculation of appropriate max_tokens values based on context window size
- Identify any potential issues with implementation

**How to run:**
```bash
cd local-deep-research
source ../venv/bin/activate  # Or your virtual environment path
python tests/feature_tests/test_custom_context.py
```

**What it demonstrates:**
This test simulates adding a `llm.context_window_size` setting that would allow users to specify the context window size of their model. This is especially useful for custom models like Llama.cpp and LM Studio where the default 30000 tokens might exceed the model's capabilities.

The test patches the settings system to include the new setting, then demonstrates how max_tokens would be calculated based on the specified context window size.
