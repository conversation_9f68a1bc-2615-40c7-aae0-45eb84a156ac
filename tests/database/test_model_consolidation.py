#!/usr/bin/env python3
"""Test that all models are properly consolidated in the database.models package."""

import sys
from pathlib import Path

sys.path.insert(
    0,
    str(Path(__file__).parent.parent.parent.resolve()),
)


def test_all_models_importable():
    """Test that all models can be imported from the consolidated location."""
    # This should not raise any ImportError
    from src.local_deep_research.database.models import (
        Base,
        BenchmarkRun,
        # Benchmark
        ResearchHistory,
        User,
    )

    # If we get here, all imports worked
    assert Base is not None
    assert User is not None
    assert BenchmarkRun is not None
    assert ResearchHistory is not None
    print("✓ All models successfully imported from consolidated location")


def test_benchmark_models_relationships():
    """Test that benchmark model relationships are properly defined."""
    from src.local_deep_research.database.models import (
        BenchmarkProgress,
        BenchmarkResult,
        BenchmarkRun,
    )

    # Check that relationships are defined
    assert hasattr(BenchmarkRun, "results")
    assert hasattr(BenchmarkRun, "progress_updates")
    assert hasattr(BenchmarkResult, "benchmark_run")
    assert hasattr(BenchmarkProgress, "benchmark_run")
    print("✓ Benchmark model relationships properly defined")


def test_research_models_have_correct_columns():
    """Test that research models have the expected columns after consolidation."""
    from src.local_deep_research.database.models import (
        ResearchHistory,
        ResearchResource,
    )

    # Check ResearchHistory has renamed metadata column
    assert hasattr(ResearchHistory, "research_meta")
    assert hasattr(ResearchHistory, "query")
    assert hasattr(ResearchHistory, "status")

    # Check ResearchResource has renamed metadata column
    assert hasattr(ResearchResource, "resource_metadata")
    assert hasattr(ResearchResource, "title")
    assert hasattr(ResearchResource, "url")
    print("✓ Research models have correct column names")


if __name__ == "__main__":
    test_all_models_importable()
    test_benchmark_models_relationships()
    test_research_models_have_correct_columns()
    print("\n✅ All model consolidation tests passed!")
