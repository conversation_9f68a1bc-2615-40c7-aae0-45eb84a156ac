"""
Mock fixtures for testing - inspired by scottvr's contributions.

This module contains reusable mock data and fixtures for various components.
"""

from typing import Any, Dict, List

# ============== Search Engine Mock Responses ==============


def get_mock_search_results() -> List[Dict[str, Any]]:
    """Sample search results for testing."""
    return [
        {
            "title": "Test Result 1",
            "link": "https://example.com/1",
            "snippet": "This is the first test result snippet.",
            "full_content": "This is the full content of the first test result.",
        },
        {
            "title": "Test Result 2",
            "link": "https://example.com/2",
            "snippet": "This is the second test result snippet.",
            "full_content": "This is the full content of the second test result.",
        },
    ]


def get_mock_wikipedia_response() -> Dict[str, Any]:
    """Mock response from Wikipedia API."""
    return {
        "query": {
            "search": [
                {
                    "title": "Artificial intelligence",
                    "snippet": "Artificial intelligence (AI) is intelligence demonstrated by machines...",
                    "pageid": 12345,
                },
                {
                    "title": "Machine learning",
                    "snippet": "Machine learning (ML) is a subset of artificial intelligence (AI)...",
                    "pageid": 67890,
                },
            ]
        }
    }


def get_mock_arxiv_response() -> str:
    """Mock XML response from arXiv API."""
    return """<?xml version="1.0" encoding="UTF-8"?>
    <feed xmlns="http://www.w3.org/2005/Atom">
        <entry>
            <title>Test Paper Title</title>
            <id>http://arxiv.org/abs/2301.12345</id>
            <summary>This is a test paper abstract.</summary>
            <published>2023-01-15T00:00:00Z</published>
            <author>
                <name>Test Author</name>
            </author>
        </entry>
    </feed>
    """


def get_mock_pubmed_response() -> str:
    """Mock XML response from PubMed search API."""
    return """<?xml version="1.0" encoding="UTF-8"?>
    <eSearchResult>
        <IdList>
            <Id>12345678</Id>
        </IdList>
    </eSearchResult>
    """


def get_mock_pubmed_article() -> str:
    """Mock PubMed article detail XML."""
    return """<?xml version="1.0" encoding="UTF-8"?>
    <PubmedArticleSet>
        <PubmedArticle>
            <MedlineCitation>
                <PMID>12345678</PMID>
                <Article>
                    <ArticleTitle>Test Medical Research Paper</ArticleTitle>
                    <Abstract>
                        <AbstractText>This is a test medical abstract.</AbstractText>
                    </Abstract>
                    <AuthorList>
                        <Author>
                            <LastName>Smith</LastName>
                            <ForeName>John</ForeName>
                        </Author>
                    </AuthorList>
                    <Journal>
                        <Title>Test Medical Journal</Title>
                        <JournalIssue>
                            <Volume>10</Volume>
                            <Issue>2</Issue>
                            <PubDate>
                                <Year>2023</Year>
                            </PubDate>
                        </JournalIssue>
                    </Journal>
                </Article>
            </MedlineCitation>
        </PubmedArticle>
    </PubmedArticleSet>
    """


def get_mock_semantic_scholar_response() -> Dict[str, Any]:
    """Mock response from Semantic Scholar API."""
    return {
        "data": [
            {
                "paperId": "abc123",
                "title": "Test Research Paper",
                "abstract": "This is a test abstract from Semantic Scholar.",
                "year": 2023,
                "authors": [{"name": "John Doe"}, {"name": "Jane Smith"}],
                "url": "https://www.semanticscholar.org/paper/abc123",
            }
        ]
    }


def get_mock_google_pse_response() -> Dict[str, Any]:
    """Mock response from Google Programmable Search Engine."""
    return {
        "items": [
            {
                "title": "Google Search Result 1",
                "link": "https://example.com/google1",
                "snippet": "This is a Google search result snippet.",
                "pagemap": {
                    "metatags": [{"og:description": "Extended description"}]
                },
            }
        ]
    }


# ============== Research System Mock Data ==============


def get_mock_findings() -> Dict[str, Any]:
    """Sample research findings for testing."""
    return {
        "findings": [
            {
                "content": "Finding 1 about AI research",
                "source": "https://example.com/1",
            },
            {
                "content": "Finding 2 about machine learning applications",
                "source": "https://example.com/2",
            },
        ],
        "current_knowledge": "AI research has made significant progress in recent years with applications in various fields.",
        "iterations": 2,
        "questions_by_iteration": {
            1: [
                "What are the latest advances in AI?",
                "How is AI applied in healthcare?",
            ],
            2: [
                "What ethical concerns exist in AI development?",
                "What is the future of AI research?",
            ],
        },
    }


def get_mock_ollama_response() -> Dict[str, Any]:
    """Mock response from Ollama API."""
    return {
        "model": "gemma3:12b",
        "created_at": "2023-06-01T12:00:00Z",
        "response": "This is a test response from the mocked LLM API.",
        "done": True,
    }


# ============== Error Response Mocks ==============


def get_mock_error_responses() -> Dict[str, Any]:
    """Collection of error responses for testing error handling."""
    return {
        "http_500": {"status_code": 500, "error": "Internal Server Error"},
        "http_404": {"status_code": 404, "error": "Not Found"},
        "http_429": {"status_code": 429, "error": "Too Many Requests"},
        "network_timeout": {"error": "Connection timeout"},
        "invalid_json": '{"invalid": json"',
        "empty_response": "",
        "null_response": None,
    }


# ============== Database Mock Data ==============


def get_mock_research_history() -> List[Dict[str, Any]]:
    """Mock research history entries."""
    return [
        {
            "id": 1,
            "query": "artificial intelligence applications",
            "timestamp": "2023-06-01 10:00:00",
            "status": "completed",
            "results": '{"findings": ["AI is used in healthcare", "AI powers recommendation systems"]}',
        },
        {
            "id": 2,
            "query": "climate change solutions",
            "timestamp": "2023-06-01 11:00:00",
            "status": "in_progress",
            "results": None,
        },
    ]


# ============== Settings Mock Data ==============


def get_mock_settings() -> Dict[str, Any]:
    """Mock settings configuration."""
    return {
        "llm": {
            "provider": "ollama",
            "model": "gemma3:12b",
            "temperature": 0.7,
            "max_tokens": 4096,
        },
        "search": {
            "tool": "searxng",
            "iterations": 3,
            "questions_per_iteration": 2,
            "max_results": 50,
        },
        "general": {
            "enable_fact_checking": True,
            "cache_results": True,
            "debug_mode": False,
        },
    }
