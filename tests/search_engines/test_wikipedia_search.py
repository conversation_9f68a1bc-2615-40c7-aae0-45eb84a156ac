import sys
from pathlib import Path

import wikipedia

# Handle import paths for testing
sys.path.append(str(Path(__file__).parent.parent))


def test_wikipedia_search_init(monkeypatch):
    """Test initialization of Wikipedia search."""
    # Mock wikipedia functions to avoid actual API calls
    monkeypatch.setattr("wikipedia.search", lambda *args, **kwargs: [])
    monkeypatch.setattr("wikipedia.set_lang", lambda *args, **kwargs: None)

    from local_deep_research.web_search_engines.engines.search_engine_wikipedia import (
        WikipediaSearchEngine as WikipediaSearch,
    )

    # Create search engine with default parameters
    search = WikipediaSearch()

    # Check default parameters (default is 10, not 5)
    assert search.max_results == 10

    # Create with custom parameters
    search = WikipediaSearch(max_results=5)
    assert search.max_results == 5


def test_wikipedia_search_run(monkeypatch, mock_wikipedia_response):
    """Test Wikipedia search run method."""
    from local_deep_research.web_search_engines.engines.search_engine_wikipedia import (
        WikipediaSearchEngine as WikipediaSearch,
    )

    # Mock wikipedia functions
    monkeypatch.setattr("wikipedia.set_lang", lambda *args, **kwargs: None)
    monkeypatch.setattr(
        "wikipedia.search",
        lambda query, results: ["Artificial intelligence", "Machine learning"],
    )

    # Mock wikipedia.summary to return appropriate summaries
    def mock_summary(title, sentences=5, auto_suggest=False):
        if title == "Artificial intelligence":
            return "Artificial intelligence (AI) is intelligence demonstrated by machines, in contrast to the natural intelligence displayed by humans."
        elif title == "Machine learning":
            return "Machine learning is a field of artificial intelligence that uses statistical techniques."
        return "Default summary"

    monkeypatch.setattr("wikipedia.summary", mock_summary)

    # Create the search engine and run a query
    wiki_search = WikipediaSearch(max_results=5)
    results = wiki_search.run("artificial intelligence")

    # Verify results structure
    assert len(results) == 2  # Two results from our mock

    # Check first result
    assert results[0]["title"] == "Artificial intelligence"
    assert "intelligence demonstrated by machines" in results[0]["snippet"]
    # Use secure URL validation instead of simple string check
    link = results[0]["link"]
    assert link.startswith("https://en.wikipedia.org/wiki/") or link.startswith(
        "https://wikipedia.org/wiki/"
    )
    assert results[0]["source"] == "Wikipedia"


def test_wikipedia_search_error_handling(monkeypatch):
    """Test Wikipedia search error handling."""
    from local_deep_research.web_search_engines.engines.search_engine_wikipedia import (
        WikipediaSearchEngine as WikipediaSearch,
    )

    # Mock wikipedia functions to raise an exception
    monkeypatch.setattr("wikipedia.set_lang", lambda *args, **kwargs: None)

    def mock_search_error(query, results):
        raise wikipedia.exceptions.WikipediaException("Search error")

    monkeypatch.setattr("wikipedia.search", mock_search_error)

    # Create the search engine and run a query
    wiki_search = WikipediaSearch()
    results = wiki_search.run("artificial intelligence")

    # Should return empty list on error
    assert isinstance(results, list)
    assert len(results) == 0


def test_wikipedia_search_request_exception(monkeypatch):
    """Test Wikipedia search handling of request exceptions."""
    from local_deep_research.web_search_engines.engines.search_engine_wikipedia import (
        WikipediaSearchEngine as WikipediaSearch,
    )

    # Mock wikipedia functions
    monkeypatch.setattr("wikipedia.set_lang", lambda *args, **kwargs: None)
    monkeypatch.setattr(
        "wikipedia.search", lambda query, results: ["Test Page"]
    )

    # Mock summary to raise a PageError
    def mock_summary_error(title, sentences=5, auto_suggest=False):
        raise wikipedia.exceptions.PageError(title)

    monkeypatch.setattr("wikipedia.summary", mock_summary_error)

    # Create the search engine and run a query
    wiki_search = WikipediaSearch()
    results = wiki_search.run("artificial intelligence")

    # Should return empty list on exception
    assert isinstance(results, list)
    assert len(results) == 0


def test_wikipedia_search_empty_results(monkeypatch):
    """Test Wikipedia search with empty results."""
    from local_deep_research.web_search_engines.engines.search_engine_wikipedia import (
        WikipediaSearchEngine as WikipediaSearch,
    )

    # Mock wikipedia to return empty results
    monkeypatch.setattr("wikipedia.set_lang", lambda *args, **kwargs: None)
    monkeypatch.setattr("wikipedia.search", lambda query, results: [])

    # Create the search engine and run a query
    wiki_search = WikipediaSearch()
    results = wiki_search.run("nonexistent topic xyzabc123")

    # Should return empty list for no results
    assert isinstance(results, list)
    assert len(results) == 0


def test_wikipedia_search_rate_limiting(monkeypatch):
    """Test Wikipedia search rate limiting handling."""
    from local_deep_research.web_search_engines.engines.search_engine_wikipedia import (
        WikipediaSearchEngine as WikipediaSearch,
    )

    # Mock wikipedia functions
    monkeypatch.setattr("wikipedia.set_lang", lambda *args, **kwargs: None)
    monkeypatch.setattr(
        "wikipedia.search", lambda query, results: ["Test Page"]
    )

    # Mock summary to raise a generic exception (simulating rate limit)
    def mock_summary_rate_limit(title, sentences=5, auto_suggest=False):
        raise Exception("HTTPError: 429 Too Many Requests")

    monkeypatch.setattr("wikipedia.summary", mock_summary_rate_limit)

    # Create the search engine and run a query
    wiki_search = WikipediaSearch()
    results = wiki_search.run("artificial intelligence")

    # Should return empty list on rate limiting
    assert isinstance(results, list)
    assert len(results) == 0


def test_wikipedia_search_url_formation(monkeypatch):
    """Test that Wikipedia search forms URLs correctly."""
    from local_deep_research.web_search_engines.engines.search_engine_wikipedia import (
        WikipediaSearchEngine as WikipediaSearch,
    )

    # Mock wikipedia functions
    monkeypatch.setattr("wikipedia.set_lang", lambda *args, **kwargs: None)

    # First test - simple title
    monkeypatch.setattr(
        "wikipedia.search", lambda query, results: ["Test Page"]
    )
    monkeypatch.setattr(
        "wikipedia.summary",
        lambda title, sentences=5, auto_suggest=False: "Test snippet content",
    )

    # Create the search engine and run a query
    wiki_search = WikipediaSearch()
    results = wiki_search.run("test page")

    # Check that the URL was formed correctly
    assert len(results) > 0
    assert results[0]["link"] == "https://en.wikipedia.org/wiki/Test_Page"

    # Try with a title that has spaces and special characters
    monkeypatch.setattr(
        "wikipedia.search",
        lambda query, results: ["Artificial intelligence & ethics"],
    )

    results = wiki_search.run("AI ethics")
    assert len(results) > 0
    assert (
        results[0]["link"]
        == "https://en.wikipedia.org/wiki/Artificial_intelligence_&_ethics"
    )
