#!/usr/bin/env python3
"""
Quick health check runner - automatically detects if server is running
and provides helpful feedback
"""

import subprocess
import sys
from pathlib import Path


def check_server_running():
    """Check if the server is running on localhost:5000"""
    try:
        import requests

        requests.get("http://localhost:5000", timeout=2)
        return True
    except Exception:
        return False


def run_health_check():
    """Run the appropriate health check script"""
    script_dir = Path(__file__).parent.resolve()

    # Try Python version first (more detailed output)
    python_script = str(script_dir / "test_endpoints_health.py")
    shell_script = str(script_dir / "test_endpoints_health.sh")

    print("🚀 Quick Health Check for Local Deep Research")
    print("=" * 50)

    # Check if server is running
    print("🔍 Checking if server is running...")
    if not check_server_running():
        print("❌ Server is not running on localhost:5000")
        print("\n💡 To start the server, run:")
        print("   python app.py")
        print("   # or")
        print("   python -m src.local_deep_research.web.app")
        return False

    print("✅ Server is running!")
    print()

    # Try to run Python script first
    try:
        import requests  # noqa: F401

        print("🐍 Running Python health check...")
        result = subprocess.run(
            [sys.executable, python_script], capture_output=False, text=True
        )
        return result.returncode == 0
    except ImportError:
        print("📦 requests module not available, falling back to curl...")

    # Fall back to shell script
    if Path(shell_script).exists():
        print("🐚 Running shell health check...")
        result = subprocess.run([shell_script], capture_output=False, text=True)
        return result.returncode == 0
    else:
        print("❌ No health check scripts found")
        return False


def main():
    """Main function"""
    try:
        success = run_health_check()
        if success:
            print("\n🎉 Health check completed successfully!")
        else:
            print("\n⚠️  Health check found issues")
        return success
    except KeyboardInterrupt:
        print("\n🛑 Health check interrupted")
        return False
    except Exception as e:
        print(f"\n❌ Health check failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
