#!/usr/bin/env python3
"""
Fast health check test for all web endpoints
Tests that pages return 200 status using Flask test client
"""

import time
import pytest

# All endpoints to test
ENDPOINTS = [
    # Main pages
    "/",  # Home/Research page (may redirect)
    "/history/",  # History page (HTML)
    "/history/api",  # History data (JSON)
    "/settings/",  # Settings page (HTML)
    "/metrics/",  # Metrics dashboard
    # Metrics subpages
    "/metrics/costs",
    "/metrics/star-reviews",
    # API endpoints (should return JSON)
    "/metrics/api/cost-analytics",
    "/metrics/api/pricing",
    "/settings/api/available-models",  # Settings API endpoints
    "/settings/api/available-search-engines",  # Settings API endpoints
]


def check_single_endpoint(client, endpoint):
    """Check a single endpoint and return results"""
    start_time = time.time()

    try:
        response = client.get(endpoint)
        duration = time.time() - start_time

        return {
            "endpoint": endpoint,
            "status": response.status_code,
            "success": response.status_code == 200,
            "duration": round(duration * 1000, 2),  # milliseconds
            "content_type": response.headers.get("content-type", ""),
            "error": None,
        }
    except Exception as e:
        duration = time.time() - start_time
        return {
            "endpoint": endpoint,
            "status": None,
            "success": False,
            "duration": round(duration * 1000, 2),
            "content_type": "",
            "error": str(e),
        }


@pytest.mark.parametrize("endpoint", ENDPOINTS)
def test_endpoint_health(endpoint, authenticated_client):
    """Test that each endpoint returns 200 OK"""
    response = authenticated_client.get(endpoint)
    assert response.status_code == 200, (
        f"Expected 200, got {response.status_code} for {endpoint}"
    )


def test_all_endpoints_summary(authenticated_client, capsys):
    """Run comprehensive health check and print summary"""
    print(f"\n🏥 Starting health check for {len(ENDPOINTS)} endpoints...")
    print("-" * 60)

    results = []

    # Test endpoints (note: can't use ThreadPoolExecutor with Flask test client)
    for endpoint in ENDPOINTS:
        result = check_single_endpoint(authenticated_client, endpoint)
        results.append(result)

        # Print result immediately
        status_icon = "✅" if result["success"] else "❌"
        status_text = f"{result['status']}" if result["status"] else "FAIL"
        duration_text = f"{result['duration']:>6.0f}ms"

        print(
            f"{status_icon} {status_text:>3} {duration_text} {result['endpoint']}"
        )

        if result["error"]:
            print(f"    ⚠️  Error: {result['error']}")

    # Summary
    print("-" * 60)
    successful = sum(1 for r in results if r["success"])
    total = len(results)
    success_rate = (successful / total) * 100
    avg_duration = sum(r["duration"] for r in results) / total

    print(
        f"📊 Results: {successful}/{total} endpoints successful ({success_rate:.1f}%)"
    )
    print(f"⏱️  Average response time: {avg_duration:.0f}ms")

    # Failed endpoints details
    failed = [r for r in results if not r["success"]]
    if failed:
        print(f"\n❌ Failed endpoints ({len(failed)}):")
        for result in failed:
            error_msg = result["error"] or f"Status {result['status']}"
            print(f"   • {result['endpoint']} - {error_msg}")

    # API endpoints check
    api_results = [
        r
        for r in results
        if r["endpoint"].startswith("/api") or "/api/" in r["endpoint"]
    ]
    api_successful = sum(1 for r in api_results if r["success"])
    if api_results:
        print(
            f"\n🔌 API endpoints: {api_successful}/{len(api_results)} working"
        )

    # Assert all endpoints are healthy
    assert success_rate == 100.0, (
        f"Only {successful}/{total} endpoints are healthy"
    )
