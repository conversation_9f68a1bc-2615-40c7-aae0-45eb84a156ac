#!/bin/bash
# Fast endpoint health check using curl
# Tests that all pages return 200 status codes

BASE_URL="http://localhost:5000"

# ANSI color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Endpoints to test
ENDPOINTS=(
    "/"
    "/research"
    "/research/results/1"
    "/research/api/history"
    "/research/settings"
    "/metrics"
    "/metrics/costs"
    "/metrics/star-reviews"
    "/metrics/api/cost-analytics"
    "/metrics/api/pricing"
    "/research/settings/api/available-models"
    "/research/settings/api/available-search-engines"
)

echo -e "${BLUE}🏥 Starting health check for ${#ENDPOINTS[@]} endpoints...${NC}"
echo -e "${BLUE}🌐 Base URL: $BASE_URL${NC}"
echo "$(printf '=%.0s' {1..60})"

# Test connectivity first
echo -e "${YELLOW}🔍 Testing connectivity...${NC}"
if curl -s -f --max-time 5 "$BASE_URL" > /dev/null; then
    echo -e "${GREEN}✅ Server is reachable${NC}"
else
    echo -e "${RED}❌ Cannot reach server at $BASE_URL${NC}"
    echo -e "${RED}   Make sure the application is running on localhost:5000${NC}"
    exit 1
fi

echo ""
echo "$(printf '=%.0s' {1..60})"

# Test each endpoint
success_count=0
total_count=${#ENDPOINTS[@]}
failed_endpoints=()

for endpoint in "${ENDPOINTS[@]}"; do
    url="$BASE_URL$endpoint"

    # Use curl to test the endpoint with timing
    start_time=$(date +%s%3N)

    # Capture both status code and response time
    http_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$url" 2>/dev/null)
    curl_exit_code=$?

    end_time=$(date +%s%3N)
    duration=$((end_time - start_time))

    if [ $curl_exit_code -eq 0 ] && [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 200${NC} $(printf '%6d' $duration)ms $endpoint"
        ((success_count++))
    else
        if [ $curl_exit_code -ne 0 ]; then
            echo -e "${RED}❌ FAIL${NC} $(printf '%6d' $duration)ms $endpoint (curl error: $curl_exit_code)"
        else
            echo -e "${RED}❌ $http_code${NC} $(printf '%6d' $duration)ms $endpoint"
        fi
        failed_endpoints+=("$endpoint")
    fi
done

echo "$(printf '=%.0s' {1..60})"

# Calculate success rate
success_rate=$((success_count * 100 / total_count))

echo -e "${BLUE}📊 Results: $success_count/$total_count endpoints successful (${success_rate}%)${NC}"

# Show failed endpoints if any
if [ ${#failed_endpoints[@]} -gt 0 ]; then
    echo ""
    echo -e "${RED}❌ Failed endpoints (${#failed_endpoints[@]}):${NC}"
    for failed_endpoint in "${failed_endpoints[@]}"; do
        echo -e "${RED}   • $failed_endpoint${NC}"
    done
fi

# API endpoints summary
api_count=0
api_success=0
for endpoint in "${ENDPOINTS[@]}"; do
    if [[ "$endpoint" == *"/api/"* || "$endpoint" == "/api/"* ]]; then
        ((api_count++))
        # Check if this endpoint was successful
        if [[ ! " ${failed_endpoints[@]} " =~ " ${endpoint} " ]]; then
            ((api_success++))
        fi
    fi
done

if [ $api_count -gt 0 ]; then
    echo ""
    echo -e "${BLUE}🔌 API endpoints: $api_success/$api_count working${NC}"
fi

echo ""
if [ $success_count -eq $total_count ]; then
    echo -e "${GREEN}🎉 All endpoints are healthy!${NC}"
    exit 0
else
    echo -e "${YELLOW}⚠️  Some endpoints have issues${NC}"
    exit 1
fi
