# Health Check Tests

Fast, lightweight tests to verify all web endpoints are working without requiring heavy browser automation.

## Quick Start

```bash
# Auto-detecting runner (recommended)
python run_quick_health_check.py

# Python implementation (detailed output)
python test_endpoints_health.py

# Shell implementation (minimal dependencies)
./test_endpoints_health.sh
```

**Performance:** 1-5 seconds for 14+ endpoints • Concurrent execution • Works on any hardware

---

## Available Scripts

### 1. `run_quick_health_check.py` (Recommended)
**Auto-detecting runner that chooses the best available method**

```bash
# Run from health_check directory
python run_quick_health_check.py

# Or from project root
python tests/health_check/run_quick_health_check.py
```

**Features:**
- Automatically detects if server is running
- Provides helpful startup instructions if server is down
- Falls back between Python/curl implementations
- Most user-friendly option

### 2. `test_endpoints_health.py` (Python)
**Detailed health check using Python requests**

```bash
# Requires: pip install requests
python test_endpoints_health.py
```

**Features:**
- Concurrent testing for speed
- Detailed timing information
- JSON response validation for API endpoints
- Colored output with detailed error messages
- Returns proper exit codes for CI/CD

### 3. `test_endpoints_health.sh` (Shell/curl)
**Lightweight health check using only curl**

```bash
# No dependencies, works everywhere curl is available
./test_endpoints_health.sh
```

**Features:**
- Zero Python dependencies
- Very fast execution
- Works on any system with curl
- Colored terminal output
- Perfect for minimal environments

---

## What Gets Tested

All scripts test these endpoints:

**Main Pages:**
- `/` - Home page
- `/research` - Research interface
- `/research/results` - Results page
- `/history` - Research history
- `/settings` - Settings page

**Metrics Pages:**
- `/metrics` - Metrics dashboard
- `/metrics/costs` - Cost analytics
- `/metrics/star-reviews` - Star reviews

**API Endpoints:**
- `/api/health` - Health check API
- `/metrics/api/search-activity` - Search activity data
- `/metrics/api/cost-analytics` - Cost analytics data
- `/metrics/api/pricing` - Pricing information
- `/settings/api/llm-models` - LLM models list
- `/settings/api/search-engines` - Search engines list

---

## Usage Examples

### Quick Check Before Deployment
```bash
# Start server
python app.py &

# Wait for startup
sleep 5

# Run health check
python tests/health_check/run_quick_health_check.py

# Results
echo "Health check exit code: $?"
```

### CI/CD Integration
```bash
# In your CI script
if python tests/health_check/test_endpoints_health.py; then
    echo "✅ All endpoints healthy - deployment can proceed"
else
    echo "❌ Health check failed - stopping deployment"
    exit 1
fi
```

### Development Workflow
```bash
# Quick check during development
./tests/health_check/test_endpoints_health.sh

# Check specific issues
python tests/health_check/test_endpoints_health.py | grep "❌"
```

---

## Performance

- **Shell version**: ~1-3 seconds for all endpoints
- **Python version**: ~2-5 seconds for all endpoints
- **Concurrent execution**: Tests run in parallel for speed
- **No browser overhead**: Pure HTTP requests only

---

## Requirements

- **Shell version**: Only requires `curl` (available everywhere)
- **Python version**: Requires `requests` library (`pip install requests`)
- **Server**: Application must be running on `localhost:5000`

---

## Setup Instructions

### Minimal Setup (Health Check Only)
```bash
# Option 1: Use curl (no dependencies)
./test_endpoints_health.sh

# Option 2: Use Python
pip install requests
python test_endpoints_health.py
```

### Make Scripts Executable
```bash
chmod +x test_endpoints_health.sh
chmod +x run_quick_health_check.py
```

---

## Exit Codes

- `0`: All endpoints healthy
- `1`: Some endpoints failed or server unreachable

---

## Example Output

```
🏥 Starting health check for 14 endpoints...
🌐 Base URL: http://localhost:5000
============================================================
✅ 200    45ms /
✅ 200    12ms /research
✅ 200    23ms /research/results
✅ 200    18ms /history
✅ 200    67ms /settings
✅ 200    34ms /metrics
✅ 200    89ms /metrics/costs
✅ 200    45ms /metrics/star-reviews
✅ 200     8ms /api/health
✅ 200    56ms /metrics/api/search-activity
✅ 200    78ms /metrics/api/cost-analytics
✅ 200    23ms /metrics/api/pricing
✅ 200    34ms /settings/api/llm-models
✅ 200    45ms /settings/api/search-engines
============================================================
📊 Results: 14/14 endpoints successful (100%)
⏱️  Average response time: 41ms
🔌 API endpoints: 6/6 working

🎉 All endpoints are healthy!
```

---

## Troubleshooting

### Server Not Running
```
❌ Cannot reach server at http://localhost:5000
💡 To start the server, run:
   python app.py
   # or
   python -m src.local_deep_research.web.app
```

### Dependencies Missing
```bash
# For Python tests
pip install requests

# For shell tests
# curl is usually pre-installed on most systems
```

### Permission Errors
```bash
chmod +x test_endpoints_health.sh
chmod +x run_quick_health_check.py
```

---

## Adding New Endpoints

Edit `test_endpoints_health.py` and add to the `ENDPOINTS` list:

```python
ENDPOINTS = [
    # ... existing endpoints
    "/your/new/endpoint",
]
```

Also update the shell script `test_endpoints_health.sh`:

```bash
ENDPOINTS=(
    # ... existing endpoints
    "/your/new/endpoint"
)
```

---

## Integration with Testing Suite

These health checks are part of the larger testing ecosystem:

- **Health Checks** (this directory) - Fast endpoint verification
- **Feature Tests** (`../feature_tests/`) - Specific functionality testing
- **UI Tests** (`../ui_tests/`) - Browser automation for complex interactions
- **Integration Tests** (`../searxng/`, etc.) - External service testing

**Back to:** [Main Testing Guide](../README.md)
