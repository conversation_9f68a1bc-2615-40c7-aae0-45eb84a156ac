"""
This is a mock llm_config.py module for testing.
Place this in your tests directory as mock_llm_config.py
"""

import sys
import types
from unittest.mock import Mock


def create_mock_llm_config():
    """Create a mock llm_config module for testing."""
    # Create a mock module
    mock_module = types.ModuleType("mock_llm_config")

    # Add necessary functions and variables
    def get_llm(*args, **kwargs):
        return Mock()

    mock_module.get_llm = get_llm
    mock_module.VALID_PROVIDERS = [
        "ollama",
        "openai",
        "anthropic",
        "vllm",
        "openai_endpoint",
        "lmstudio",
        "llamacpp",
        "none",
    ]

    # Add other necessary attributes
    mock_module.AVAILABLE_PROVIDERS = {"ollama": "Ollama (local models)"}
    mock_module.get_available_providers = (
        lambda: mock_module.AVAILABLE_PROVIDERS
    )

    return mock_module


# For use in test setup
def patch_llm_config(monkeypatch):
    """
    Patch the llm_config module in the pytest setup.

    Usage in a test:

    def test_something(monkeypatch):
        patch_llm_config(monkeypatch)
        # Now imports will use the mock module
    """
    mock_module = create_mock_llm_config()
    monkeypatch.setitem(
        sys.modules, "src.local_deep_research.config.llm_config", mock_module
    )
    monkeypatch.setattr(
        "src.local_deep_research.config.llm_config", mock_module
    )
