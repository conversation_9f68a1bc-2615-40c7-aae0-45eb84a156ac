#!/bin/bash
# Run all LLM integration tests

echo "Running LLM Integration Tests"
echo "============================="

# Set up environment
export PYTHONPATH=$PYTHONPATH:$(pwd)
export LDR_USE_FALLBACK_LLM=true

# Run registry tests
echo -e "\n1. Running LLM Registry Tests..."
python -m pytest tests/test_llm/test_llm_registry.py -v

# Run integration tests
echo -e "\n2. Running LLM Integration Tests..."
python -m pytest tests/test_llm/test_llm_integration.py -v

# Run API integration tests
echo -e "\n3. Running API LLM Integration Tests..."
python -m pytest tests/test_llm/test_api_llm_integration.py -v

echo -e "\nAll LLM tests completed!"
