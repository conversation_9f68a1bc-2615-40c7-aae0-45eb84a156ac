# Accessibility Tests

This directory contains accessibility tests for the Local Deep Research web interface, focusing on ensuring compliance with WCAG 2.1 AA standards and screen reader compatibility.

## Test Coverage

### JavaScript Tests (`test_accessibility.js`)
These tests use Playwright to verify:

- **Screen Reader Compatibility**
  - Proper radio button structure for research mode selection
  - ARIA attributes and roles
  - Screen reader only elements (`.sr-only` class)
  - Fieldset and legend usage

- **Keyboard Navigation**
  - Tab order and focus management
  - Arrow key navigation for radio button groups
  - Enter and Space key activation
  - Keyboard shortcuts (Enter to submit, Shift+Enter for new lines)

- **Form Accessibility**
  - Proper labeling of form controls
  - Keyboard hint visibility
  - Form submission via keyboard
  - Focus indicators

- **WCAG Compliance**
  - Automated accessibility testing with axe-core
  - Color contrast and focus visibility
  - Semantic markup validation

### Python Tests (`test_accessibility_backend.py`)
These tests verify server-side HTML structure:

- **HTML Structure**
  - Proper form labeling
  - Semantic markup usage
  - Heading hierarchy
  - Required field marking

- **ARIA Implementation**
  - Radio button group structure
  - ARIA roles and attributes
  - Screen reader support elements

- **Configuration**
  - HTML lang attribute
  - Viewport meta tag
  - CSS focus styles

## Running the Tests

### Prerequisites
```bash
# Install Python dependencies
pip install pytest beautifulsoup4 requests

# Install Node.js dependencies for Playwright tests
npm install playwright @playwright/test
npx playwright install
```

### Running JavaScript Tests
```bash
# Run all accessibility tests
npx playwright test test_accessibility.js

# Run with UI mode for debugging
npx playwright test test_accessibility.js --ui

# Run specific test
npx playwright test test_accessibility.js -g "mode selection should have proper radio button structure"
```

### Running Python Tests
```bash
# Run from the tests directory
cd tests
python -m pytest ui_tests/test_accessibility_backend.py -v

# Run with coverage
python -m pytest ui_tests/test_accessibility_backend.py --cov=src

# Run specific test
python -m pytest ui_tests/test_accessibility_backend.py::TestHTMLAccessibility::test_radio_button_structure -v
```

## Test Environment Setup

### Environment Variables
```bash
# Set base URL for testing (default: http://localhost:5000)
export TEST_BASE_URL=http://localhost:8080
```

### Running the Application
Make sure the application is running before executing tests:
```bash
# Start the web server
python app.py
# or
python -m src.local_deep_research.web.app
```

## Accessibility Features Tested

### Issue #75 - Screen Reader Compatibility
- ✅ Radio buttons replace div-based mode selection
- ✅ Proper ARIA roles and attributes
- ✅ Keyboard navigation with arrow keys
- ✅ Screen reader announcements
- ✅ Focus management

### Keyboard Shortcuts Enhancement
- ✅ Enter key submits form from textarea
- ✅ Shift+Enter creates new lines in textarea
- ✅ Ctrl+Enter alternative submission method
- ✅ Visible keyboard hints for users
- ✅ Tab navigation through form elements

### WCAG 2.1 AA Compliance
- ✅ Proper heading hierarchy
- ✅ Semantic HTML elements
- ✅ Focus indicators
- ✅ Color contrast (automated testing)
- ✅ Keyboard accessibility
- ✅ Screen reader support

## Common Test Failures and Solutions

### Radio Button Structure Issues
**Error**: Missing radio button or improper labeling
**Solution**: Ensure HTML template includes proper `<input type="radio">` elements with corresponding `<label>` elements

### ARIA Attribute Mismatches
**Error**: `aria-checked` not matching actual radio state
**Solution**: Verify JavaScript `selectMode()` function updates both visual state and ARIA attributes

### Keyboard Navigation Failures
**Error**: Arrow keys not working for mode selection
**Solution**: Check that event listeners are properly attached in `research.js`

### Focus Visibility Issues
**Error**: Focus indicators not visible
**Solution**: Verify CSS focus styles are properly defined in `custom_dropdown.css`

## Integration with CI/CD

These tests are designed to be integrated into the CI pipeline. See the main CI configuration for setup details.

### Test Commands for CI
```bash
# Quick accessibility check
npm run test:accessibility:quick

# Full accessibility suite
npm run test:accessibility:full

# Python accessibility tests
python -m pytest tests/ui_tests/test_accessibility_backend.py
```

## Browser Compatibility

The JavaScript tests are run against multiple browsers:
- Chromium (Chrome/Edge)
- Firefox
- WebKit (Safari)

Each browser may have different accessibility behaviors, particularly around screen reader APIs.

## Manual Testing Recommendations

While automated tests catch many accessibility issues, manual testing is still recommended:

1. **Screen Reader Testing**
   - Test with NVDA (Windows)
   - Test with JAWS (Windows)
   - Test with VoiceOver (macOS)

2. **Keyboard-Only Navigation**
   - Disconnect mouse and navigate using only keyboard
   - Verify all functionality is accessible

3. **High Contrast Mode**
   - Test in Windows High Contrast mode
   - Verify focus indicators remain visible

4. **Zoom Testing**
   - Test at 200% zoom level
   - Ensure all content remains accessible

## Contributing

When adding new UI features:

1. Add corresponding accessibility tests
2. Ensure ARIA attributes are properly implemented
3. Test keyboard navigation
4. Verify screen reader compatibility
5. Run full accessibility test suite before submitting PR

## Resources

- [WCAG 2.1 Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [ARIA Authoring Practices Guide](https://www.w3.org/WAI/ARIA/apg/)
- [Playwright Accessibility Testing](https://playwright.dev/docs/accessibility-testing)
- [axe-core Documentation](https://github.com/dequelabs/axe-core)
