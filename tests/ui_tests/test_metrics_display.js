const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const fs = require('fs').promises;
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

const BASE_URL = 'http://127.0.0.1:5000';

async function testMetricsDisplay() {
    console.log('🧪 Starting Metrics Display Test');
    console.log('================================\n');

    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();

    try {
        // Step 1: Create and authenticate user
        const username = `metrics_test_${Date.now()}`;
        const password = `test'pass"with$pecial`;

        console.log(`📝 Creating user: ${username}`);
        console.log(`   Password has special characters: ${password}\n`);

        const auth = new AuthHelper(page, BASE_URL);
        await auth.register(username, password);

        // Step 2: Start a simple research
        console.log('🔬 Starting research...');
        await page.goto(`${BASE_URL}/`, { waitUntil: 'domcontentloaded' });

        // Wait for the query field to be visible
        await page.waitForSelector('#query', { visible: true });

        // Type a simple query
        await page.type('#query', 'What is 2+2?');

        // Submit - find submit button
        const submitButton = await page.$('button[type="submit"], button#submit-research');
        if (submitButton) {
            await Promise.all([
                page.waitForNavigation(),
                submitButton.click()
            ]);
        } else {
            // Try pressing Enter
            await Promise.all([
                page.waitForNavigation(),
                page.keyboard.press('Enter')
            ]);
        }

        console.log('✅ Research started\n');

        // Step 3: Wait a bit for metrics to be generated
        console.log('⏳ Waiting 20 seconds for metrics to be generated...');
        await new Promise(resolve => setTimeout(resolve, 20000));

        // Step 4: Go to metrics page
        console.log('\n📊 Navigating to metrics dashboard...');
        await page.goto(`${BASE_URL}/metrics`, { waitUntil: 'domcontentloaded' });
        await new Promise(resolve => setTimeout(resolve, 3000)); // Let page fully load

        // Step 5: Check what's on the page (with error handling)
        let pageContent;
        try {
            pageContent = await page.evaluate(() => {
                return {
                    title: document.title,
                    bodyText: document.body.innerText,
                    // Look for any elements that might contain metrics
                    elements: {
                        tables: document.querySelectorAll('table').length,
                        charts: document.querySelectorAll('canvas').length,
                        cards: document.querySelectorAll('.ldr-card, .metric-card, .stat-box').length,
                        divs: document.querySelectorAll('div').length
                    }
                };
            });
        } catch (err) {
            console.log('⚠️ Error evaluating page, likely frame detached');
            // Provide default values
            pageContent = {
                title: 'Error reading page',
                bodyText: '',
                elements: { tables: 0, charts: 0, cards: 0, divs: 0 }
            };
        }

        console.log('\n=== METRICS PAGE CONTENT ===');
        console.log(`Page Title: ${pageContent.title}`);
        console.log(`Elements found:`);
        console.log(`- Tables: ${pageContent.elements.tables}`);
        console.log(`- Charts (canvas): ${pageContent.elements.charts}`);
        console.log(`- Cards: ${pageContent.elements.cards}`);
        console.log(`- Total divs: ${pageContent.elements.divs}`);

        console.log('\n=== PAGE TEXT (first 1000 chars) ===');
        console.log(pageContent.bodyText.substring(0, 1000));
        console.log('...\n');


        // Check if metrics are visible
        const hasMetrics = pageContent.bodyText.toLowerCase().includes('token') ||
                          pageContent.bodyText.toLowerCase().includes('search') ||
                          pageContent.bodyText.toLowerCase().includes('metric');

        if (hasMetrics) {
            console.log('✅ Metrics page contains metric-related text');
        } else {
            console.log('❌ No metric-related text found on page');
        }

        // Look for specific metric values
        const numbers = pageContent.bodyText.match(/\d+/g);
        if (numbers && numbers.length > 0) {
            console.log(`✅ Found ${numbers.length} numeric values on page`);
            console.log(`   First few numbers: ${numbers.slice(0, 10).join(', ')}`);
        } else {
            console.log('❌ No numeric values found on page');
        }

    } catch (error) {
        console.error('❌ Test error:', error.message);
    }

    console.log('\n✅ Test completed successfully!');

    // Close browser properly
    await browser.close();
}

testMetricsDisplay().catch(console.error);
