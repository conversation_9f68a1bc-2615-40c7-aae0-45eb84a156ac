const puppeteer = require('puppeteer');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testMetricsThreadFix() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();

    // Enable console logging
    page.on('console', msg => {
        if (msg.type() === 'error' || msg.text().includes('ERROR')) {
            console.log('PAGE ERROR:', msg.text());
        }
    });

    console.log('🚀 Testing metrics thread fix...');

    try {
        // Register a new user with simple password
        const username = 'testmetrics' + Date.now();
        const password = 'simplepass123'; // No special characters

        console.log(`📝 Registering user: ${username} with password: ${password}`);

        await page.goto('http://127.0.0.1:5000/auth/register', { waitUntil: 'networkidle0' });

        await page.type('#username', username);
        await page.type('#password', password);
        await page.type('#confirm_password', password);

        // Check acknowledge if present
        const acknowledgeCheckbox = await page.$('#acknowledge');
        if (acknowledgeCheckbox) {
            await page.click('#acknowledge');
        }

        await page.click('button[type="submit"]');
        await page.waitForNavigation({ waitUntil: 'networkidle0' });

        console.log('✅ Registration successful');

        // Run a quick research to generate metrics
        console.log('🔬 Starting a research to generate metrics...');

        // Fill in research form
        await page.waitForSelector('#query');
        await page.type('#query', 'test metrics collection');

        // Select quick mode
        const quickModeButton = await page.$('input[value="quick"]');
        if (quickModeButton) {
            await page.click('input[value="quick"]');
        }

        // Submit research
        await page.click('button[type="submit"]');
        console.log('✅ Research submitted');

        // Wait a bit for the research to start
        await page.waitForTimeout(5000);

        // Navigate to metrics dashboard
        console.log('📊 Navigating to metrics dashboard...');
        await page.goto('http://127.0.0.1:5000/metrics/', { waitUntil: 'networkidle0' });

        // Check if metrics page loaded
        const metricsContainer = await page.$('.metrics-container');
        if (metricsContainer) {
            console.log('✅ Metrics dashboard loaded');
        } else {
            console.log('❌ Metrics dashboard not found');
        }

        // Wait for metrics data to load
        await page.waitForTimeout(3000);

        // Check for any metrics data
        const hasData = await page.evaluate(() => {
            const tokenElements = document.querySelectorAll('.metric-value');
            for (let elem of tokenElements) {
                if (elem.textContent && elem.textContent.trim() !== '0') {
                    return true;
                }
            }
            return false;
        });

        if (hasData) {
            console.log('✅ Metrics data found!');
        } else {
            console.log('⚠️  No metrics data yet (may need more time)');
        }

        // Take screenshot
        await page.screenshot({ path: 'screenshots/metrics_thread_test.png' });
        console.log('📸 Screenshot saved');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        await page.screenshot({ path: 'screenshots/metrics_thread_error.png' });
    } finally {
        await browser.close();
    }
}

testMetricsThreadFix().catch(console.error);
