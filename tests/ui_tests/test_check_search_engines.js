/**
 * Check Search Engines Test
 * Verifies what search engines are available in the system
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function checkSearchEngines() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    console.log('🔍 Checking Search Engines\n');

    try {
        // Login
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in\n');

        // Check search engines via API
        console.log('📡 Checking search engines API...');
        const searchEngines = await page.evaluate(async () => {
            try {
                const response = await fetch('/settings/api/search-engines');
                if (!response.ok) {
                    return { error: `HTTP ${response.status}` };
                }
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        });

        console.log('Search engines response:', JSON.stringify(searchEngines, null, 2));

        // Also check what's in the form
        await page.goto(baseUrl, { waitUntil: 'networkidle2' });
        await page.waitForSelector('#query', { timeout: 10000 });

        const formSearchEngines = await page.evaluate(() => {
            const select = document.querySelector('#search_engine');
            if (!select) return { error: 'Search engine select not found' };

            const options = [];
            for (let i = 0; i < select.options.length; i++) {
                const opt = select.options[i];
                options.push({
                    value: opt.value,
                    text: opt.text,
                    selected: opt.selected
                });
            }

            return {
                currentValue: select.value,
                options: options
            };
        });

        console.log('\n📝 Form search engines:');
        console.log('Current value:', formSearchEngines.currentValue);
        console.log('Available options:');
        if (formSearchEngines.options) {
            formSearchEngines.options.forEach(opt => {
                console.log(`  ${opt.selected ? '→' : ' '} ${opt.value} (${opt.text})`);
            });
        }

        // Try to use duckduckgo which should work without API key
        console.log('\n🧪 Testing research with DuckDuckGo...');

        const testResult = await page.evaluate(async () => {
            const response = await fetch('/api/start_research', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    query: 'What is Node.js?',
                    mode: 'quick',
                    model_provider: 'OLLAMA',
                    model: 'llama3.1:8b',
                    search_engine: 'duckduckgo',  // Try duckduckgo
                    iterations: 1,
                    questions_per_iteration: 1
                })
            });

            return {
                status: response.status,
                data: await response.json()
            };
        });

        console.log('\nTest result:', testResult);

        if (testResult.status === 200 && testResult.data.research_id) {
            console.log(`✅ Research created with ID: ${testResult.data.research_id}`);

            // Check if it actually runs
            await page.goto(`${baseUrl}/research/${testResult.data.research_id}`);
            await new Promise(resolve => setTimeout(resolve, 5000));

            const progress = await page.evaluate(() => {
                const text = document.body.innerText || '';
                return text.substring(0, 200);
            });

            console.log('\nProgress page shows:');
            console.log(progress);
        }

    } catch (error) {
        console.error('Error:', error.message);
    }

    await browser.close();
    console.log('\n✅ Done');
}

checkSearchEngines().catch(console.error);
