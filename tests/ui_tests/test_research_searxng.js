/**
 * Test research with specific search engine (searxng)
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

const BASE_URL = 'http://127.0.0.1:5000';
const RESEARCH_QUESTION = 'What is the capital of France?';

async function testResearchWithSearxng() {
    console.log('🔬 Testing Research with Searxng\n');

    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const authHelper = new AuthHelper(page, BASE_URL);

    // Log console messages
    page.on('console', msg => {
        if (msg.type() === 'error' || msg.type() === 'warning') {
            console.log(`📝 [${msg.type().toUpperCase()}] ${msg.text()}`);
        }
    });

    // Log requests
    page.on('request', request => {
        if (request.url().includes('/api/start_research')) {
            console.log(`→ REQUEST: ${request.method()} ${request.url()}`);
            console.log(`   Body: ${request.postData()}`);
        }
    });

    page.on('response', response => {
        if (response.url().includes('/api/start_research')) {
            console.log(`← RESPONSE: ${response.status()} ${response.url()}`);
        }
    });

    try {
        // Login
        console.log('🔐 Logging in...');
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in\n');

        // Navigate to home
        await page.goto(BASE_URL, { waitUntil: 'networkidle2' });

        // Fill form
        console.log('📝 Filling research form...');
        await page.type('#query', RESEARCH_QUESTION);

        // Wait for models to load
        await page.waitForTimeout(1000);

        // Select searxng as search engine
        console.log('🔍 Selecting searxng search engine...');

        // Try to find and select searxng
        const searchEngineSet = await page.evaluate(() => {
            const select = document.querySelector('#search_engine');
            if (select) {
                // Try regular select
                for (let option of select.options) {
                    if (option.value === 'searxng' || option.text.toLowerCase().includes('searxng')) {
                        select.value = option.value;
                        select.dispatchEvent(new Event('change'));
                        return true;
                    }
                }
            }

            // Try custom dropdown
            const hiddenInput = document.querySelector('#search_engine_hidden');
            if (hiddenInput) {
                hiddenInput.value = 'searxng';
                hiddenInput.dispatchEvent(new Event('change'));
                return true;
            }

            return false;
        });

        if (!searchEngineSet) {
            console.log('⚠️  Could not set search engine to searxng');
        } else {
            console.log('✅ Search engine set to searxng');
        }

        // Submit research
        console.log('\n📤 Submitting research...');
        await Promise.all([
            page.waitForResponse(response =>
                response.url().includes('/api/start_research'),
                { timeout: 10000 }
            ),
            page.click('#submit-research')
        ]);

        // Wait a bit for research to start
        await page.waitForTimeout(3000);

        // Get research ID from URL or response
        const currentUrl = page.url();
        const researchIdMatch = currentUrl.match(/progress\/([a-f0-9-]+)/);

        if (researchIdMatch) {
            const researchId = researchIdMatch[1];
            console.log(`\n✅ Research started with ID: ${researchId}`);

            // Check server logs will show search engine usage
            console.log('\n📋 Check server logs for search engine usage:');
            console.log('   tail -100 /tmp/ldr_server.log | grep -i searxng');
        } else {
            console.log('\n⚠️  Could not extract research ID from URL');
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await browser.close();
    }
}

testResearchWithSearxng().catch(console.error);
