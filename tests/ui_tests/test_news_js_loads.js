/**
 * Simple test to verify news.js loads without syntax errors
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');

const BASE_URL = process.env.BASE_URL || 'http://127.0.0.1:5000';

async function testNewsJsLoads() {
    const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Track JavaScript errors
    const jsErrors = [];
    page.on('pageerror', error => {
        jsErrors.push({
            message: error.message,
            stack: error.stack
        });
    });

    const authHelper = new AuthHelper(page, BASE_URL);

    try {
        // Login
        console.log('🔐 Logging in...');
        await authHelper.ensureAuthenticated();

        // Load news page
        console.log('📄 Loading news page...');
        await page.goto(`${BASE_URL}/news`, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Wait a bit for scripts to execute
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Check for JavaScript errors
        if (jsErrors.length > 0) {
            console.log('❌ JavaScript errors detected:');
            jsErrors.forEach(err => {
                console.log('  Error:', err.message);
                if (err.stack) {
                    console.log('  Stack:', err.stack.split('\n')[0]);
                }
            });
        } else {
            console.log('✅ No JavaScript errors');
        }

        // Check if useNewsTemplate function is available
        const functionExists = await page.evaluate(() => {
            return typeof window.useNewsTemplate === 'function';
        });

        if (functionExists) {
            console.log('✅ useNewsTemplate function is available');

            // Test clicking a template button
            const clicked = await page.evaluate(() => {
                const buttons = Array.from(document.querySelectorAll('.template-btn'));
                const breakingNewsBtn = buttons.find(b => b.textContent.includes('Breaking News'));
                if (breakingNewsBtn) {
                    console.log('Found Breaking News button');
                    return true;
                }
                return false;
            });

            console.log(clicked ? '✅ Breaking News button found' : '❌ Breaking News button not found');
        } else {
            console.log('❌ useNewsTemplate function NOT available');

            // Check what functions are available
            const availableFunctions = await page.evaluate(() => {
                return Object.keys(window).filter(key =>
                    key.includes('News') && typeof window[key] === 'function'
                );
            });
            console.log('  Available news-related functions:', availableFunctions);
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        await browser.close();
    }
}

// Run the test
testNewsJsLoads().catch(console.error);
