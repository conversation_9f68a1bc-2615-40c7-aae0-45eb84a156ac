/**
 * Settings Page UI Test
 *
 * Comprehensive test of the settings page loading functionality. Monitors all API
 * calls including available models, search engines, and settings. Validates that
 * all setting form elements load correctly.
 *
 * Prerequisites: Web server running on http://127.0.0.1:5000
 *
 * Usage: node tests/ui_tests/test_settings_page.js
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testSettingsPage() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());
    const page = await browser.newPage();
    const authHelper = new AuthHelper(page);

    // Monitor network requests
    await page.setRequestInterception(true);
    page.on('request', request => {
        console.log('REQUEST:', request.method(), request.url());
        request.continue();
    });

    page.on('response', response => {
        console.log('RESPONSE:', response.status(), response.url());
    });

    try {
        // Ensure authentication first
        console.log('🔐 Ensuring authentication...');
        await authHelper.ensureAuthenticated();

        console.log('Navigating to settings page...');
        await page.goto('http://127.0.0.1:5000/settings/', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Wait for settings to load
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Check if settings are loaded
        const settingsElements = await page.$$('.setting-item, .form-group, input[type="text"], select');
        console.log(`Found ${settingsElements.length} setting elements`);

        if (settingsElements.length > 0) {
            console.log('✅ Settings page loaded successfully with setting elements');
        } else {
            console.log('❌ No setting elements found');
            // Take screenshot for debugging
            await page.screenshot({ path: 'settings_page_debug.png' });
        }

    } catch (error) {
        console.error('Error during test:', error);
    }

    await browser.close();
}

testSettingsPage();
