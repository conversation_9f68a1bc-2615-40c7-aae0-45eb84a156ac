const { spawn } = require('child_process');
const fs = require('fs');

// Tests from the CI configuration
const tests = [
    { name: 'Research Submit', file: 'test_research_submit.js' },
    { name: 'Research Cancellation', file: 'test_research_cancellation.js' },
    { name: 'Export Functionality', file: 'test_export_functionality.js' },
    { name: 'Complete Workflow', file: 'test_complete_workflow.js' },
    { name: 'Concurrent Limit', file: 'test_concurrent_limit.js' },
    { name: 'Multi Research', file: 'test_multi_research.js' },
    { name: 'Research Simple', file: 'test_research_simple.js' },
    { name: 'Research Form', file: 'test_research_form.js' },
    { name: 'Research API', file: 'test_research_api.js' },
    { name: 'History Page', file: 'test_history_page.js' },
    { name: 'Full Navigation', file: 'test_full_navigation.js' },
    { name: 'Queue Simple', file: 'test_queue_simple.js' },
    { name: 'Direct Mode', file: 'test_direct_mode.js' }
];

async function checkTest(test) {
    if (!fs.existsSync(test.file)) {
        console.log(`⚠️  ${test.name}: FILE NOT FOUND`);
        return 'missing';
    }

    console.log(`Checking ${test.name}...`);
    return new Promise((resolve) => {
        const testProcess = spawn('node', [test.file], {
            stdio: 'pipe',
            env: { ...process.env, HEADLESS: 'true' }
        });

        let output = '';
        testProcess.stdout.on('data', (data) => {
            output += data.toString();
        });
        testProcess.stderr.on('data', (data) => {
            output += data.toString();
        });

        const timeout = setTimeout(() => {
            testProcess.kill();
            console.log(`⏱️  ${test.name}: TIMEOUT`);
            resolve('timeout');
        }, 60000); // 60 second timeout

        testProcess.on('close', (code) => {
            clearTimeout(timeout);
            if (code === 0) {
                console.log(`✅ ${test.name}: PASSED`);
                resolve('passed');
            } else {
                console.log(`❌ ${test.name}: FAILED (exit code ${code})`);
                // Show last 5 lines of output for failed tests
                const lines = output.split('\n').filter(l => l.trim());
                console.log('   Last output:', lines.slice(-5).join('\n   '));
                resolve('failed');
            }
        });
    });
}

async function checkAll() {
    console.log('Checking Extended UI Tests\n');
    console.log('Note: Each test has a 60-second timeout\n');

    const results = {
        passed: [],
        failed: [],
        missing: [],
        timeout: []
    };

    for (const test of tests) {
        const result = await checkTest(test);
        results[result].push(test.name);
    }

    console.log('\n📊 Summary:');
    console.log(`  Passed: ${results.passed.length}`);
    console.log(`  Failed: ${results.failed.length}`);
    console.log(`  Missing: ${results.missing.length}`);
    console.log(`  Timeout: ${results.timeout.length}`);

    if (results.failed.length > 0) {
        console.log('\n❌ Failed tests:', results.failed.join(', '));
    }
    if (results.missing.length > 0) {
        console.log('\n⚠️  Missing tests:', results.missing.join(', '));
    }

    // Exit with error if any tests failed or timed out
    const exitCode = (results.failed.length > 0 || results.timeout.length > 0) ? 1 : 0;
    process.exit(exitCode);
}

checkAll();
