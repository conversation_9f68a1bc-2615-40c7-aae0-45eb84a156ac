const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

(async () => {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());
  const chartLoaded = await page.evaluate(() => typeof Chart !== 'undefined');
  console.log('Chart.js loaded on main metrics page:', chartLoaded);

  await new Promise(resolve => setTimeout(resolve, 1000));
  await browser.close();
})();
