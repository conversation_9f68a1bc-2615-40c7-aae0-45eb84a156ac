const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

// Simple test to verify settings save functionality
(async () => {
    let browser;

    try {
        console.log('=== Simple Settings Save Test ===');

    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

        const page = await browser.newPage();
        await page.setViewport({ width: 1920, height: 1080 });

        // Monitor API calls
        await page.setRequestInterception(true);
        const apiCalls = [];

        page.on('request', request => {
            const url = request.url();
            if (url.includes('/settings/api/') && request.method() === 'PUT') {
                apiCalls.push({
                    method: request.method(),
                    url: url,
                    body: request.postData()
                });
                console.log(`API ${request.method()}: ${url}`);
                console.log(`Body: ${request.postData()}`);
            }
            request.continue();
        });

        const authHelper = new AuthHelper(page);

        // Create and login user
        const timestamp = Date.now();
        const testUsername = `simple_test_${timestamp}`;
        await authHelper.register(testUsername, 'testpass123');

        console.log('User registered');

        // Navigate to settings
        await page.goto('http://localhost:5000/settings', { waitUntil: 'networkidle2' });

        // Check if we're on settings page
        const url = page.url();
        if (url.includes('/auth/login')) {
            console.log('Redirected to login, logging in...');
            await authHelper.login(testUsername, 'testpass123');
            await page.goto('http://localhost:5000/settings', { waitUntil: 'networkidle2' });
        }

        console.log('On settings page');

        // Wait for page to load
        await page.waitForSelector('input', { timeout: 10000 });

        // Find a simple text input field to test with
        const testField = await page.$('input[name="app.port"]'); // Port field should be simple

        if (testField) {
            console.log('Found app.port field, testing save...');

            // Get current value
            const currentValue = await testField.evaluate(el => el.value);
            console.log(`Current value: ${currentValue}`);

            // Change value
            await testField.click({ clickCount: 3 });
            await page.keyboard.press('Backspace');
            const newValue = currentValue === '5000' ? '5001' : '5000';
            await testField.type(newValue);

            console.log(`Typed new value: ${newValue}`);

            // Press Enter
            console.log('Pressing Enter...');
            await page.keyboard.press('Enter');

            // Wait for save
            await new Promise(resolve => setTimeout(resolve, 3000));

            console.log(`API calls made: ${apiCalls.length}`);
            apiCalls.forEach(call => {
                console.log(`- ${call.method} ${call.url}`);
                console.log(`  Body: ${call.body}`);
            });

            // Try blur save
            if (apiCalls.length === 0) {
                console.log('No API calls from Enter, trying blur...');
                await page.click('body');
                await new Promise(resolve => setTimeout(resolve, 2000));

                console.log(`API calls after blur: ${apiCalls.length}`);
            }
        } else {
            console.log('Could not find app.port field');

            // List all input fields
            const inputs = await page.evaluate(() => {
                return Array.from(document.querySelectorAll('input')).map(input => ({
                    name: input.name,
                    type: input.type,
                    id: input.id,
                    value: input.value ? input.value.substring(0, 20) : '',
                    visible: input.offsetParent !== null
                }));
            });

            console.log('Input fields found:');
            inputs.forEach(input => {
                if (input.visible && input.name) {
                    console.log(`- ${input.name} (${input.type})`);
                }
            });
        }

        // Now test with API key field
        console.log('\nTesting API key field...');
        const apiKeyField = await page.$('input[name="llm.openai_endpoint.api_key"]');

        if (apiKeyField) {
            console.log('Found API key field');

            // Clear and type
            await apiKeyField.focus();
            await page.keyboard.down('Control');
            await page.keyboard.press('a');
            await page.keyboard.up('Control');
            await page.keyboard.press('Delete');

            await apiKeyField.type('test-key-12345');

            // Press Enter
            console.log('Pressing Enter on API key field...');
            apiCalls.length = 0; // Clear previous calls
            await page.keyboard.press('Enter');

            await new Promise(resolve => setTimeout(resolve, 3000));

            console.log(`API calls for API key: ${apiCalls.length}`);
            apiCalls.forEach(call => {
                console.log(`- ${call.method} ${call.url}`);
            });
        } else {
            console.log('API key field not found');
        }

        console.log('\nTest complete');

    } catch (error) {
        console.error('Error:', error.message);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
})();
