const puppeteer = require('puppeteer');
const AuthHelper = require('../auth_helper');
const assert = require('assert');

// Test that the scheduler registers user activity when accessing news endpoints
describe('News Scheduler Activity Test', function() {
    this.timeout(60000); // Increase timeout for login + API calls

    let browser;
    let page;
    let authHelper;
    let cookies;

    before(async function() {
        browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        page = await browser.newPage();

        // Enable console logging from the page
        page.on('console', msg => {
            if (msg.type() === 'log' || msg.type() === 'info') {
                console.log('[Browser]:', msg.text());
            }
        });

        // Create auth helper and login
        authHelper = new AuthHelper(page);
        console.log('Logging in...');
        await authHelper.ensureAuthenticated();

        // Save cookies for API requests
        cookies = await page.cookies();
        console.log('Login successful, got', cookies.length, 'cookies');
    });

    after(async function() {
        if (browser) {
            await browser.close();
        }
    });

    it('should trigger register_activity when loading subscriptions', async function() {
        // First, let's check the scheduler status
        console.log('Checking scheduler status...');

        const schedulerResponse = await page.evaluate(async () => {
            const response = await fetch('/news/api/scheduler/status', {
                credentials: 'same-origin'
            });
            return await response.json();
        });

        console.log('Scheduler status:', schedulerResponse);
        assert(schedulerResponse.scheduler_available, 'Scheduler should be available');
        assert(schedulerResponse.is_running, 'Scheduler should be running');

        // Now load subscriptions which should trigger register_activity
        console.log('Loading subscriptions...');

        const subscriptionsResponse = await page.evaluate(async () => {
            const response = await fetch('/news/api/subscriptions/current', {
                credentials: 'same-origin'
            });
            return {
                ok: response.ok,
                status: response.status,
                data: await response.json()
            };
        });

        console.log('Subscriptions response:', subscriptionsResponse);
        assert(subscriptionsResponse.ok, 'Subscriptions request should succeed');

        // Check scheduler status again to see if user was registered
        console.log('Checking scheduler status after subscription load...');

        const schedulerStatusAfter = await page.evaluate(async () => {
            const response = await fetch('/news/api/scheduler/status', {
                credentials: 'same-origin'
            });
            return await response.json();
        });

        console.log('Scheduler status after:', schedulerStatusAfter);
        console.log('Active users:', schedulerStatusAfter.active_users);
        console.log('User sessions:', schedulerStatusAfter.user_sessions);

        // The user should now be registered with the scheduler
        assert(schedulerStatusAfter.active_users > 0, 'Scheduler should have active users after subscription load');
    });

    it('should schedule user subscriptions when activity is registered', async function() {
        // Create a test subscription first
        console.log('Creating test subscription...');

        const createResponse = await page.evaluate(async () => {
            const response = await fetch('/news/api/subscribe', {
                method: 'POST',
                credentials: 'same-origin',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.content || ''
                },
                body: JSON.stringify({
                    query: 'Test scheduler activity',
                    refresh_minutes: 60,
                    name: 'Scheduler Test Subscription'
                })
            });
            return {
                ok: response.ok,
                status: response.status,
                data: await response.json()
            };
        });

        console.log('Create subscription response:', createResponse);
        assert(createResponse.ok, 'Subscription creation should succeed');

        // Wait a bit for scheduler to process
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check scheduler status to see scheduled jobs
        const finalStatus = await page.evaluate(async () => {
            const response = await fetch('/news/api/scheduler/status', {
                credentials: 'same-origin'
            });
            return await response.json();
        });

        console.log('Final scheduler status:', finalStatus);
        console.log('Scheduled jobs:', finalStatus.scheduled_jobs);

        // There should be scheduled jobs for the user's subscriptions
        if (finalStatus.scheduled_jobs !== undefined) {
            assert(finalStatus.scheduled_jobs >= 0, 'Scheduler should have scheduled jobs');
        }

        // Clean up - delete the test subscription
        if (createResponse.data && createResponse.data.id) {
            console.log('Cleaning up test subscription...');
            await page.evaluate(async (subId) => {
                await fetch(`/news/api/subscriptions/${subId}`, {
                    method: 'DELETE',
                    credentials: 'same-origin',
                    headers: {
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]')?.content || ''
                    }
                });
            }, createResponse.data.id);
        }
    });
});
