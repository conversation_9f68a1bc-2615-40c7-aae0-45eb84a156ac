const assert = require('assert');
const puppeteer = require('puppeteer');

async function testScheduler() {
    let browser;
    let page;
    const baseUrl = 'http://127.0.0.1:5000';

    try {
        console.log('Starting scheduler test...');

        browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        page = await browser.newPage();

        // Navigate to the root page first
        await page.goto(baseUrl);

        // Check the scheduler status
        console.log('Checking scheduler status...');

        const schedulerResponse = await page.evaluate(async () => {
            const response = await fetch('/news/api/scheduler/status', {
                credentials: 'same-origin'
            });
            return await response.json();
        });

        console.log('Scheduler status:', schedulerResponse);

        // Basic assertions
        assert(schedulerResponse.scheduler_available, 'Scheduler should be available');
        assert(schedulerResponse.is_running, 'Scheduler should be running');
        assert(schedulerResponse.config, 'Scheduler should have configuration');
        assert(schedulerResponse.config.enabled, 'Scheduler should be enabled');

        console.log('✅ News scheduler is running successfully');
        console.log('Active users:', schedulerResponse.active_users);
        console.log('Scheduled jobs:', schedulerResponse.total_scheduled_jobs);

    } catch (error) {
        console.error('Test failed:', error);
        process.exit(1);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// Run the test
testScheduler();
