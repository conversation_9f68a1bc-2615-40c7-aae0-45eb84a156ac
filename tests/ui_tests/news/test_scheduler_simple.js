const { describe, it, before, after } = require('mocha');
const assert = require('assert');
const puppeteer = require('puppeteer');

describe('News Scheduler Basic Test', function() {
    let browser;
    let page;
    const baseUrl = 'http://127.0.0.1:5000';

    before(async function() {
        this.timeout(30000);
        browser = await puppeteer.launch({
            headless: 'new',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        page = await browser.newPage();
    });

    after(async function() {
        if (browser) {
            await browser.close();
        }
    });

    it('should verify scheduler is running', async function() {
        // Navigate to the root page first
        await page.goto(baseUrl);

        // Check the scheduler status
        console.log('Checking scheduler status...');

        const schedulerResponse = await page.evaluate(async () => {
            const response = await fetch('/news/api/scheduler/status', {
                credentials: 'same-origin'
            });
            return await response.json();
        });

        console.log('Scheduler status:', schedulerResponse);

        // Basic assertions
        assert(schedulerResponse.scheduler_available, 'Scheduler should be available');
        assert(schedulerResponse.is_running, 'Scheduler should be running');
        assert(schedulerResponse.config, 'Scheduler should have configuration');
        assert(schedulerResponse.config.enabled, 'Scheduler should be enabled');

        console.log('✅ News scheduler is running successfully');
    });
});
