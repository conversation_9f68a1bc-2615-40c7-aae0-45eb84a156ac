/**
 * Test Fixed Research Submission
 * A test to verify that research submission works with proper model configuration
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');
const { setupDefaultModel } = require('./model_helper');

async function testFixedResearch() {
    console.log('📊 Running: Fixed Research Test');
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    try {
        const page = await browser.newPage();
        const baseUrl = 'http://127.0.0.1:5000';
        const authHelper = new AuthHelper(page, baseUrl);

        // Monitor console errors
        page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log('❌ BROWSER ERROR:', msg.text());
            }
        });

        // Step 1: Authenticate
        console.log('🔐 Authenticating...');
        await authHelper.ensureAuthenticated();
        console.log('✅ Authenticated');

        // Step 2: Navigate to home
        console.log('🏠 Navigating to home...');
        await page.goto(baseUrl, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Step 3: Configure model
        console.log('🔧 Configuring model...');
        const modelConfigured = await setupDefaultModel(page);
        if (!modelConfigured) {
            throw new Error('Failed to configure model');
        }
        console.log('✅ Model configured');

        // Step 4: Enter query
        console.log('📝 Entering query...');
        await page.waitForSelector('#query', { timeout: 10000 });
        await page.type('#query', 'What is JavaScript?');
        console.log('✅ Query entered');

        // Step 5: Submit research
        console.log('🚀 Submitting research...');
        const submitButton = await page.$('#start-research-btn') || await page.$('button[type="submit"]');
        if (!submitButton) {
            throw new Error('Submit button not found');
        }

        // Click submit and wait for navigation
        await Promise.all([
            page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 }),
            submitButton.click()
        ]).catch(async (error) => {
            console.log('⚠️  Navigation timeout, checking current state...');
            // Even if navigation times out, check if we're on research page
        });

        // Step 6: Verify research started
        const currentUrl = page.url();
        console.log('📍 Current URL:', currentUrl);

        if (currentUrl.includes('/research/') || currentUrl.includes('/progress/')) {
            console.log('✅ Research submitted successfully!');

            // Wait a moment to verify research is processing
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Check for progress indicators
            const hasProgress = await page.evaluate(() => {
                const statusElements = document.querySelectorAll('.status, .research-status, [class*="status"], .progress, .progress-bar');
                return statusElements.length > 0;
            });

            if (hasProgress) {
                console.log('✅ Research is processing');
                console.log('✅ Fixed Research Test passed');
                process.exit(0);
            }
        } else {
            // Check for errors
            const errorMessage = await page.evaluate(() => {
                const alert = document.querySelector('.alert-danger, .error-message');
                return alert ? alert.textContent.trim() : null;
            });

            if (errorMessage) {
                throw new Error(`Research failed: ${errorMessage}`);
            } else {
                throw new Error('Research did not redirect to progress page');
            }
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        process.exit(1);
    } finally {
        await browser.close();
    }
}

// Run the test
testFixedResearch().catch(error => {
    console.error('❌ Test error:', error);
    process.exit(1);
});
