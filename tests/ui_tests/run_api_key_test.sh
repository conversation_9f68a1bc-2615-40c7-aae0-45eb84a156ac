#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== API Key Settings Comprehensive Test ===${NC}"
echo -e "${BLUE}This test will verify all aspects of API key settings functionality${NC}"
echo ""

# Check if server is running
if ! curl -s http://localhost:5000 > /dev/null; then
    echo -e "${RED}Error: Server is not running on http://localhost:5000${NC}"
    echo -e "${YELLOW}Please start the server first with:${NC}"
    echo "cd src && nohup python -m local_deep_research.web.app &"
    exit 1
fi

echo -e "${GREEN}✓ Server is running${NC}"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}Installing test dependencies...${NC}"
    npm install
fi

# Check if we should show the browser
if [ "$1" == "--show" ] || [ "$1" == "-s" ]; then
    export SHOW_BROWSER=1
    echo -e "${YELLOW}Running in visible browser mode${NC}"
else
    echo -e "${BLUE}Running in headless mode (use --show to see browser)${NC}"
fi

# Run the comprehensive test
echo ""
echo -e "${BLUE}Starting comprehensive API key test...${NC}"
echo "----------------------------------------"

node test_api_key_comprehensive.js

# Check exit code
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✓ All tests passed!${NC}"
    echo -e "${BLUE}Check screenshots in /tmp/ for visual confirmation${NC}"
else
    echo ""
    echo -e "${RED}✗ Some tests failed${NC}"
    echo -e "${YELLOW}Check the output above and screenshots in /tmp/ for details${NC}"
    exit 1
fi
