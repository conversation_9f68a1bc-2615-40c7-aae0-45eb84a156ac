const { spawn } = require('child_process');

const tests = [
    // Core metrics tests
    { name: 'Metrics Dashboard', file: 'test_metrics_dashboard.js' },
    { name: 'Cost Analytics', file: 'test_cost_analytics.js' },
    { name: 'Metrics Verification', file: 'test_metrics_verification.js' },
    { name: 'Metrics Full Flow', file: 'test_metrics_full_flow.js' },
    { name: 'Metrics Display', file: 'test_metrics_display.js' },
    { name: 'Metrics Browser', file: 'test_metrics_browser.js' },
];

let passed = 0;
let failed = 0;

async function runTest(test) {
    console.log(`\n📊 Running: ${test.name}`);
    return new Promise((resolve) => {
        const testProcess = spawn('node', [test.file], {
            stdio: 'inherit',
            env: { ...process.env, HEADLESS: 'true' }
        });

        const timeout = setTimeout(() => {
            testProcess.kill();
            console.log(`⏱️ ${test.name} timed out`);
            failed++;
            resolve();
        }, 60000); // 60 second timeout per test

        testProcess.on('close', (code) => {
            clearTimeout(timeout);
            if (code === 0) {
                console.log(`✅ ${test.name} passed`);
                passed++;
            } else {
                console.log(`❌ ${test.name} failed`);
                failed++;
            }
            resolve();
        });
    });
}

async function runAll() {
    console.log('Starting Metrics Test Suite\n');

    for (const test of tests) {
        const fs = require('fs');
        if (fs.existsSync(test.file)) {
            await runTest(test);
        } else {
            console.log(`⚠️ Skipping ${test.name} - file not found`);
        }
    }

    console.log(`\n📈 Results: ${passed} passed, ${failed} failed`);
    process.exit(failed > 0 ? 1 : 0);
}

runAll();
