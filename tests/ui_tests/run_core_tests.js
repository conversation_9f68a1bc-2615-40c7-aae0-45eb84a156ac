const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Test configuration
const TEST_TIMEOUT = 60000; // 1 minute per test

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    magenta: '\x1b[35m'
};

function log(message, type = 'info') {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const typeColors = {
        'info': colors.cyan,
        'success': colors.green,
        'error': colors.red,
        'warning': colors.yellow,
        'section': colors.blue,
        'test': colors.magenta
    };
    const color = typeColors[type] || colors.reset;
    console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
}

// Core test files to verify functionality
const testFiles = [
    'test_auth_flow.js',
    'test_simple_auth.js',
    'test_settings_page.js',
    'test_simple_research.js',
    'test_history_page.js'
];

// Test results
const results = {
    total: testFiles.length,
    passed: 0,
    failed: 0,
    details: []
};

async function runTest(testFile) {
    return new Promise((resolve) => {
        const testPath = path.join(__dirname, testFile);

        const startTime = Date.now();
        const testProcess = spawn('node', [testPath], {
            cwd: __dirname,
            env: { ...process.env, NODE_ENV: 'test' }
        });

        let lastOutput = '';
        let errorOutput = '';

        // Capture stdout - only show last line
        testProcess.stdout.on('data', (data) => {
            const text = data.toString();
            const lines = text.trim().split('\n');
            if (lines.length > 0) {
                lastOutput = lines[lines.length - 1];
                // Show progress
                process.stdout.write('.');
            }
        });

        // Capture stderr
        testProcess.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });

        // Set timeout
        const timeout = setTimeout(() => {
            log(`\n⏱️ Test timeout after ${TEST_TIMEOUT/1000} seconds`, 'error');
            testProcess.kill('SIGTERM');
        }, TEST_TIMEOUT);

        // Handle test completion
        testProcess.on('close', (code) => {
            clearTimeout(timeout);
            const duration = ((Date.now() - startTime) / 1000).toFixed(2);

            if (code === 0) {
                log(`\n✅ ${testFile} passed (${duration}s)`, 'success');
                results.passed++;
                results.details.push({
                    name: testFile,
                    status: 'passed',
                    duration: duration
                });
            } else {
                log(`\n❌ ${testFile} failed with code ${code} (${duration}s)`, 'error');
                if (errorOutput) {
                    log(`Error: ${errorOutput.substring(0, 200)}...`, 'error');
                }
                results.failed++;
                results.details.push({
                    name: testFile,
                    status: 'failed',
                    duration: duration,
                    code: code
                });
            }

            resolve();
        });

        // Handle errors
        testProcess.on('error', (error) => {
            clearTimeout(timeout);
            log(`\n❌ Failed to run ${testFile}: ${error.message}`, 'error');
            results.failed++;
            results.details.push({
                name: testFile,
                status: 'failed',
                error: error.message
            });
            resolve();
        });
    });
}

async function runCoreTests() {
    log('🚀 Starting LDR Core UI Tests', 'section');
    log('This will run essential tests only\n', 'info');

    // Check if server is running
    const http = require('http');
    const serverCheck = await new Promise((resolve) => {
        http.get('http://127.0.0.1:5000/', (res) => {
            resolve(true);
        }).on('error', () => {
            resolve(false);
        });
    });

    if (!serverCheck) {
        log('❌ Server is not running on http://127.0.0.1:5000', 'error');
        process.exit(1);
    }

    log('✅ Server is running\n', 'success');

    // Run tests
    const startTime = Date.now();

    for (const testFile of testFiles) {
        log(`\nRunning ${testFile}`, 'test');
        await runTest(testFile);
    }

    const totalDuration = ((Date.now() - startTime) / 1000).toFixed(2);

    // Summary
    log('\n' + '='.repeat(50), 'section');
    log('📊 CORE TEST SUMMARY', 'section');
    log('='.repeat(50), 'section');
    log(`Total Tests: ${results.total}`, 'info');
    log(`✅ Passed: ${results.passed}`, 'success');
    log(`❌ Failed: ${results.failed}`, results.failed > 0 ? 'error' : 'info');
    log(`⏱️ Total Duration: ${totalDuration}s`, 'info');

    // Exit
    process.exit(results.failed > 0 ? 1 : 0);
}

// Run tests
runCoreTests().catch(error => {
    log(`\n❌ Test error: ${error.message}`, 'error');
    process.exit(1);
});
