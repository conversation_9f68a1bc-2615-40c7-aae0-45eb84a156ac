/**
 * Simple Research Diagnostic
 * Minimal test to diagnose research submission issues
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function runDiagnostic() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    console.log('🔬 Research Diagnostic\n');

    try {
        // Login
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in\n');

        // Navigate to home
        await page.goto(baseUrl, { waitUntil: 'networkidle2' });
        await page.waitForSelector('#query', { timeout: 10000 });

        // Test 1: Direct API call with minimal data
        console.log('Test 1: Minimal API call');
        const result1 = await page.evaluate(async () => {
            const response = await fetch('/api/start_research', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    query: 'test query',
                    mode: 'quick',
                    model_provider: 'OLLAMA',
                    model: 'llama3.1:8b'
                })
            });
            return {
                status: response.status,
                ok: response.ok,
                data: await response.json()
            };
        });

        console.log('Result:', result1);

        if (result1.ok && result1.data.research_id) {
            console.log(`\n✅ Research created with ID: ${result1.data.research_id}`);

            // Check the research page
            await page.goto(`${baseUrl}/research/${result1.data.research_id}`);
            await new Promise(resolve => setTimeout(resolve, 5000));

            const pageContent = await page.evaluate(() => {
                return document.body.innerText || 'No content';
            });

            console.log('\nPage content preview:');
            console.log(pageContent.substring(0, 300));

            // Check server logs hint
            console.log('\n💡 To see why research isn\'t processing:');
            console.log('1. Check server logs: tail -f /tmp/ldr_server_ui_tests.log');
            console.log('2. Look for SQLAlchemy session errors');
            console.log('3. Check if background task is starting');
        }

    } catch (error) {
        console.error('Error:', error.message);
    }

    console.log('\n⏸️  Browser will close in 10 seconds...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    await browser.close();
}

runDiagnostic().catch(console.error);
