const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');
const AuthHelper = require('./auth_helper');

// Test configuration
const BASE_URL = 'http://127.0.0.1:5000';
const SCREENSHOTS_DIR = path.join(__dirname, 'screenshots', 'metrics_visibility');

// Create screenshots directory if it doesn't exist
if (!fs.existsSync(SCREENSHOTS_DIR)) {
    fs.mkdirSync(SCREENSHOTS_DIR, { recursive: true });
}

async function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function testMetricsPage() {
    const browser = await puppeteer.launch({
        headless: false, // Set to false to see the browser
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Set viewport to standard desktop size
    await page.setViewport({ width: 1920, height: 1080 });

    // Initialize auth helper
    const auth = new AuthHelper(page, BASE_URL);

    try {
        // Register or login
        console.log('Attempting to authenticate...');
        await auth.ensureAuthenticated();
        console.log('Authentication successful!');

        // Take screenshot after login
        await page.screenshot({ path: path.join(SCREENSHOTS_DIR, '01_after_auth.png'), fullPage: true });

        // Navigate to metrics page
        console.log('Navigating to metrics page...');
        await page.goto(`${BASE_URL}/metrics/`, { waitUntil: 'networkidle0' });

        // Wait a bit for any dynamic content to load
        await delay(2000);

        // Take full page screenshot
        await page.screenshot({ path: path.join(SCREENSHOTS_DIR, '02_metrics_page.png'), fullPage: true });

        // Check if metrics page loaded
        const metricsTitle = await page.$('h1');
        if (metricsTitle) {
            const titleText = await page.evaluate(el => el.textContent, metricsTitle);
            console.log('Page title:', titleText);
        }

        // Check for the total-tokens element
        console.log('\n=== Checking Token Display ===');
        const tokenElement = await page.$('#total-tokens');

        if (tokenElement) {
            // Get all the relevant information about the element
            const tokenInfo = await page.evaluate(() => {
                const elem = document.getElementById('total-tokens');
                const computed = window.getComputedStyle(elem);
                const parentComputed = window.getComputedStyle(elem.parentElement);

                return {
                    exists: true,
                    textContent: elem.textContent,
                    innerHTML: elem.innerHTML,
                    color: computed.color,
                    backgroundColor: computed.backgroundColor,
                    fontSize: computed.fontSize,
                    display: computed.display,
                    visibility: computed.visibility,
                    opacity: computed.opacity,
                    parentBgColor: parentComputed.backgroundColor,
                    // Check CSS variables
                    primaryColor: getComputedStyle(document.documentElement).getPropertyValue('--primary-color'),
                    accentPrimary: getComputedStyle(document.documentElement).getPropertyValue('--accent-primary'),
                    textPrimary: getComputedStyle(document.documentElement).getPropertyValue('--text-primary'),
                    textSecondary: getComputedStyle(document.documentElement).getPropertyValue('--text-secondary'),
                    textMuted: getComputedStyle(document.documentElement).getPropertyValue('--text-muted'),
                    bgPrimary: getComputedStyle(document.documentElement).getPropertyValue('--bg-primary'),
                    bgSecondary: getComputedStyle(document.documentElement).getPropertyValue('--bg-secondary')
                };
            });

            console.log('Token element info:');
            console.log('  - Text content:', tokenInfo.textContent);
            console.log('  - Color:', tokenInfo.color);
            console.log('  - Background:', tokenInfo.backgroundColor);
            console.log('  - Font size:', tokenInfo.fontSize);
            console.log('  - Display:', tokenInfo.display);
            console.log('  - Visibility:', tokenInfo.visibility);
            console.log('  - Opacity:', tokenInfo.opacity);
            console.log('  - Parent background:', tokenInfo.parentBgColor);
            console.log('\nCSS Variables:');
            console.log('  - --primary-color:', tokenInfo.primaryColor);
            console.log('  - --accent-primary:', tokenInfo.accentPrimary);
            console.log('  - --text-primary:', tokenInfo.textPrimary);
            console.log('  - --text-secondary:', tokenInfo.textSecondary);
            console.log('  - --text-muted:', tokenInfo.textMuted);
            console.log('  - --bg-primary:', tokenInfo.bgPrimary);
            console.log('  - --bg-secondary:', tokenInfo.bgSecondary);

            // Highlight the element for screenshot
            await page.evaluate(() => {
                const elem = document.getElementById('total-tokens');
                elem.style.border = '3px solid red';
                elem.style.padding = '5px';
            });

            await page.screenshot({ path: path.join(SCREENSHOTS_DIR, '03_token_element_highlighted.png'), fullPage: true });

            // Try to make the text visible by changing color
            await page.evaluate(() => {
                const elem = document.getElementById('total-tokens');
                elem.style.color = 'red';
                elem.style.fontSize = '3rem';
                elem.textContent = 'TEST: ' + elem.textContent;
            });

            await page.screenshot({ path: path.join(SCREENSHOTS_DIR, '04_token_element_red.png'), fullPage: true });

        } else {
            console.log('❌ Token element (#total-tokens) not found!');
        }

        // Check all metric-label elements (the titles like "Total Tokens Used")
        console.log('\n=== Checking Metric Labels ===');
        const metricLabels = await page.$$eval('.metric-label', elements => {
            return elements.map(el => ({
                text: el.textContent.trim(),
                color: window.getComputedStyle(el).color,
                backgroundColor: window.getComputedStyle(el).backgroundColor,
                display: window.getComputedStyle(el).display,
                visibility: window.getComputedStyle(el).visibility,
                fontSize: window.getComputedStyle(el).fontSize,
                opacity: window.getComputedStyle(el).opacity
            }));
        });

        console.log('Found metric labels:', metricLabels.length);
        metricLabels.forEach(ml => {
            console.log(`  - "${ml.text}" (color: ${ml.color}, bg: ${ml.backgroundColor}, display: ${ml.display}, visibility: ${ml.visibility}, opacity: ${ml.opacity})`);
        });

        // Check all metric-value elements
        console.log('\n=== Checking All Metric Values ===');
        const metricValues = await page.$$eval('.metric-value', elements => {
            return elements.map(el => ({
                id: el.id,
                text: el.textContent,
                color: window.getComputedStyle(el).color,
                display: window.getComputedStyle(el).display,
                visibility: window.getComputedStyle(el).visibility
            }));
        });

        console.log('Found metric values:', metricValues.length);
        metricValues.forEach(mv => {
            console.log(`  - ${mv.id}: "${mv.text}" (color: ${mv.color}, display: ${mv.display}, visibility: ${mv.visibility})`);
        });

        // Check if Vite CSS is loaded
        console.log('\n=== Checking Vite Assets ===');
        const viteAssets = await page.evaluate(() => {
            const stylesheets = Array.from(document.querySelectorAll('link[rel="stylesheet"]')).map(link => link.href);
            const scripts = Array.from(document.querySelectorAll('script[src]')).map(script => script.src);

            return {
                stylesheets,
                scripts,
                hasViteCss: stylesheets.some(href => href.includes('/static/dist/css/')),
                hasViteJs: scripts.some(src => src.includes('/static/dist/js/'))
            };
        });

        console.log('Stylesheets loaded:');
        viteAssets.stylesheets.forEach(href => console.log('  -', href));
        console.log('Scripts loaded:');
        viteAssets.scripts.forEach(src => console.log('  -', src));
        console.log('Has Vite CSS:', viteAssets.hasViteCss);
        console.log('Has Vite JS:', viteAssets.hasViteJs);

        // Take a screenshot with DevTools open to show computed styles
        const client = await page.target().createCDPSession();
        await client.send('Inspector.enable');

        console.log('\n✅ Test completed! Check screenshots in:', SCREENSHOTS_DIR);

    } catch (error) {
        console.error('Error during test:', error);
        await page.screenshot({ path: path.join(SCREENSHOTS_DIR, 'error_state.png'), fullPage: true });
    } finally {
        // Keep browser open for manual inspection
        console.log('\nPress Ctrl+C to close the browser and exit...');
        // await browser.close();
    }
}

// Run the test
console.log('Starting metrics visibility test...');
console.log('Screenshots will be saved to:', SCREENSHOTS_DIR);
console.log('Base URL:', BASE_URL);
console.log('---\n');

testMetricsPage().catch(console.error);
