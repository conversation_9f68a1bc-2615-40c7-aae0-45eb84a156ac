#!/usr/bin/env python3
"""
Test UUID research functionality with a fresh user database.
"""

import json
import os
import random
import string

import requests

# Allow unencrypted databases for testing
os.environ["LDR_ALLOW_UNENCRYPTED"] = "true"

# Base URL for the application
BASE_URL = "http://127.0.0.1:5000"


# Generate random username for fresh database
def generate_random_username():
    return f"test_uuid_{''.join(random.choices(string.ascii_lowercase + string.digits, k=8))}"


def test_uuid_research():
    """Test research creation with UUID primary keys"""

    # Create a session for maintaining cookies
    session = requests.Session()

    # Generate random user credentials
    username = generate_random_username()
    password = "T3st!Secure#2024$LDR"  # pragma: allowlist secret

    print(f"Testing with new user: {username}")

    # Step 1: Register new user (creates fresh database)
    print("\n1. Registering new user...")

    # Get registration page for CSRF token
    reg_page = session.get(f"{BASE_URL}/auth/register")
    csrf_token = None

    # Extract CSRF token
    for line in reg_page.text.split("\n"):
        if 'name="csrf_token"' in line and "value=" in line:
            start = line.find('value="') + 7
            end = line.find('"', start)
            csrf_token = line[start:end]
            break

    if not csrf_token:
        print("Failed to get CSRF token")
        return False

    # Register the user
    reg_data = {
        "username": username,
        "password": password,
        "confirm_password": password,
        "acknowledge": "true",
        "csrf_token": csrf_token,
    }

    reg_response = session.post(f"{BASE_URL}/auth/register", data=reg_data)
    print(f"Registration status: {reg_response.status_code}")

    if reg_response.status_code != 200:
        print(f"Registration failed: {reg_response.text}")
        return False

    # Step 2: Submit a research request
    print("\n2. Submitting research request...")

    # Get fresh CSRF token for API request
    home_page = session.get(f"{BASE_URL}/")
    csrf_token = None

    for line in home_page.text.split("\n"):
        if 'name="csrf_token"' in line and "value=" in line:
            start = line.find('value="') + 7
            end = line.find('"', start)
            csrf_token = line[start:end]
            break

    # Prepare research request
    research_data = {
        "query": f"Test UUID research for {username}",
        "mode": "quick",
        "model_provider": "OLLAMA",
        "model": "llama3.2:3b",
        "search_engine": "searxng",
        "iterations": 1,
        "questions_per_iteration": 2,
    }

    headers = {"Content-Type": "application/json", "X-CSRF-Token": csrf_token}

    research_response = session.post(
        f"{BASE_URL}/api/start_research", json=research_data, headers=headers
    )

    print(f"Research submission status: {research_response.status_code}")

    if research_response.status_code == 200:
        result = research_response.json()
        print(f"Research response: {json.dumps(result, indent=2)}")

        research_id = result.get("research_id")
        print(f"\nResearch ID: {research_id}")
        print(f"Research ID type: {type(research_id)}")

        # Check if it's a UUID format (should be a string with dashes)
        if (
            isinstance(research_id, str)
            and len(research_id) == 36
            and research_id.count("-") == 4
        ):
            print("✅ Research ID is in UUID format!")
        else:
            print("❌ Research ID is NOT in UUID format!")

        # Step 3: Check research status
        print("\n3. Checking research status...")

        status_response = session.get(
            f"{BASE_URL}/api/research/{research_id}/status"
        )
        print(f"Status check response: {status_response.status_code}")

        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"Research status: {status_data.get('status')}")

        # Step 4: List research history
        print("\n4. Checking research history...")

        history_response = session.get(f"{BASE_URL}/api/history")
        print(f"History response: {history_response.status_code}")

        if history_response.status_code == 200:
            history_data = history_response.json()
            items = history_data.get("items", [])
            print(f"Found {len(items)} research items")

            for item in items[:3]:  # Show first 3
                item_id = item.get("id")
                print(f"  - ID: {item_id} (type: {type(item_id).__name__})")
                print(f"    Query: {item.get('query')}")
                print(f"    Status: {item.get('status')}")

        return True

    else:
        print(f"Research submission failed: {research_response.text}")
        return False


# Now let's also check the database directly
def check_database_directly():
    """Check the database schema directly"""
    print("\n5. Checking database schema directly...")

    try:
        import sys
        from pathlib import Path

        sys.path.insert(
            0,
            str(Path(__file__).parent.parent.parent.resolve()),
        )

        from sqlalchemy import inspect

        from src.local_deep_research.database.auth_db import get_auth_db_session
        from src.local_deep_research.database.encrypted_db import db_manager
        from src.local_deep_research.database.models.auth import User
        from src.local_deep_research.database.models.research import (
            ResearchHistory,
        )

        # Get the last created user
        auth_db = get_auth_db_session()
        latest_user = (
            auth_db.query(User).order_by(User.created_at.desc()).first()
        )
        auth_db.close()

        if latest_user:
            print(f"\nChecking database for user: {latest_user.username}")

            # Open their database
            engine = db_manager.open_user_database(
                latest_user.username, "T3st!Secure#2024$LDR"
            )
            if engine:
                # Inspect the research_history table schema
                inspector = inspect(engine)
                columns = inspector.get_columns("research_history")

                print("\nresearch_history table schema:")
                for col in columns:
                    if col["name"] == "id":
                        print(
                            f"  - {col['name']}: {col['type']} (primary_key: {col.get('primary_key', False)})"
                        )

                # Get actual research entries
                session = db_manager.get_session(latest_user.username)
                researches = session.query(ResearchHistory).limit(5).all()

                print(f"\nFound {len(researches)} research entries:")
                for r in researches:
                    print(f"  - ID: {r.id} (type: {type(r.id).__name__})")
                    print(f"    Query: {r.query[:50]}...")

                session.close()
                db_manager.close_user_database(latest_user.username)

    except Exception as e:
        print(f"Error checking database: {e}")


if __name__ == "__main__":
    print("=" * 60)
    print("UUID Research Test")
    print("=" * 60)

    success = test_uuid_research()

    # Also check database directly
    check_database_directly()

    print("\n" + "=" * 60)
    print("✅ Test completed" if success else "❌ Test failed")
    print("=" * 60)
