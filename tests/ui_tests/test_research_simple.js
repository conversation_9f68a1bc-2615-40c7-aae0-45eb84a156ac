/**
 * Simple Research Test
 * Minimal test to check if research can start
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');
const { setupDefaultModel } = require('./model_helper');

async function testSimpleResearch() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    console.log('🔬 Simple Research Test\n');

    // Monitor key API calls
    page.on('response', async response => {
        if (response.url().includes('/api/start_research') ||
            response.url().includes('/research/start')) {
            console.log(`\n📥 Research API Response:`);
            console.log(`   URL: ${response.url()}`);
            console.log(`   Status: ${response.status()} ${response.statusText()}`);

            try {
                const body = await response.text();
                console.log(`   Body: ${body.substring(0, 200)}...`);
            } catch (e) {
                console.log(`   Body: <unable to read>`);
            }
        }
    });

    try {
        // Login
        console.log('🔐 Logging in...');
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in\n');

        // Go to home
        await page.goto(baseUrl, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Check if we can find the form
        console.log('📋 Checking form elements...');

        const formCheck = await page.evaluate(() => {
            return {
                query: !!document.querySelector('#query'),
                submit: !!document.querySelector('button[type="submit"]'),
                form: !!document.querySelector('#research-form, form'),
                provider: document.querySelector('#model_provider')?.value || 'not found',
                model: document.querySelector('#model, input[name="model"]')?.value || 'not found'
            };
        });

        console.log('Form elements found:');
        console.log(`  Query input: ${formCheck.query ? '✅' : '❌'}`);
        console.log(`  Submit button: ${formCheck.submit ? '✅' : '❌'}`);
        console.log(`  Form: ${formCheck.form ? '✅' : '❌'}`);
        console.log(`  Provider: ${formCheck.provider}`);
        console.log(`  Model: ${formCheck.model}`);

        if (!formCheck.query || !formCheck.submit) {
            throw new Error('Essential form elements not found');
        }

        // Set up model configuration before submitting
        console.log('\n🔧 Configuring model...');
        const modelConfigured = await setupDefaultModel(page);
        if (!modelConfigured) {
            throw new Error('Failed to configure model');
        }

        // Fill and submit
        console.log('\n📝 Submitting research...');

        await page.type('#query', 'What is 2+2?');

        // Try different ways to submit - look for submit button within the form first
        console.log('Looking for submit button...');

        // First try to find submit button within the form specifically
        const submitButton = await page.evaluate(() => {
            const form = document.querySelector('#research-form, form');
            if (form) {
                const btn = form.querySelector('button[type="submit"], #start-research-btn, button:not([type="button"])');
                if (btn) {
                    btn.click();
                    return true;
                }
            }
            // Fallback: try any submit button on the page
            const anySubmit = document.querySelector('#start-research-btn') ||
                             document.querySelector('button[type="submit"]') ||
                             Array.from(document.querySelectorAll('button')).find(b =>
                                 b.textContent.includes('Start Research') ||
                                 b.textContent.includes('Submit'));
            if (anySubmit) {
                anySubmit.click();
                return true;
            }
            return false;
        });

        if (submitButton) {
            console.log('✅ Submit button clicked');

            // Wait for response or navigation
            await Promise.race([
                page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 10000 }).catch(() => null),
                page.waitForResponse(response =>
                    response.url().includes('research') || response.url().includes('api'),
                    { timeout: 10000 }
                ).catch(() => null),
                new Promise(resolve => setTimeout(resolve, 5000))
            ]);

            console.log('Navigation or response received');
        } else {
            console.log('❌ Submit button not found, trying Enter key...');
            await page.focus('#query');
            await page.keyboard.press('Enter');
            await new Promise(resolve => setTimeout(resolve, 3000));
        }

        // Wait a bit more
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Check final state
        const finalState = await page.evaluate(() => {
            const alerts = Array.from(document.querySelectorAll('.alert')).map(a => ({
                type: a.className.includes('danger') ? 'error' :
                      a.className.includes('success') ? 'success' : 'info',
                text: a.textContent.trim()
            }));

            return {
                url: window.location.href,
                alerts: alerts,
                queryValue: document.querySelector('#query')?.value || ''
            };
        });

        console.log('\n📊 Final state:');
        console.log(`  URL: ${finalState.url}`);
        console.log(`  Query still in input: ${finalState.queryValue ? 'Yes' : 'No'}`);

        if (finalState.alerts.length > 0) {
            console.log(`  Alerts:`);
            finalState.alerts.forEach(alert => {
                const icon = alert.type === 'error' ? '❌' :
                           alert.type === 'success' ? '✅' : 'ℹ️';
                console.log(`    ${icon} ${alert.text}`);
            });
        }

        // Check if we were redirected to a research page
        if (finalState.url.includes('/research/') || finalState.url.includes('/progress')) {
            console.log('\n✅ Research started successfully!');
            console.log(`   Research ID: ${finalState.url.split('/').pop()}`);
        } else if (finalState.alerts.some(a => a.type === 'error')) {
            console.log('\n❌ Research failed to start - error shown');
        } else {
            console.log('\n⚠️  Research status unclear - still on home page');
        }


    } catch (error) {
        console.error('\n❌ Test error:', error.message);
    }

    await browser.close();
    console.log('\n🏁 Test completed');
}

// Run the test
testSimpleResearch().catch(console.error);
