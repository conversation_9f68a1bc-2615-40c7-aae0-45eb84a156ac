#!/usr/bin/env node

/**
 * Check chart data for context overflow
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');

const BASE_URL = 'http://127.0.0.1:5000';

async function checkChartData() {
    console.log('🔍 Checking Context Overflow Chart Data\n');

    let browser;
    let page;

    try {
        browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        page = await browser.newPage();
        const authHelper = new AuthHelper(page, BASE_URL);

        // Login
        console.log('📝 Logging in...');
        await authHelper.login('testuser_1755337395122_150', 'T3st!Secure#2024$LDR');
        console.log('✅ Logged in\n');

        // Make API request
        console.log('🔌 Fetching context overflow data...');
        const response = await page.evaluate(async (baseUrl) => {
            const res = await fetch(`${baseUrl}/metrics/api/context-overflow?period=all`, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json'
                }
            });

            const data = await res.json();
            return data;
        }, BASE_URL);

        if (response.status === 'success') {
            console.log('✅ API Response successful\n');

            // Check chart data
            if (response.chart_data) {
                console.log(`📈 Chart data points: ${response.chart_data.length}\n`);

                if (response.chart_data.length > 0) {
                    console.log('Sample chart data points:');
                    response.chart_data.forEach((point, idx) => {
                        console.log(`\nPoint ${idx + 1}:`);
                        console.log(`  Timestamp: ${point.timestamp}`);
                        console.log(`  Research ID: ${point.research_id}`);
                        console.log(`  Model: ${point.model || 'N/A'}`);
                        console.log(`  Prompt tokens: ${point.prompt_tokens}`);
                        console.log(`  Actual prompt tokens: ${point.actual_prompt_tokens || 'N/A'}`);
                        console.log(`  Context limit: ${point.context_limit || 'N/A'}`);
                        console.log(`  Truncated: ${point.truncated}`);
                        console.log(`  Tokens truncated: ${point.tokens_truncated || 0}`);
                    });
                }
            } else {
                console.log('❌ No chart_data in response');
            }

            // Check context_limits
            if (response.context_limits) {
                console.log(`\n📊 Context limits data: ${response.context_limits.length} entries`);
                if (response.context_limits.length > 0) {
                    console.log('\nContext limits by model:');
                    response.context_limits.forEach(limit => {
                        console.log(`  ${limit.model}: ${limit.limit} tokens (${limit.count} requests)`);
                    });
                }
            }

        } else {
            console.log(`❌ API Error: ${response.message || 'Unknown error'}`);
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

checkChartData().catch(console.error);
