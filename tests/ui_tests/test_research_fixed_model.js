/**
 * Fixed Research Test
 * Tests research submission with proper model selection
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testResearchFixed() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    console.log('🔬 Fixed Research Test\n');

    // Log console messages
    page.on('console', msg => {
        console.log(`[Browser] ${msg.text()}`);
    });

    try {
        // Login
        console.log('🔐 Logging in...');
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in\n');

        // Go to home page
        await page.goto(baseUrl, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Wait for form to be ready
        await page.waitForSelector('#query', { timeout: 10000 });
        console.log('✅ Form loaded\n');

        // Fill the query
        await page.type('#query', 'What is artificial intelligence?');
        console.log('✅ Entered query\n');

        // Try to submit using API directly
        console.log('🚀 Submitting research via API...\n');

        const result = await page.evaluate(async () => {
            try {
                // Get form data
                const formData = {
                    query: document.querySelector('#query').value,
                    mode: 'quick',
                    model_provider: 'OLLAMA',
                    model: 'llama3.1:8b',  // Use available model
                    custom_endpoint: '',
                    search_engine: 'searxng',
                    iterations: 2,
                    questions_per_iteration: 3,
                    strategy: 'source-based'
                };

                console.log('Sending request with data:', formData);

                const response = await fetch('/api/start_research', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const responseText = await response.text();
                console.log('Response text:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    data = { error: 'Failed to parse response', responseText };
                }

                return {
                    ok: response.ok,
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                };
            } catch (error) {
                console.error('Request error:', error);
                return { error: error.message };
            }
        });

        console.log('\n📊 API Result:');
        console.log(`Status: ${result.status} ${result.statusText}`);
        console.log(`Success: ${result.ok}`);
        console.log('Response data:', JSON.stringify(result.data, null, 2));

        if (result.ok && result.data.research_id) {
            console.log(`\n✅ Research started! ID: ${result.data.research_id}`);

            // Navigate to the research page
            const researchUrl = `${baseUrl}/research/${result.data.research_id}`;
            console.log(`\n📍 Navigating to: ${researchUrl}`);

            await page.goto(researchUrl, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            // Wait a bit for content to load
            await new Promise(resolve => setTimeout(resolve, 5000));

            // Check what's on the page
            const pageContent = await page.evaluate(() => {
                const elements = {
                    title: document.title,
                    url: window.location.href,
                    logs: document.querySelectorAll('.log-entry, #logs li, .log-item').length,
                    status: document.querySelector('.status, #status, .research-status')?.textContent || 'not found',
                    error: document.querySelector('.alert-danger')?.textContent || null,
                    bodyPreview: document.body.innerText.substring(0, 300)
                };
                return elements;
            });

            console.log('\n📋 Research Page Content:');
            console.log(`Title: ${pageContent.title}`);
            console.log(`URL: ${pageContent.url}`);
            console.log(`Log entries: ${pageContent.logs}`);
            console.log(`Status: ${pageContent.status}`);
            if (pageContent.error) {
                console.log(`Error: ${pageContent.error}`);
            }

            // Take screenshot
            await page.screenshot({ path: './screenshots/research_progress_page.png' });
            console.log('\n📸 Screenshot saved');

            // Wait longer to see if research progresses
            console.log('\n⏳ Waiting 15 seconds for research to progress...');
            await new Promise(resolve => setTimeout(resolve, 15000));

            // Reload and check again
            await page.reload({ waitUntil: 'networkidle2' });

            const updatedContent = await page.evaluate(() => {
                return {
                    logs: document.querySelectorAll('.log-entry, #logs li, .log-item').length,
                    status: document.querySelector('.status, #status, .research-status')?.textContent || 'not found'
                };
            });

            console.log('\n📋 Updated Status:');
            console.log(`Log entries: ${updatedContent.logs}`);
            console.log(`Status: ${updatedContent.status}`);

        } else {
            console.log('\n❌ Research failed to start');

            // Check server logs hint
            if (result.status === 500) {
                console.log('\n💡 This appears to be a server-side error.');
                console.log('Common causes:');
                console.log('1. SQLAlchemy session management issues');
                console.log('2. Database transaction errors');
                console.log('3. Model not available in Ollama');
                console.log('\nCheck the server logs at: /tmp/ldr_server_ui_tests.log');
            }
        }

        // Final screenshot
        await page.screenshot({ path: './screenshots/research_final_state.png' });

    } catch (error) {
        console.error('\n❌ Test error:', error.message);
        console.error(error.stack);
        await page.screenshot({ path: './screenshots/research_test_error.png' });
    }

    console.log('\n⏸️  Keeping browser open for 15 seconds...');
    await new Promise(resolve => setTimeout(resolve, 15000));

    await browser.close();
    console.log('🏁 Test completed');
}

// Run the test
testResearchFixed().catch(console.error);
