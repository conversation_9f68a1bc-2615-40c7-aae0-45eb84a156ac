/**
 * UI Test for Follow-up Research Feature
 * Tests the complete flow of asking follow-up questions on research results
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

// Test configuration
const BASE_URL = 'http://127.0.0.1:5000';
const TIMEOUT = 60000; // 60 seconds for research operations

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, type = 'info') {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const typeColors = {
        'info': colors.cyan,
        'success': colors.green,
        'error': colors.red,
        'warning': colors.yellow,
        'section': colors.blue
    };
    const color = typeColors[type] || colors.reset;
    console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
}

async function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Create a research and wait for it to complete
 */
async function createAndCompleteResearch(page, query) {
    log(`🔬 Creating research: "${query}"`, 'info');

    // Navigate to home page
    await page.goto(BASE_URL, { waitUntil: 'networkidle2' });

    // Wait for and fill the query input
    await page.waitForSelector('#query', { timeout: 10000 });
    await page.click('#query', { clickCount: 3 });
    await page.type('#query', query);

    // Select quick summary mode for faster testing
    const modeSelect = await page.$('#mode');
    if (modeSelect) {
        await page.select('#mode', 'quick_summary');
    }

    // Submit the research
    log('📤 Submitting research...', 'info');
    await Promise.all([
        page.waitForNavigation({ waitUntil: 'networkidle2', timeout: TIMEOUT }),
        page.click('button[type="submit"]')
    ]);

    // Wait for research to complete
    log('⏳ Waiting for research to complete...', 'info');
    const startTime = Date.now();

    while (Date.now() - startTime < TIMEOUT) {
        const url = page.url();

        // Check if we're on the results page
        if (url.includes('/results/')) {
            log('✅ Research completed, on results page', 'success');

            // Extract research ID from URL
            const match = url.match(/\/results\/([a-zA-Z0-9-]+)/);
            if (match) {
                return match[1];
            }
            break;
        }

        // Check if we're on progress page and research is complete
        if (url.includes('/progress/')) {
            const isComplete = await page.evaluate(() => {
                const statusEl = document.querySelector('.progress-status, .status-text');
                if (statusEl) {
                    const text = statusEl.textContent.toLowerCase();
                    return text.includes('completed') || text.includes('100%');
                }
                return false;
            });

            if (isComplete) {
                // Click view results button if available
                const viewResultsBtn = await page.$('a[href*="/results/"], button:has-text("View Results")');
                if (viewResultsBtn) {
                    await Promise.all([
                        page.waitForNavigation({ waitUntil: 'networkidle2', timeout: TIMEOUT }),
                        viewResultsBtn.click()
                    ]);
                }
            }
        }

        await delay(2000);
    }

    // Try to extract research ID from current URL
    const finalUrl = page.url();
    const match = finalUrl.match(/\/(results|progress)\/([a-zA-Z0-9-]+)/);
    return match ? match[2] : null;
}

/**
 * Test the follow-up research feature
 */
async function testFollowUpResearch() {
    let browser;
    let exitCode = 0;

    try {
        log('🚀 Starting Follow-up Research UI Test', 'section');

        // Launch browser
        browser = await puppeteer.launch(getPuppeteerLaunchOptions());
        const page = await browser.newPage();

        // Set viewport
        await page.setViewport({ width: 1280, height: 800 });

        // Initialize auth helper
        const auth = new AuthHelper(page, BASE_URL);

        // Ensure authenticated
        log('🔐 Authenticating...', 'info');
        await auth.ensureAuthenticated();

        // Step 1: Create initial research
        log('\n📝 Step 1: Creating initial research', 'section');
        const initialQuery = 'What is quantum computing?';
        const researchId = await createAndCompleteResearch(page, initialQuery);

        if (!researchId) {
            throw new Error('Failed to create initial research or get research ID');
        }
        log(`✅ Initial research created with ID: ${researchId}`, 'success');

        // Step 2: Navigate to results page if not already there
        const currentUrl = page.url();
        if (!currentUrl.includes(`/results/${researchId}`)) {
            log('📍 Navigating to results page...', 'info');
            await page.goto(`${BASE_URL}/results/${researchId}`, { waitUntil: 'networkidle2' });
        }

        // Wait for results to load
        await page.waitForSelector('#results-content, .results-content', { timeout: 10000 });

        // Step 3: Test follow-up button presence
        log('\n🔍 Step 2: Testing follow-up button', 'section');
        const followUpBtn = await page.$('#ask-followup-btn');

        if (!followUpBtn) {
            throw new Error('Follow-up button not found on results page');
        }
        log('✅ Follow-up button found', 'success');

        // Step 4: Click follow-up button and test modal
        log('\n🖱️ Step 3: Opening follow-up modal', 'section');
        await followUpBtn.click();
        await delay(1000); // Wait for modal animation

        // Check if modal opened
        const modal = await page.$('#followUpModal');
        if (!modal) {
            throw new Error('Follow-up modal did not open');
        }

        // Check if modal is visible
        const modalVisible = await page.evaluate(() => {
            const modal = document.getElementById('followUpModal');
            if (!modal) return false;

            // Check Bootstrap modal classes or inline styles
            const hasShowClass = modal.classList.contains('show');
            const displayStyle = window.getComputedStyle(modal).display;

            return hasShowClass || displayStyle !== 'none';
        });

        if (!modalVisible) {
            log('⚠️ Modal exists but may not be visible, continuing...', 'warning');
        }

        log('✅ Follow-up modal opened', 'success');

        // Step 5: Check parent context is loaded
        log('\n📊 Step 4: Checking parent context', 'section');

        // Wait a bit for context to load
        await delay(2000);

        const parentContextVisible = await page.evaluate(() => {
            const contextDiv = document.getElementById('parentContext');
            return contextDiv && contextDiv.style.display !== 'none';
        });

        if (parentContextVisible) {
            const parentSummary = await page.$eval('#parentSummary', el => el.textContent);
            const parentSources = await page.$eval('#parentSources', el => el.textContent);

            log(`✅ Parent context loaded:`, 'success');
            log(`   Summary: ${parentSummary.substring(0, 50)}...`, 'info');
            log(`   Sources: ${parentSources} available`, 'info');
        } else {
            log('⚠️ Parent context not visible (may be due to async loading)', 'warning');
        }

        // Step 6: Enter follow-up question
        log('\n✍️ Step 5: Entering follow-up question', 'section');
        const followUpQuestion = 'How do quantum gates work?';

        await page.waitForSelector('#followUpQuestion', { timeout: 5000 });
        await page.type('#followUpQuestion', followUpQuestion);
        log(`✅ Entered follow-up question: "${followUpQuestion}"`, 'success');

        // Step 7: Test advanced options
        log('\n⚙️ Step 6: Testing advanced options', 'section');

        // Try to expand advanced options
        const advancedLink = await page.$('a[onclick*="toggleAdvancedOptions"]');
        if (advancedLink) {
            await advancedLink.click();
            await delay(500);

            const advancedVisible = await page.evaluate(() => {
                const panel = document.getElementById('advancedOptionsPanel');
                return panel && panel.style.display !== 'none';
            });

            if (advancedVisible) {
                // Set to quick summary (1 iteration)
                await page.select('#followUpIterations', '1');
                log('✅ Advanced options configured for quick summary', 'success');
            }
        }

        // Step 8: Submit follow-up research
        log('\n🚀 Step 7: Submitting follow-up research', 'section');

        // Find and click submit button in modal
        const submitBtn = await page.$('#followUpModal button.btn-primary');
        if (!submitBtn) {
            throw new Error('Submit button not found in follow-up modal');
        }

        // Click submit and wait for navigation
        await Promise.all([
            page.waitForNavigation({ waitUntil: 'networkidle2', timeout: TIMEOUT }).catch(e => {
                log('⚠️ Navigation timeout, checking URL...', 'warning');
            }),
            submitBtn.click()
        ]);

        await delay(2000);

        // Step 9: Verify follow-up research started
        log('\n✔️ Step 8: Verifying follow-up research', 'section');

        const newUrl = page.url();

        // Check if we're on a new research page
        if (newUrl.includes('/progress/') || newUrl.includes('/results/')) {
            const newResearchId = newUrl.match(/\/(progress|results)\/([a-zA-Z0-9-]+)/)?.[2];

            if (newResearchId && newResearchId !== researchId) {
                log(`✅ Follow-up research started with ID: ${newResearchId}`, 'success');

                // Check for follow-up indicator in URL params
                if (newUrl.includes('followup=true')) {
                    log('✅ Follow-up flag detected in URL', 'success');
                }
            } else {
                log('⚠️ May be on same research page, checking content...', 'warning');
            }
        } else {
            log(`⚠️ Unexpected URL after submission: ${newUrl}`, 'warning');
        }

        // Final success
        log('\n🎉 Follow-up Research Test Completed Successfully!', 'section');

    } catch (error) {
        log(`\n❌ Test Failed: ${error.message}`, 'error');
        console.error(error);
        exitCode = 1;
    } finally {
        if (browser) {
            await browser.close();
        }
        process.exit(exitCode);
    }
}

// Run the test
(async () => {
    try {
        await testFollowUpResearch();
    } catch (error) {
        console.error('Test execution failed:', error);
        process.exit(1);
    }
})();
