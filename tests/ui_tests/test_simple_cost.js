const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

(async () => {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    // Monitor console
    page.on('console', msg => {
        console.log(`CONSOLE ${msg.type()}: ${msg.text()}`);
    });

    // Monitor network
    page.on('response', async (response) => {
        if (response.url().includes('/metrics/api/cost-analytics')) {
            console.log(`API Response: ${response.status()}`);
            try {
                const text = await response.text();
                console.log('API Data:', text.substring(0, 200));
            } catch (e) {}
        }
    });

    console.log('Loading page...');
    // Clear cache and reload
    await page.setCacheEnabled(false);
    await page.goto('http://localhost:5000/metrics/costs?' + Date.now(), {
        waitUntil: 'domcontentloaded',
        timeout: 15000
    });

    // Wait for initial load
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check loading state
    const states = await page.evaluate(() => {
        const loading = document.getElementById('loading');
        const error = document.getElementById('error');
        const content = document.getElementById('cost-content');
        const noData = document.getElementById('no-data');

        return {
            loading: loading ? loading.style.display : 'not found',
            error: error ? error.style.display : 'not found',
            content: content ? content.style.display : 'not found',
            noData: noData ? noData.style.display : 'not found'
        };
    });

    console.log('Element states:', states);

    // Try manual API call and simulate the loadCostData function
    const apiTest = await page.evaluate(async () => {
        try {
            console.log('Starting manual API test...');
            const response = await fetch('/metrics/api/cost-analytics?period=7d');
            const data = await response.json();
            console.log('API Response received:', data.status);
            console.log('Total calls:', data.overview?.total_calls);
            console.log('Total cost:', data.overview?.total_cost);

            // Now simulate what the loadCostData function should do
            if (data.status === 'success') {
                console.log('Status is success');
                if (data.overview.total_calls > 0) {
                    console.log('Total calls > 0, should show content');

                    // Try to manually call the show functions
                    function showContent() {
                        document.getElementById('loading').style.display = 'none';
                        document.getElementById('cost-content').style.display = 'block';
                        document.getElementById('error').style.display = 'none';
                        document.getElementById('no-data').style.display = 'none';
                    }

                    showContent();
                    console.log('Called showContent() manually');
                } else {
                    console.log('Total calls is 0, should show no data');
                }
            } else {
                console.log('Status is not success:', data.status);
            }

            return { success: true, data: data };
        } catch (error) {
            console.error('Manual API test failed:', error);
            return { success: false, error: error.message };
        }
    });

    console.log('Manual API test:', apiTest.success ? 'SUCCESS' : 'FAILED');
    if (apiTest.success) {
        console.log('API returned cost:', apiTest.data.overview?.total_cost);
        console.log('API returned calls:', apiTest.data.overview?.total_calls);
    }

    // Test the actual logic from the page
    const testLogic = await page.evaluate(() => {
        // Simulate what the loadCostData function does
        const testData = {
            status: 'success',
            overview: {
                total_cost: 0.0,
                total_calls: 58
            }
        };

        console.log('Testing logic: total_calls =', testData.overview.total_calls);
        console.log('Testing logic: total_cost =', testData.overview.total_cost);

        if (testData.status === 'success') {
            if (testData.overview.total_calls > 0) {
                console.log('Should show content!');
                return 'should_show_content';
            } else {
                console.log('Should show no data');
                return 'should_show_no_data';
            }
        }
        return 'unknown';
    });

    console.log('Logic test result:', testLogic);

    // Wait a bit more
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check final states after manual showContent
    const finalStates = await page.evaluate(() => {
        const loading = document.getElementById('loading');
        const error = document.getElementById('error');
        const content = document.getElementById('cost-content');
        const noData = document.getElementById('no-data');

        return {
            loading: loading ? loading.style.display : 'not found',
            error: error ? error.style.display : 'not found',
            content: content ? content.style.display : 'not found',
            noData: noData ? noData.style.display : 'not found'
        };
    });

    console.log('Final element states:', finalStates);

    console.log('Done');
    await browser.close();
})();
