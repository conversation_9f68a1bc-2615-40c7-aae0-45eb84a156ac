/**
 * Simple Authentication Test
 * Basic test to verify registration and login work
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testSimpleAuth() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    // Unique test user
    const testUser = {
        username: `test_${Date.now()}`,
        password: 'testpass123'
    };

    console.log('🧪 Simple Authentication Test\n');

    try {
        // Test 1: Can we reach the login page?
        console.log('📄 Test 1: Accessing login page');
        await page.goto(`${baseUrl}/auth/login`, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        const loginPageTitle = await page.title();
        console.log(`✅ Login page accessible - Title: ${loginPageTitle}`);

        // Test 2: Try registration
        console.log('\n📝 Test 2: Registration');
        await page.goto(`${baseUrl}/auth/register`, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Fill form manually to debug
        await page.type('input[name="username"]', testUser.username);
        await page.type('input[name="password"]', testUser.password);
        await page.type('input[name="confirm_password"]', testUser.password);

        // Check the acknowledge checkbox
        await page.click('input[name="acknowledge"]');

        // Submit
        console.log('Submitting registration form...');
        await Promise.all([
            page.waitForNavigation({ waitUntil: 'networkidle2' }),
            page.click('button[type="submit"]')
        ]);

        const afterRegUrl = page.url();
        console.log(`After registration URL: ${afterRegUrl}`);

        // Check for success
        if (afterRegUrl.includes('/auth/register')) {
            // Still on register page - check for errors
            const errorText = await page.$eval('.alert', el => el.textContent).catch(() => 'No alerts');
            console.log(`Registration might have failed. Alert text: ${errorText}`);
        } else {
            console.log('✅ Registration completed - redirected away from register page');
        }

        // Test 3: Check if we're logged in
        console.log('\n🔍 Test 3: Login status check');
        const isLoggedIn = await authHelper.isLoggedIn();
        console.log(`Logged in status: ${isLoggedIn}`);

        // Test 4: Try to access settings (protected page)
        console.log('\n🔒 Test 4: Accessing protected page');
        await page.goto(`${baseUrl}/settings/`, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        const settingsUrl = page.url();
        if (settingsUrl.includes('/settings')) {
            console.log('✅ Successfully accessed settings page - user is authenticated');
        } else {
            console.log(`❌ Redirected to: ${settingsUrl}`);
        }

        // Take final screenshot
        await page.screenshot({ path: './screenshots/simple_auth_final.png' });
        console.log('\n📸 Screenshot saved to ./screenshots/simple_auth_final.png');

    } catch (error) {
        console.error('\n❌ Test error:', error.message);
        await page.screenshot({ path: './screenshots/simple_auth_error.png' });
    }

    await browser.close();
    console.log('\n✅ Test completed');
}

// Run the test
testSimpleAuth().catch(console.error);
