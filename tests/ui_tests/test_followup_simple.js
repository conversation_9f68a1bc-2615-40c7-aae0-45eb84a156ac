#!/usr/bin/env node

/**
 * Simple test for follow-up research using existing testuser
 */

const puppeteer = require('puppeteer');

const BASE_URL = 'http://127.0.0.1:5000';
const PARENT_RESEARCH_ID = '6fe358f9-82de-43b1-8cae-0b673e61fe4d';
const TEST_USER = 'testuser';
const TEST_PASSWORD = 'testpass';

async function testFollowupResearch() {
    let browser;
    let page;

    try {
        console.log('🚀 Starting Simple Follow-up Research Test');
        console.log(`   Parent Research: ${PARENT_RESEARCH_ID}`);
        console.log(`   User: ${TEST_USER}`);

        // Launch browser
        browser = await puppeteer.launch({
            headless: process.env.HEADLESS === 'true',
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        page = await browser.newPage();

        // Set viewport
        await page.setViewport({ width: 1280, height: 800 });

        // Enable console logging
        page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log('Browser console error:', msg.text());
            }
        });

        // Step 1: Login
        console.log('\n1️⃣  Logging in as testuser...');
        await page.goto(`${BASE_URL}/auth/login`, { waitUntil: 'networkidle2' });

        await page.type('input[name="username"]', TEST_USER);
        await page.type('input[name="password"]', TEST_PASSWORD);

        await Promise.all([
            page.waitForNavigation({ waitUntil: 'networkidle2' }),
            page.click('button[type="submit"]')
        ]);

        console.log('✅ Logged in successfully');

        // Step 2: Navigate to parent research
        console.log(`\n2️⃣  Navigating to parent research...`);
        await page.goto(`${BASE_URL}/results/${PARENT_RESEARCH_ID}`, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Wait for page to load
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Step 3: Test direct API call for follow-up
        console.log('\n3️⃣  Testing follow-up API directly...');

        const apiResponse = await page.evaluate(async (parentId) => {
            try {
                const response = await fetch('/api/followup/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include',
                    body: JSON.stringify({
                        parent_research_id: parentId,
                        question: 'What are the key points from this research?'
                    })
                });

                const data = await response.json();
                return {
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return {
                    error: error.message
                };
            }
        }, PARENT_RESEARCH_ID);

        console.log('API Response:', JSON.stringify(apiResponse, null, 2));

        if (apiResponse.status === 200 && apiResponse.data.success) {
            console.log(`\n✅ Follow-up research started successfully!`);
            console.log(`   Research ID: ${apiResponse.data.research_id}`);

            // Navigate to the new research
            const newResearchId = apiResponse.data.research_id;
            console.log(`\n4️⃣  Navigating to follow-up research: ${newResearchId}`);

            await page.goto(`${BASE_URL}/results/${newResearchId}`, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            // Wait a bit to see the page
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Check if we're on the results page
            const currentUrl = page.url();
            if (currentUrl.includes(`/results/${newResearchId}`) || currentUrl.includes(`/progress/${newResearchId}`)) {
                console.log('✅ Successfully navigated to follow-up research page');
            }

        } else {
            console.log(`\n❌ Follow-up failed:`);
            console.log(`   Status: ${apiResponse.status}`);
            console.log(`   Error: ${apiResponse.data?.error || 'Unknown error'}`);

            // Try to get more error details from server logs
            const serverLogs = await page.evaluate(() => {
                return document.body.innerText;
            });

            if (serverLogs.includes('Error') || serverLogs.includes('error')) {
                console.log('\nPage content (may contain error details):');
                console.log(serverLogs.substring(0, 500));
            }
        }

        console.log('\n✅ Test completed!');

    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        console.error(error.stack);

        // Take screenshot on error
        if (page) {
            await page.screenshot({
                path: '/tmp/followup_test_error.png',
                fullPage: true
            });
            console.log('Screenshot saved to /tmp/followup_test_error.png');
        }

        process.exit(1);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

// Run the test
testFollowupResearch().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
});
