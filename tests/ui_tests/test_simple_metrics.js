const puppeteer = require('puppeteer');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testMetricsDashboard() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();

    console.log('🚀 Testing metrics dashboard...');

    try {
        // Navigate to login page
        await page.goto('http://127.0.0.1:5000/auth/login', { waitUntil: 'networkidle0' });

        // Login with test user
        const username = 'test_' + Date.now();
        const password = 'testpass123';

        // Register first
        console.log('📝 Registering user:', username);
        await page.click('a[href="/auth/register"]');
        await page.waitForSelector('#username');
        await page.type('#username', username);
        await page.type('#password', password);
        await page.type('#confirm_password', password);
        await page.click('button[type="submit"]');

        // Wait for redirect to home
        await page.waitForNavigation({ waitUntil: 'networkidle0' });
        console.log('✅ Registration successful');

        // Navigate to metrics page
        console.log('📊 Navigating to metrics dashboard...');
        await page.goto('http://127.0.0.1:5000/metrics/', { waitUntil: 'networkidle0' });

        // Check if metrics page loaded
        await page.waitForSelector('.metrics-container', { timeout: 5000 });
        console.log('✅ Metrics container found');

        // Check for error messages
        const errorElement = await page.$('.error-message, .alert-danger');
        if (errorElement) {
            const errorText = await page.evaluate(el => el.textContent, errorElement);
            console.log('❌ Error on page:', errorText);
        } else {
            console.log('✅ No error messages found');
        }

        // Check for metrics data
        const hasCharts = await page.$('.chart-container, #token-usage-chart, canvas');
        if (hasCharts) {
            console.log('✅ Chart elements found');
        } else {
            console.log('⚠️  No chart elements found (expected for new user)');
        }


        // Try to access metrics API directly
        console.log('🔍 Testing metrics API...');
        const apiResponse = await page.evaluate(async () => {
            try {
                const response = await fetch('/metrics/api/data?period=30d&research_mode=all');
                return {
                    status: response.status,
                    ok: response.ok,
                    data: await response.json()
                };
            } catch (error) {
                return { error: error.message };
            }
        });

        console.log('API Response:', JSON.stringify(apiResponse, null, 2));

        console.log('✅ Metrics dashboard test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        await browser.close();
    }
}

testMetricsDashboard().catch(console.error);
