#!/usr/bin/env python3
"""
Trace the exact error when accessing history for UUID user
"""

import os
from pathlib import Path

os.environ["LDR_ALLOW_UNENCRYPTED"] = "true"

import sys

sys.path.insert(
    0,
    str(Path(__file__).parent.parent.parent.resolve()),
)

from src.local_deep_research.web.app_factory import create_app


def test_history_error():
    """Test to trace the exact error"""

    # Create the Flask app
    app, socket_service = create_app()

    with app.test_client() as client:
        # <PERSON><PERSON> as user with UUID tables
        login_resp = client.get("/auth/login")
        csrf_token = None
        for line in login_resp.data.decode().split("\n"):
            if "csrf_token" in line and "value=" in line:
                start = line.find('value="') + 7
                end = line.find('"', start)
                csrf_token = line[start:end]
                break

        login_data = {
            "username": "test_uuid_fresh_bew2zgek",
            "password": "T3st!Secure#2024$LDR",
            "csrf_token": csrf_token,
        }

        resp = client.post(
            "/auth/login", data=login_data, follow_redirects=True
        )
        print(f"Login status: {resp.status_code}")

        # Try to access history
        print("\nAccessing /api/history...")
        hist_resp = client.get("/api/history")
        print(f"Status: {hist_resp.status_code}")

        if hist_resp.status_code != 200:
            print(f"Error response: {hist_resp.data.decode()}")

        # Also try the /history/api endpoint
        print("\nAccessing /history/api...")
        hist2_resp = client.get("/history/api")
        print(f"Status: {hist2_resp.status_code}")

        if hist2_resp.status_code == 200:
            import json

            data = json.loads(hist2_resp.data)
            print(f"Success! Got {len(data.get('items', []))} items")


if __name__ == "__main__":
    print("Tracing History Error")
    print("=" * 60)

    test_history_error()
