const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');
const { setupDefaultModel } = require('./model_helper');

(async () => {
    let browser;

    try {
        browser = await puppeteer.launch(getPuppeteerLaunchOptions());

        const page = await browser.newPage();

        // Set timeout
        page.setDefaultTimeout(30000);

        // Log console messages only if verbose
        if (process.env.VERBOSE) {
            page.on('console', msg => {
                console.log(`[${msg.type()}] ${msg.text()}`);
            });
        }

        // Register and login
        const auth = new AuthHelper(page);
        const username = 'simple_' + Date.now();
        const password = 'TestPass123!';

        console.log('🔐 Creating user:', username);
        await auth.register(username, password);

        console.log('\n🏠 Navigating to home page...');
        await page.goto('http://127.0.0.1:5000/', { waitUntil: 'networkidle2' });

        // Set up model configuration
        console.log('🔧 Configuring model...');
        const modelConfigured = await setupDefaultModel(page);
        if (!modelConfigured) {
            throw new Error('Failed to configure model');
        }

        // Wait for and fill the query field
        await page.waitForSelector('#query', { timeout: 10000 });
        await page.type('#query', 'What is Node.js?');
        console.log('✅ Query entered');

        // Check if model and search engine are pre-selected
        const formValues = await page.evaluate(() => {
            return {
                query: document.querySelector('#query')?.value,
                model: document.querySelector('#model')?.value || document.querySelector('input[name="model"]')?.value,
                searchEngine: document.querySelector('#search_engine')?.value || document.querySelector('input[name="search_engine"]')?.value
            };
        });
        console.log('📋 Form values:', formValues);

        console.log('\n🚀 Submitting research...');

        // Submit the form
        const submitButton = await page.$('button[type="submit"]');
        if (submitButton) {
            await Promise.all([
                page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 }),
                submitButton.click()
            ]);
        } else {
            // Try alternative submit method
            await page.keyboard.press('Enter');
            await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 });
        }

        // Check where we ended up
        const url = page.url();
        console.log('📍 Current URL:', url);

        if (url.includes('/research/') || url.includes('/progress/')) {
            console.log('✅ Research submitted successfully!');

            // Wait a bit to see if research starts
            await new Promise(resolve => setTimeout(resolve, 5000));

            // Check if we have any progress indicators
            const progressInfo = await page.evaluate(() => {
                const status = document.querySelector('.status, .research-status, [class*="status"]')?.textContent;
                const progress = document.querySelector('.progress, .progress-bar, [class*="progress"]')?.textContent;
                const title = document.title;
                return { status, progress, title };
            });

            console.log('📊 Progress info:', progressInfo);

            // Don't wait for full completion - just verify research started
            if (progressInfo.status || progressInfo.progress || url.includes('/research/')) {
                console.log('✅ Research is processing');
                console.log('\n🎉 Simple research test passed!');
                process.exit(0);
            } else {
                throw new Error('Research did not start properly');
            }
        } else {
            // Check for error messages
            const errorMessage = await page.evaluate(() => {
                const alert = document.querySelector('.alert-danger, .error-message');
                return alert ? alert.textContent : null;
            });

            if (errorMessage) {
                throw new Error(`Research submission failed: ${errorMessage}`);
            } else {
                throw new Error(`Unexpected redirect to: ${url}`);
            }
        }

    } catch (error) {
        console.error('❌ Test failed:', error.message);

        // Take screenshot on error
        if (browser) {
            const page = (await browser.pages())[0];
            if (page) {
                try {
                    const fs = require('fs');
                    const path = require('path');
                    const screenshotDir = path.join(__dirname, 'screenshots');

                    if (!fs.existsSync(screenshotDir)) {
                        fs.mkdirSync(screenshotDir, { recursive: true });
                    }

                    await page.screenshot({
                        path: path.join(screenshotDir, `simple_research_error_${Date.now()}.png`),
                        fullPage: true
                    });
                    console.log('📸 Error screenshot saved');
                } catch (screenshotError) {
                    console.error('Failed to save screenshot:', screenshotError.message);
                }
            }
        }

        process.exit(1);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
})();
