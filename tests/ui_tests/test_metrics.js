// Simple test to simulate browser behavior
const { exec } = require('child_process');

// First, let's just test if we can access the page and get the HTML
console.log('Testing metrics page access...');

exec('curl -s http://127.0.0.1:5000/metrics/', (error, stdout, stderr) => {
    if (error) {
        console.error('Error accessing page:', error);
        return;
    }

    // Check if the JavaScript is present in the HTML
    if (stdout.includes('=== METRICS SCRIPT STARTED ===')) {
        console.log('✓ JavaScript debug code found in HTML');
    } else {
        console.log('✗ JavaScript debug code NOT found in HTML');
    }

    if (stdout.includes('loadMetrics()')) {
        console.log('✓ loadMetrics() call found in HTML');
    } else {
        console.log('✗ loadMetrics() call NOT found in HTML');
    }

    // Test the API endpoints that the JavaScript would call
    console.log('\nTesting API endpoints...');

    exec('curl -s "http://127.0.0.1:5000/metrics/api/metrics?period=30d&mode=all"', (error, stdout, stderr) => {
        if (error) {
            console.error('Basic API error:', error);
            return;
        }

        try {
            const data = JSON.parse(stdout);
            if (data.status === 'success') {
                console.log('✓ Basic metrics API working');
                console.log('  - Total tokens:', data.metrics.total_tokens);
                console.log('  - Total researches:', data.metrics.total_researches);
                console.log('  - Has by_model data:', data.metrics.by_model ? data.metrics.by_model.length > 0 : false);
            } else {
                console.log('✗ Basic API returned error status');
            }
        } catch (e) {
            console.log('✗ Basic API returned invalid JSON');
        }
    });

    exec('curl -s "http://127.0.0.1:5000/metrics/api/metrics/enhanced?period=30d&mode=all"', (error, stdout, stderr) => {
        if (error) {
            console.error('Enhanced API error:', error);
            return;
        }

        try {
            const data = JSON.parse(stdout);
            if (data.status === 'success') {
                console.log('✓ Enhanced metrics API working');
                console.log('  - Has search_engine_stats:', !!data.metrics.search_engine_stats);
                console.log('  - Has phase_breakdown:', !!data.metrics.phase_breakdown);
                console.log('  - Has time_series_data:', !!data.metrics.time_series_data);
            } else {
                console.log('✗ Enhanced API returned error status');
            }
        } catch (e) {
            console.log('✗ Enhanced API returned invalid JSON');
        }
    });
});
