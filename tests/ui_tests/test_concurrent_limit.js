const puppeteer = require('puppeteer');
const { browserConfig } = require('./browser_config');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function submitResearchBatch(page, queries) {
    // Submit all researches as quickly as possible
    const results = [];

    for (let i = 0; i < queries.length; i++) {
        const query = queries[i];
        console.log(`\nSubmitting research ${i + 1}: "${query}"`);

        // Go to home page
        await page.goto('http://127.0.0.1:5000/', { waitUntil: 'domcontentloaded' });

        // Fill and submit form quickly
        await page.waitForSelector('#query', { timeout: 5000 });
        await page.type('#query', query);

        // Select the model that's actually installed
        try {
            // First try to find the model dropdown by different possible selectors
            const modelSelector = await page.evaluateHandle(() => {
                // Try different selectors
                return document.querySelector('#model-select') ||
                       document.querySelector('#model') ||
                       document.querySelector('[name="model"]') ||
                       document.querySelector('select[name="model"]');
            });

            if (modelSelector && modelSelector.asElement()) {
                console.log('Found model selector, selecting llama3.1:8b');
                await page.evaluate(() => {
                    const selector = document.querySelector('#model-select') ||
                                   document.querySelector('#model') ||
                                   document.querySelector('[name="model"]') ||
                                   document.querySelector('select[name="model"]');
                    if (selector) {
                        selector.value = 'llama3.1:8b';
                        // Trigger change event
                        selector.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                });
            } else {
                console.log('No model selector found');
            }
        } catch (e) {
            console.log('Error selecting model:', e.message);
        }

        // Intercept the response
        const responsePromise = new Promise((resolve) => {
            const handler = async (response) => {
                if (response.url().includes('/api/start_research') && response.status() === 200) {
                    try {
                        const data = await response.json();
                        page.off('response', handler);
                        resolve(data);
                    } catch (e) {
                        resolve(null);
                    }
                }
            };
            page.on('response', handler);

            // Timeout after 3 seconds
            setTimeout(() => {
                page.off('response', handler);
                resolve({ timeout: true });
            }, 3000);
        });

        // Submit
        await page.click('button[type="submit"]');

        // Wait for response
        const response = await responsePromise;

        if (response && !response.timeout) {
            results.push({
                query,
                ...response
            });
            console.log(`Response: ${JSON.stringify(response)}`);
        } else {
            console.log(`Failed to get response for research ${i + 1}`);
        }

        // Very short delay between submissions
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    return results;
}

(async () => {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();

    try {
        // Create a fresh user
        const auth = new AuthHelper(page);
        const username = 'conctest_' + Date.now();
        const password = 'T3st!Secure#2024$LDR';

        console.log('1. Creating user:', username);
        await auth.register(username, password);

        const isLoggedIn = await auth.isLoggedIn();
        if (!isLoggedIn) {
            await auth.login(username, password);
        }

        console.log('\n2. Testing concurrent research limit...\n');

        // Submit 5 researches quickly
        const queries = [
            'First research about AI',
            'Second research about Python',
            'Third research about databases',
            'Fourth research should be queued',
            'Fifth research should be queued'
        ];

        const results = await submitResearchBatch(page, queries);

        // Analyze results
        console.log('\n3. Results Summary:');
        console.log('===================');

        let started = 0;
        let queued = 0;

        results.forEach((result, index) => {
            if (result.status === 'success') {
                started++;
                console.log(`Research ${index + 1}: STARTED (ID: ${result.research_id})`);
            } else if (result.status === 'queued') {
                queued++;
                console.log(`Research ${index + 1}: QUEUED (ID: ${result.research_id}, Position: ${result.queue_position})`);
            }
        });

        console.log(`\nTotal Started: ${started}`);
        console.log(`Total Queued: ${queued}`);

        if (started === 3 && queued === 2) {
            console.log('\n✅ SUCCESS: Concurrent limit working correctly!');
        } else {
            console.log('\n❌ FAILURE: Expected 3 started and 2 queued');
        }

    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        await browser.close();
    }
})();
