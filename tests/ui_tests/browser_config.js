/**
 * Common browser configuration for Puppeteer tests
 * Prevents bookmark prompts and other UI interruptions
 */

const BROWSER_CONFIG = {
    headless: process.env.CI ? true : false,
    args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-extensions',
        '--disable-default-apps',
        '--disable-sync',
        '--disable-bookmarks-ui',
        '--disable-features=TranslateUI',
        '--disable-popup-blocking',
        '--disable-bookmark-prompt-on-load',
        '--disable-save-password-bubble',
        '--disable-prompt-on-repost',
        '--disable-background-networking',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-breakpad',
        '--disable-client-side-phishing-detection',
        '--disable-component-extensions-with-background-pages',
        '--disable-features=site-per-process',
        '--disable-hang-monitor',
        '--disable-ipc-flooding-protection',
        '--disable-renderer-backgrounding'
    ],
    ignoreDefaultArgs: ['--enable-automation'],
    defaultViewport: {
        width: 1280,
        height: 800
    }
};

/**
 * Page initialization options
 */
const PAGE_CONFIG = {
    waitUntil: 'networkidle2',
    timeout: 5000  // Reduced from 30000 to 5000ms
};

module.exports = {
    BROWSER_CONFIG,
    PAGE_CONFIG
};
