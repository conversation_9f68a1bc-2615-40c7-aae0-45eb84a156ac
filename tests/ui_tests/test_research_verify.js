/**
 * Research Verification Test
 * Checks if research actually starts and appears in history
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testResearchVerify() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    console.log('🔬 Research Verification Test\n');

    try {
        // Login
        console.log('🔐 Logging in...');
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in\n');

        // First, check history to see existing researches
        console.log('📋 Checking existing history...');
        await page.goto(`${baseUrl}/history/`, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        const historyBefore = await page.evaluate(() => {
            const items = document.querySelectorAll('.history-item, .research-item, .ldr-card');
            return items.length;
        });
        console.log(`Found ${historyBefore} existing research items\n`);

        // Go back to home
        await page.goto(baseUrl, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Try to start a research using the API directly
        console.log('🚀 Starting research via API...');

        const apiResult = await page.evaluate(async () => {
            try {
                const response = await fetch('/api/start_research', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        query: `Test research at ${new Date().toISOString()}`,
                        mode: 'quick',
                        model_provider: 'OLLAMA',
                        model: 'llama3.2:3b',
                        custom_endpoint: '',
                        search_engine: 'searxng',
                        iterations: 1,
                        questions_per_iteration: 1,
                        strategy: 'source-based'
                    })
                });

                const data = await response.json();
                return {
                    success: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });

        console.log('API Result:', apiResult);

        if (apiResult.success && apiResult.data.research_id) {
            console.log(`✅ Research started with ID: ${apiResult.data.research_id}\n`);

            // Try to navigate to the research progress page
            const researchUrl = `${baseUrl}/research/${apiResult.data.research_id}`;
            console.log(`📍 Navigating to: ${researchUrl}`);

            await page.goto(researchUrl, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            // Check what's on the progress page
            const progressInfo = await page.evaluate(() => {
                return {
                    url: window.location.href,
                    title: document.title,
                    hasProgress: !!document.querySelector('.progress, #progress'),
                    hasLogs: !!document.querySelector('.log-entry, #logs'),
                    hasError: !!document.querySelector('.alert-danger'),
                    bodyText: document.body.innerText.substring(0, 200)
                };
            });

            console.log('\nProgress page info:');
            console.log(`  URL: ${progressInfo.url}`);
            console.log(`  Title: ${progressInfo.title}`);
            console.log(`  Has progress indicator: ${progressInfo.hasProgress}`);
            console.log(`  Has logs: ${progressInfo.hasLogs}`);
            console.log(`  Has error: ${progressInfo.hasError}`);

            // Take screenshot
            await page.screenshot({ path: './screenshots/research_progress.png' });
            console.log('\n📸 Progress page screenshot saved');

            // Wait a bit for research to process
            console.log('\n⏳ Waiting 10 seconds for research to process...');
            await new Promise(resolve => setTimeout(resolve, 10000));

            // Refresh to see updates
            await page.reload({ waitUntil: 'networkidle2' });

            // Check status again
            const statusUpdate = await page.evaluate(() => {
                const status = document.querySelector('.status, .research-status');
                const logs = document.querySelectorAll('.log-entry, .log-item');
                return {
                    status: status ? status.textContent : 'not found',
                    logCount: logs.length
                };
            });

            console.log('\nStatus update:');
            console.log(`  Status: ${statusUpdate.status}`);
            console.log(`  Log entries: ${statusUpdate.logCount}`);

        } else {
            console.log('❌ Research failed to start via API');
        }

        // Check history again
        console.log('\n📋 Checking history after research...');
        await page.goto(`${baseUrl}/history/`, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        const historyAfter = await page.evaluate(() => {
            const items = document.querySelectorAll('.history-item, .research-item, .ldr-card');
            const latestItem = items[0];
            if (latestItem) {
                const text = latestItem.textContent;
                return {
                    count: items.length,
                    latestText: text.substring(0, 200)
                };
            }
            return { count: items.length };
        });

        console.log(`History items now: ${historyAfter.count}`);
        if (historyAfter.count > historyBefore) {
            console.log('✅ New research appeared in history!');
            console.log(`Latest item preview: ${historyAfter.latestText}`);
        } else {
            console.log('⚠️  No new items in history');
        }

        // Take final screenshot
        await page.screenshot({ path: './screenshots/research_history.png' });

    } catch (error) {
        console.error('\n❌ Test error:', error.message);
        await page.screenshot({ path: './screenshots/research_verify_error.png' });
    }

    console.log('\n⏸️  Keeping browser open for 15 seconds...');
    await new Promise(resolve => setTimeout(resolve, 15000));

    await browser.close();
    console.log('🏁 Test completed');
}

// Run the test
testResearchVerify().catch(console.error);
