/**
 * Check Available Models
 * Quick script to see what models are available
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function checkAvailableModels() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    console.log('🔍 Checking Available Models\n');

    try {
        // Login
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in\n');

        // Check available models via API
        const models = await page.evaluate(async () => {
            try {
                const response = await fetch('/settings/api/available-models');
                if (!response.ok) {
                    return { error: `HTTP ${response.status}: ${response.statusText}` };
                }
                return await response.json();
            } catch (error) {
                return { error: error.message };
            }
        });

        if (models.error) {
            console.log('❌ Error fetching models:', models.error);
        } else {
            console.log('📋 Available Models:');
            console.log(JSON.stringify(models, null, 2));
        }

        // Also check what's in the model dropdown on the form
        await page.goto(baseUrl, { waitUntil: 'networkidle2' });

        const formModels = await page.evaluate(() => {
            const select = document.querySelector('#model, select[name="model"]');
            if (!select) return null;

            const options = Array.from(select.options).map(opt => ({
                value: opt.value,
                text: opt.text,
                selected: opt.selected
            }));

            return {
                currentValue: select.value,
                options: options
            };
        });

        if (formModels) {
            console.log('\n📝 Form Model Options:');
            console.log(`Current value: ${formModels.currentValue}`);
            console.log('Options:');
            formModels.options.forEach(opt => {
                console.log(`  ${opt.selected ? '→' : ' '} ${opt.value} (${opt.text})`);
            });
        }

    } catch (error) {
        console.error('\n❌ Error:', error.message);
    }

    await browser.close();
    console.log('\n✅ Done');
}

// Run the check
checkAvailableModels().catch(console.error);
