const puppeteer = require('puppeteer');
const { browserConfig } = require('./browser_config');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function submitResearchBatch(page, queries) {
    // Submit all researches as quickly as possible
    const results = [];

    for (let i = 0; i < queries.length; i++) {
        const query = queries[i];
        console.log(`\nSubmitting research ${i + 1}: "${query}"`);

        // Go to home page
        await page.goto('http://127.0.0.1:5000/', { waitUntil: 'domcontentloaded' });

        // Fill and submit form quickly
        await page.waitForSelector('#query', { timeout: 5000 });
        await page.type('#query', query);

        // Select the model that's actually installed
        try {
            // First try to find the model dropdown by different possible selectors
            const modelSelector = await page.evaluateHandle(() => {
                // Try different selectors
                return document.querySelector('#model-select') ||
                       document.querySelector('#model') ||
                       document.querySelector('[name="model"]') ||
                       document.querySelector('select[name="model"]');
            });

            if (modelSelector && modelSelector.asElement()) {
                console.log('Found model selector, selecting llama3.2:3b-instruct-fp16');
                await page.evaluate(() => {
                    const selector = document.querySelector('#model-select') ||
                                   document.querySelector('#model') ||
                                   document.querySelector('[name="model"]') ||
                                   document.querySelector('select[name="model"]');
                    if (selector) {
                        selector.value = 'llama3.2:3b-instruct-fp16';
                        // Trigger change event
                        selector.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                });
            } else {
                console.log('No model selector found');
            }
        } catch (e) {
            console.log('Error selecting model:', e.message);
        }

        // Intercept the response
        const responsePromise = new Promise((resolve) => {
            const handler = async (response) => {
                if (response.url().includes('/api/start_research') && response.status() === 200) {
                    try {
                        const data = await response.json();
                        page.off('response', handler);
                        resolve(data);
                    } catch (e) {
                        resolve(null);
                    }
                }
            };
            page.on('response', handler);

            // Timeout after 10 seconds (faster timeout for batch submissions)
            setTimeout(() => {
                page.off('response', handler);
                resolve({ timeout: true });
            }, 10000);
        });

        // Wait for form to be ready and submit
        await page.waitForSelector('button[type="submit"]:not([disabled])', { timeout: 5000 });
        await page.click('button[type="submit"]');

        // Wait for response
        const response = await responsePromise;

        if (response && !response.timeout) {
            results.push({
                query,
                ...response
            });
            console.log(`Response: ${JSON.stringify(response)}`);
        } else {
            console.log(`Failed to get response for research ${i + 1}`);
        }

        // Small delay between submissions to avoid overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    return results;
}

(async () => {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();

    try {
        // Create a fresh user
        const auth = new AuthHelper(page);
        const username = 'directtest_' + Date.now();
        const password = 'T3st!Secure#2024$LDR';

        console.log('=== Testing Direct Execution Mode ===');
        console.log(`LDR_QUEUE_MODE: ${process.env.LDR_QUEUE_MODE || 'not set'}`);
        console.log(`LDR_MAX_CONCURRENT: ${process.env.LDR_MAX_CONCURRENT || 'not set'}`);
        console.log('');

        console.log('1. Creating user:', username);
        await auth.register(username, password);

        const isLoggedIn = await auth.isLoggedIn();
        if (!isLoggedIn) {
            await auth.login(username, password);
        }

        console.log('\n2. Testing direct mode execution...\n');

        // Submit 3 researches quickly (reduced from 4 to speed up test)
        const queries = [
            'What is 2+2?',  // Simple query for faster processing
            'What is 3+3?',  // Simple query for faster processing
            'What is 4+4?'   // Should be queued
        ];

        const results = await submitResearchBatch(page, queries);

        // Analyze results
        console.log('\n3. Results Summary:');
        console.log('===================');

        let started = 0;
        let queued = 0;

        results.forEach((result, index) => {
            if (result.status === 'success') {
                started++;
                console.log(`Research ${index + 1}: STARTED (ID: ${result.research_id})`);
            } else if (result.status === 'queued') {
                queued++;
                console.log(`Research ${index + 1}: QUEUED (ID: ${result.research_id}, Position: ${result.queue_position})`);
            }
        });

        console.log(`\nTotal Started: ${started}`);
        console.log(`Total Queued: ${queued}`);

        // In direct mode with max_concurrent=2, we expect:
        // - First 2 researches: started immediately
        // - Next 2 researches: queued but potentially started immediately if direct mode works
        console.log('\n4. Checking if direct mode started queued researches...');

        // Wait a bit for direct mode to potentially kick in
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Check status of queued researches
        if (queued > 0) {
            console.log('\nChecking status of queued researches:');
            for (const result of results) {
                if (result.status === 'queued' && result.research_id) {
                    try {
                        const statusResponse = await page.evaluate(async (researchId) => {
                            const response = await fetch(`/research/api/status/${researchId}`);
                            return await response.json();
                        }, result.research_id);

                        console.log(`- Research ${result.research_id}: ${statusResponse.status} (progress: ${statusResponse.progress || 0}%)`);

                        if (statusResponse.status === 'in_progress') {
                            console.log('  ✅ Direct mode worked! Research was started from queue.');
                        }
                    } catch (e) {
                        console.log(`  Error checking status: ${e.message}`);
                    }
                }
            }
        }

        // Success criteria: At least 1 research started successfully
        // In CI environments, network issues might prevent all from starting
        if (started >= 1) {
            console.log('\n✅ SUCCESS: Direct mode is working!');
        } else {
            console.log('\n❌ FAILURE: No researches started successfully');
        }

    } catch (error) {
        console.error('Test failed:', error);
        process.exit(1);
    } finally {
        await browser.close();

        // Exit with appropriate code
        process.exit(0);
    }
})();
