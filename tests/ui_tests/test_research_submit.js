/**
 * Research Submit Test
 * Tests the actual research submission process
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const fs = require('fs');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');


async function testResearchSubmit() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    // Enable request interception to monitor network
    await page.setRequestInterception(true);

    page.on('request', request => {
        if (request.url().includes('research') || request.method() === 'POST') {
            console.log(`📡 ${request.method()} ${request.url()}`);
            if (request.method() === 'POST') {
                console.log('   POST data:', request.postData());
            }
        }
        request.continue();
    });

    page.on('response', response => {
        if (response.url().includes('research')) {
            console.log(`📨 Response: ${response.status()} ${response.url()}`);
        }
    });

    console.log('🧪 Research Submit Test\n');

    try {
        // Ensure authentication
        await authHelper.ensureAuthenticated();
        console.log('✅ Authenticated\n');

        // Navigate to home
        await page.goto(baseUrl, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Double-check we're logged in
        const isLoggedIn = await authHelper.isLoggedIn();
        if (!isLoggedIn) {
            console.log('⚠️  Not logged in, attempting login again...');
            await authHelper.login();
        }

        // Fill the form
        console.log('📝 Filling research form...');

        // Query
        await page.type('#query', 'What are the latest developments in quantum computing?');
        console.log('✅ Entered query');

        // Select quick mode if available
        const quickMode = await page.$('#mode-quick');
        if (quickMode) {
            await page.click('#mode-quick');
            console.log('✅ Selected quick mode');
        }

        // Check form submit handler
        const formHandler = await page.evaluate(() => {
            const form = document.getElementById('research-form');
            if (form) {
                return {
                    hasOnSubmit: !!form.onsubmit,
                    action: form.action,
                    method: form.method
                };
            }
            return null;
        });

        console.log('\nForm handler info:', formHandler);

        // Find submit button - specifically the research submit button
        let submitButton = await page.$('#start-research-btn');
        if (!submitButton) {
            // Fallback to form submit button
            submitButton = await page.$('#research-form button[type="submit"]');
            if (!submitButton) {
                throw new Error('Submit button not found');
            }
            console.log('Using fallback submit button selector');
        }

        console.log('\n🚀 Clicking submit button...\n');

        // Set up promises to catch navigation or form submission
        const navigationPromise = page.waitForNavigation({
            waitUntil: 'networkidle2',
            timeout: 15000
        }).then(() => 'navigation').catch(() => null);

        const responsePromise = page.waitForResponse(
            response => response.url().includes('research') || response.url().includes('start'),
            { timeout: 15000 }
        ).then(() => 'response').catch(() => null);

        // Click submit
        await submitButton.click();
        console.log('✅ Clicked submit button');

        // Wait for either navigation or response
        const result = await Promise.race([navigationPromise, responsePromise]);
        console.log(`\nResult: ${result || 'timeout'}`);

        // Wait a bit for any updates
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Check current state
        const currentUrl = page.url();
        console.log(`\n📍 Current URL: ${currentUrl}`);

        // Check for any messages or errors
        const alerts = await page.$$('.alert');
        if (alerts.length > 0) {
            console.log('\n⚠️  Alerts found:');
            for (const alert of alerts) {
                const text = await page.evaluate(el => el.textContent, alert);
                console.log(`   - ${text.trim()}`);
            }
        }


    } catch (error) {
        console.error('\n❌ Error:', error.message);
    }

    console.log('\n⏸️  Keeping browser open for 10 seconds...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    await browser.close();
    console.log('🏁 Test ended');
}

testResearchSubmit().catch(console.error);
