const puppeteer = require('puppeteer');
const { browserConfig } = require('./browser_config');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function submitResearchAjax(page, query, index) {
    console.log(`\n=== Submitting Research ${index}: "${query}" ===`);

    // Navigate to home
    await page.goto('http://127.0.0.1:5000/', { waitUntil: 'networkidle2' });

    // Override form submission to use AJAX
    await page.evaluate(() => {
        const form = document.querySelector('#research-form');
        if (form) {
            form.onsubmit = async (e) => {
                e.preventDefault();

                const formData = new FormData(form);
                const data = {
                    query: formData.get('query'),
                    mode: formData.get('mode') || 'quick',
                    model_provider: formData.get('model_provider') || 'OLLAMA',
                    model: formData.get('model') || 'llama3.1:8b',
                    search_engine: formData.get('search_engine') || 'searxng',
                    max_results: formData.get('max_results') || 10,
                    iterations: formData.get('iterations') || 3,
                    questions_per_iteration: formData.get('questions_per_iteration') || 3,
                    strategy: formData.get('strategy') || 'source-based'
                };

                console.log('Sending data:', JSON.stringify(data));

                try {
                    const response = await fetch('/api/start_research', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(data)
                    });

                    const result = await response.json();

                    // Store result for retrieval
                    window.lastSubmissionResult = result;

                    // If successful, redirect like normal
                    if (result.status === 'success' && result.research_id) {
                        window.location.href = `/progress/${result.research_id}`;
                    }
                } catch (error) {
                    window.lastSubmissionResult = { error: error.message };
                }
            };
        }
    });

    // Fill the form
    await page.type('#query', query);

    // Select model
    await page.evaluate(() => {
        const modelSelect = document.querySelector('#model') ||
                           document.querySelector('[name="model"]');
        if (modelSelect) {
            modelSelect.value = 'llama3.1:8b';
        }
    });

    // Submit the form
    await page.click('button[type="submit"]');

    // Wait a bit for AJAX to complete
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Get the result
    const result = await page.evaluate(() => window.lastSubmissionResult);

    console.log('Submission result:', result);

    // Take screenshot
    await page.screenshot({ path: `/tmp/ajax_submit_${index}.png` });

    return result;
}

(async () => {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();

    // Log errors
    page.on('console', msg => {
        if (msg.type() === 'error') {
            console.log('PAGE ERROR:', msg.text());
        }
    });

    try {
        // Register and login
        const auth = new AuthHelper(page);
        const username = 'ajax_' + Date.now();
        const password = 'T3st!Secure#2024$LDR';  // pragma: allowlist secret

        console.log('Creating user:', username);
        await auth.register(username, password);

        console.log('\nTesting concurrent research limit with AJAX...\n');

        const results = [];

        // Submit 5 researches
        for (let i = 1; i <= 5; i++) {
            const result = await submitResearchAjax(page, `Research ${i} test`, i);

            if (result) {
                results.push(result);

                if (result.status === 'queued') {
                    console.log(`✅ Research ${i}: QUEUED (position ${result.queue_position})`);
                } else if (result.status === 'success') {
                    console.log(`✅ Research ${i}: STARTED (ID: ${result.research_id})`);
                } else if (result.status === 'error') {
                    console.log(`❌ Research ${i}: ERROR - ${result.message}`);
                }
            } else {
                console.log(`❌ Research ${i}: No response`);
            }
        }

        // Summary
        console.log('\n=== SUMMARY ===');
        const started = results.filter(r => r && r.status === 'success').length;
        const queued = results.filter(r => r && r.status === 'queued').length;
        const errors = results.filter(r => r && r.status === 'error').length;

        console.log(`Started: ${started}`);
        console.log(`Queued: ${queued}`);
        console.log(`Errors: ${errors}`);

        if (started === 3 && queued === 2) {
            console.log('\n✅ SUCCESS: Concurrent limit working correctly!');
        } else {
            console.log('\n❌ FAILED: Expected 3 started, 2 queued');
        }

        // Keep browser open briefly
        await new Promise(resolve => setTimeout(resolve, 3000));

    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        await browser.close();
    }
})();
