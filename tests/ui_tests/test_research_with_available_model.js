/**
 * Research Test with Available Model
 * Tests research submission using an available Ollama model
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testResearchWithAvailableModel() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    console.log('🔬 Research Test with Available Model\n');

    // Enable request interception to log API calls
    await page.setRequestInterception(true);

    page.on('request', request => {
        if (request.url().includes('/api/start_research')) {
            console.log(`\n📤 Research API Request:`);
            console.log(`   URL: ${request.url()}`);
            console.log(`   Method: ${request.method()}`);
            if (request.postData()) {
                console.log(`   Body: ${request.postData()}`);
            }
        }
        request.continue();
    });

    page.on('response', async response => {
        if (response.url().includes('/api/start_research')) {
            console.log(`\n📥 Research API Response:`);
            console.log(`   Status: ${response.status()} ${response.statusText()}`);

            try {
                const body = await response.text();
                console.log(`   Body: ${body}`);
            } catch (e) {
                console.log(`   Body: <unable to read>`);
            }
        }
    });

    try {
        // Login
        console.log('🔐 Logging in...');
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in\n');

        // Go to home page
        await page.goto(baseUrl, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Check form state
        console.log('📋 Checking form state...');
        const formState = await page.evaluate(() => {
            const modelSelect = document.querySelector('#model, select[name="model"]');
            const providerSelect = document.querySelector('#model_provider');

            return {
                provider: providerSelect ? providerSelect.value : 'not found',
                model: modelSelect ? modelSelect.value : 'not found',
                modelOptions: modelSelect ?
                    Array.from(modelSelect.options).map(opt => ({
                        value: opt.value,
                        text: opt.text,
                        selected: opt.selected
                    })) : []
            };
        });

        console.log(`Provider: ${formState.provider}`);
        console.log(`Current model: ${formState.model}`);
        console.log(`Available models: ${formState.modelOptions.length}`);

        // Fill and submit the form using an available model
        console.log('\n📝 Submitting research with available model...\n');

        // Clear and type query
        await page.click('#query', { clickCount: 3 });
        await page.type('#query', 'What is artificial intelligence?');

        // Make sure we're using OLLAMA provider
        const providerSelect = await page.$('#model_provider');
        if (providerSelect) {
            await page.select('#model_provider', 'OLLAMA');
            // Wait for models to update
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Select an available model - use llama3.1:8b which we know exists
        const modelSelect = await page.$('#model, select[name="model"]');
        if (modelSelect) {
            try {
                await page.select('#model, select[name="model"]', 'llama3.1:8b');
                console.log('✅ Selected llama3.1:8b model');
            } catch (e) {
                console.log('⚠️  Could not select llama3.1:8b, using current selection');
            }
        }

        // Set mode to quick
        const quickMode = await page.$('#mode-quick');
        if (quickMode) {
            await quickMode.click();
            console.log('✅ Selected quick mode');
        }

        // Get final form data before submission
        const finalFormData = await page.evaluate(() => {
            const data = {};
            const inputs = document.querySelectorAll('input[name], select[name], textarea[name]');
            inputs.forEach(input => {
                if (input.type === 'radio' || input.type === 'checkbox') {
                    if (input.checked) {
                        data[input.name] = input.value;
                    }
                } else {
                    data[input.name] = input.value;
                }
            });
            return data;
        });

        console.log('\n📊 Form data to submit:');
        console.log(JSON.stringify(finalFormData, null, 2));

        // Take screenshot before submit
        await page.screenshot({ path: './screenshots/research_before_submit.png' });

        // Submit the form
        console.log('\n🚀 Submitting...');

        // Try multiple submission methods
        const submitButton = await page.$('button[type="submit"]');
        if (submitButton) {
            // Method 1: Click submit and wait for response
            const [response] = await Promise.all([
                page.waitForResponse(
                    response => response.url().includes('/api/start_research') ||
                               response.url().includes('/research/'),
                    { timeout: 30000 }
                ).catch(() => null),
                submitButton.click()
            ]);

            if (response) {
                console.log(`\n✅ Got response from: ${response.url()}`);
                console.log(`   Status: ${response.status()}`);

                // If we got a successful response, check where we ended up
                if (response.status() === 200 || response.status() === 302) {
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    const currentUrl = page.url();
                    console.log(`\n📍 Current URL: ${currentUrl}`);

                    if (currentUrl.includes('/research/')) {
                        const researchId = currentUrl.split('/').pop();
                        console.log(`✅ Research started! ID: ${researchId}`);

                        // Wait a bit for the page to load
                        await new Promise(resolve => setTimeout(resolve, 5000));

                        // Check for progress indicators
                        const progress = await page.evaluate(() => {
                            const logs = document.querySelectorAll('.log-entry, .log-item, #logs li');
                            const status = document.querySelector('.status, .research-status, #status');
                            const progress = document.querySelector('.progress, #progress');

                            return {
                                logCount: logs.length,
                                hasStatus: !!status,
                                statusText: status ? status.textContent : '',
                                hasProgress: !!progress
                            };
                        });

                        console.log('\n📊 Progress page state:');
                        console.log(`   Log entries: ${progress.logCount}`);
                        console.log(`   Has status: ${progress.hasStatus}`);
                        console.log(`   Status text: ${progress.statusText}`);
                        console.log(`   Has progress indicator: ${progress.hasProgress}`);
                    }
                }
            } else {
                console.log('\n⚠️  No response received within timeout');
            }
        }

        // Take final screenshot
        await page.screenshot({ path: './screenshots/research_after_submit.png' });

        // Check for any alerts or errors
        const alerts = await page.evaluate(() => {
            return Array.from(document.querySelectorAll('.alert')).map(alert => ({
                type: alert.className,
                text: alert.textContent.trim()
            }));
        });

        if (alerts.length > 0) {
            console.log('\n⚠️  Alerts found:');
            alerts.forEach(alert => {
                console.log(`   - [${alert.type}] ${alert.text}`);
            });
        }

        // Summary
        console.log('\n' + '='.repeat(60));
        console.log('📊 TEST SUMMARY');
        console.log('='.repeat(60));

        const finalUrl = page.url();
        if (finalUrl.includes('/research/')) {
            console.log('✅ Research submission successful!');
            console.log(`   Research URL: ${finalUrl}`);
        } else if (alerts.some(a => a.type.includes('danger'))) {
            console.log('❌ Research submission failed with error');
            console.log('   Check server logs for SQLAlchemy issues');
        } else {
            console.log('⚠️  Research submission status unclear');
            console.log(`   Still on: ${finalUrl}`);
        }

    } catch (error) {
        console.error('\n❌ Test error:', error.message);
        await page.screenshot({ path: './screenshots/research_error.png' });
    }

    console.log('\n⏸️  Keeping browser open for 20 seconds...');
    await new Promise(resolve => setTimeout(resolve, 20000));

    await browser.close();
    console.log('🏁 Test completed');
}

// Run the test
testResearchWithAvailableModel().catch(console.error);
