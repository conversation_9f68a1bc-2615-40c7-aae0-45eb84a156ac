const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Tests to check
const testsToCheck = [
    'test_metrics_display.js',
    'test_simple_metrics.js',
    'test_research_form.js',
    'test_full_navigation.js',
    'test_complete_workflow.js',
    'test_export_functionality.js',
    'test_cost_analytics.js',
    'test_benchmark_settings.js'
];

async function checkTest(testFile) {
    return new Promise((resolve) => {
        const testPath = path.join(__dirname, testFile);

        if (!fs.existsSync(testPath)) {
            resolve({ name: testFile, status: 'NOT_FOUND' });
            return;
        }

        const testProcess = spawn('node', [testPath], {
            cwd: __dirname,
            env: { ...process.env, NODE_ENV: 'test' }
        });

        let hasError = false;
        let errorMsg = '';

        testProcess.stdout.on('data', () => {
            // Just consume output
        });

        testProcess.stderr.on('data', (data) => {
            hasError = true;
            errorMsg = data.toString().substring(0, 100);
        });

        const timeout = setTimeout(() => {
            testProcess.kill();
            resolve({ name: testFile, status: 'TIMEOUT' });
        }, 45000); // 45 second timeout

        testProcess.on('close', (code) => {
            clearTimeout(timeout);
            if (code === 0) {
                resolve({ name: testFile, status: 'PASS' });
            } else {
                resolve({
                    name: testFile,
                    status: 'FAIL',
                    code: code,
                    error: errorMsg
                });
            }
        });

        testProcess.on('error', (err) => {
            clearTimeout(timeout);
            resolve({ name: testFile, status: 'ERROR', error: err.message });
        });
    });
}

async function main() {
    console.log('🔍 Checking test status...\n');

    const results = {
        pass: [],
        fail: [],
        timeout: [],
        notFound: [],
        error: []
    };

    for (const test of testsToCheck) {
        process.stdout.write(`Checking ${test}...`);
        const result = await checkTest(test);

        switch(result.status) {
            case 'PASS':
                console.log(' ✅ PASS');
                results.pass.push(test);
                break;
            case 'FAIL':
                console.log(` ❌ FAIL (code ${result.code})`);
                results.fail.push(test);
                break;
            case 'TIMEOUT':
                console.log(' ⏱️ TIMEOUT');
                results.timeout.push(test);
                break;
            case 'NOT_FOUND':
                console.log(' ❓ NOT FOUND');
                results.notFound.push(test);
                break;
            case 'ERROR':
                console.log(' 💥 ERROR');
                results.error.push(test);
                break;
        }
    }

    console.log('\n📊 Summary:');
    console.log(`✅ Passed: ${results.pass.length}`);
    console.log(`❌ Failed: ${results.fail.length}`);
    console.log(`⏱️ Timeout: ${results.timeout.length}`);
    console.log(`❓ Not Found: ${results.notFound.length}`);
    console.log(`💥 Error: ${results.error.length}`);

    if (results.pass.length > 0) {
        console.log('\n✅ Passing tests:');
        results.pass.forEach(t => console.log(`   - ${t}`));
    }

    if (results.fail.length > 0) {
        console.log('\n❌ Failing tests:');
        results.fail.forEach(t => console.log(`   - ${t}`));
    }
}

main().catch(console.error);
