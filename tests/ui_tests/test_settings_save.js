/**
 * Settings Save Functionality UI Test
 *
 * Tests the complete settings save workflow by monitoring network requests,
 * console messages, and response handling. Specifically tests the save_all_settings
 * endpoint and validates proper error handling and success feedback.
 *
 * What this tests:
 * - Settings save button functionality
 * - Network request monitoring for save operations
 * - API response validation (200 vs 4xx/5xx errors)
 * - Success/error message display
 * - Console logging during save operations
 * - Form submission workflow
 *
 * Prerequisites: Web server running on http://127.0.0.1:5000
 *
 * Usage: node tests/ui_tests/test_settings_save.js
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testSettingsSave() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());
    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    // Monitor all console messages
    page.on('console', msg => {
        console.log(`[${msg.type().toUpperCase()}]`, msg.text());
    });

    // Monitor all network requests and responses
    await page.setRequestInterception(true);
    page.on('request', request => {
        if (request.url().includes('/settings/')) {
            console.log('→ REQUEST:', request.method(), request.url());
        }
        request.continue();
    });

    page.on('response', response => {
        if (response.url().includes('/settings/')) {
            console.log('← RESPONSE:', response.status(), response.url());
            if (response.status() >= 400) {
                console.log('❌ ERROR RESPONSE:', response.status(), response.statusText());
            }
        }
    });

    try {
        console.log('🔧 Testing settings save functionality...');
        await page.goto('http://127.0.0.1:5000/settings/', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Wait for page to load completely
        await new Promise(resolve => setTimeout(resolve, 5000));

        console.log('🔍 Looking for save button...');

        // Look for save buttons
        const saveButtons = await page.$$('button[type="submit"], .save-btn, button[onclick*="save"], #save-all-btn');
        console.log(`Found ${saveButtons.length} save buttons`);

        if (saveButtons.length > 0) {
            console.log('✅ Clicking save button...');
            await saveButtons[0].click();

            // Wait and monitor for any responses
            console.log('⏳ Waiting for save response...');
            await new Promise(resolve => setTimeout(resolve, 5000));

            // Check for success/error messages
            const messages = await page.$$('.alert, .message, .notification, .success, .error');
            if (messages.length > 0) {
                console.log(`📝 Found ${messages.length} message elements:`);
                for (let i = 0; i < messages.length; i++) {
                    const text = await page.evaluate(el => ({
                        text: el.textContent.trim(),
                        className: el.className
                    }), messages[i]);
                    console.log(`   ${i + 1}. [${text.className}]: ${text.text}`);
                }
            }
        } else {
            console.log('❌ No save buttons found');
        }

    } catch (error) {
        console.error('❌ Test error:', error);
    }

    await browser.close();
}

testSettingsSave();
