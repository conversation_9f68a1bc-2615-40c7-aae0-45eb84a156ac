#!/usr/bin/env python3
"""
Test handling of mixed integer and UUID IDs
"""

import os
from pathlib import Path

os.environ["LDR_ALLOW_UNENCRYPTED"] = "true"

import sys

sys.path.insert(
    0,
    str(Path(__file__).parent.parent.parent.resolve()),
)

import requests

from src.local_deep_research.database.encrypted_db import db_manager
from src.local_deep_research.database.models.research import ResearchHistory


def test_mixed_id_handling():
    """Test that the API can handle both integer and UUID IDs"""

    print("Testing Mixed ID Handling")
    print("=" * 60)

    # Test different users
    test_cases = [
        ("testuser", "T3st!Secure#2024$LDR", "User with integer IDs"),
        (
            "test_uuid_fresh_bew2zgek",
            "T3st!Secure#2024$LDR",
            "User with UUID IDs",
        ),
    ]

    for username, password, description in test_cases:
        print(f"\n{description}: {username}")
        print("-" * 40)

        # Login
        session = requests.Session()
        resp = session.get("http://127.0.0.1:5000/auth/login")
        csrf = None
        for line in resp.text.split("\n"):
            if "csrf_token" in line and "value=" in line:
                start = line.find('value="') + 7
                end = line.find('"', start)
                csrf = line[start:end]
                break

        login_data = {
            "username": username,
            "password": password,
            "csrf_token": csrf,
        }

        login_resp = session.post(
            "http://127.0.0.1:5000/auth/login", data=login_data
        )
        print(f"Login: {login_resp.status_code}")

        if login_resp.status_code != 200:
            print("Login failed, skipping...")
            continue

        # Test history endpoint
        print("\nTesting /api/history:")
        hist = session.get("http://127.0.0.1:5000/api/history")
        print(f"  Status: {hist.status_code}")

        if hist.status_code == 200:
            data = hist.json()
            items = data.get("items", [])
            print(f"  Items: {len(items)}")
            if items:
                first_id = items[0].get("id")
                print(
                    f"  First ID: {first_id} (type: {type(first_id).__name__})"
                )
        else:
            print(f"  Error: {hist.text[:200]}...")

        # Check database directly
        print("\nChecking database directly:")
        try:
            engine = db_manager.open_user_database(username, password)
            if engine:
                session_db = db_manager.get_session(username)

                # Get first research entry
                first_research = session_db.query(ResearchHistory).first()
                if first_research:
                    print(
                        f"  First research ID: {first_research.id} (type: {type(first_research.id).__name__})"
                    )

                    # Test specific endpoint
                    research_id = first_research.id
                    print(f"\nTesting /api/research/{research_id}:")

                    detail_resp = session.get(
                        f"http://127.0.0.1:5000/api/research/{research_id}"
                    )
                    print(f"  Status: {detail_resp.status_code}")

                    if detail_resp.status_code == 200:
                        detail_data = detail_resp.json()
                        print(f"  ID in response: {detail_data.get('id')}")
                    else:
                        print(f"  Error: {detail_resp.text[:200]}...")

                    # Test status endpoint
                    print(f"\nTesting /api/research/{research_id}/status:")
                    status_resp = session.get(
                        f"http://127.0.0.1:5000/api/research/{research_id}/status"
                    )
                    print(f"  Status: {status_resp.status_code}")

                session_db.close()
                db_manager.close_user_database(username)
        except Exception as e:
            print(f"  Database error: {e}")


if __name__ == "__main__":
    test_mixed_id_handling()
