#!/usr/bin/env python3
"""Check if research thread is running"""

import json

import requests

# Login first
session = requests.Session()
login_data = {"username": "testuser", "password": "T3st!Secure#2024$LDR"}

# Get CSRF token
login_page = session.get("http://127.0.0.1:5000/auth/login")
# Simple CSRF extraction (this is hacky but works for testing)
csrf_token = login_page.text.split('name="csrf_token" value="')[1].split('"')[0]

# Login
login_data["csrf_token"] = csrf_token
login_resp = session.post("http://127.0.0.1:5000/auth/login", data=login_data)
print(f"Login status: {login_resp.status_code}")

# Check research status
research_id = 24
status_resp = session.get(
    f"http://127.0.0.1:5000/api/research/{research_id}/status"
)
print(f"\nStatus response: {status_resp.status_code}")
if status_resp.ok:
    data = status_resp.json()
    print(json.dumps(data, indent=2))

# Check logs
logs_resp = session.get(
    f"http://127.0.0.1:5000/api/research/{research_id}/logs"
)
print(f"\nLogs response: {logs_resp.status_code}")
if logs_resp.ok:
    logs = logs_resp.json()
    if isinstance(logs, list):
        print(f"Number of log entries: {len(logs)}")
        for log in logs[-5:]:  # Last 5 logs
            print(f"  - {log.get('message', 'No message')}")
    else:
        print(f"Logs data: {logs}")
