/**
 * Research Test with Working Configuration
 * Uses wikipedia search engine and available model
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testResearchWorkingConfig() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    console.log('🔬 Research Test with Working Configuration\n');

    try {
        // Login
        console.log('🔐 Logging in...');
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in\n');

        // Go to home page
        await page.goto(baseUrl, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Wait for form
        await page.waitForSelector('#query', { timeout: 10000 });

        // Check available search engines
        console.log('🔍 Checking available search engines...');
        const searchEngines = await page.evaluate(() => {
            const select = document.querySelector('#search_engine, select[name="search_engine"]');
            if (!select) return [];
            return Array.from(select.options).map(opt => ({
                value: opt.value,
                text: opt.text
            }));
        });

        console.log('Available search engines:');
        searchEngines.forEach(engine => {
            console.log(`  - ${engine.value} (${engine.text})`);
        });

        // Submit research with working configuration
        console.log('\n🚀 Submitting research with valid configuration...\n');

        const result = await page.evaluate(async () => {
            try {
                const formData = {
                    query: 'What is artificial intelligence?',
                    mode: 'quick',
                    model_provider: 'OLLAMA',
                    model: 'llama3.1:8b',  // Available model
                    custom_endpoint: '',
                    search_engine: 'wikipedia',  // Use wikipedia since searxng is not configured
                    iterations: 1,  // Start with just 1 iteration
                    questions_per_iteration: 2,
                    strategy: 'source-based'
                };

                console.log('Sending request with:', JSON.stringify(formData, null, 2));

                const response = await fetch('/api/start_research', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });

                const responseText = await response.text();
                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    data = { error: 'Failed to parse response', responseText };
                }

                return {
                    ok: response.ok,
                    status: response.status,
                    data: data
                };
            } catch (error) {
                return { error: error.message };
            }
        });

        console.log('📊 API Result:');
        console.log(`Status: ${result.status}`);
        console.log(`Success: ${result.ok}`);
        console.log('Response:', JSON.stringify(result.data, null, 2));

        if (result.ok && result.data.research_id) {
            const researchId = result.data.research_id;
            console.log(`\n✅ Research created! ID: ${researchId}`);

            // Go to research page
            const researchUrl = `${baseUrl}/research/${researchId}`;
            await page.goto(researchUrl, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            // Monitor for changes
            console.log('\n⏳ Monitoring research progress...\n');

            let previousLogCount = 0;
            let noChangeCount = 0;
            const maxChecks = 30; // Check for up to 60 seconds

            for (let i = 0; i < maxChecks; i++) {
                await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds

                const status = await page.evaluate(() => {
                    const logs = document.querySelectorAll('.log-entry, #logs li, .research-log, [class*="log"]');
                    const statusEl = document.querySelector('.status, #status, .research-status, [class*="status"]');
                    const errorEl = document.querySelector('.alert-danger, .error');
                    const progressEl = document.querySelector('.progress-bar, #progress, [class*="progress"]');

                    // Also check for any text content that might indicate logs
                    const bodyText = document.body.innerText || '';
                    const hasLogText = bodyText.includes('Starting research') ||
                                      bodyText.includes('Generating questions') ||
                                      bodyText.includes('Searching') ||
                                      bodyText.includes('Processing');

                    return {
                        logCount: logs.length,
                        statusText: statusEl ? statusEl.textContent.trim() : '',
                        hasError: !!errorEl,
                        errorText: errorEl ? errorEl.textContent.trim() : '',
                        hasProgress: !!progressEl,
                        pageTitle: document.title,
                        url: window.location.href,
                        hasLogText: hasLogText,
                        bodyPreview: bodyText.substring(0, 200)
                    };
                });

                if (status.logCount > previousLogCount || status.hasLogText) {
                    console.log(`[${i+1}/${maxChecks}] Progress detected!`);
                    console.log(`  Logs: ${status.logCount}`);
                    console.log(`  Status: ${status.statusText}`);
                    console.log(`  Has log text: ${status.hasLogText}`);
                    previousLogCount = status.logCount;
                    noChangeCount = 0;
                } else {
                    noChangeCount++;
                    if (noChangeCount % 5 === 0) {
                        console.log(`[${i+1}/${maxChecks}] No changes for ${noChangeCount*2} seconds...`);
                    }
                }

                if (status.hasError) {
                    console.log(`\n❌ Error detected: ${status.errorText}`);
                    break;
                }

                // If we see actual progress, continue monitoring
                if (status.logCount > 0 || status.hasLogText) {
                    console.log('✅ Research is running!');
                }
            }

            // Final check
            await page.reload({ waitUntil: 'networkidle2' });

            const finalStatus = await page.evaluate(() => {
                const allText = document.body.innerText || '';
                return {
                    title: document.title,
                    hasContent: allText.length > 100,
                    contentPreview: allText.substring(0, 500)
                };
            });

            console.log('\n📊 Final Status:');
            console.log(`Page title: ${finalStatus.title}`);
            console.log(`Has content: ${finalStatus.hasContent}`);
            console.log('\nContent preview:');
            console.log(finalStatus.contentPreview);

            // Take final screenshot
            await page.screenshot({ path: './screenshots/research_working_final.png' });

        } else {
            console.log('\n❌ Failed to start research');
            if (result.data.error) {
                console.log(`Error: ${result.data.error}`);
            }
        }

    } catch (error) {
        console.error('\n❌ Test error:', error.message);
        await page.screenshot({ path: './screenshots/research_working_error.png' });
    }

    console.log('\n⏸️  Keeping browser open for 20 seconds...');
    await new Promise(resolve => setTimeout(resolve, 20000));

    await browser.close();
    console.log('🏁 Test completed');
}

// Run the test
testResearchWorkingConfig().catch(console.error);
