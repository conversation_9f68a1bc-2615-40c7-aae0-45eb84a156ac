/**
 * Complete Workflow Test
 * Tests the full user workflow: login, navigate pages, configure settings, and run a search
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testCompleteWorkflow() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    console.log('🧪 Complete Workflow Test');
    console.log('=' .repeat(50) + '\n');

    try {
        // Step 1: Authentication
        console.log('📌 Step 1: Authentication');
        await authHelper.ensureAuthenticated();
        console.log('✅ Authenticated successfully\n');

        // Step 2: Navigate to Settings
        console.log('📌 Step 2: Configure Settings');
        await page.goto(`${baseUrl}/settings/`, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Check current settings
        const currentSettings = await page.evaluate(() => {
            const settings = {};
            const inputs = document.querySelectorAll('input[name*="."], select[name*="."]');
            inputs.forEach(input => {
                if (input.name) {
                    settings[input.name] = input.value;
                }
            });
            return settings;
        });

        console.log('Current settings:');
        Object.entries(currentSettings).forEach(([key, value]) => {
            if (value) console.log(`  ${key}: ${value}`);
        });

        console.log('✅ Settings page loaded\n');

        // Step 3: Navigate through other pages
        console.log('📌 Step 3: Page Navigation');
        const pages = [
            { name: 'Metrics', path: '/metrics/' },
            { name: 'History', path: '/history/' },
            { name: 'Home', path: '/' }
        ];

        for (const pageInfo of pages) {
            await page.goto(`${baseUrl}${pageInfo.path}`, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });
            console.log(`✅ ${pageInfo.name} page loaded`);
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        console.log('');

        // Step 4: Attempt Research (with error handling)
        console.log('📌 Step 4: Research Test');

        // Monitor API calls
        const apiCalls = [];
        page.on('response', response => {
            if (response.url().includes('/api/')) {
                apiCalls.push({
                    url: response.url(),
                    status: response.status(),
                    method: response.request().method()
                });
            }
        });

        // Fill research form
        await page.waitForSelector('#query', { timeout: 5000 });
        await page.type('#query', 'Explain the basics of machine learning in simple terms');
        console.log('✅ Entered research query');

        // Select mode
        const quickMode = await page.$('#mode-quick');
        if (quickMode) {
            await page.click('#mode-quick');
            console.log('✅ Selected quick mode');
        }


        // Submit research
        console.log('🚀 Submitting research...');
        const submitButton = await page.$('button[type="submit"]');

        if (submitButton) {
            // Click and wait for response
            await submitButton.click();

            // Wait a bit for API calls
            await new Promise(resolve => setTimeout(resolve, 3000));

            console.log('\nAPI calls made:');
            apiCalls.forEach(call => {
                console.log(`  ${call.method} ${call.url} - Status: ${call.status}`);
            });

            // Check current state
            const currentUrl = page.url();
            console.log(`\nCurrent URL: ${currentUrl}`);

            // Check for errors or success
            const alerts = await page.$$('.alert');
            if (alerts.length > 0) {
                console.log('\nAlerts:');
                for (const alert of alerts) {
                    const classes = await page.evaluate(el => el.className, alert);
                    const text = await page.evaluate(el => el.textContent.trim(), alert);
                    const type = classes.includes('alert-danger') ? '❌' :
                                classes.includes('alert-success') ? '✅' :
                                classes.includes('alert-warning') ? '⚠️' : 'ℹ️';
                    console.log(`  ${type} ${text}`);
                }
            }


            if (currentUrl.includes('/research/') || currentUrl.includes('/progress')) {
                console.log('\n✅ Research started successfully!');
                console.log('📍 Progress page loaded');

                // Wait to see some progress
                await new Promise(resolve => setTimeout(resolve, 5000));
            } else if (apiCalls.some(call => call.status === 500)) {
                console.log('\n❌ Research failed - Server error (500)');
                console.log('This might be due to the SQLAlchemy database issues noted earlier');
            } else {
                console.log('\n⚠️  Research submission unclear - still on home page');
            }
        }

        console.log('\n✅ Workflow test completed!');

    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
    }

    // Keep browser open if not headless
    if (!process.argv.includes('--headless')) {
        console.log('\n⏸️  Browser will close in 10 seconds...');
        await new Promise(resolve => setTimeout(resolve, 10000));
    }

    await browser.close();
    console.log('\n🏁 Test session ended');
}

// Run the test
testCompleteWorkflow().catch(error => {
    console.error('💥 Test runner error:', error);
    process.exit(1);
});

// Usage:
// node test_complete_workflow.js           # Run with visible browser
// node test_complete_workflow.js --headless # Run in headless mode
