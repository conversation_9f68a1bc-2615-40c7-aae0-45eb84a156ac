/**
 * Focused test for metrics dashboard only
 * Simpler version to validate the metrics fix
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testMetricsPage() {
    console.log('🚀 Testing metrics dashboard...');

    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    // Listen to console logs
    page.on('console', msg => {
        const type = msg.type();
        const text = msg.text();
        if (text.includes('METRICS SCRIPT') || text.includes('tokens') || text.includes('displaying')) {
            console.log(`📝 [${type.toUpperCase()}] ${text}`);
        }
    });

    // Listen to JavaScript errors
    page.on('pageerror', error => {
        console.log(`❌ [JS ERROR] ${error.message}`);
    });

    try {
        await page.goto('http://127.0.0.1:5000/metrics/', {
            waitUntil: 'domcontentloaded',
            timeout: 10000
        });

        console.log('✅ Page loaded');

        // Wait for metrics to load
        await new Promise(resolve => setTimeout(resolve, 4000));

        // Check metrics values
        const metrics = await page.evaluate(() => {
            const totalTokens = document.getElementById('total-tokens');
            const totalResearches = document.getElementById('total-researches');
            const loading = document.getElementById('loading');
            const content = document.getElementById('metrics-content');
            const error = document.getElementById('error');

            return {
                tokens: totalTokens ? totalTokens.textContent : 'NOT FOUND',
                researches: totalResearches ? totalResearches.textContent : 'NOT FOUND',
                loadingVisible: loading ? window.getComputedStyle(loading).display !== 'none' : false,
                contentVisible: content ? window.getComputedStyle(content).display !== 'none' : false,
                errorVisible: error ? window.getComputedStyle(error).display !== 'none' : false
            };
        });

        console.log('📊 Metrics Dashboard Results:');
        console.log(`   Total Tokens: ${metrics.tokens}`);
        console.log(`   Total Researches: ${metrics.researches}`);
        console.log(`   Loading visible: ${metrics.loadingVisible}`);
        console.log(`   Content visible: ${metrics.contentVisible}`);
        console.log(`   Error visible: ${metrics.errorVisible}`);

        // Determine success
        const success = !metrics.errorVisible &&
                       (metrics.contentVisible || !metrics.loadingVisible) &&
                       metrics.tokens !== 'NOT FOUND' &&
                       metrics.researches !== 'NOT FOUND';

        if (success) {
            console.log('🎉 Metrics dashboard test PASSED!');
            console.log(`   Successfully loaded ${metrics.tokens} tokens and ${metrics.researches} researches`);
        } else {
            console.log('💥 Metrics dashboard test FAILED!');
        }

        await browser.close();
        return success;

    } catch (error) {
        console.log(`❌ Test failed: ${error.message}`);
        await browser.close();
        return false;
    }
}

if (require.main === module) {
    testMetricsPage().then(success => {
        process.exit(success ? 0 : 1);
    }).catch(console.error);
}

module.exports = { testMetricsPage };
