/**
 * Test for News Subscription Form
 * Tests the test run functionality
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');

const BASE_URL = process.env.BASE_URL || 'http://127.0.0.1:5000';

// Breaking news table query
const BREAKING_NEWS_QUERY = `Find UP TO 10 IMPORTANT breaking news stories from TODAY ${new Date().toLocaleDateString()} ONLY.

START YOUR RESPONSE DIRECTLY WITH THE TABLE. NO INTRODUCTION OR PREAMBLE.`;

async function testNewsSubscriptionForm() {
    const browser = await puppeteer.launch({
        headless: false, // Show browser for debugging
        slowMo: 50, // Slow down actions
        devtools: true, // Open DevTools
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 800 });

    // Enable console logging
    page.on('console', msg => {
        console.log(`BROWSER LOG [${msg.type()}]:`, msg.text());
    });

    // Log network errors
    page.on('pageerror', error => {
        console.log('PAGE ERROR:', error.message);
    });

    // Log failed requests
    page.on('requestfailed', request => {
        console.log('REQUEST FAILED:', request.url(), request.failure().errorText);
    });

    const authHelper = new AuthHelper(page, BASE_URL);

    console.log('🧪 News Subscription Form Test\n');

    try {
        // Ensure we're logged in
        console.log('🔐 Ensuring authentication...');
        await authHelper.ensureAuthenticated();
        console.log('✅ Authenticated\n');

        // Test 1: Load subscription form page
        console.log('📄 Test 1: Loading subscription form page');
        await page.goto(`${BASE_URL}/news/subscriptions/new`, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        const title = await page.title();
        console.log(`✅ Page loaded - Title: ${title}`);

        // Check if form exists
        const form = await page.$('#subscription-form');
        if (!form) {
            throw new Error('Subscription form not found');
        }
        console.log('✅ Subscription form found\n');

        // Take screenshot
        await page.screenshot({
            path: 'screenshots/subscription_form_loaded.png',
            fullPage: true
        });

        // Test 2: Check for test run button
        console.log('🔘 Test 2: Checking for test run button');
        const testButton = await page.$('#test-subscription-btn');
        if (!testButton) {
            throw new Error('Test run button not found');
        }

        const buttonText = await page.evaluate(() => {
            const btn = document.getElementById('test-subscription-btn');
            return btn ? btn.textContent : null;
        });

        console.log(`✅ Test button found with text: "${buttonText}"\n`);

        // Test 3: Check button event listeners
        console.log('🔍 Test 3: Checking button event listeners');
        const buttonInfo = await page.evaluate(() => {
            const btn = document.getElementById('test-subscription-btn');
            if (!btn) return { exists: false };

            // Check if handleTestRun function exists
            const functionExists = typeof handleTestRun === 'function';

            return {
                exists: true,
                functionExists,
                onclick: btn.onclick ? 'has onclick' : 'no onclick',
                id: btn.id,
                className: btn.className,
                type: btn.type || 'button'
            };
        });

        console.log('Button info:', JSON.stringify(buttonInfo, null, 2));
        console.log(`✅ handleTestRun function exists: ${buttonInfo.functionExists}\n`);

        // Test 4: Test empty query validation
        console.log('📝 Test 4: Testing empty query validation');
        await page.click('#test-subscription-btn');

        // Wait for alert
        await new Promise(resolve => setTimeout(resolve, 1000));

        const alertText = await page.evaluate(() => {
            const alert = document.querySelector('.alert');
            return alert ? alert.textContent.trim() : null;
        });

        console.log(`✅ Alert shown: "${alertText}"\n`);

        // Take screenshot
        await page.screenshot({
            path: 'screenshots/subscription_test_empty_query.png',
            fullPage: true
        });

        // Test 5: Test with breaking news query
        console.log('📰 Test 5: Testing with breaking news query');

        // Enter the query
        const queryTextarea = await page.$('#subscription-query');
        await queryTextarea.click({ clickCount: 3 }); // Select all
        await queryTextarea.type(BREAKING_NEWS_QUERY);
        console.log('✅ Query entered\n');

        // Take screenshot before clicking
        await page.screenshot({
            path: 'screenshots/subscription_before_test_run.png',
            fullPage: true
        });

        // Set up request monitoring
        const requests = [];
        page.on('request', request => {
            if (request.url().includes('/research/api/start_research')) {
                console.log('🌐 Research API request intercepted:');
                console.log('  Method:', request.method());
                console.log('  URL:', request.url());
                console.log('  Headers:', JSON.stringify(request.headers(), null, 2));
                const postData = request.postData();
                if (postData) {
                    try {
                        const data = JSON.parse(postData);
                        console.log('  Body:', JSON.stringify(data, null, 2));
                    } catch (e) {
                        console.log('  Body (raw):', postData);
                    }
                }
                requests.push(request);
            }
        });

        // Set up response monitoring
        page.on('response', response => {
            if (response.url().includes('/research/api/start_research')) {
                console.log('🌐 Research API response:');
                console.log('  Status:', response.status());
                console.log('  Headers:', JSON.stringify(response.headers(), null, 2));
            }
        });

        // Click test run
        console.log('🚀 Clicking test run button...');
        await page.click('#test-subscription-btn');

        // Wait for something to happen
        console.log('⏳ Waiting for response...');
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Take screenshot after clicking
        await page.screenshot({
            path: 'screenshots/subscription_after_test_run.png',
            fullPage: true
        });

        // Check results
        console.log(`\n📊 Results:`);
        console.log(`  Total research API requests: ${requests.length}`);

        const currentUrl = page.url();
        console.log(`  Current URL: ${currentUrl}`);

        // Check for alerts
        const finalAlerts = await page.$$eval('.alert', alerts =>
            alerts.map(alert => ({
                class: alert.className,
                text: alert.textContent.trim()
            }))
        );

        if (finalAlerts.length > 0) {
            console.log(`  Alerts found:`);
            finalAlerts.forEach(alert => {
                console.log(`    - [${alert.class}] ${alert.text}`);
            });
        }

        // Final status
        if (currentUrl.includes('/news') && !currentUrl.includes('/subscriptions')) {
            console.log('\n✅ SUCCESS: Redirected to news page');
        } else if (currentUrl.includes('/auth/login')) {
            console.log('\n❌ FAILURE: Redirected to login - authentication issue');
        } else if (requests.length > 0) {
            console.log('\n⚠️  PARTIAL: API request made but no redirect');
        } else {
            console.log('\n❌ FAILURE: Test run button click had no effect');

            // Try to manually call the function
            console.log('\n🔧 Attempting manual function call...');
            const result = await page.evaluate(async () => {
                if (typeof handleTestRun === 'function') {
                    try {
                        await handleTestRun();
                        return { success: true, message: 'Function called' };
                    } catch (e) {
                        return { success: false, error: e.message };
                    }
                } else {
                    return { success: false, error: 'handleTestRun function not found' };
                }
            });
            console.log('Manual call result:', result);
        }

        console.log('\n✅ All tests completed');

    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        await page.screenshot({
            path: 'screenshots/subscription_test_error.png',
            fullPage: true
        });
    } finally {
        console.log('\n🧹 Cleaning up...');
        await browser.close();
    }
}

// Run the test
testNewsSubscriptionForm().catch(console.error);
