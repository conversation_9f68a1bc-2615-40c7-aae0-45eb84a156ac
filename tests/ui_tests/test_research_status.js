const puppeteer = require('puppeteer');

const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testResearchStatus() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    try {
        const page = await browser.newPage();
        const auth = new AuthHelper(page);

        // Login first
        await auth.ensureAuthenticated();

        // Navigate to a progress page (use ID from previous test)
        const researchId = 24; // From the successful submission
        console.log(`Checking research status for ID: ${researchId}`);

        await page.goto(`http://127.0.0.1:5000/progress/${researchId}`, {
            waitUntil: 'networkidle2'
        });

        // Wait a bit for the page to load
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check the status
        const status = await page.evaluate(() => {
            // Look for status elements
            const statusEl = document.querySelector('.status-indicator, .research-status, [class*="status"]');
            const progressEl = document.querySelector('.progress-value, .progress-percentage, [class*="progress"]');
            const logsEl = document.querySelector('.log-messages, .progress-log, [class*="log"]');

            return {
                statusText: statusEl ? statusEl.textContent.trim() : 'Not found',
                progressText: progressEl ? progressEl.textContent.trim() : 'Not found',
                hasLogs: !!logsEl,
                logCount: logsEl ? logsEl.children.length : 0,
                pageTitle: document.title,
                pageUrl: window.location.href
            };
        });

        console.log('Research Status:', status);

        // Make API call to check status - try different endpoints
        const apiEndpoints = [
            `/api/research/${researchId}/status`,
            `/research/api/status/${researchId}`,
            `/api/status/${researchId}`,
            `/api/v1/research/${researchId}/status`
        ];

        let apiStatus = null;
        for (const endpoint of apiEndpoints) {
            const result = await page.evaluate(async (url) => {
                try {
                    const response = await fetch(url);
                    return {
                        url: url,
                        status: response.status,
                        ok: response.ok,
                        data: response.ok ? await response.json() : null
                    };
                } catch (e) {
                    return { url: url, error: e.message };
                }
            }, endpoint);

            console.log(`Tried ${endpoint}:`, result.status || result.error);

            if (result.ok) {
                apiStatus = result.data;
                break;
            }
        }

        console.log('API Status:', apiStatus);

        // Get the log messages
        const logs = await page.evaluate(() => {
            const logElements = document.querySelectorAll('.log-message, .progress-log-item, [class*="log"] li, [class*="log"] div');
            return Array.from(logElements).map(el => el.textContent.trim()).filter(text => text.length > 0);
        });

        console.log('\nLog messages:');
        logs.forEach((log, i) => console.log(`  ${i + 1}. ${log}`));

        // Check for errors
        const errors = await page.evaluate(() => {
            const errorElements = document.querySelectorAll('.error, .alert-danger, [class*="error"]');
            return Array.from(errorElements).map(el => el.textContent.trim()).filter(text => text.length > 0);
        });

        if (errors.length > 0) {
            console.log('\nErrors found:');
            errors.forEach(err => console.log(`  - ${err}`));
        }

        // Take screenshot
        await page.screenshot({
            path: './screenshots/research_status.png',
            fullPage: true
        });

        console.log('Screenshot saved to ./screenshots/research_status.png');

    } catch (error) {
        console.error('Error:', error);
    }

    console.log('\nKeeping browser open for 10 seconds...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    await browser.close();
}

testResearchStatus();
