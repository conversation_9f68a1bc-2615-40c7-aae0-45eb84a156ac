const axios = require('axios');

async function testFollowupAPI() {
    try {
        console.log('Testing follow-up API directly...');

        // First login
        const loginRes = await axios.post('http://127.0.0.1:5000/auth/login', {
            username: 'testuser',
            password: 'testpass'
        }, {
            headers: { 'Content-Type': 'application/json' },
            withCredentials: true
        });

        const cookies = loginRes.headers['set-cookie'];
        console.log('Login successful, got cookies');

        // Test follow-up prepare endpoint
        console.log('Testing prepare endpoint...');
        const prepareRes = await axios.post('http://127.0.0.1:5000/api/followup/prepare', {
            parent_research_id: '175a78a1-a1ce-4a65-8507-167575babedf',
            question: 'test question'
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Cookie': cookies.join('; ')
            }
        });

        console.log('Prepare response:', prepareRes.data);

        // Test follow-up start endpoint
        console.log('Testing start endpoint...');
        const startRes = await axios.post('http://127.0.0.1:5000/api/followup/start', {
            parent_research_id: '175a78a1-a1ce-4a65-8507-167575babedf',
            question: 'test question',
            strategy: 'source-based',
            max_iterations: 1,
            questions_per_iteration: 3
        }, {
            headers: {
                'Content-Type': 'application/json',
                'Cookie': cookies.join('; ')
            }
        });

        console.log('Start response:', startRes.data);
        console.log('Test completed successfully');

    } catch (error) {
        console.error('Test failed:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
            console.error('Response status:', error.response.status);
        }
        process.exit(1);
    }
}

// Run the test
testFollowupAPI().catch(console.error);
