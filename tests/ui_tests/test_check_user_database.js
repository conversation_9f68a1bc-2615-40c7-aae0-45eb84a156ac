/**
 * Check User Database Test
 * Verifies what tables exist in the user's database
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function checkUserDatabase() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    console.log('🗄️  Checking User Database\n');

    try {
        // Login
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in\n');

        // Check database tables via settings page
        await page.goto(`${baseUrl}/settings/`, { waitUntil: 'networkidle2' });

        // Try to get some indication of what's in the database
        const pageContent = await page.evaluate(() => {
            return {
                title: document.title,
                hasSettingsForm: !!document.querySelector('form'),
                settingsCount: document.querySelectorAll('.setting-item, input[type="text"], select').length,
                bodyText: document.body.innerText.substring(0, 500)
            };
        });

        console.log('Settings page info:');
        console.log(`Title: ${pageContent.title}`);
        console.log(`Has settings form: ${pageContent.hasSettingsForm}`);
        console.log(`Settings elements: ${pageContent.settingsCount}`);

        // Try to check research history to see if research table exists
        console.log('\n📋 Checking history page...');
        await page.goto(`${baseUrl}/history/`, { waitUntil: 'networkidle2' });

        const historyContent = await page.evaluate(() => {
            return {
                hasHistory: !!document.querySelector('.history-list, .research-item, #history-container'),
                itemCount: document.querySelectorAll('.history-item, .research-item, .ldr-card').length,
                hasError: !!document.querySelector('.alert-danger'),
                errorText: document.querySelector('.alert-danger')?.textContent || '',
                bodyPreview: document.body.innerText.substring(0, 300)
            };
        });

        console.log('\nHistory page info:');
        console.log(`Has history container: ${historyContent.hasHistory}`);
        console.log(`History items: ${historyContent.itemCount}`);
        if (historyContent.hasError) {
            console.log(`Error: ${historyContent.errorText}`);
        }

        // Try a simple research submission to see what error we get
        console.log('\n🧪 Testing research submission for database errors...');

        await page.goto(baseUrl, { waitUntil: 'networkidle2' });

        const testResult = await page.evaluate(async () => {
            try {
                const response = await fetch('/api/start_research', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        query: 'test',
                        mode: 'quick'
                    })
                });

                const text = await response.text();
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    data = { parseError: true, text: text };
                }

                return {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                };
            } catch (error) {
                return { error: error.message };
            }
        });

        console.log('\nResearch API response:');
        console.log(`Status: ${testResult.status} ${testResult.statusText}`);
        console.log('Data:', JSON.stringify(testResult.data, null, 2));

        // Check server logs for SQLAlchemy errors
        console.log('\n💡 To see database errors, check:');
        console.log('tail -f /tmp/ldr_server_ui_tests.log | grep -E "(table|Table|CREATE|research_sessions)"');

    } catch (error) {
        console.error('Error:', error.message);
    }

    console.log('\n⏸️  Browser open for inspection...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    await browser.close();
    console.log('✅ Done');
}

checkUserDatabase().catch(console.error);
