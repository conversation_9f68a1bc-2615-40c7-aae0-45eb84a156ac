/**
 * Research Form Test
 * Examines the research form structure and attempts to submit a search
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testResearchForm() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    console.log('🧪 Research Form Test\n');

    try {
        // Ensure authentication
        console.log('🔐 Ensuring authentication...');
        await authHelper.ensureAuthenticated();
        console.log('✅ Authentication successful\n');

        // Navigate to home page
        await page.goto(baseUrl, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        console.log('📝 Examining research form structure...\n');

        // Get all form elements
        const formInfo = await page.evaluate(() => {
            const forms = document.querySelectorAll('form');
            const formData = [];

            forms.forEach((form, index) => {
                const inputs = form.querySelectorAll('input, select, textarea');
                const buttons = form.querySelectorAll('button');

                const inputData = [];
                inputs.forEach(input => {
                    inputData.push({
                        type: input.type,
                        name: input.name,
                        id: input.id,
                        value: input.value,
                        placeholder: input.placeholder
                    });
                });

                const buttonData = [];
                buttons.forEach(button => {
                    buttonData.push({
                        type: button.type,
                        text: button.textContent.trim(),
                        onclick: button.onclick ? 'has onclick' : 'no onclick'
                    });
                });

                formData.push({
                    action: form.action,
                    method: form.method,
                    id: form.id,
                    inputs: inputData,
                    buttons: buttonData
                });
            });

            // Also check for elements outside forms
            const queryInput = document.getElementById('query');
            const submitButtons = document.querySelectorAll('button[type="submit"]');

            return {
                forms: formData,
                queryInput: queryInput ? {
                    found: true,
                    name: queryInput.name,
                    type: queryInput.type,
                    placeholder: queryInput.placeholder
                } : { found: false },
                submitButtons: Array.from(submitButtons).map(btn => ({
                    text: btn.textContent.trim(),
                    form: btn.form ? btn.form.id : 'no form'
                }))
            };
        });

        console.log('Forms found:', formInfo.forms.length);
        formInfo.forms.forEach((form, index) => {
            console.log(`\nForm ${index + 1}:`);
            console.log(`  Action: ${form.action}`);
            console.log(`  Method: ${form.method}`);
            console.log(`  ID: ${form.id || 'no id'}`);
            console.log(`  Inputs: ${form.inputs.length}`);
            form.inputs.forEach(input => {
                console.log(`    - ${input.type} (name: ${input.name}, id: ${input.id})`);
            });
            console.log(`  Buttons: ${form.buttons.length}`);
            form.buttons.forEach(button => {
                console.log(`    - ${button.type}: "${button.text}"`);
            });
        });

        console.log('\nQuery input element:', formInfo.queryInput.found ? 'Found' : 'Not found');
        if (formInfo.queryInput.found) {
            console.log('  Details:', formInfo.queryInput);
        }

        console.log('\nSubmit buttons:', formInfo.submitButtons.length);
        formInfo.submitButtons.forEach(btn => {
            console.log(`  - "${btn.text}" (form: ${btn.form})`);
        });

        // Try to find the correct form and submit a search
        console.log('\n🔎 Attempting to submit a search...\n');

        // Find the research form
        const researchForm = await page.$('#research-form, form');
        if (researchForm) {
            console.log('✅ Found research form');

            // Fill query
            const queryInput = await page.$('#query');
            if (queryInput) {
                await page.type('#query', 'What is machine learning?');
                console.log('✅ Entered search query');
            }


            // Find and click submit button
            const submitButton = await researchForm.$('button[type="submit"]');
            if (submitButton) {
                console.log('🚀 Clicking submit button...');

                // Set up navigation promise
                const navPromise = page.waitForNavigation({
                    waitUntil: 'networkidle2',
                    timeout: 10000
                }).catch(e => console.log('Navigation timeout:', e.message));

                await submitButton.click();
                await navPromise;

                const newUrl = page.url();
                console.log(`📍 After submit URL: ${newUrl}`);

                // Check if we got redirected
                if (newUrl !== baseUrl && newUrl !== baseUrl + '/') {
                    console.log('✅ Form submitted successfully!');
                } else {
                    console.log('⚠️  Still on same page after submit');
                }
            } else {
                console.log('❌ Submit button not found in form');
            }
        } else {
            console.log('❌ Research form not found');
        }

    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
    }

    console.log('\n⏸️  Keeping browser open for inspection...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    await browser.close();
    console.log('🏁 Test ended');
}

// Run the test
testResearchForm().catch(console.error);
