/**
 * Complete Research Test
 * Submits research with all required fields properly set
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testCompleteResearch() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    console.log('🚀 Complete Research Test\n');

    // Enable console logging
    page.on('console', msg => {
        if (msg.type() === 'error') {
            console.log(`[Browser Error] ${msg.text()}`);
        }
    });

    try {
        // Login
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in\n');

        // Navigate to home
        await page.goto(baseUrl, { waitUntil: 'networkidle2' });
        await page.waitForSelector('#query', { timeout: 10000 });

        // First, let's see what the form expects
        console.log('📋 Checking form requirements...');
        const formData = await page.evaluate(() => {
            const form = document.querySelector('#research-form, form');
            if (!form) return { error: 'No form found' };

            const inputs = {};
            const elements = form.querySelectorAll('input, select, textarea');
            elements.forEach(el => {
                if (el.name || el.id) {
                    const key = el.name || el.id;
                    if (el.type === 'radio' || el.type === 'checkbox') {
                        if (el.checked) {
                            inputs[key] = el.value;
                        }
                    } else {
                        inputs[key] = el.value || '';
                    }
                }
            });

            return inputs;
        });

        console.log('Current form data:', JSON.stringify(formData, null, 2));

        // Submit with complete data
        console.log('\n📝 Submitting research with complete data...\n');

        const result = await page.evaluate(async () => {
            // Build complete request data
            const requestData = {
                query: 'What is artificial intelligence and how does it work?',
                mode: 'quick',
                model_provider: 'OLLAMA',
                model: 'llama3.1:8b',  // Use available model
                custom_endpoint: '',
                search_engine: 'duckduckgo',  // Use search engine that doesn't need API key
                iterations: 1,
                questions_per_iteration: 2,
                strategy: 'source-based',
                follow_up: false,
                auto_search: true
            };

            console.log('Sending request:', JSON.stringify(requestData, null, 2));

            try {
                const response = await fetch('/api/start_research', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const responseText = await response.text();
                console.log('Response text:', responseText);

                let data;
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    data = { parseError: true, responseText };
                }

                return {
                    ok: response.ok,
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    data: data
                };
            } catch (error) {
                console.error('Fetch error:', error);
                return { fetchError: true, error: error.message };
            }
        });

        console.log('\n📊 Response Details:');
        console.log(`Status: ${result.status} ${result.statusText}`);
        console.log(`OK: ${result.ok}`);
        console.log('Headers:', JSON.stringify(result.headers, null, 2));
        console.log('Data:', JSON.stringify(result.data, null, 2));

        if (result.ok && result.data.research_id) {
            const researchId = result.data.research_id;
            console.log(`\n✅ Research started successfully! ID: ${researchId}`);

            // Navigate to research page
            const researchUrl = `${baseUrl}/research/${researchId}`;
            console.log(`\n📍 Going to: ${researchUrl}`);

            await page.goto(researchUrl, { waitUntil: 'networkidle2' });

            // Monitor progress
            console.log('\n⏳ Monitoring research progress...\n');

            for (let i = 0; i < 10; i++) {
                await new Promise(resolve => setTimeout(resolve, 3000));

                const progress = await page.evaluate(() => {
                    const logs = document.querySelectorAll('.log-entry, .research-log, [class*="log"] li');
                    const status = document.querySelector('.status, .research-status, #status');
                    const error = document.querySelector('.alert-danger');

                    // Get all text content
                    const allText = document.body.innerText || '';

                    return {
                        logCount: logs.length,
                        logs: Array.from(logs).slice(0, 5).map(l => l.textContent.trim()),
                        status: status ? status.textContent.trim() : 'No status element',
                        hasError: !!error,
                        errorText: error ? error.textContent.trim() : '',
                        pageHasContent: allText.length > 100,
                        contentPreview: allText.substring(0, 300)
                    };
                });

                console.log(`[Check ${i+1}/10]`);
                console.log(`  Logs: ${progress.logCount}`);
                console.log(`  Status: ${progress.status}`);

                if (progress.logs.length > 0) {
                    console.log('  Recent logs:');
                    progress.logs.forEach(log => console.log(`    - ${log}`));
                }

                if (progress.hasError) {
                    console.log(`  ❌ Error: ${progress.errorText}`);
                    break;
                }

                if (progress.logCount > 0) {
                    console.log('  ✅ Research is processing!');
                }

                // Take screenshot on interesting states
                if (i === 0 || progress.logCount > 0 || progress.hasError) {
                    await page.screenshot({ path: `./screenshots/research_progress_${i}.png` });
                }
            }

            // Final state
            await page.reload({ waitUntil: 'networkidle2' });
            const finalState = await page.evaluate(() => {
                return {
                    url: window.location.href,
                    title: document.title,
                    fullText: document.body.innerText || 'No content'
                };
            });

            console.log('\n📊 Final State:');
            console.log(`URL: ${finalState.url}`);
            console.log(`Title: ${finalState.title}`);
            console.log('\nPage content:');
            console.log(finalState.fullText.substring(0, 1000));

            // Take final screenshot
            await page.screenshot({ path: './screenshots/research_final.png' });

        } else {
            console.log('\n❌ Research failed to start');

            // Get more error details
            if (result.data.message) {
                console.log(`Error message: ${result.data.message}`);
            }
            if (result.data.error) {
                console.log(`Error details: ${result.data.error}`);
            }
        }

    } catch (error) {
        console.error('\n❌ Test error:', error.message);
        console.error(error.stack);
        await page.screenshot({ path: './screenshots/test_error.png' });
    }

    console.log('\n⏸️  Keeping browser open for 30 seconds...');
    await new Promise(resolve => setTimeout(resolve, 30000));

    await browser.close();
    console.log('🏁 Test completed');
}

// Run the test
testCompleteResearch().catch(console.error);
