const puppeteer = require('puppeteer');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');
const AuthHelper = require('./auth_helper');

async function testMetricsPage() {
    console.log('🚀 Starting browser test of metrics page...');

    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();

    // Set longer timeout for CI environments
    page.setDefaultTimeout(30000);
    page.setDefaultNavigationTimeout(30000);

    // Create auth helper
    const authHelper = new AuthHelper(page);

    // Listen to console logs from the page
    page.on('console', msg => {
        const type = msg.type();
        const text = msg.text();
        console.log(`📝 [${type.toUpperCase()}] ${text}`);
    });

    // Listen to JavaScript errors
    page.on('pageerror', error => {
        console.log(`❌ [ERROR] ${error.message}`);
    });

    // Listen to failed requests
    page.on('requestfailed', request => {
        console.log(`🔴 [REQUEST FAILED] ${request.url()} - ${request.failure().errorText}`);
    });

    try {
        // Ensure we're authenticated first
        console.log('🔐 Ensuring authentication...');
        await authHelper.ensureAuthenticated();

        console.log('📄 Navigating to metrics page...');
        await page.goto('http://127.0.0.1:5000/metrics/', {
            waitUntil: 'domcontentloaded',
            timeout: 30000
        });

        console.log('✅ Page loaded successfully');

        // Wait a bit for JavaScript to execute
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if specific elements are visible
        const loadingVisible = await page.evaluate(() => {
            const loading = document.getElementById('loading');
            return loading ? window.getComputedStyle(loading).display !== 'none' : false;
        });

        const contentVisible = await page.evaluate(() => {
            const content = document.getElementById('metrics-content');
            return content ? window.getComputedStyle(content).display !== 'none' : false;
        });

        const errorVisible = await page.evaluate(() => {
            const error = document.getElementById('error');
            return error ? window.getComputedStyle(error).display !== 'none' : false;
        });

        console.log(`🔍 Element visibility check:`);
        console.log(`   Loading: ${loadingVisible}`);
        console.log(`   Content: ${contentVisible}`);
        console.log(`   Error: ${errorVisible}`);

        // Check if token values are populated
        const tokenValues = await page.evaluate(() => {
            const totalTokens = document.getElementById('total-tokens');
            const totalResearches = document.getElementById('total-researches');
            return {
                totalTokens: totalTokens ? totalTokens.textContent : 'NOT FOUND',
                totalResearches: totalResearches ? totalResearches.textContent : 'NOT FOUND'
            };
        });

        console.log(`📊 Token values:`);
        console.log(`   Total Tokens: ${tokenValues.totalTokens}`);
        console.log(`   Total Researches: ${tokenValues.totalResearches}`);


    } catch (error) {
        console.log(`❌ Error during test: ${error.message}`);
    } finally {
        await browser.close();
        console.log('🏁 Browser test completed');
    }
}

testMetricsPage().catch(console.error);
