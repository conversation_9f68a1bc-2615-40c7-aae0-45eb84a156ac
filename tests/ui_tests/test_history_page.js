const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

// Test configuration
const BASE_URL = 'http://127.0.0.1:5000';

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, type = 'info') {
    const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
    const typeColors = {
        'info': colors.cyan,
        'success': colors.green,
        'error': colors.red,
        'warning': colors.yellow,
        'section': colors.blue
    };
    const color = typeColors[type] || colors.reset;
    console.log(`${color}[${timestamp}] ${message}${colors.reset}`);
}

async function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

async function createResearch(page, query, waitForCompletion = false) {
    log(`🔬 Creating research: "${query}"`, 'info');

    // Navigate to home/research page
    await page.goto(BASE_URL, { waitUntil: 'networkidle2' });

    // Wait for the query input
    await page.waitForSelector('#query', { timeout: 10000 });

    // Clear and type query
    await page.click('#query', { clickCount: 3 });
    await page.type('#query', query);

    // Submit the research
    const submitButton = await page.$('button[type="submit"]');
    if (submitButton) {
        await submitButton.click();
    } else {
        // Try pressing Enter
        await page.keyboard.press('Enter');
    }

    // Wait for navigation or status update
    await delay(3000);

    if (waitForCompletion) {
        log('⏳ Waiting for research to complete...', 'info');

        // Wait for completion (max 30 seconds for test)
        const startTime = Date.now();
        const maxWaitTime = 30000;

        while (Date.now() - startTime < maxWaitTime) {
            try {
                // Check if we're on results page
                const url = page.url();
                if (url.includes('/results/') || url.includes('/research/')) {
                    // Check for completion indicators
                    const completed = await page.evaluate(() => {
                        const statusEl = document.querySelector('.status-badge, .research-status');
                        if (statusEl && statusEl.textContent.toLowerCase().includes('completed')) {
                            return true;
                        }
                        const progressEl = document.querySelector('.progress-bar, .progress');
                        if (progressEl && progressEl.textContent.includes('100%')) {
                            return true;
                        }
                        return false;
                    });

                    if (completed) {
                        log('✅ Research completed', 'success');
                        break;
                    }
                }
            } catch (e) {
                // Continue waiting
            }

            await delay(1000);
        }
    }
}

async function testHistoryPage() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();

    // Set console log handler
    page.on('console', msg => {
        if (msg.type() === 'error') {
            log(`Browser console error: ${msg.text()}`, 'error');
        }
    });

    try {
        // Create auth helper and register user
        const authHelper = new AuthHelper(page, BASE_URL);
        const timestamp = Date.now();
        const testUsername = `history_test_${timestamp}`;
        const testPassword = 'TestPass123!';

        log('📝 Registering new user...', 'info');
        await authHelper.register(testUsername, testPassword);

        // Verify we're logged in
        const isLoggedIn = await authHelper.isLoggedIn();
        if (!isLoggedIn) {
            throw new Error('Failed to login after registration');
        }
        log('✅ User registered and logged in', 'success');

        // Create some research items for history
        log('\n=== CREATING TEST RESEARCH ITEMS ===', 'section');
        const queries = [
            'What is machine learning?',
            'History of artificial intelligence'
        ];

        for (const query of queries) {
            await createResearch(page, query, false); // Don't wait for full completion
            await delay(2000); // Wait between researches
        }

        // Navigate to history page
        log('\n=== TESTING HISTORY PAGE ===', 'section');
        await page.goto(`${BASE_URL}/history`, { waitUntil: 'networkidle2' });

        // Wait for history content to load
        await page.waitForSelector('body', { timeout: 10000 });

        // Check if we have history items or appropriate message
        const pageContent = await page.evaluate(() => {
            const historyItems = document.querySelectorAll('.history-item, .research-item, .list-group-item, [data-research-id]');
            const emptyMessage = document.querySelector('.empty-state, .no-history, .alert-info');

            return {
                title: document.title,
                hasHistoryContainer: !!document.querySelector('.history-container, .history-list, .research-history'),
                itemCount: historyItems.length,
                hasEmptyMessage: !!emptyMessage,
                emptyMessageText: emptyMessage?.textContent || '',
                items: Array.from(historyItems).slice(0, 5).map(item => ({
                    text: item.textContent?.trim().substring(0, 100) || '',
                    hasLink: !!item.querySelector('a'),
                    hasDeleteButton: !!item.querySelector('.delete-btn, .btn-danger, [onclick*="delete"]')
                }))
            };
        });

        log(`📊 History page content:`, 'info');
        log(`  - Title: ${pageContent.title}`, 'info');
        log(`  - Has history container: ${pageContent.hasHistoryContainer}`, 'info');
        log(`  - Total items found: ${pageContent.itemCount}`, 'info');
        log(`  - Has empty message: ${pageContent.hasEmptyMessage}`, 'info');

        if (pageContent.itemCount > 0) {
            log('✅ History items found on page', 'success');

            // Log first few items
            pageContent.items.forEach((item, index) => {
                log(`  - Item ${index + 1}: ${item.text.substring(0, 50)}...`, 'info');
            });
        } else if (pageContent.hasEmptyMessage) {
            log(`ℹ️ Empty history message: ${pageContent.emptyMessageText.substring(0, 100)}`, 'info');
            log('⚠️ No history items found (research might not have saved)', 'warning');
        } else {
            log('⚠️ No history items or empty message found', 'warning');
        }

        // Test search/filter if available
        const searchInput = await page.$('#search-input, input[type="search"], .search-input');
        if (searchInput && pageContent.itemCount > 0) {
            log('\n=== TESTING SEARCH/FILTER ===', 'section');
            await searchInput.type('machine learning');
            await delay(500); // Wait for filter to apply

            const filteredCount = await page.evaluate(() => {
                const visibleItems = document.querySelectorAll('.history-item:not([style*="display: none"]), .research-item:not(.d-none)');
                return visibleItems.length;
            });

            log(`📊 Filtered results: ${filteredCount} items`, 'info');

            if (filteredCount >= 0) {
                log('✅ Search field is present', 'success');
            }
        }

        // Test navigation elements
        const navigationElements = await page.evaluate(() => {
            return {
                hasPagination: !!document.querySelector('.pagination, .page-link'),
                hasBackButton: !!document.querySelector('a[href="/"], .back-btn'),
                hasNewResearchButton: !!document.querySelector('a[href="/research"], .new-research-btn')
            };
        });

        log('\n=== NAVIGATION ELEMENTS ===', 'section');
        log(`  - Has pagination: ${navigationElements.hasPagination}`, 'info');
        log(`  - Has back/home button: ${navigationElements.hasBackButton}`, 'info');
        log(`  - Has new research button: ${navigationElements.hasNewResearchButton}`, 'info');


        log('\n✅ History page test completed successfully!', 'success');

    } catch (error) {
        log(`\n❌ Test failed: ${error.message}`, 'error');


        throw error;
    } finally {
        await browser.close();
    }
}

// Run the test
testHistoryPage().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
});
