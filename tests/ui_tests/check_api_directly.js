#!/usr/bin/env node

/**
 * Direct API check for context overflow data
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');

const BASE_URL = 'http://127.0.0.1:5000';

async function checkAPI() {
    console.log('🔍 Checking Context Overflow API directly\n');

    let browser;
    let page;

    try {
        browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        page = await browser.newPage();
        const authHelper = new AuthHelper(page, BASE_URL);

        // Login with the specific test user
        console.log('📝 Logging in as testuser_1755337395122_150...');
        await authHelper.login('testuser_1755337395122_150', 'T3st!Secure#2024$LDR');
        console.log('✅ Logged in\n');

        // Make API request
        console.log('🔌 Fetching context overflow data...');
        const response = await page.evaluate(async (baseUrl) => {
            const res = await fetch(`${baseUrl}/metrics/api/context-overflow?period=all`, {
                method: 'GET',
                credentials: 'include',
                headers: {
                    'Accept': 'application/json'
                }
            });

            const data = await res.json();
            return data;
        }, BASE_URL);

        if (response.status === 'success') {
            console.log('✅ API Response successful\n');

            console.log('📊 Overview:');
            console.log(`  Total requests: ${response.overview.total_requests}`);
            console.log(`  Requests with context data: ${response.overview.requests_with_context_data}`);
            console.log(`  Truncated requests: ${response.overview.truncated_requests}`);
            console.log(`  Truncation rate: ${response.overview.truncation_rate}%`);
            console.log(`  Avg tokens truncated: ${response.overview.avg_tokens_truncated}\n`);

            if (response.model_stats && response.model_stats.length > 0) {
                console.log('🤖 Model Stats:');
                response.model_stats.forEach(stat => {
                    console.log(`  ${stat.model} (${stat.provider}):`);
                    console.log(`    Total: ${stat.total_requests}, Truncated: ${stat.truncated_count}`);
                    console.log(`    Rate: ${stat.truncation_rate}%, Avg Context: ${stat.avg_context_limit}`);
                });
                console.log('');
            }

            if (response.recent_truncated && response.recent_truncated.length > 0) {
                console.log('⚠️  Recent Truncated Requests:');
                response.recent_truncated.slice(0, 3).forEach(req => {
                    console.log(`  ${req.timestamp}: ${req.model}`);
                    console.log(`    Tokens: ${req.prompt_tokens}, Limit: ${req.context_limit}, Lost: ${req.tokens_truncated}`);
                });
                console.log('');
            }

            if (response.chart_data && response.chart_data.length > 0) {
                console.log(`📈 Chart data points: ${response.chart_data.length}`);

                // Check if any have context data
                const withContext = response.chart_data.filter(d => d.context_limit).length;
                const truncated = response.chart_data.filter(d => d.truncated).length;

                console.log(`  Points with context limit: ${withContext}`);
                console.log(`  Truncated points: ${truncated}\n`);

                // Show first few data points
                if (response.chart_data.length > 0) {
                    console.log('  Sample data points:');
                    response.chart_data.slice(0, 3).forEach(point => {
                        console.log(`    ${point.timestamp.substring(0, 19)}: ${point.model || 'N/A'}`);
                        console.log(`      Tokens: ${point.prompt_tokens}, Actual: ${point.actual_prompt_tokens}`);
                        console.log(`      Context Limit: ${point.context_limit || 'N/A'}, Truncated: ${point.truncated}`);
                    });
                }
            }

        } else {
            console.log(`❌ API Error: ${response.message || 'Unknown error'}`);
        }

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
}

checkAPI().catch(console.error);
