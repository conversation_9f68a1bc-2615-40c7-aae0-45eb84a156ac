/**
 * Test to verify news features work after JavaScript fix
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');

const BASE_URL = process.env.BASE_URL || 'http://127.0.0.1:5000';

async function testNewsFeatures() {
    const browser = await puppeteer.launch({
        headless: false,
        slowMo: 50,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 800 });

    const authHelper = new AuthHelper(page, BASE_URL);

    console.log('🧪 Testing News Features\n');

    try {
        // Login
        console.log('🔐 Logging in...');
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in\n');

        // Test 1: Template buttons work
        console.log('📄 Test 1: Testing template buttons');
        await page.goto(`${BASE_URL}/news`, {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Wait for page to load
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if template buttons exist
        const templateButtons = await page.$$('.template-btn');
        console.log(`  Found ${templateButtons.length} template buttons`);

        // Click Breaking News template
        const clicked = await page.evaluate(() => {
            const buttons = Array.from(document.querySelectorAll('.template-btn'));
            const breakingNewsBtn = buttons.find(b => b.textContent.includes('Breaking News'));
            if (breakingNewsBtn) {
                breakingNewsBtn.click();
                return true;
            }
            return false;
        });

        if (clicked) {
            console.log('✅ Clicked Breaking News template button');

            // Wait for navigation
            await page.waitForNavigation({ waitUntil: 'networkidle2' });

            const currentUrl = page.url();
            if (currentUrl.includes('/news/subscriptions/new')) {
                console.log('✅ Successfully navigated to subscription form');

                // Check if query is pre-filled
                const queryValue = await page.$eval('#subscription-query', el => el.value);
                console.log(`  Query pre-filled: ${queryValue ? 'Yes' : 'No'}`);

                // Go back to news page
                await page.goto(`${BASE_URL}/news`, {
                    waitUntil: 'networkidle2'
                });
            }
        } else {
            console.log('❌ Breaking News template button not found');
        }

        // Test 2: Create subscription from news item
        console.log('\n📰 Test 2: Testing subscription button on news items');

        // First, create a news search to get some results
        const searchQuery = 'Latest technology news';
        console.log(`  Running search: "${searchQuery}"`);

        await page.type('#news-search-input', searchQuery);
        await page.click('#advanced-search-btn');

        console.log('  Waiting for results...');
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Check for news items
        const newsItems = await page.$$('.news-item');
        console.log(`  Found ${newsItems.length} news items`);

        if (newsItems.length > 0) {
            // Check if Subscribe button exists
            const hasSubscribeButton = await page.evaluate(() => {
                const buttons = document.querySelectorAll('.news-item button');
                return Array.from(buttons).some(btn => btn.textContent.includes('Subscribe'));
            });

            if (hasSubscribeButton) {
                console.log('✅ Subscribe button found on news items');
            } else {
                console.log('⚠️  Subscribe button might only appear for items with queries');
            }
        }

        console.log('\n✅ All tests completed');

    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        await page.screenshot({
            path: 'screenshots/news_features_error.png',
            fullPage: true
        });
    } finally {
        console.log('\n🧹 Closing browser...');
        await browser.close();
    }
}

// Run the test
testNewsFeatures().catch(console.error);
