/**
 * Metrics Charts UI Test
 *
 * Tests the metrics dashboard to ensure both token consumption and search activity
 * charts are rendering correctly using Chart.js. Validates canvas elements and
 * takes screenshots for visual verification.
 *
 * Prerequisites: Web server running on http://127.0.0.1:5000
 *
 * Usage: node tests/ui_tests/test_metrics_charts.js
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testChartsScroll() {
    console.log('📊 Testing charts with scrolling...');

    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);
    await page.setViewport({ width: 1400, height: 900 });

    try {
        // Login first
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in');

        await page.goto(`${baseUrl}/metrics/`, {
            waitUntil: 'domcontentloaded',
            timeout: 10000
        });

        // Wait for metrics to load
        await new Promise(resolve => setTimeout(resolve, 6000));

        // Scroll to the charts section
        await page.evaluate(() => {
            const chartsSection = document.querySelector('.chart-container');
            if (chartsSection) {
                chartsSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
            } else {
                // Fallback: scroll to middle of page
                window.scrollTo(0, document.body.scrollHeight / 2);
            }
        });

        // Wait for scroll to complete
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Take screenshot of charts area
        await page.screenshot({ path: 'charts_scroll_test.png', fullPage: false });
        console.log('📸 Charts screenshot saved as charts_scroll_test.png');

        // Check if both charts have content
        const chartContent = await page.evaluate(() => {
            const tokenChart = document.getElementById('time-series-chart');
            const searchChart = document.getElementById('search-activity-chart');

            return {
                tokenChartExists: !!tokenChart,
                searchChartExists: !!searchChart,
                // Check if canvas has been drawn on (Chart.js renders to canvas)
                tokenChartCanvas: tokenChart ? tokenChart.tagName === 'CANVAS' : false,
                searchChartCanvas: searchChart ? searchChart.tagName === 'CANVAS' : false
            };
        });

        console.log('📊 Chart Canvas Check:');
        console.log(`   Token chart canvas: ${chartContent.tokenChartCanvas}`);
        console.log(`   Search chart canvas: ${chartContent.searchChartCanvas}`);

        await browser.close();
        console.log('🎉 Chart scroll test completed!');

    } catch (error) {
        console.log(`❌ Test failed: ${error.message}`);
        await browser.close();
    }
}

testChartsScroll().catch(console.error);
