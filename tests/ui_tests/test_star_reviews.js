/**
 * Star Reviews Page UI Test
 *
 * Tests the star reviews analytics page to ensure proper loading, chart rendering,
 * and data visualization. Validates API integration and user interface components.
 *
 * What this tests:
 * - Star reviews page loading and navigation
 * - API endpoint functionality (/metrics/api/star-reviews)
 * - Chart.js rendering for bar charts and line charts
 * - Period selector functionality
 * - Overall statistics display
 * - Rating distribution visualization
 * - Recent ratings list population
 *
 * Prerequisites: Web server running on http://127.0.0.1:5000
 *
 * Usage: node tests/ui_tests/test_star_reviews.js
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testStarReviews() {
    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());
    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    // Monitor console errors
    page.on('console', msg => {
        if (msg.type() === 'error') {
            console.log('❌ BROWSER ERROR:', msg.text());
        }
    });

    // Monitor network requests
    await page.setRequestInterception(true);
    page.on('request', request => {
        if (request.url().includes('/star-reviews')) {
            console.log('→ REQUEST:', request.method(), request.url());
        }
        request.continue();
    });

    page.on('response', response => {
        if (response.url().includes('/star-reviews')) {
            console.log('← RESPONSE:', response.status(), response.url());
        }
    });

    try {
        console.log('🌟 Testing star reviews page...');

        // Ensure authenticated before accessing metrics
        await authHelper.ensureAuthenticated();

        // First test navigation from metrics page
        await page.goto('http://127.0.0.1:5000/metrics/', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Look for star reviews link
        console.log('🔗 Looking for star reviews navigation link...');
        const starReviewsLink = await page.$('a[href="/metrics/star-reviews"]');

        if (starReviewsLink) {
            console.log('✅ Found star reviews link, clicking...');
            await starReviewsLink.click();

            // Wait for navigation
            await page.waitForNavigation({ waitUntil: 'networkidle2' });
        } else {
            console.log('⚠️  Star reviews link not found, navigating directly...');
            await page.goto('http://127.0.0.1:5000/metrics/star-reviews', {
                waitUntil: 'networkidle2',
                timeout: 30000
            });
        }

        console.log('📊 Checking star reviews page elements...');

        // Wait for content to load
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Check for main page elements
        const pageElements = await page.evaluate(() => {
            return {
                title: !!document.querySelector('h1'),
                periodSelector: !!document.querySelector('#period-select'),
                overallStats: !!document.querySelector('.overall-stats'),
                avgRating: !!document.querySelector('#avg-rating'),
                totalRatings: !!document.querySelector('#total-ratings'),
                ratingDistribution: !!document.querySelector('.rating-distribution'),
                llmChart: !!document.querySelector('#llm-ratings-chart'),
                searchEngineChart: !!document.querySelector('#search-engine-ratings-chart'),
                trendsChart: !!document.querySelector('#rating-trends-chart'),
                recentRatings: !!document.querySelector('#recent-ratings'),
                backLink: !!document.querySelector('a[href="/metrics/"]')
            };
        });

        console.log('📋 Page Elements Check:');
        Object.entries(pageElements).forEach(([element, exists]) => {
            console.log(`   ${exists ? '✅' : '❌'} ${element}: ${exists}`);
        });

        // Test period selector
        console.log('🕐 Testing period selector...');
        const periodSelect = await page.$('#period-select');
        if (periodSelect) {
            // Change to "7d" period
            await page.select('#period-select', '7d');
            console.log('✅ Period selector changed to 7 days');

            // Wait for data reload
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        // Check if charts are rendered (Canvas elements)
        const chartStatus = await page.evaluate(() => {
            const llmChart = document.getElementById('llm-ratings-chart');
            const searchChart = document.getElementById('search-engine-ratings-chart');
            const trendsChart = document.getElementById('rating-trends-chart');

            return {
                llmChartRendered: llmChart && llmChart.tagName === 'CANVAS',
                searchChartRendered: searchChart && searchChart.tagName === 'CANVAS',
                trendsChartRendered: trendsChart && trendsChart.tagName === 'CANVAS'
            };
        });

        console.log('📊 Chart Rendering Check:');
        Object.entries(chartStatus).forEach(([chart, rendered]) => {
            console.log(`   ${rendered ? '✅' : '❌'} ${chart}: ${rendered}`);
        });

        // Test back navigation
        console.log('🔙 Testing back navigation...');
        const backLink = await page.$('a[href="/metrics/"]');
        if (backLink) {
            console.log('✅ Back link found and functional');
        }


        console.log('🎉 Star reviews page test completed successfully!');

    } catch (error) {
        console.error('❌ Test error:', error);
    }

    await browser.close();
}

testStarReviews();
