const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

// Explore the settings page to find input fields
(async () => {
    let browser;

    try {
        console.log('=== Exploring Settings Page ===');

    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

        const page = await browser.newPage();
        await page.setViewport({ width: 1920, height: 1080 });

        const authHelper = new AuthHelper(page);

        // Create and login user
        const timestamp = Date.now();
        const testUsername = `explore_test_${timestamp}`;
        await authHelper.register(testUsername, 'testpass123');

        console.log('User registered and logged in');

        // Navigate to settings
        await page.goto('http://localhost:5000/settings', { waitUntil: 'networkidle2' });
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Get page title
        const title = await page.title();
        console.log(`\nPage title: ${title}`);

        // Find all forms
        const forms = await page.evaluate(() => {
            const formElements = document.querySelectorAll('form');
            return Array.from(formElements).map((form, index) => ({
                index,
                id: form.id,
                action: form.action,
                method: form.method,
                inputCount: form.querySelectorAll('input').length
            }));
        });

        console.log(`\nFound ${forms.length} forms:`);
        forms.forEach(form => {
            console.log(`  Form ${form.index}: id="${form.id}", action="${form.action}", method="${form.method}", inputs=${form.inputCount}`);
        });

        // Find all input fields
        const inputs = await page.evaluate(() => {
            const inputElements = document.querySelectorAll('input');
            return Array.from(inputElements).map(input => ({
                type: input.type,
                name: input.name,
                id: input.id,
                placeholder: input.placeholder,
                value: input.value ? (input.type === 'password' ? '***' : input.value.substring(0, 20)) : '',
                dataAttributes: Object.keys(input.dataset),
                visible: input.offsetParent !== null
            }));
        });

        console.log(`\nFound ${inputs.length} input fields:`);

        // Filter for API key related fields
        const apiKeyFields = inputs.filter(input =>
            input.name.includes('api_key') ||
            input.id.includes('api_key') ||
            input.placeholder?.toLowerCase().includes('api') ||
            input.name.includes('openai') ||
            input.id.includes('openai')
        );

        console.log(`\nAPI key related fields (${apiKeyFields.length}):`);
        apiKeyFields.forEach(field => {
            console.log(`  ${field.type} - name: "${field.name}", id: "${field.id}", visible: ${field.visible}`);
            if (field.dataAttributes.length > 0) {
                console.log(`    Data attributes: ${field.dataAttributes.join(', ')}`);
            }
        });

        // Look for password type inputs
        const passwordFields = inputs.filter(input => input.type === 'password');
        console.log(`\nPassword fields (${passwordFields.length}):`);
        passwordFields.forEach(field => {
            console.log(`  name: "${field.name}", id: "${field.id}", visible: ${field.visible}`);
        });

        // Check for navigation or tabs
        const navLinks = await page.evaluate(() => {
            const links = document.querySelectorAll('a[href*="settings"], .nav-link, .tab-link, [role="tab"]');
            return Array.from(links).map(link => ({
                text: link.textContent.trim(),
                href: link.href,
                classes: link.className
            }));
        });

        console.log(`\nNavigation/Tab links (${navLinks.length}):`);
        navLinks.forEach(link => {
            if (link.text.toLowerCase().includes('llm') || link.text.toLowerCase().includes('api') || link.text.toLowerCase().includes('openai')) {
                console.log(`  "${link.text}" - ${link.href}`);
            }
        });

        // Check if we need to expand any sections
        const expandables = await page.evaluate(() => {
            const elements = document.querySelectorAll('[data-toggle="collapse"], .collapse, .accordion-button, details');
            return Array.from(elements).map(el => ({
                tag: el.tagName,
                text: el.textContent.substring(0, 50),
                classes: el.className
            }));
        });

        if (expandables.length > 0) {
            console.log(`\nFound ${expandables.length} expandable sections`);
        }

        // Take screenshot
        await page.screenshot({
            path: `/tmp/settings_exploration_${timestamp}.png`,
            fullPage: true
        });
        console.log(`\nScreenshot saved: /tmp/settings_exploration_${timestamp}.png`);

        // Try to find LLM section and click it
        const llmSection = await page.$('a[href*="llm"], [data-section="llm"], .nav-link:has-text("LLM")');
        if (llmSection) {
            console.log('\nFound LLM section, clicking...');
            await llmSection.click();
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Re-scan for inputs after clicking
            const newInputs = await page.evaluate(() => {
                const inputElements = document.querySelectorAll('input[name*="openai"], input[id*="openai"]');
                return Array.from(inputElements).map(input => ({
                    type: input.type,
                    name: input.name,
                    id: input.id,
                    visible: input.offsetParent !== null
                }));
            });

            console.log(`\nOpenAI-related inputs after clicking LLM section (${newInputs.length}):`);
            newInputs.forEach(field => {
                console.log(`  ${field.type} - name: "${field.name}", id: "${field.id}", visible: ${field.visible}`);
            });
        }

    } catch (error) {
        console.error('Error:', error.message);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
})();
