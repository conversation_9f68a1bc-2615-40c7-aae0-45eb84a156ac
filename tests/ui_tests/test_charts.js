const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { BROWSER_CONFIG, PAGE_CONFIG } = require('./browser_config');

async function testCharts() {
    console.log('🎯 Testing chart functionality...');

    const browser = await puppeteer.launch(BROWSER_CONFIG);

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    // Handle any dialogs that appear
    page.on('dialog', async dialog => {
        console.log(`📢 Dialog appeared: ${dialog.type()} - ${dialog.message()}`);
        await dialog.dismiss();
    });

    // Listen to console logs to see chart creation
    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('chart') || text.includes('Chart') || text.includes('METRICS') || text.includes('displaying')) {
            console.log(`📝 ${text}`);
        }
    });

    // Listen to errors
    page.on('pageerror', error => {
        console.log(`❌ JS Error: ${error.message}`);
    });

    try {
        // Login first
        await authHelper.ensureAuthenticated();
        console.log('✅ Logged in');

        await page.goto(`${baseUrl}/metrics/`, {
            waitUntil: 'domcontentloaded',
            timeout: 10000
        });

        // Wait for all processing to complete
        await new Promise(resolve => setTimeout(resolve, 6000));

        // Check if charts were created
        const chartInfo = await page.evaluate(() => {
            const tokenChart = document.getElementById('time-series-chart');
            const searchChart = document.getElementById('search-activity-chart');

            return {
                hasTokenChart: !!tokenChart,
                hasSearchChart: !!searchChart,
                tokenChartVisible: tokenChart ? window.getComputedStyle(tokenChart).display !== 'none' : false,
                searchChartVisible: searchChart ? window.getComputedStyle(searchChart).display !== 'none' : false,
                // Check if Chart.js created canvas charts
                timeSeriesChartExists: !!window.timeSeriesChart,
                searchActivityChartExists: !!window.searchActivityChart
            };
        });

        console.log('📊 Chart Analysis:');
        console.log(`   Token chart element: ${chartInfo.hasTokenChart}`);
        console.log(`   Search chart element: ${chartInfo.hasSearchChart}`);
        console.log(`   Token chart visible: ${chartInfo.tokenChartVisible}`);
        console.log(`   Search chart visible: ${chartInfo.searchChartVisible}`);
        console.log(`   Time series chart object: ${chartInfo.timeSeriesChartExists}`);
        console.log(`   Search activity chart object: ${chartInfo.searchActivityChartExists}`);

        // Take screenshot
        await page.screenshot({ path: 'charts_test.png' });
        console.log('📸 Screenshot saved as charts_test.png');

        await browser.close();

        const success = chartInfo.hasTokenChart && chartInfo.hasSearchChart;
        console.log(success ? '🎉 Charts test PASSED!' : '💥 Charts test FAILED!');
        return success;

    } catch (error) {
        console.log(`❌ Test failed: ${error.message}`);
        await browser.close();
        return false;
    }
}

testCharts().catch(console.error);
