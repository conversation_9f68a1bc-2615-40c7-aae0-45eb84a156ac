# UI Tests

Browser-based testing using P<PERSON><PERSON>eer to verify that web pages load correctly and JavaScript executes without errors.

## Setup

Install Puppeteer (if not already installed):
```bash
npm install puppeteer
```

## Running Tests

### Run All UI Tests (Recommended)
```bash
cd /path/to/local-deep-research
node tests/ui_tests/run_all_tests.js
```

### Run All Page Tests
```bash
cd /path/to/local-deep-research
node tests/ui_tests/test_pages_browser.js
```

### Run Individual Component Tests
```bash
# Test metrics charts functionality
node tests/ui_tests/test_metrics_charts.js

# Test research results page loading
node tests/ui_tests/test_research_results.js

# Test settings page functionality
node tests/ui_tests/test_settings_page.js

# Test settings error detection
node tests/ui_tests/test_settings_errors.js

# Test settings save functionality
node tests/ui_tests/test_settings_save.js

# Test star reviews analytics page
node tests/ui_tests/test_star_reviews.js
```

### Prerequisites
- Web server must be running on http://127.0.0.1:5000
- Start the server with: `python -m local_deep_research.web.app`

## What These Tests Check

### All Pages
- Page loads without timeout
- Has title and body elements
- No critical JavaScript errors
- Basic DOM structure is present

### Metrics Dashboard (`/metrics/`)
- Loading, content, and error elements exist
- Metrics data loads (token counts, research counts)
- JavaScript executes without syntax errors
- Takes screenshot for visual debugging

### Research Page (`/`)
- Query input field exists and is enabled
- Submit button exists and is enabled
- Mode selection dropdown exists

### History Page (`/history/`)
- History container elements exist
- Search functionality is present
- Content is visible

### Settings Page (`/settings/`)
- Forms and input elements exist
- Save buttons are present
- Configuration interface loads

## Individual Component Tests

### Metrics Charts Test (`test_metrics_charts.js`)
- Verifies both token consumption and search activity charts render correctly
- Checks for Canvas elements (Chart.js charts)
- Scrolls through the page to test dynamic loading
- Takes screenshot for visual verification
- Validates chart data loading from API endpoints

### Research Results Test (`test_research_results.js`)
- Tests loading of specific research report (research ID 67)
- Monitors network requests to identify API failures
- Checks for error messages vs. actual content
- Validates research report file loading functionality

### Settings Page Test (`test_settings_page.js`)
- Comprehensive test of settings page loading
- Monitors all API calls (available models, search engines, settings)
- Counts setting form elements to verify proper loading
- Validates 300+ setting elements are present on the page
- Tests settings synchronization and form functionality

### Settings Error Detection Test (`test_settings_errors.js`)
- Tests for error messages when changing setting values
- Monitors browser console for JavaScript errors
- Detects network errors (4xx/5xx HTTP responses)
- Searches for error DOM elements (.error, .alert-danger, etc.)
- Simulates user interaction with dropdowns and input fields
- Validates error handling and user feedback mechanisms

### Settings Save Test (`test_settings_save.js`)
- Tests the complete settings save workflow
- Monitors network requests to `/research/settings/save_all_settings`
- Validates save button functionality and form submission
- Checks for proper API response handling (success/error states)
- Monitors console logging during save operations
- Verifies success/error message display to users
- Tests CSRF token handling and form validation

### Star Reviews Test (`test_star_reviews.js`)
- Tests the star reviews analytics page and navigation
- Monitors API endpoint `/metrics/api/star-reviews` functionality
- Validates Chart.js rendering for bar charts and line charts
- Tests period selector and data filtering
- Checks overall statistics display and rating distribution
- Verifies recent ratings list population
- Tests navigation between metrics dashboard and star reviews page

## Testing Strategy

The UI test suite follows a layered approach:

### 1. **Page Load Tests** (`test_pages_browser.js`)
- **Purpose**: Verify basic page functionality works across all routes
- **Coverage**: Home, metrics, history, settings pages
- **Validates**: Page loads, DOM structure, basic JavaScript execution

### 2. **Component-Specific Tests**
- **Purpose**: Deep dive into specific UI components and workflows
- **Coverage**: Charts, forms, API integrations, file loading
- **Validates**: Complex interactions, data flow, error handling

### 3. **Error Detection Tests**
- **Purpose**: Catch JavaScript errors and API failures that could break user experience
- **Coverage**: Console errors, network failures, validation errors
- **Validates**: Proper error handling and user feedback

### 4. **Integration Tests**
- **Purpose**: Test complete user workflows end-to-end
- **Coverage**: Settings save, research report loading, chart rendering
- **Validates**: Data persistence, file operations, API coordination

## What Problems These Tests Solve

1. **Regression Detection**: Catch when code changes break existing functionality
2. **JavaScript Error Monitoring**: Identify runtime errors that users would encounter
3. **API Integration Validation**: Ensure frontend properly communicates with backend
4. **File Path Resolution**: Verify reports and assets load correctly regardless of server setup
5. **Cross-Browser Compatibility**: Puppeteer testing ensures consistent behavior
6. **Performance Monitoring**: Detect slow loading or hanging operations

## Output

- Console logs show detailed test progress
- Screenshots saved to `tests/ui_tests/screenshots/`
- Summary report shows pass/fail status for each page

## Why These Tests Are Useful

1. **Catch JavaScript Errors**: Unlike unit tests, these catch runtime JS errors in the browser
2. **End-to-End Validation**: Verify the complete request → response → render cycle
3. **Regression Prevention**: Detect when changes break the UI
4. **Cross-Page Coverage**: Test all major application pages consistently

## Example Output

```
🚀 Starting browser test session...

📄 Testing Metrics Dashboard: http://127.0.0.1:5000/metrics/
📝 [LOG] === METRICS SCRIPT STARTED ===
📝 [LOG] === STARTING LOADMETRICS ===
📝 [LOG] Basic data success, setting metricsData
✅ Metrics Dashboard loaded successfully
🔍 Basic checks for Metrics Dashboard:
   Has title: true
   Has body: true
   Body visible: true
📊 Metrics page checks:
   Content visible: true
   Total tokens: 41,801
   Total researches: 11

==================================================
📋 TEST SUMMARY
==================================================
✅ PASS Home/Research Page
✅ PASS Metrics Dashboard
✅ PASS History Page
✅ PASS Settings Page

Total: 4 tests
Passed: 4
Failed: 0
🎉 All tests passed!
```

## Adding New Page Tests

To test a new page, add an entry to the `testCases` array in `test_pages_browser.js`:

```javascript
{ path: '/new-page/', name: 'New Page', tests: newPageTests }
```

Then create a custom test function:

```javascript
const newPageTests = async (page) => {
    console.log('🧪 Running new page tests...');

    const checks = await page.evaluate(() => {
        // Check for page-specific elements
        return {
            hasSpecificElement: !!document.getElementById('specific-element')
        };
    });

    console.log(`Specific element present: ${checks.hasSpecificElement}`);
};
```
