const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

// Simple test to verify API key can be set and retrieved
(async () => {
    let browser;

    try {
        console.log('=== Simple API Key Verification Test ===\n');

        const TEST_API_KEY = 'sk-test-' + Date.now();
        const BASE_URL = 'http://127.0.0.1:5000';

    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

        const page = await browser.newPage();
        const authHelper = new AuthHelper(page, BASE_URL);

        // Create and authenticate user
        const testUser = `test_${Date.now()}`;
        await authHelper.register(testUser, 'testpass123');
        console.log(`✓ User ${testUser} created\n`);

        // Get CSRF token
        await page.goto(`${BASE_URL}/settings`, { waitUntil: 'networkidle2' });
        const csrfToken = await page.evaluate(() => {
            const meta = document.querySelector('meta[name="csrf-token"]');
            return meta ? meta.content : null;
        });

        if (!csrfToken) {
            throw new Error('CSRF token not found');
        }

        // Test 1: Set API key
        console.log('Test 1: Setting API key...');
        const setResult = await page.evaluate(async (key, token) => {
            const response = await fetch('/settings/api/llm.openai_endpoint.api_key', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': token
                },
                credentials: 'same-origin',
                body: JSON.stringify({ value: key })
            });
            return {
                status: response.status,
                data: await response.json()
            };
        }, TEST_API_KEY, csrfToken);

        console.log(`Status: ${setResult.status}`);
        console.log(`Response: ${JSON.stringify(setResult.data)}\n`);

        // Test 2: Get API key back
        console.log('Test 2: Getting API key...');
        const getResult = await page.evaluate(async () => {
            const response = await fetch('/settings/api/llm.openai_endpoint.api_key', {
                credentials: 'same-origin'
            });
            return await response.json();
        });

        console.log(`Retrieved value: ${getResult.value || 'empty'}`);
        console.log(`Match: ${getResult.value === TEST_API_KEY ? '✓' : '✗'}\n`);

        // Test 3: Get all settings to see if it's there
        console.log('Test 3: Checking in all settings...');
        const allSettings = await page.evaluate(async () => {
            const response = await fetch('/settings/api', {
                credentials: 'same-origin'
            });
            const data = await response.json();
            return {
                hasKey: 'llm.openai_endpoint.api_key' in data,
                value: data['llm.openai_endpoint.api_key']?.value
            };
        });

        console.log(`Found in all settings: ${allSettings.hasKey ? '✓' : '✗'}`);
        console.log(`Value in all settings: ${allSettings.value || 'empty'}\n`);

        // Test 4: Try alternative update method
        console.log('Test 4: Using bulk update...');
        const bulkResult = await page.evaluate(async (key, token) => {
            const response = await fetch('/settings/save_all_settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': token
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    'llm.openai_endpoint.api_key': key + '-bulk'
                })
            });
            return {
                status: response.status,
                data: await response.json()
            };
        }, TEST_API_KEY, csrfToken);

        console.log(`Bulk update status: ${bulkResult.status}`);
        console.log(`Bulk update response: ${JSON.stringify(bulkResult.data)}\n`);

        // Verify bulk update
        const bulkVerify = await page.evaluate(async () => {
            const response = await fetch('/settings/api/llm.openai_endpoint.api_key', {
                credentials: 'same-origin'
            });
            return await response.json();
        });

        console.log(`Value after bulk update: ${bulkVerify.value || 'empty'}`);
        console.log(`Bulk update worked: ${bulkVerify.value === TEST_API_KEY + '-bulk' ? '✓' : '✗'}\n`);

        // Summary
        console.log('=== Summary ===');
        console.log(`Individual PUT: ${getResult.value === TEST_API_KEY ? '✓ Working' : '✗ Not working'}`);
        console.log(`Bulk POST: ${bulkVerify.value === TEST_API_KEY + '-bulk' ? '✓ Working' : '✗ Not working'}`);

    } catch (error) {
        console.error(`\n❌ Error: ${error.message}`);
    } finally {
        if (browser) {
            await browser.close();
        }
    }
})();
