/**
 * Authentication Flow Test
 * Tests registration, login, and logout functionality
 */

const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');
const fs = require('fs');
const path = require('path');

async function testAuthFlow() {
    // Create screenshots directory if it doesn't exist
    const screenshotsDir = path.join(__dirname, 'screenshots');
    if (!fs.existsSync(screenshotsDir)) {
        fs.mkdirSync(screenshotsDir, { recursive: true });
    }

    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    // Test credentials
    const testUser = {
        username: `testuser_${Date.now()}`,  // Unique username
        password: 'testpassword123'  // pragma: allowlist secret
    };

    console.log('🧪 Starting authentication flow test...\n');

    try {
        // Test 1: Registration
        console.log('📝 Test 1: Registration');
        await authHelper.register(testUser.username, testUser.password);

        // Verify we're logged in after registration
        console.log('Current URL after registration:', page.url());

        // Wait a bit for any redirects to complete
        await new Promise(resolve => setTimeout(resolve, 2000));

        const isLoggedIn = await authHelper.isLoggedIn();
        if (isLoggedIn) {
            console.log('✅ Registration successful and auto-logged in');
        } else {
            // Debug: Check what's on the page
            const pageTitle = await page.title();
            console.log('Page title:', pageTitle);

            // Check for any alerts or messages
            const alerts = await page.$$('.alert');
            console.log('Number of alerts on page:', alerts.length);

            throw new Error('Not logged in after registration');
        }

        // Take screenshot of logged-in state
        await page.screenshot({ path: './screenshots/after_registration.png' });

        // Test 2: Logout
        console.log('\n🚪 Test 2: Logout');
        await authHelper.logout();

        // Verify we're logged out
        const isLoggedOut = !(await authHelper.isLoggedIn());
        if (isLoggedOut) {
            console.log('✅ Logout successful');
        } else {
            throw new Error('Still logged in after logout');
        }

        // Test 3: Login
        console.log('\n🔐 Test 3: Login');
        await authHelper.login(testUser.username, testUser.password);

        // Verify we're logged in
        const isLoggedInAgain = await authHelper.isLoggedIn();
        if (isLoggedInAgain) {
            console.log('✅ Login successful');
        } else {
            throw new Error('Not logged in after login');
        }

        // Test 4: Navigate to protected pages
        console.log('\n📄 Test 4: Access protected pages');
        const protectedPages = [
            { url: '/', name: 'Home' },
            { url: '/settings/', name: 'Settings' },
            { url: '/metrics/', name: 'Metrics' },
            { url: '/history/', name: 'History' }
        ];

        for (const pageInfo of protectedPages) {
            await page.goto(`${baseUrl}${pageInfo.url}`, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            // Check we didn't get redirected to login
            const currentUrl = page.url();
            if (!currentUrl.includes('/auth/login')) {
                console.log(`✅ Successfully accessed ${pageInfo.name} page`);
            } else {
                throw new Error(`Redirected to login when accessing ${pageInfo.name}`);
            }
        }

        // Test 5: Test with existing user (should login, not register)
        console.log('\n🔄 Test 5: Ensure authenticated with existing user');
        await authHelper.ensureAuthenticated(testUser.username, testUser.password);
        console.log('✅ Ensure authenticated works with existing user');

        console.log('\n🎉 All authentication tests passed!');

    } catch (error) {
        console.error('\n❌ Test failed:', error.message);

        // Take error screenshot
        await page.screenshot({ path: './screenshots/auth_error.png' });
        console.log('📸 Error screenshot saved');

        // Check current URL for debugging
        console.log('Current URL:', page.url());

        // Check for error messages on the page
        try {
            const errorText = await page.$eval('.alert-danger, .error-message', el => el.textContent);
            console.log('Error message on page:', errorText);
        } catch (e) {
            // No error message found
        }

        process.exit(1);
    }

    await browser.close();
    console.log('\n✅ Test completed successfully');
}

// Run the test
testAuthFlow().catch(console.error);
