#!/usr/bin/env python3
"""
Test direct database insertion with UUID
"""

import os
import uuid
from datetime import datetime
from pathlib import Path

# Allow unencrypted databases for testing
os.environ["LDR_ALLOW_UNENCRYPTED"] = "true"

import sys

sys.path.insert(
    0,
    str(Path(__file__).parent.parent.parent.resolve()),
)

from sqlalchemy import inspect

from src.local_deep_research.database.auth_db import get_auth_db_session
from src.local_deep_research.database.encrypted_db import db_manager
from src.local_deep_research.database.models.auth import User
from src.local_deep_research.database.models.research import ResearchHistory


def test_direct_uuid_insertion():
    """Test inserting research with UUID directly into database"""

    # Get the most recent test user
    auth_db = get_auth_db_session()
    test_user = (
        auth_db.query(User)
        .filter(User.username.like("test_uuid_fresh_%"))
        .order_by(User.created_at.desc())
        .first()
    )
    auth_db.close()

    if not test_user:
        print("No test user found")
        return False

    username = test_user.username
    print(f"Using user: {username}")

    # Open their database
    engine = db_manager.open_user_database(username, "T3st!Secure#2024$LDR")
    if not engine:
        print("Failed to open database")
        return False

    # Check table schema
    inspector = inspect(engine)
    columns = inspector.get_columns("research_history")
    print("\nTable schema:")
    for col in columns:
        print(f"  {col['name']}: {col['type']}")

    # Get session factory
    Session = db_manager.Session

    with Session() as session:
        try:
            # Create a new research with UUID
            research_id = str(uuid.uuid4())
            print(f"\nCreating research with UUID: {research_id}")

            research = ResearchHistory(
                id=research_id,
                query="Direct UUID test",
                mode="quick",
                status="in_progress",
                created_at=datetime.utcnow().isoformat(),
                progress_log=[
                    {"time": datetime.utcnow().isoformat(), "progress": 0}
                ],
                research_meta={"test": "direct_uuid"},
            )

            session.add(research)
            session.commit()

            print("✅ Successfully created research with UUID!")

            # Verify it was saved
            saved_research = (
                session.query(ResearchHistory).filter_by(id=research_id).first()
            )
            if saved_research:
                print("\nVerified in database:")
                print(f"  ID: {saved_research.id}")
                print(f"  Type: {type(saved_research.id).__name__}")
                print(f"  Query: {saved_research.query}")

            # List all research entries
            all_research = session.query(ResearchHistory).all()
            print(f"\nTotal research entries: {len(all_research)}")
            for r in all_research:
                print(f"  - {r.id} ({type(r.id).__name__}): {r.query}")

            return True

        except Exception as e:
            print(f"❌ Error creating research: {e}")
            import traceback

            traceback.print_exc()
            session.rollback()
            return False

    db_manager.close_user_database(username)


if __name__ == "__main__":
    print("=" * 60)
    print("Direct UUID Insertion Test")
    print("=" * 60)

    success = test_direct_uuid_insertion()

    print("\n" + "=" * 60)
    print("✅ Test passed" if success else "❌ Test failed")
    print("=" * 60)
