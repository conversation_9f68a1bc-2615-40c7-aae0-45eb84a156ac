const puppeteer = require('puppeteer');
const AuthHelper = require('./auth_helper');
const { getPuppeteerLaunchOptions } = require('./puppeteer_config');

async function testNetworkRequests() {
    console.log('🌐 Testing network requests for research results page...');

    const browser = await puppeteer.launch(getPuppeteerLaunchOptions());

    const page = await browser.newPage();
    const baseUrl = 'http://127.0.0.1:5000';
    const authHelper = new AuthHelper(page, baseUrl);

    // Track all network requests
    const requests = [];
    const responses = [];

    page.on('request', request => {
        requests.push({
            url: request.url(),
            method: request.method(),
            headers: request.headers()
        });
        console.log(`📤 REQUEST: ${request.method()} ${request.url()}`);
    });

    page.on('response', response => {
        responses.push({
            url: response.url(),
            status: response.status(),
            statusText: response.statusText()
        });

        const status = response.status();
        const emoji = status >= 400 ? '❌' : status >= 300 ? '⚠️' : '✅';
        console.log(`${emoji} RESPONSE: ${status} ${response.url()}`);
    });

    page.on('requestfailed', request => {
        console.log(`💥 FAILED REQUEST: ${request.url()} - ${request.failure().errorText}`);
    });

    page.on('console', msg => {
        const text = msg.text();
        if (text.includes('Error') || text.includes('error') || text.includes('failed')) {
            console.log(`📝 [ERROR] ${text}`);
        }
    });

    try {
        console.log('📄 Loading research results page and tracking requests...');

        await page.goto('http://127.0.0.1:5000/research/results/67', {
            waitUntil: 'networkidle0',
            timeout: 15000
        });

        // Wait a bit more for any async requests
        await new Promise(resolve => setTimeout(resolve, 3000));

        console.log('\n📊 REQUEST SUMMARY:');
        console.log(`Total requests: ${requests.length}`);
        console.log(`Total responses: ${responses.length}`);

        // Find failed requests
        const failedResponses = responses.filter(r => r.status >= 400);

        if (failedResponses.length > 0) {
            console.log('\n💥 FAILED REQUESTS:');
            failedResponses.forEach(r => {
                console.log(`   ${r.status} ${r.statusText} - ${r.url}`);
            });
        } else {
            console.log('✅ All requests succeeded!');
        }

        // Look for specific patterns
        const apiRequests = responses.filter(r => r.url.includes('/api/'));
        console.log(`\n🔌 API Requests: ${apiRequests.length}`);
        apiRequests.forEach(r => {
            const emoji = r.status >= 400 ? '❌' : '✅';
            console.log(`   ${emoji} ${r.status} ${r.url}`);
        });

        await browser.close();

    } catch (error) {
        console.log(`❌ Test failed: ${error.message}`);
        await browser.close();
    }
}

testNetworkRequests().catch(console.error);
