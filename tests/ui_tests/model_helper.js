/**
 * Model Helper
 * Utility functions for selecting models in tests
 */

/**
 * Select a model in the custom dropdown
 * @param {Page} page - Puppeteer page object
 * @param {string} modelValue - The model value to select (e.g., 'llama3.2:3b')
 * @returns {Promise<boolean>} - True if successful
 */
async function selectModel(page, modelValue) {
    try {
        // Set the model value directly in both inputs
        const success = await page.evaluate((model) => {
            // Set the visible input (for display)
            const modelInput = document.querySelector('#model');
            if (!modelInput) {
                console.error('Model input not found');
                return false;
            }
            modelInput.value = model;

            // More importantly, set the hidden input that gets submitted
            const hiddenInput = document.querySelector('#model_hidden');
            if (!hiddenInput) {
                console.error('Hidden model input not found');
                return false;
            }
            hiddenInput.value = model;

            // Trigger change event on hidden input
            hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));

            console.log(`Model set to: ${model}`);
            return true;
        }, modelValue);

        return success;
    } catch (error) {
        console.error('Error selecting model:', error);
        return false;
    }
}

/**
 * Select a model provider
 * @param {Page} page - Puppeteer page object
 * @param {string} provider - The provider to select (e.g., 'OLLAMA', 'OPENAI')
 * @returns {Promise<boolean>} - True if successful
 */
async function selectProvider(page, provider) {
    try {
        const providerSelect = await page.$('#model_provider');
        if (!providerSelect) {
            console.error('Provider select not found');
            return false;
        }

        await page.select('#model_provider', provider);
        console.log(`Provider set to: ${provider}`);
        return true;
    } catch (error) {
        console.error('Error selecting provider:', error);
        return false;
    }
}

/**
 * Set up a research with a default working model configuration
 * @param {Page} page - Puppeteer page object
 * @param {Object} options - Configuration options
 * @returns {Promise<boolean>} - True if successful
 */
async function setupDefaultModel(page, options = {}) {
    const defaults = {
        provider: 'OLLAMA',
        model: 'llama3.2:3b',
        ...options
    };

    // Select provider
    const providerSet = await selectProvider(page, defaults.provider);
    if (!providerSet) {
        console.error('Failed to set provider');
        return false;
    }

    // Select model
    const modelSet = await selectModel(page, defaults.model);
    if (!modelSet) {
        console.error('Failed to set model');
        return false;
    }

    console.log(`✅ Model configuration set: ${defaults.provider}/${defaults.model}`);
    return true;
}

module.exports = {
    selectModel,
    selectProvider,
    setupDefaultModel
};
