# Test Organization Guide

## API Key Test Suite Structure

### Core Tests (Keep These)

#### 1. **test_api_key_integrated.js** (NEW)
- **Purpose**: Main comprehensive test combining all best practices
- **Features**: Dialog detection, CSRF handling, API + UI testing, persistence checks
- **Use Case**: Primary test for CI/CD and debugging user selection issues

#### 2. **test_api_key_settings.js**
- **Purpose**: Mocha/Chai framework test with advanced dialog detection
- **Features**: Screenshot capture on dialogs, full registration flow
- **Use Case**: Framework-based testing, dialog debugging

#### 3. **test_api_key_with_auth.js**
- **Purpose**: Authentication and password validation testing
- **Features**: Strong password checks, registration fallback
- **Use Case**: Auth flow testing

#### 4. **test_api_key_detailed_debug.js**
- **Purpose**: Interactive debugging with visible browser
- **Features**: DevTools open, network logging, keeps browser open
- **Use Case**: Manual debugging of issues

#### 5. **test_api_key_fixed.js**
- **Purpose**: Pure API testing without UI interaction
- **Features**: Direct API calls only, isolates backend issues
- **Use Case**: Backend API verification

#### 6. **test_api_key_simple_verify.js**
- **Purpose**: Quick smoke test
- **Features**: Minimal checks, fast execution
- **Use Case**: Pre-commit hooks, quick CI checks

#### 7. **test_api_key_functionality.js**
- **Purpose**: Model and provider testing
- **Features**: Checks model availability, provider switching
- **Use Case**: LLM integration testing

#### 8. **test_api_key_with_csrf.js**
- **Purpose**: CSRF token handling
- **Features**: Multiple CSRF detection methods
- **Use Case**: Security testing

### Running Tests

```bash
# Quick smoke test
node tests/ui_tests/test_api_key_simple_verify.js

# Full test suite
node tests/ui_tests/test_api_key_integrated.js

# Debug mode (opens browser with devtools)
DEBUG=true node tests/ui_tests/test_api_key_detailed_debug.js

# API-only test (no UI)
node tests/ui_tests/test_api_key_fixed.js

# Framework test with Mocha
cd tests && npm test test_api_key_settings.js
```

### Test Selection Guide

- **Debugging user selection dialog**: Use `test_api_key_integrated.js` or `test_api_key_settings.js`
- **Quick verification**: Use `test_api_key_simple_verify.js`
- **API issues**: Use `test_api_key_fixed.js`
- **Authentication problems**: Use `test_api_key_with_auth.js`
- **Manual debugging**: Use `DEBUG=true test_api_key_detailed_debug.js`

### Files Moved to Backup

The following duplicate files were moved to `backup/test_cleanup_2025_01_04/`:
- test_api_key_simple.js (duplicate of settings test)
- test_api_key_user_ldr1.js (minor variation)
- test_api_key_error.js (error cases covered in integrated)
- test_api_key_final.js (superseded by integrated)
- test_api_key_persistence.js (persistence covered in integrated)
- test_api_key_ui_settings.js (UI covered in integrated)
- test_fresh_user_api_key.js (covered in integrated)
- test_create_apitest.js (user creation covered elsewhere)
