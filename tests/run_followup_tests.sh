#!/bin/bash
# Script to run follow-up research tests locally

echo "🚀 Running Follow-up Research Tests"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if server is running
check_server() {
    curl -s -f http://127.0.0.1:5000 > /dev/null 2>&1
    return $?
}

# Start server if not running
if ! check_server; then
    echo -e "${YELLOW}Starting local server...${NC}"
    cd src
    nohup python -m local_deep_research.web.app --port 5000 > ../server_test.log 2>&1 &
    SERVER_PID=$!
    cd ..

    # Wait for server to start
    for i in {1..30}; do
        if check_server; then
            echo -e "${GREEN}Server started successfully${NC}"
            break
        fi
        sleep 1
    done

    if ! check_server; then
        echo -e "${RED}Failed to start server${NC}"
        cat server_test.log
        exit 1
    fi
else
    echo -e "${GREEN}Server already running${NC}"
    SERVER_PID=""
fi

# Create test directories
mkdir -p tests/ui_tests/screenshots/followup
mkdir -p tests/ui_tests/results/followup

# Run API tests
echo ""
echo "📝 Running API Tests..."
echo "----------------------"
pdm run pytest tests/test_followup_api.py -v --tb=short
API_EXIT_CODE=$?

if [ $API_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✓ API tests passed${NC}"
else
    echo -e "${RED}✗ API tests failed${NC}"
fi

# Run UI tests
echo ""
echo "🖥️ Running UI Tests..."
echo "---------------------"
cd tests/ui_tests

# Install npm dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing npm dependencies..."
    npm install
fi

# Run the follow-up research test
HEADLESS=false timeout 300 node test_followup_research.js
UI_EXIT_CODE=$?

cd ../..

if [ $UI_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✓ UI tests passed${NC}"
else
    echo -e "${RED}✗ UI tests failed or timed out${NC}"
fi

# Stop server if we started it
if [ ! -z "$SERVER_PID" ]; then
    echo ""
    echo "Stopping test server..."
    kill $SERVER_PID 2>/dev/null
fi

# Summary
echo ""
echo "===================================="
echo "Test Summary:"
echo "===================================="
if [ $API_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✓ API Tests: PASSED${NC}"
else
    echo -e "${RED}✗ API Tests: FAILED${NC}"
fi

if [ $UI_EXIT_CODE -eq 0 ]; then
    echo -e "${GREEN}✓ UI Tests: PASSED${NC}"
else
    echo -e "${RED}✗ UI Tests: FAILED${NC}"
fi

# Exit with error if any test failed
if [ $API_EXIT_CODE -ne 0 ] || [ $UI_EXIT_CODE -ne 0 ]; then
    echo ""
    echo -e "${RED}Some tests failed. Please check the output above.${NC}"
    exit 1
else
    echo ""
    echo -e "${GREEN}All tests passed successfully! 🎉${NC}"
    exit 0
fi
