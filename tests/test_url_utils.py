"""Test URL utility functions."""

import pytest

from local_deep_research.utilities.url_utils import normalize_url


class TestNormalizeUrl:
    """Test cases for the normalize_url function."""

    def test_localhost_without_scheme(self):
        """Test that localhost addresses get http:// prefix."""
        assert normalize_url("localhost:11434") == "http://localhost:11434"
        assert normalize_url("127.0.0.1:11434") == "http://127.0.0.1:11434"
        assert normalize_url("[::1]:11434") == "http://[::1]:11434"
        assert normalize_url("0.0.0.0:11434") == "http://0.0.0.0:11434"

    def test_external_host_without_scheme(self):
        """Test that external hosts get https:// prefix."""
        assert normalize_url("example.com:11434") == "https://example.com:11434"
        assert normalize_url("api.example.com") == "https://api.example.com"

    def test_malformed_url_with_scheme(self):
        """Test correction of malformed URLs like 'http:hostname'."""
        assert normalize_url("http:localhost:11434") == "http://localhost:11434"
        assert (
            normalize_url("https:example.com:11434")
            == "https://example.com:11434"
        )

    def test_well_formed_urls(self):
        """Test that well-formed URLs are unchanged."""
        assert (
            normalize_url("http://localhost:11434") == "http://localhost:11434"
        )
        assert (
            normalize_url("https://example.com:11434")
            == "https://example.com:11434"
        )
        assert (
            normalize_url("http://*************:11434")
            == "http://*************:11434"
        )

    def test_urls_with_double_slash_prefix(self):
        """Test URLs that start with //."""
        assert normalize_url("//localhost:11434") == "http://localhost:11434"
        assert (
            normalize_url("//example.com:11434") == "https://example.com:11434"
        )

    def test_empty_or_none_url(self):
        """Test handling of empty or None URLs."""
        with pytest.raises(ValueError):
            normalize_url("")
        with pytest.raises(ValueError):
            normalize_url(None)

    def test_url_with_path(self):
        """Test URLs with paths."""
        assert (
            normalize_url("localhost:11434/api") == "http://localhost:11434/api"
        )
        assert (
            normalize_url("example.com/api/v1") == "https://example.com/api/v1"
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
