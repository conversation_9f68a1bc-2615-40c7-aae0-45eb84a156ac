const puppeteer = require('puppeteer');
const fs = require('fs').promises;

async function testNewsNoCache() {
    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();
        await page.setViewport({ width: 1280, height: 800 });

        // Disable all caching
        await page.setCacheEnabled(false);

        // Clear any application data
        const client = await page.target().createCDPSession();
        await client.send('Network.clearBrowserCache');
        await client.send('Network.clearBrowserCookies');

        console.log('Loading news page with completely fresh data...');

        // Add timestamp to force fresh load
        await page.goto(`http://127.0.0.1:5000/news?t=${Date.now()}`, {
            waitUntil: 'networkidle2',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        // Wait for API call to complete
        await page.waitForResponse(response =>
            response.url().includes('/news/api/feed'),
            { timeout: 10000 }
        );

        // Wait a bit more for rendering
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check content of first news item
        const firstItemContent = await page.evaluate(() => {
            const firstItem = document.querySelector('.news-item');
            if (!firstItem) return null;

            const headline = firstItem.querySelector('.news-headline')?.textContent;
            const findings = firstItem.querySelector('.news-findings')?.textContent;

            return {
                headline: headline?.trim(),
                findingsStart: findings?.substring(0, 200).trim(),
                hasQuery: findings?.toLowerCase().includes('query:'),
                hasSummaryHeader: findings?.includes('Quick Research Summary')
            };
        });

        console.log('\nFirst news item content:');
        console.log('Headline:', firstItemContent?.headline);
        console.log('\nFindings start:', firstItemContent?.findingsStart);
        console.log('\nContains "Query:"?', firstItemContent?.hasQuery ? '❌ YES' : '✅ NO');
        console.log('Contains "Quick Research Summary"?', firstItemContent?.hasSummaryHeader ? '❌ YES' : '✅ NO');

        // Take screenshot
        await fs.mkdir('screenshots', { recursive: true });
        await page.screenshot({
            path: 'screenshots/news_no_cache_final.png',
            fullPage: true
        });
        console.log('\nScreenshot saved to screenshots/news_no_cache_final.png');

    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        await browser.close();
    }
}

testNewsNoCache();
