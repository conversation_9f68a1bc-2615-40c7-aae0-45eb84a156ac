const puppeteer = require('puppeteer');

async function testNewsWithData() {
    const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();

        // Set viewport
        await page.setViewport({ width: 1920, height: 1080 });

        // Listen to console logs
        page.on('console', msg => console.log(`[${msg.type()}] ${msg.text()}`));

        // Listen for API calls
        page.on('response', response => {
            if (response.url().includes('/api/history') || response.url().includes('/api/report')) {
                console.log(`[API Response] ${response.url()} - ${response.status()}`);
            }
        });

        // Navigate to news page
        console.log('Navigating to news page...');
        await page.goto('http://localhost:5000/news/', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Wait for the checkForCompletedNewsSearches to run
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Check what's in the news feed now
        const newsContent = await page.evaluate(() => {
            const cards = document.querySelectorAll('.news-card');
            const researchCards = document.querySelectorAll('[data-research-id]');

            return {
                totalCards: cards.length,
                researchCards: researchCards.length,
                cardDetails: Array.from(cards).map(card => ({
                    title: card.querySelector('.news-title')?.textContent,
                    researchId: card.getAttribute('data-research-id'),
                    summary: card.querySelector('.news-summary')?.textContent?.substring(0, 100)
                }))
            };
        });

        console.log('News cards found:', JSON.stringify(newsContent, null, 2));

        // Take screenshot
        await page.screenshot({
            path: 'news_with_completed.png',
            fullPage: false
        });
        console.log('Screenshot saved: news_with_completed.png');

    } catch (error) {
        console.error('Test error:', error);
    } finally {
        await browser.close();
    }
}

testNewsWithData();
