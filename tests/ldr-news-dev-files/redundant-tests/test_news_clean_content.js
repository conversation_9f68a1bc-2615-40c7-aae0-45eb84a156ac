const puppeteer = require('puppeteer');
const fs = require('fs').promises;

async function testNewsCleanContent() {
    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();
        await page.setViewport({ width: 1280, height: 800 });

        // Disable cache
        await page.setCacheEnabled(false);

        console.log('Loading news page to check clean content...');
        await page.goto('http://127.0.0.1:5000/news', {
            waitUntil: 'networkidle2',
            headers: {
                'Cache-Control': 'no-cache'
            }
        });

        // Wait for news items
        await page.waitForSelector('.news-item', { timeout: 10000 });

        // Check content of first news item
        const firstItemContent = await page.evaluate(() => {
            const firstItem = document.querySelector('.news-item');
            if (!firstItem) return null;

            const headline = firstItem.querySelector('.news-headline')?.textContent;
            const findings = firstItem.querySelector('.news-findings')?.textContent;
            const summary = firstItem.querySelector('.news-summary')?.textContent;

            return {
                headline: headline?.trim(),
                findingsStart: findings?.substring(0, 150).trim(),
                summaryStart: summary?.substring(0, 150).trim(),
                hasQuery: findings?.toLowerCase().includes('query:') || summary?.toLowerCase().includes('query:')
            };
        });

        console.log('\nFirst news item content:');
        console.log('Headline:', firstItemContent?.headline);
        console.log('\nFindings start:', firstItemContent?.findingsStart);
        console.log('\nContains "Query:"?', firstItemContent?.hasQuery ? '❌ YES (needs fixing)' : '✅ NO (good!)');

        // Take screenshot
        await fs.mkdir('screenshots', { recursive: true });
        await page.screenshot({
            path: 'screenshots/news_clean_content.png',
            fullPage: true
        });
        console.log('\nScreenshot saved to screenshots/news_clean_content.png');

    } catch (error) {
        console.error('Test failed:', error);
    } finally {
        await browser.close();
    }
}

testNewsCleanContent();
