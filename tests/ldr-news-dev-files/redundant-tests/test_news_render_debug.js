const puppeteer = require('puppeteer');

async function debugNewsRendering() {
    console.log('🔍 Debugging News Rendering Issue\n');

    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();

        // Capture ALL console logs and errors
        page.on('console', msg => {
            const type = msg.type();
            const text = msg.text();
            if (type === 'error') {
                console.log(`[JS ERROR]:`, text);
            } else {
                console.log(`[Browser ${type}]:`, text);
            }
        });

        page.on('pageerror', error => {
            console.log('[Page Error]:', error.message);
        });

        await page.setViewport({ width: 1280, height: 800 });

        // Load news page
        console.log('📰 Loading news page...');
        const response = await page.goto('http://localhost:5000/news/', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        console.log(`Page loaded with status: ${response.status()}`);

        // Wait a bit for JavaScript to execute
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if news-feed-content exists
        const feedContentInfo = await page.evaluate(() => {
            const element = document.getElementById('news-feed-content');
            if (!element) return { exists: false };

            return {
                exists: true,
                innerHTML: element.innerHTML.substring(0, 200),
                childCount: element.children.length,
                className: element.className,
                style: element.getAttribute('style')
            };
        });

        console.log('\n📋 news-feed-content element:');
        console.log(JSON.stringify(feedContentInfo, null, 2));

        // Check what's in the global scope
        const globalFunctions = await page.evaluate(() => {
            return {
                loadNewsFeed: typeof loadNewsFeed,
                renderNewsItems: typeof renderNewsItems,
                newsItems: typeof newsItems !== 'undefined' ?
                    (Array.isArray(newsItems) ? `Array(${newsItems.length})` : typeof newsItems) :
                    'undefined'
            };
        });

        console.log('\n🌐 Global functions:');
        console.log(JSON.stringify(globalFunctions, null, 2));

        // Try to inspect the news items
        const newsItemsData = await page.evaluate(() => {
            if (typeof newsItems !== 'undefined' && Array.isArray(newsItems)) {
                return {
                    count: newsItems.length,
                    firstItem: newsItems[0] || null,
                    hasFindings: newsItems.some(item => item.findings),
                    hasSummary: newsItems.some(item => item.summary)
                };
            }
            return null;
        });

        console.log('\n📊 News items data:');
        console.log(JSON.stringify(newsItemsData, null, 2));

        // Check if the rendering function is being called
        console.log('\n🎨 Checking rendering...');
        await page.evaluate(() => {
            console.log('About to check rendering function...');
            if (typeof renderNewsItems === 'function') {
                console.log('renderNewsItems exists, calling it...');
                try {
                    renderNewsItems();
                    console.log('renderNewsItems called successfully');
                } catch (e) {
                    console.error('Error calling renderNewsItems:', e);
                }
            } else {
                console.error('renderNewsItems is not a function');
            }
        });

        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check results after render attempt
        const afterRenderInfo = await page.evaluate(() => {
            const container = document.getElementById('news-feed-content');
            return {
                newsItemCount: document.querySelectorAll('.news-item').length,
                containerHTML: container ? container.innerHTML.substring(0, 500) : 'no container',
                hasEmptyState: container ? container.querySelector('.empty-state') !== null : false
            };
        });

        console.log('\n📈 After render attempt:');
        console.log(JSON.stringify(afterRenderInfo, null, 2));

        // Take screenshot for visual debugging
        await page.screenshot({
            path: 'tests/ui_tests/screenshots/news_render_debug.png',
            fullPage: true
        });
        console.log('\n📸 Screenshot saved');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error(error.stack);
    } finally {
        await browser.close();
    }
}

debugNewsRendering().catch(console.error);
