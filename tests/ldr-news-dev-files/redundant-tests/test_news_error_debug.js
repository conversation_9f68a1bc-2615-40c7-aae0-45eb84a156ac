const puppeteer = require('puppeteer');

async function debugNewsError() {
    console.log('🔍 Debugging News JavaScript Error\n');

    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();

        // Capture console errors with more detail
        page.on('console', msg => {
            const type = msg.type();
            if (type === 'error') {
                console.log(`[JS ERROR]: ${msg.text()}`);
                msg.args().forEach(async (arg, i) => {
                    const val = await arg.jsonValue().catch(() => 'Complex object');
                    console.log(`  Arg ${i}:`, val);
                });
            }
        });

        page.on('pageerror', error => {
            console.log('[Page Error]:', error.message);
            console.log('[Stack]:', error.stack);
        });

        await page.setViewport({ width: 1280, height: 800 });

        console.log('📰 Loading news page...');
        await page.goto('http://localhost:5000/news/', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Wait a bit
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Try to manually call renderNewsItems and catch errors
        console.log('\n🎨 Manually calling renderNewsItems...');
        const renderResult = await page.evaluate(() => {
            try {
                if (typeof renderNewsItems === 'function') {
                    renderNewsItems();
                    return { success: true, message: 'Called successfully' };
                } else {
                    return { success: false, error: 'renderNewsItems not found' };
                }
            } catch (e) {
                return {
                    success: false,
                    error: e.toString(),
                    stack: e.stack,
                    name: e.name
                };
            }
        });

        console.log('Render result:', JSON.stringify(renderResult, null, 2));

        // Check container content
        const containerInfo = await page.evaluate(() => {
            const container = document.getElementById('news-feed-content');
            return {
                exists: !!container,
                innerHTML: container ? container.innerHTML.substring(0, 300) : 'no container',
                childCount: container ? container.children.length : 0
            };
        });

        console.log('\n📋 Container info:');
        console.log(JSON.stringify(containerInfo, null, 2));

        // Check if specific functions exist
        const functionCheck = await page.evaluate(() => {
            return {
                renderMarkdown: typeof renderMarkdown,
                windowUi: typeof window.ui,
                windowUiRenderMarkdown: window.ui ? typeof window.ui.renderMarkdown : 'no window.ui',
                marked: typeof marked
            };
        });

        console.log('\n🔧 Function availability:');
        console.log(JSON.stringify(functionCheck, null, 2));

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        await browser.close();
    }
}

debugNewsError().catch(console.error);
