const puppeteer = require('puppeteer');

async function finalNewsCheck() {
    console.log('🔍 Final News System Check\n');

    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();
        await page.setViewport({ width: 1280, height: 800 });

        // Load news page
        console.log('📰 Loading news page...');
        await page.goto('http://localhost:5000/news/', { waitUntil: 'networkidle2' });

        // Wait for initial render
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Get current state
        const initialState = await page.evaluate(() => {
            return {
                newsItemsArray: typeof newsItems !== 'undefined' ? newsItems.length : 0,
                newsItemElements: document.querySelectorAll('.news-item').length,
                newsCardElements: document.querySelectorAll('.news-card').length,
                containerContent: document.getElementById('news-feed-content')?.children.length || 0
            };
        });

        console.log('📊 Initial state:');
        console.log(`- newsItems array: ${initialState.newsItemsArray} items`);
        console.log(`- DOM .news-item elements: ${initialState.newsItemElements}`);
        console.log(`- DOM .news-card elements: ${initialState.newsCardElements}`);
        console.log(`- Container children: ${initialState.containerContent}`);

        // Force render of news items
        console.log('\n🔄 Forcing renderNewsItems()...');
        const renderResult = await page.evaluate(() => {
            // Clear any existing content first
            const container = document.getElementById('news-feed-content');
            if (container) {
                container.innerHTML = '';
            }

            // Check if renderNewsItems exists and call it
            if (typeof renderNewsItems === 'function') {
                try {
                    renderNewsItems();
                    return { success: true, message: 'renderNewsItems called' };
                } catch (e) {
                    return { success: false, error: e.toString() };
                }
            } else {
                // If it doesn't exist, try to render manually
                if (typeof newsItems !== 'undefined' && Array.isArray(newsItems) && newsItems.length > 0) {
                    const container = document.getElementById('news-feed-content');
                    if (container) {
                        // Render each news item
                        const html = newsItems.map(item => `
                            <div class="news-item priority-medium" data-news-id="${item.id}">
                                <div class="news-headline">${item.headline}</div>
                                <div class="news-meta">
                                    <span class="news-category">${item.category || 'General'}</span>
                                    <span><i class="fas fa-clock"></i> ${item.time_ago || 'Recently'}</span>
                                </div>
                                <div class="news-summary">${item.summary || ''}</div>
                            </div>
                        `).join('');
                        container.innerHTML = html;
                        return { success: true, message: 'Manually rendered news items' };
                    }
                }
                return { success: false, error: 'renderNewsItems not found' };
            }
        });

        console.log('Render result:', renderResult);

        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check final state
        const finalState = await page.evaluate(() => {
            return {
                newsItemElements: document.querySelectorAll('.news-item').length,
                newsCardElements: document.querySelectorAll('.news-card').length,
                containerHTML: document.getElementById('news-feed-content')?.innerHTML.substring(0, 200) || 'empty'
            };
        });

        console.log('\n📈 Final state:');
        console.log(`- DOM .news-item elements: ${finalState.newsItemElements}`);
        console.log(`- DOM .news-card elements: ${finalState.newsCardElements}`);
        console.log(`- Container preview: ${finalState.containerHTML}...`);

        // Take screenshot
        await page.screenshot({
            path: 'tests/ui_tests/screenshots/news_final_check.png',
            fullPage: true
        });

        // Test API directly
        console.log('\n🔌 Testing API directly...');
        const apiTest = await page.evaluate(async () => {
            const response = await fetch('/news/api/feed?user_id=test&limit=5');
            const data = await response.json();
            return {
                status: response.status,
                itemCount: data.news_items?.length || 0,
                source: data.source,
                firstItem: data.news_items?.[0] || null
            };
        });

        console.log('API Response:');
        console.log(`- Status: ${apiTest.status}`);
        console.log(`- Items: ${apiTest.itemCount}`);
        console.log(`- Source: ${apiTest.source}`);
        if (apiTest.firstItem) {
            console.log(`- First item headline: "${apiTest.firstItem.headline}"`);
        }

        console.log('\n✅ Test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        await browser.close();
    }
}

finalNewsCheck().catch(console.error);
