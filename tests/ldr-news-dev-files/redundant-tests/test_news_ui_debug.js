const puppeteer = require('puppeteer');

async function debugNewsUI() {
    console.log('🔍 Debugging News UI Rendering\n');

    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();

        // Capture console logs
        page.on('console', msg => {
            console.log(`[Browser ${msg.type()}]:`, msg.text());
        });

        // Capture errors
        page.on('pageerror', error => {
            console.log('[Browser Error]:', error.message);
        });

        await page.setViewport({ width: 1280, height: 800 });

        // Load news page
        console.log('📰 Loading news page...');
        await page.goto('http://localhost:5000/news/', { waitUntil: 'networkidle2' });

        // Check if news.js loaded
        const hasNewsJS = await page.evaluate(() => {
            return typeof loadNewsFeed !== 'undefined';
        });
        console.log(`✅ news.js loaded: ${hasNewsJS}`);

        // Check global variables
        const globalState = await page.evaluate(() => {
            return {
                newsItems: typeof newsItems !== 'undefined' ? newsItems : 'undefined',
                currentUser: typeof currentUser !== 'undefined' ? currentUser : 'undefined',
                subscriptions: typeof subscriptions !== 'undefined' ? subscriptions : 'undefined'
            };
        });
        console.log('\n📊 Global state:');
        console.log('- newsItems:', Array.isArray(globalState.newsItems) ? `Array(${globalState.newsItems.length})` : globalState.newsItems);
        console.log('- currentUser:', globalState.currentUser);
        console.log('- subscriptions:', Array.isArray(globalState.subscriptions) ? `Array(${globalState.subscriptions.length})` : globalState.subscriptions);

        // Check DOM elements
        const domInfo = await page.evaluate(() => {
            return {
                feedContent: document.getElementById('news-feed-content') ? 'exists' : 'missing',
                cardView: document.getElementById('news-card-view') ? 'exists' : 'missing',
                newsItems: document.querySelectorAll('.news-item').length,
                cards: document.querySelectorAll('.ldr-card').length,
                errorMessages: document.querySelectorAll('.error-message').length,
                loadingPlaceholder: document.querySelectorAll('.loading-placeholder').length
            };
        });
        console.log('\n📋 DOM elements:');
        console.log(JSON.stringify(domInfo, null, 2));

        // Call loadNewsFeed manually and check result
        console.log('\n🔄 Manually calling loadNewsFeed()...');
        await page.evaluate(() => loadNewsFeed());
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check newsItems after manual load
        const afterLoad = await page.evaluate(() => {
            return {
                newsItemsCount: newsItems ? newsItems.length : 0,
                newsItemsSample: newsItems && newsItems.length > 0 ? newsItems[0] : null,
                domNewsItems: document.querySelectorAll('.news-item').length,
                domCards: document.querySelectorAll('.ldr-card').length,
                cardViewHTML: document.getElementById('news-card-view')?.innerHTML.substring(0, 200) || 'no content'
            };
        });
        console.log('\n📈 After manual loadNewsFeed:');
        console.log('- newsItems array length:', afterLoad.newsItemsCount);
        console.log('- First news item:', JSON.stringify(afterLoad.newsItemsSample, null, 2));
        console.log('- DOM .news-item count:', afterLoad.domNewsItems);
        console.log('- DOM .card count:', afterLoad.domCards);
        console.log('- Card view HTML preview:', afterLoad.cardViewHTML);

        // Check renderNewsItems function
        console.log('\n🎨 Testing renderNewsItems()...');
        await page.evaluate(() => {
            // Add test data directly
            newsItems = [{
                id: 'test-123',
                headline: 'Test Headline',
                category: 'Test',
                summary: 'Test summary',
                impact_score: 5,
                time_ago: 'Just now',
                upvotes: 0,
                downvotes: 0
            }];
            renderNewsItems();
        });

        await new Promise(resolve => setTimeout(resolve, 500));

        const afterRender = await page.evaluate(() => {
            return {
                newsItemsCount: document.querySelectorAll('.news-item').length,
                cardViewContent: document.getElementById('news-card-view')?.innerHTML.includes('Test Headline')
            };
        });
        console.log('\n✅ After manual renderNewsItems with test data:');
        console.log('- News items rendered:', afterRender.newsItemsCount);
        console.log('- Test headline visible:', afterRender.cardViewContent);

        // Take screenshot
        await page.screenshot({
            path: 'tests/ui_tests/screenshots/news_ui_debug.png',
            fullPage: true
        });
        console.log('\n📸 Screenshot saved to: tests/ui_tests/screenshots/news_ui_debug.png');

    } catch (error) {
        console.error('❌ Debug failed:', error.message);
        console.error(error.stack);
    } finally {
        await browser.close();
    }
}

debugNewsUI().catch(console.error);
