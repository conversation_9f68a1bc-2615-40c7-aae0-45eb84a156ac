/**
 * Debug news API and feed issues
 */

const puppeteer = require('puppeteer');

async function debugNewsAPI() {
    console.log('🔍 Debugging News API\n');

    const browser = await puppeteer.launch({
        headless: false,
        args: ['--no-sandbox', '--disable-setuid-sandbox', '--window-size=1400,900'],
        defaultViewport: { width: 1400, height: 900 }
    });

    const page = await browser.newPage();

    try {
        console.log('📍 Loading news page...');
        await page.goto('http://localhost:5000/news/', { waitUntil: 'networkidle2' });
        await new Promise(r => setTimeout(r, 2000));

        // Test 1: Check news feed endpoint
        console.log('\n1️⃣ Testing /news/api/feed endpoint');
        const feedTest = await page.evaluate(async () => {
            const response = await fetch('/news/api/feed?user_id=test&limit=10');
            const data = await response.json();
            return {
                status: response.status,
                ok: response.ok,
                data: data
            };
        });

        console.log('Feed response:', JSON.stringify(feedTest, null, 2));

        // Test 2: Check Flask API response structure
        console.log('\n2️⃣ Checking Flask API wrapper');
        const flaskCheck = await page.evaluate(async () => {
            const response = await fetch('/news/api/feed?user_id=test&limit=5&use_cache=false');
            const text = await response.text();
            try {
                const json = JSON.parse(text);
                return { type: 'json', data: json };
            } catch {
                return { type: 'text', data: text.substring(0, 200) };
            }
        });

        console.log('Flask response:', JSON.stringify(flaskCheck, null, 2));

        // Test 3: Create a subscription to trigger news generation
        console.log('\n3️⃣ Creating subscription to trigger news generation');
        const subResponse = await page.evaluate(async () => {
            const response = await fetch('/news/api/subscribe', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    user_id: 'test',
                    query: 'AI technology news',
                    subscription_type: 'search',
                    refresh_minutes: 60
                })
            });

            return {
                status: response.status,
                ok: response.ok,
                data: await response.json()
            };
        });

        console.log('Subscription response:', JSON.stringify(subResponse, null, 2));

        // Wait for subscription to process
        await new Promise(r => setTimeout(r, 3000));

        // Test 4: Check feed again after subscription
        console.log('\n4️⃣ Checking feed after subscription');
        const feedAfterSub = await page.evaluate(async () => {
            const response = await fetch('/news/api/feed?user_id=test&limit=10');
            const data = await response.json();
            return {
                cardCount: data.cards?.length || 0,
                newsItemsCount: data.news_items?.length || 0,
                hasError: !!data.error,
                keys: Object.keys(data)
            };
        });

        console.log('Feed after subscription:', feedAfterSub);

        // Test 5: Try direct research endpoint
        console.log('\n5️⃣ Triggering news research directly');
        const researchResponse = await page.evaluate(async () => {
            const response = await fetch('/api/start_research', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    query: 'Latest breaking news in technology',
                    mode: 'quick',
                    metadata: {
                        is_news_search: true,
                        source: 'news_feed_test'
                    }
                })
            });

            return {
                status: response.status,
                ok: response.ok,
                data: await response.json()
            };
        });

        console.log('Research response:', JSON.stringify(researchResponse, null, 2));

        // Test 6: Check loadNewsFeed function
        console.log('\n6️⃣ Testing loadNewsFeed JavaScript function');
        const loadFeedTest = await page.evaluate(async () => {
            if (typeof loadNewsFeed !== 'function') {
                return { error: 'loadNewsFeed function not found' };
            }

            try {
                // Override renderNewsFeed to capture data
                const originalRender = window.renderNewsFeed;
                let capturedData = null;

                window.renderNewsFeed = function(feed) {
                    capturedData = feed;
                    if (originalRender) originalRender(feed);
                };

                await loadNewsFeed();

                // Restore original
                window.renderNewsFeed = originalRender;

                return {
                    success: true,
                    capturedData: capturedData,
                    cardsAfterLoad: document.querySelectorAll('.news-card').length
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message
                };
            }
        });

        console.log('LoadNewsFeed test:', JSON.stringify(loadFeedTest, null, 2));

        // Test 7: Check if we need to initialize news data
        console.log('\n7️⃣ Checking news system initialization');
        const initCheck = await page.evaluate(async () => {
            // Check localStorage for news data
            const savedNews = localStorage.getItem('news_feed_cache');
            const lastUpdate = localStorage.getItem('news_last_update');

            return {
                hasCachedNews: !!savedNews,
                lastUpdate: lastUpdate,
                subscriptionCount: document.querySelectorAll('.subscription-item').length
            };
        });

        console.log('Initialization check:', initCheck);

        // ANALYSIS
        console.log('\n' + '='.repeat(60));
        console.log('📊 ANALYSIS');
        console.log('='.repeat(60));

        if (feedTest.data && feedTest.data.cards && feedTest.data.cards.length === 0) {
            console.log('\n❌ ISSUE: API returns empty cards array');
            console.log('   The news system may not have any data yet');
            console.log('   Need to trigger news generation through subscriptions or searches');
        }

        if (feedTest.data && feedTest.data.news_items && feedTest.data.news_items.length === 0) {
            console.log('\n❌ ISSUE: API returns empty news_items array');
            console.log('   The recommender may not be finding any content');
        }

        if (flaskCheck.data && flaskCheck.data.error) {
            console.log('\n❌ ISSUE: API returns error:', flaskCheck.data.error);
        }

        console.log('\n💡 SOLUTION:');
        console.log('   1. Create subscriptions to trigger news collection');
        console.log('   2. Use the search feature to generate news content');
        console.log('   3. The system needs to populate data before showing in feed');

        console.log('\n✨ Debug complete! Browser will close in 15 seconds...');
        await new Promise(r => setTimeout(r, 15000));

    } catch (error) {
        console.error('❌ Test error:', error.message);
    } finally {
        await browser.close();
    }
}

// Run test
debugNewsAPI().catch(console.error);
