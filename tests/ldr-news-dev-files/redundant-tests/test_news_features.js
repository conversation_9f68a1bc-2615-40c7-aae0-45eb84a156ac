const puppeteer = require('puppeteer');

async function testNewsFeatures() {
    console.log('🚀 Testing News Features: Topic Filtering and Source Links\n');

    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();

        // Enable console logging
        page.on('console', msg => {
            if (msg.type() !== 'error') return; // Only show errors
            console.log('Browser console:', msg.text());
        });

        console.log('1️⃣ Loading news page...');
        await page.goto('http://127.0.0.1:5000/news', { waitUntil: 'networkidle2' });

        // Wait for news to load
        await page.waitForSelector('.news-item', { timeout: 10000 });

        // Check for source links
        console.log('\n2️⃣ Checking source links...');
        const sourceLinks = await page.evaluate(() => {
            const links = [];
            document.querySelectorAll('.news-sources').forEach(sourceDiv => {
                const sourceLinks = sourceDiv.querySelectorAll('.source-link');
                sourceLinks.forEach(link => {
                    links.push({
                        title: link.textContent.trim(),
                        url: link.href
                    });
                });
            });
            return links;
        });

        console.log(`Found ${sourceLinks.length} source links`);
        if (sourceLinks.length > 0) {
            console.log('First 3 source links:');
            sourceLinks.slice(0, 3).forEach(link => {
                console.log(`  - ${link.title}: ${link.url}`);
            });
        }

        // Check trending topics
        console.log('\n3️⃣ Checking trending topics...');
        const topics = await page.evaluate(() => {
            return Array.from(document.querySelectorAll('#trending-topics .topic-tag'))
                .map(tag => tag.textContent);
        });

        console.log(`Found ${topics.length} trending topics:`, topics);

        // Test topic filtering
        console.log('\n4️⃣ Testing topic filtering...');
        if (topics.length > 0) {
            const topicToFilter = topics[0];
            console.log(`Clicking on topic: ${topicToFilter}`);

            // Click the first topic
            await page.click('#trending-topics .topic-tag:first-child');

            // Wait a bit for filter to apply
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Check if filter is active
            const filterState = await page.evaluate(() => {
                const filterBar = document.querySelector('.active-filter-bar');
                const activeTopics = document.querySelectorAll('.topic-tag.active');
                const visibleNewsItems = document.querySelectorAll('.news-item:not([style*="display: none"])');

                return {
                    hasFilterBar: !!filterBar,
                    filterText: filterBar?.textContent || '',
                    activeTopicCount: activeTopics.length,
                    visibleItemCount: visibleNewsItems.length
                };
            });

            console.log('Filter state:', filterState);

            // Take screenshot
            await page.screenshot({
                path: 'screenshots/news_tests/topic_filter_active.png',
                fullPage: true
            });
            console.log('📸 Screenshot saved: topic_filter_active.png');

            // Clear filter
            const clearButton = await page.$('.active-filter-bar button');
            if (clearButton) {
                console.log('\n5️⃣ Clearing filter...');
                await clearButton.click();
                await new Promise(resolve => setTimeout(resolve, 500));

                const clearedState = await page.evaluate(() => {
                    return {
                        hasFilterBar: !!document.querySelector('.active-filter-bar'),
                        activeTopicCount: document.querySelectorAll('.topic-tag.active').length
                    };
                });

                console.log('Filter cleared:', clearedState);
            }
        }

        // Check news card structure
        console.log('\n6️⃣ Checking news card structure...');
        const cardInfo = await page.evaluate(() => {
            const firstCard = document.querySelector('.news-item');
            if (!firstCard) return null;

            return {
                hasHeadline: !!firstCard.querySelector('.news-headline'),
                hasFindings: !!firstCard.querySelector('.news-findings'),
                hasTopics: !!firstCard.querySelector('.news-topics'),
                hasSources: !!firstCard.querySelector('.news-sources'),
                hasVoteButtons: !!firstCard.querySelector('.vote-buttons'),
                hasViewButton: !!firstCard.querySelector('a[href*="/results/"]'),
                topicCount: firstCard.querySelectorAll('.news-topics .topic-tag').length,
                sourceCount: firstCard.querySelectorAll('.source-link').length
            };
        });

        console.log('News card structure:', cardInfo);

        // Take final screenshot
        await page.screenshot({
            path: 'screenshots/news_tests/news_with_features.png',
            fullPage: false
        });
        console.log('\n📸 Final screenshot saved: news_with_features.png');

        console.log('\n✅ All tests completed!');

    } catch (error) {
        console.error('❌ Test error:', error.message);
    } finally {
        await browser.close();
    }
}

// Run the test
testNewsFeatures();
