const puppeteer = require('puppeteer');

async function testNewsFinal() {
    console.log('📸 Taking final screenshot of working news page\n');

    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();
        await page.setViewport({ width: 1280, height: 800 });

        console.log('Loading news page...');
        await page.goto('http://localhost:5000/news/', { waitUntil: 'networkidle2' });

        // Wait for content to render
        await page.waitForSelector('.news-item', { timeout: 5000 });

        // Get stats
        const stats = await page.evaluate(() => {
            return {
                newsItems: document.querySelectorAll('.news-item').length,
                newsCards: document.querySelectorAll('.news-card').length,
                totalItems: document.querySelectorAll('.news-item, .news-card').length,
                firstHeadline: document.querySelector('.news-headline')?.textContent || 'No headline',
                hasFindings: document.querySelectorAll('.news-findings').length
            };
        });

        console.log('📊 Page Stats:');
        console.log(`- News items: ${stats.newsItems}`);
        console.log(`- News cards: ${stats.newsCards}`);
        console.log(`- Total items: ${stats.totalItems}`);
        console.log(`- First headline: "${stats.firstHeadline}"`);
        console.log(`- Items with findings: ${stats.hasFindings}`);

        // Take full page screenshot
        await page.screenshot({
            path: 'tests/ui_tests/screenshots/news_working_final.png',
            fullPage: true
        });

        console.log('\n✅ Screenshot saved to: tests/ui_tests/screenshots/news_working_final.png');
        console.log('\n🎉 News system is working correctly!');
        console.log('   - News items are pulled from research history');
        console.log('   - Markdown findings are rendered');
        console.log('   - No JavaScript errors');

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await browser.close();
    }
}

testNewsFinal().catch(console.error);
