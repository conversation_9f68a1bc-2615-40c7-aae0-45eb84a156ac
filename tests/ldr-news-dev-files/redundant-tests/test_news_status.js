const puppeteer = require('puppeteer');

async function checkNewsSystemStatus() {
    console.log('📊 LDR News System Status Check\n');

    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();

        // Test 1: API Feed
        console.log('🔍 Checking API Feed...');
        const feedResponse = await page.goto('http://localhost:5000/news/api/feed?user_id=test&limit=5');
        const feedData = await feedResponse.json();
        console.log(`✅ API Feed: ${feedData.news_items?.length || 0} items`);
        console.log(`   Source: ${feedData.source || 'unknown'}`);
        console.log(`   Strategy: ${feedData.search_strategy || 'default'}`);

        // Test 2: Page Load
        console.log('\n🔍 Checking News Page...');
        await page.goto('http://localhost:5000/news/', { waitUntil: 'networkidle2' });

        // Get page statistics
        const pageStats = await page.evaluate(() => {
            const cards = document.querySelectorAll('.ldr-card, .news-card');
            const voteButtons = document.querySelectorAll('.vote-button, .btn-upvote, .btn-downvote');
            const hasModal = document.querySelector('#newsModal') !== null;
            const hasBootstrap = typeof window.bootstrap !== 'undefined';

            return {
                cardCount: cards.length,
                voteButtonCount: voteButtons.length,
                hasModal,
                hasBootstrap,
                pageTitle: document.title
            };
        });

        console.log(`✅ Page loaded: "${pageStats.pageTitle}"`);
        console.log(`   News cards: ${pageStats.cardCount}`);
        console.log(`   Vote buttons: ${pageStats.voteButtonCount}`);
        console.log(`   Bootstrap loaded: ${pageStats.hasBootstrap}`);
        console.log(`   Modal present: ${pageStats.hasModal}`);

        // Test 3: Subscription API
        console.log('\n🔍 Checking Subscription API...');
        const subResponse = await page.goto('http://localhost:5000/news/api/subscriptions/test_user');
        const subData = await subResponse.json();
        console.log(`✅ Subscriptions: ${subData.subscriptions?.length || 0} active`);

        console.log('\n✅ News system is operational with the following status:');
        console.log('   - API endpoints are working');
        console.log('   - News feed is generating content');
        console.log('   - UI is displaying news cards');
        console.log('   - Vote functionality is present (but needs card storage)');
        console.log('   - Modal needs to be added to template');

    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        await browser.close();
    }
}

checkNewsSystemStatus().catch(console.error);
