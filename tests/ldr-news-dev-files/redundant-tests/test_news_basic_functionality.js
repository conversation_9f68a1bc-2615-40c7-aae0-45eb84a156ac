const puppeteer = require('puppeteer');

const BASE_URL = 'http://localhost:5000';
const TIMEOUT = 10000;

async function runBasicNewsTests() {
    console.log('🚀 Starting LDR News Basic Functionality Tests\n');

    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    let passed = 0;
    let failed = 0;

    try {
        const page = await browser.newPage();
        await page.setViewport({ width: 1280, height: 800 });

        // Capture console messages
        page.on('console', msg => {
            if (msg.type() === 'error') {
                console.log('🔴 Console error:', msg.text());
            }
        });

        console.log('📰 Test 1: Load News Page');
        try {
            await page.goto(`${BASE_URL}/news/`, { waitUntil: 'networkidle2' });
            const title = await page.title();
            console.log(`✅ Page loaded - Title: ${title}`);
            passed++;
        } catch (e) {
            console.log(`❌ Failed to load page: ${e.message}`);
            failed++;
            return;
        }

        console.log('\n📰 Test 2: Check News Feed Structure');
        try {
            // Check for news container or cards
            const hasNewsDisplay = await page.evaluate(() => {
                return document.querySelector('.news-card') !== null ||
                       document.querySelector('#news-feed') !== null ||
                       document.querySelector('.ldr-card') !== null;
            });

            if (hasNewsDisplay) {
                console.log('✅ News display elements found');
                passed++;

                // Count cards
                const cardCount = await page.$$eval('.card, .news-card', cards => cards.length);
                console.log(`   Found ${cardCount} news cards`);
            } else {
                console.log('❌ No news display elements found');
                failed++;
            }
        } catch (e) {
            console.log(`❌ Error checking news structure: ${e.message}`);
            failed++;
        }

        console.log('\n📰 Test 3: Check Modal Functionality');
        try {
            // Check if Bootstrap is loaded
            const hasBootstrap = await page.evaluate(() => {
                return typeof window.bootstrap !== 'undefined';
            });
            console.log(`   Bootstrap loaded: ${hasBootstrap}`);

            // Check for modal element
            const hasModal = await page.$('#newsModal') !== null;
            if (hasModal) {
                console.log('✅ Modal element found');
                passed++;
            } else {
                console.log('❌ Modal element not found');
                failed++;
            }
        } catch (e) {
            console.log(`❌ Error checking modal: ${e.message}`);
            failed++;
        }

        console.log('\n📰 Test 4: Check API Connectivity');
        try {
            // Make API call
            const response = await page.evaluate(async () => {
                try {
                    const res = await fetch('/news/api/feed?user_id=test_user&limit=5');
                    return {
                        status: res.status,
                        ok: res.ok,
                        data: await res.json()
                    };
                } catch (e) {
                    return { error: e.message };
                }
            });

            if (response.ok) {
                console.log('✅ API responded successfully');
                console.log(`   Status: ${response.status}`);
                console.log(`   News items: ${response.data.news_items?.length || 0}`);
                passed++;
            } else {
                console.log(`❌ API error - Status: ${response.status}`);
                failed++;
            }
        } catch (e) {
            console.log(`❌ Error testing API: ${e.message}`);
            failed++;
        }

        console.log('\n📰 Test 5: Check Vote Buttons');
        try {
            const voteButtons = await page.$$('.vote-button, .btn-upvote, .btn-downvote, [onclick*="vote"]');
            if (voteButtons.length > 0) {
                console.log(`✅ Found ${voteButtons.length} vote buttons`);
                passed++;

                // Try clicking first vote button
                const firstButton = voteButtons[0];
                await firstButton.click();
                await new Promise(resolve => setTimeout(resolve, 1000));
                console.log('   Clicked vote button');
            } else {
                console.log('⚠️  No vote buttons found');
            }
        } catch (e) {
            console.log(`❌ Error with vote buttons: ${e.message}`);
            failed++;
        }

        console.log('\n📰 Test 6: Check for JavaScript Errors');
        const jsErrors = [];
        page.on('pageerror', error => jsErrors.push(error.message));

        // Reload to catch errors
        await page.reload({ waitUntil: 'networkidle2' });
        await new Promise(resolve => setTimeout(resolve, 2000));

        if (jsErrors.length === 0) {
            console.log('✅ No JavaScript errors');
            passed++;
        } else {
            console.log(`❌ Found ${jsErrors.length} JavaScript errors`);
            jsErrors.forEach(err => console.log(`   - ${err}`));
            failed++;
        }

        console.log('\n' + '─'.repeat(50));
        console.log(`✅ Passed: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`📊 Success rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

    } catch (error) {
        console.error('\n❌ Fatal error:', error.message);
    } finally {
        await browser.close();
    }
}

// Run tests
runBasicNewsTests().catch(console.error);
