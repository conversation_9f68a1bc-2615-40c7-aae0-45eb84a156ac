/**
 * Final comprehensive test of news system
 */

const puppeteer = require('puppeteer');

async function testNewsFinal() {
    console.log('🧪 Final News System Test\n');

    const browser = await puppeteer.launch({
        headless: false,
        args: ['--no-sandbox', '--disable-setuid-sandbox'],
        devtools: false
    });

    const page = await browser.newPage();
    const results = {
        pageLoad: false,
        feedAPI: false,
        subscriptionCreate: false,
        subscriptionList: false,
        modalFunction: false,
        feedDisplay: false,
        voteFunction: false
    };

    try {
        // 1. Page Load Test
        console.log('1️⃣ Testing page load...');
        await page.goto('http://localhost:5000/news/', { waitUntil: 'networkidle2' });
        results.pageLoad = true;
        console.log('✅ Page loaded successfully');

        // 2. Feed API Test
        console.log('\n2️⃣ Testing feed API...');
        const feedTest = await page.evaluate(async () => {
            try {
                const response = await fetch('/news/api/feed?user_id=final_test&limit=5');
                const data = await response.json();
                return {
                    ok: response.ok,
                    hasItems: data.news_items && data.news_items.length > 0,
                    itemCount: data.news_items ? data.news_items.length : 0,
                    source: data.source
                };
            } catch (e) {
                return { ok: false, error: e.message };
            }
        });

        if (feedTest.ok) {
            results.feedAPI = true;
            console.log(`✅ Feed API working (${feedTest.itemCount} items from ${feedTest.source})`);
        } else {
            console.log(`❌ Feed API failed: ${feedTest.error}`);
        }

        // 3. Create Subscription Test
        console.log('\n3️⃣ Testing subscription creation...');
        const subTest = await page.evaluate(async () => {
            try {
                const response = await fetch('/news/api/subscribe', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        user_id: 'final_test',
                        query: 'Final test subscription',
                        subscription_type: 'search',
                        refresh_minutes: 360
                    })
                });
                const data = await response.json();
                return {
                    ok: response.ok,
                    status: data.status,
                    id: data.subscription_id
                };
            } catch (e) {
                return { ok: false, error: e.message };
            }
        });

        if (subTest.ok && subTest.status === 'success') {
            results.subscriptionCreate = true;
            console.log(`✅ Subscription created: ${subTest.id}`);
        } else {
            console.log(`❌ Subscription creation failed: ${subTest.error || 'Unknown error'}`);
        }

        // 4. List Subscriptions Test
        console.log('\n4️⃣ Testing subscription list...');
        const listTest = await page.evaluate(async () => {
            try {
                const response = await fetch('/news/api/subscriptions/final_test');
                const data = await response.json();
                return {
                    ok: response.ok,
                    count: data.subscriptions ? data.subscriptions.length : 0,
                    hasOurSub: data.subscriptions?.some(s => s.query === 'Final test subscription')
                };
            } catch (e) {
                return { ok: false, error: e.message };
            }
        });

        if (listTest.ok && listTest.hasOurSub) {
            results.subscriptionList = true;
            console.log(`✅ Subscription list working (${listTest.count} subscriptions)`);
        } else {
            console.log(`❌ Subscription list failed`);
        }

        // 5. Modal Functionality Test
        console.log('\n5️⃣ Testing modal functionality...');
        const modalTest = await page.evaluate(() => {
            try {
                showCreateSubscriptionModal();
                const modal = document.getElementById('subscriptionModal');
                const isOpen = modal && modal.classList.contains('show');

                if (isOpen) {
                    closeSubscriptionModal();
                    const isClosed = !modal.classList.contains('show');
                    return { success: true, opened: isOpen, closed: isClosed };
                }
                return { success: false, error: 'Modal did not open' };
            } catch (e) {
                return { success: false, error: e.message };
            }
        });

        if (modalTest.success) {
            results.modalFunction = true;
            console.log('✅ Modal functionality working');
        } else {
            console.log(`❌ Modal test failed: ${modalTest.error}`);
        }

        // 6. Feed Display Test
        console.log('\n6️⃣ Testing feed display...');
        await page.evaluate(() => loadNewsFeed());
        await new Promise(r => setTimeout(r, 2000));

        const displayTest = await page.evaluate(() => {
            const container = document.getElementById('news-feed-content');
            const cards = document.querySelectorAll('.news-card, .news-item');
            const skeleton = document.querySelectorAll('.loading-skeleton');
            const emptyState = document.querySelector('.empty-state');

            return {
                hasContainer: !!container,
                cardCount: cards.length,
                skeletonCount: skeleton.length,
                hasEmptyState: !!emptyState,
                containerHTML: container ? container.innerHTML.substring(0, 100) : null
            };
        });

        if (displayTest.cardCount > 0 || displayTest.hasEmptyState) {
            results.feedDisplay = true;
            console.log(`✅ Feed display working (${displayTest.cardCount} cards)`);
        } else {
            console.log('❌ Feed display not showing content');
        }

        // 7. Vote Functionality Test (if we have cards)
        if (displayTest.cardCount > 0) {
            console.log('\n7️⃣ Testing vote functionality...');
            const voteTest = await page.evaluate(async () => {
                try {
                    // Get first card's ID (mock or real)
                    const firstCard = document.querySelector('.news-card, .news-item');
                    const cardId = firstCard?.dataset.cardId || 'test-card-1';

                    const response = await fetch('/news/api/vote', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            user_id: 'final_test',
                            card_id: cardId,
                            vote: 'up'
                        })
                    });

                    return {
                        ok: response.ok,
                        status: response.status
                    };
                } catch (e) {
                    return { ok: false, error: e.message };
                }
            });

            if (voteTest.ok) {
                results.voteFunction = true;
                console.log('✅ Vote functionality working');
            } else {
                console.log(`⚠️  Vote test skipped or failed: ${voteTest.error || 'No cards to vote on'}`);
            }
        }

        // Summary
        console.log('\n📊 TEST SUMMARY');
        console.log('='.repeat(50));

        const passed = Object.values(results).filter(v => v).length;
        const total = Object.keys(results).length;
        const passRate = ((passed / total) * 100).toFixed(1);

        Object.entries(results).forEach(([test, passed]) => {
            console.log(`${passed ? '✅' : '❌'} ${test}`);
        });

        console.log(`\nPass Rate: ${passed}/${total} (${passRate}%)`);

        if (passed === total) {
            console.log('\n🎉 All tests passed! News system is fully functional.');
        } else {
            console.log('\n⚠️  Some tests failed. Check server logs for details.');
        }

        // Take final screenshot
        await page.screenshot({
            path: 'news_final_test_screenshot.png',
            fullPage: true
        });
        console.log('\n📸 Screenshot saved as news_final_test_screenshot.png');

        console.log('\n✨ Test complete! Browser will close in 5 seconds...');
        await new Promise(r => setTimeout(r, 5000));

    } catch (error) {
        console.error('❌ Test error:', error);
    } finally {
        await browser.close();
    }
}

// Run test
testNewsFinal().catch(console.error);
