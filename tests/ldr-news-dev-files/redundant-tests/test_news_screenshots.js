/**
 * Visual regression test for news page
 */

const BaseTest = require('../../utils/BaseTest');
const config = require('../../config/test.config');

async function testNewsVisual() {
    const test = new BaseTest('NewsVisual');

    await test.run(async (t) => {
        // Test different viewport sizes
        const viewports = [
            { name: 'desktop', width: 1920, height: 1080 },
            { name: 'laptop', width: 1366, height: 768 },
            { name: 'tablet', width: 768, height: 1024 },
            { name: 'mobile', width: 375, height: 667 }
        ];

        for (const viewport of viewports) {
            t.log(viewport.name, `Testing ${viewport.width}x${viewport.height} viewport`);

            // Set viewport
            await t.browserUtils.page.setViewport({
                width: viewport.width,
                height: viewport.height
            });

            // Navigate to news page
            await t.newsPage.goto();
            await t.wait(2000); // Wait for layout to stabilize

            // Take screenshot of initial state
            await t.screenshot(`${viewport.name}_initial`);

            // Perform search
            await t.newsPage.search('technology news');
            await t.wait(3000);

            // Screenshot with results
            await t.screenshot(`${viewport.name}_with_results`);

            // Check layout integrity
            const metrics = await t.newsPage.getPageMetrics();
            t.assert(metrics.errors === 0, `No errors in ${viewport.name} view`);

            // Test hover states (desktop only)
            if (viewport.width >= 1024) {
                const cards = await t.browserUtils.page.$$('.news-card');
                if (cards.length > 0) {
                    await cards[0].hover();
                    await t.wait(500);
                    await t.screenshot(`${viewport.name}_hover_state`);
                }
            }

            // Test dark mode if available
            const darkModeToggle = await t.browserUtils.page.$('[class*="dark-mode"], [class*="theme-toggle"]');
            if (darkModeToggle) {
                await darkModeToggle.click();
                await t.wait(1000);
                await t.screenshot(`${viewport.name}_dark_mode`);

                // Toggle back
                await darkModeToggle.click();
                await t.wait(500);
            }
        }

        // Test loading states
        t.log('Loading', 'Testing loading states');
        await t.browserUtils.page.setViewport(config.browser.defaultViewport);

        // Trigger a slow search
        await t.newsPage.search('comprehensive analysis of global events', 'comprehensive');

        // Capture loading state quickly
        await t.wait(100);
        await t.screenshot('loading_state');

        // Wait for completion
        await t.newsPage.waitForNewsToLoad();
        await t.screenshot('loaded_state');

        // Test error states
        t.log('Errors', 'Testing error states');

        // Try invalid API call
        try {
            await t.apiUtils.post('/news/api/invalid_endpoint', {});
        } catch (error) {
            // Expected
        }

        // Check if error message appears
        await t.wait(1000);
        const alert = await t.newsPage.getAlertMessage();
        if (alert) {
            await t.screenshot('error_state');
            t.success('Error state captured');
        }

        // Performance visualization
        const performance = await t.measurePerformance();
        t.log('Performance', `Visual tests completed. Load time: ${performance.loadTime}ms`);
    });
}

// Run test if executed directly
if (require.main === module) {
    testNewsVisual().catch(console.error);
}

module.exports = testNewsVisual;
