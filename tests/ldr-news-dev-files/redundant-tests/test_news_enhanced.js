const puppeteer = require('puppeteer');

async function testEnhancedNews() {
    console.log('🔍 Testing Enhanced News Features\n');

    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();
        await page.setViewport({ width: 1280, height: 800 });

        console.log('📰 Loading news page...');
        await page.goto('http://localhost:5000/news/', { waitUntil: 'networkidle2' });

        // Wait for news items to load
        await page.waitForSelector('.news-item', { timeout: 10000 });
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check enhanced features
        const features = await page.evaluate(() => {
            const firstItem = document.querySelector('.news-item');
            if (!firstItem) return { error: 'No news items found' };

            return {
                // Check headline
                headline: firstItem.querySelector('.news-headline')?.textContent || 'No headline',

                // Check findings preview
                hasFindings: !!firstItem.querySelector('.news-findings'),
                findingsLength: firstItem.querySelector('.news-findings')?.textContent.length || 0,

                // Check topics
                topics: Array.from(firstItem.querySelectorAll('.topic-tag')).map(tag => tag.textContent),
                topicCount: firstItem.querySelectorAll('.topic-tag').length,

                // Check View Full Report button
                hasViewButton: !!firstItem.querySelector('a[href*="/results/"]'),
                viewButtonText: firstItem.querySelector('a[href*="/results/"]')?.textContent || 'No button',
                viewButtonHref: firstItem.querySelector('a[href*="/results/"]')?.href || 'No href',

                // Check card height
                cardHeight: firstItem.offsetHeight,

                // Check if card has proper structure
                hasVoteButtons: firstItem.querySelectorAll('.vote-btn').length === 2,
                hasActionButtons: !!firstItem.querySelector('.action-buttons')
            };
        });

        console.log('\n📊 Enhanced Features Check:');
        console.log(`✅ Headline: "${features.headline}"`);
        console.log(`✅ Has findings preview: ${features.hasFindings}`);
        console.log(`✅ Findings preview length: ${features.findingsLength} chars`);
        console.log(`✅ Topics found: ${features.topicCount}`);
        if (features.topics.length > 0) {
            console.log(`   Topics: ${features.topics.join(', ')}`);
        }
        console.log(`✅ View Full Report button: ${features.hasViewButton}`);
        console.log(`   Button text: "${features.viewButtonText}"`);
        console.log(`   Links to: ${features.viewButtonHref}`);
        console.log(`✅ Card height: ${features.cardHeight}px (min should be 350px)`);
        console.log(`✅ Has vote buttons: ${features.hasVoteButtons}`);
        console.log(`✅ Has action buttons: ${features.hasActionButtons}`);

        // Test topic click
        console.log('\n🔘 Testing topic click...');
        if (features.topicCount > 0) {
            await page.click('.topic-tag');
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Check if alert showed
            const alertVisible = await page.evaluate(() => {
                const alert = document.getElementById('news-alert');
                return alert && alert.style.display !== 'none';
            });
            console.log(`✅ Topic click alert shown: ${alertVisible}`);
        }

        // Take screenshot
        await page.screenshot({
            path: 'tests/ui_tests/screenshots/news_enhanced.png',
            fullPage: true
        });
        console.log('\n📸 Screenshot saved to: tests/ui_tests/screenshots/news_enhanced.png');

        // Test API response for enhanced data
        console.log('\n🔌 Testing API for enhanced data...');
        const apiResponse = await page.evaluate(async () => {
            const response = await fetch('/news/api/feed?user_id=test&limit=1');
            const data = await response.json();
            return data.news_items?.[0] || null;
        });

        if (apiResponse) {
            console.log('\nAPI Response sample:');
            console.log(`- Headline: "${apiResponse.headline}"`);
            console.log(`- Original query length: ${apiResponse.original_query?.length || 0}`);
            console.log(`- Topics: ${apiResponse.topics?.join(', ') || 'None'}`);
            console.log(`- Has findings: ${!!apiResponse.findings}`);
        }

        console.log('\n✅ Enhanced news features test completed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        await browser.close();
    }
}

testEnhancedNews().catch(console.error);
