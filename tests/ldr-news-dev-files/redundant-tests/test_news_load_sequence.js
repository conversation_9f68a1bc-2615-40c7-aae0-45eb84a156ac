const puppeteer = require('puppeteer');

async function testLoadSequence() {
    console.log('🔍 Testing News Page Load Sequence\n');

    const browser = await puppeteer.launch({
        headless: 'new',
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    try {
        const page = await browser.newPage();

        // Track console logs
        const logs = [];
        page.on('console', msg => {
            const text = msg.text();
            logs.push(text);
            console.log(`[Browser]: ${text}`);
        });

        await page.setViewport({ width: 1280, height: 800 });

        // Navigate and wait for network idle
        console.log('📰 Loading news page...\n');
        await page.goto('http://localhost:5000/news/', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });

        // Wait for JavaScript to execute
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Check what happened
        const pageState = await page.evaluate(() => {
            const container = document.getElementById('news-feed-content');
            const items = container ? container.querySelectorAll('.news-item, .news-card') : [];

            return {
                containerExists: !!container,
                totalItems: items.length,
                itemTypes: Array.from(items).map(item => ({
                    class: item.className,
                    id: item.dataset.newsId || item.dataset.researchId || 'no-id',
                    headline: item.querySelector('.news-headline, .news-title')?.innerText?.substring(0, 50) || 'no-headline'
                })),
                newsItemsArrayLength: typeof newsItems !== 'undefined' ? newsItems.length : 0,
                firstNewsItem: typeof newsItems !== 'undefined' && newsItems.length > 0 ? {
                    id: newsItems[0].id,
                    headline: newsItems[0].headline
                } : null
            };
        });

        console.log('\n📊 Page State:');
        console.log(`- Container exists: ${pageState.containerExists}`);
        console.log(`- Total items displayed: ${pageState.totalItems}`);
        console.log(`- newsItems array length: ${pageState.newsItemsArrayLength}`);

        if (pageState.firstNewsItem) {
            console.log(`- First array item: ${pageState.firstNewsItem.id} - "${pageState.firstNewsItem.headline}"`);
        }

        console.log('\n📋 Displayed items:');
        pageState.itemTypes.forEach((item, i) => {
            console.log(`  ${i + 1}. [${item.class}] ${item.id}: "${item.headline}..."`);
        });

        // Check if renderNewsItems was called
        const renderCheck = await page.evaluate(() => {
            return {
                renderNewsFunctionExists: typeof renderNewsItems === 'function',
                loadNewsFeedExists: typeof loadNewsFeed === 'function'
            };
        });

        console.log('\n🔧 Function availability:');
        console.log(`- renderNewsItems exists: ${renderCheck.renderNewsFunctionExists}`);
        console.log(`- loadNewsFeed exists: ${renderCheck.loadNewsFeedExists}`);

        // Look for specific console logs
        const apiLog = logs.find(log => log.includes('Loaded') && log.includes('news items from API'));
        if (apiLog) {
            console.log(`\n✅ Found API load log: "${apiLog}"`);
        } else {
            console.log('\n⚠️  No API load log found');
        }

        // Take screenshot
        await page.screenshot({
            path: 'tests/ui_tests/screenshots/news_load_sequence.png',
            fullPage: true
        });
        console.log('\n📸 Screenshot saved');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
    } finally {
        await browser.close();
    }
}

testLoadSequence().catch(console.error);
