# Citation Formatting Bugs

This folder contains tests for known bugs in the citation formatting system.

## Bug: Multiple Citations to Same Source

### Description
When using non-IEEE citation formats (domain-based formats), if a source is cited multiple times, the system might incorrectly revert to IEEE-style numbering [1] instead of maintaining the selected format.

### Expected Behavior
- **Domain format**: All citations to `arxiv.org/abs/1234` should appear as `[arxiv.org]`
- **Domain ID format**: Should use `[arxiv.org-1]` consistently when there are multiple arxiv sources
- **Domain ID Always format**: Should always use `[arxiv.org-1]` even for single sources

### Current Behavior
The citation formatter might mix formats or revert to IEEE-style `[1]` numbering when the same source is cited multiple times.

### Test Coverage
- `test_multiple_citations_bug.py` - Contains comprehensive tests for this issue

## Running the Tests

```bash
# Run all citation bug tests
pdm run pytest tests/citation_bugs/ -v

# Run specific test
pdm run pytest tests/citation_bugs/test_multiple_citations_bug.py -v
```

## Notes
The current implementation doesn't actually have IEEE/APA/MLA citation styles. The available modes are:
- `NUMBER_HYPERLINKS`: [1] with hyperlinks
- `DOMAIN_HYPERLINKS`: [arxiv.org] with hyperlinks
- `DOMAIN_ID_HYPERLINKS`: Smart numbering - [arxiv.org] or [arxiv.org-1]
- `DOMAIN_ID_ALWAYS_HYPERLINKS`: Always numbered - [arxiv.org-1]
- `NO_HYPERLINKS`: [1] without hyperlinks

These control how citations appear in the text, not the bibliographic format of the references.
