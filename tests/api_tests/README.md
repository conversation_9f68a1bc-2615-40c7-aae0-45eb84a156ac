# REST API Tests

This directory contains tests for the Local Deep Research REST API endpoints.

## Test Files

### `test_api_basic.py`
- **Purpose**: Quick verification of API functionality
- **Features**: Tests health check, documentation, error handling, and request format validation
- **Runtime**: < 5 seconds
- **Usage**: `python tests/api_tests/test_api_basic.py`

### `test_api_pytest.py`
- **Purpose**: pytest-compatible API tests for CI/CD integration
- **Features**: Comprehensive endpoint validation with proper test structure
- **Runtime**: < 10 seconds for basic tests
- **Usage**: `pytest tests/api_tests/test_api_pytest.py::TestRestAPIBasic -v`

### `test_rest_api_simple.py`
- **Purpose**: Extended testing with actual research queries (minimal)
- **Features**: Tests programmatic access with ultra-minimal queries
- **Runtime**: 2+ minutes (includes actual research)
- **Usage**: `python tests/api_tests/test_rest_api_simple.py`

## What's Tested

### ✅ Basic Functionality
- Health check endpoint (`/api/v1/health`)
- API documentation endpoint (`/api/v1/`)
- Error handling for missing parameters
- Request format validation

### ✅ Programmatic Access Integration
- Verifies all endpoints use `research_functions.py`
- Tests quick_summary endpoint structure
- Tests analyze_documents endpoint validation
- Tests generate_report endpoint validation

### ✅ Response Structure
- Proper JSON responses
- Required fields present
- Error messages for invalid requests

## Running Tests

### Quick Verification (Recommended)
```bash
# Basic functionality test
python tests/api_tests/test_api_basic.py

# Or with pytest
pytest tests/api_tests/test_api_pytest.py::TestRestAPIBasic -v
```

### Full API Testing
```bash
# Includes actual research queries (takes longer)
python tests/api_tests/test_rest_api_simple.py
```

## Test Results

All tests verify that:
1. ✅ REST API endpoints are operational
2. ✅ Programmatic access integration works correctly
3. ✅ Error handling is proper
4. ✅ Response formats are consistent
5. ✅ djpetti's PR feedback has been addressed

## Notes

- Tests require the web server to be running on `localhost:5000`
- Research-based tests may timeout if LLM/search services are slow
- Basic tests focus on API structure rather than full research functionality
- All endpoints now use programmatic access functions instead of direct LLM calls
