#!/usr/bin/env python3
"""
Populate search engine settings in the database
"""

import os
import sys
from pathlib import Path

from loguru import logger

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.local_deep_research.database.models import Setting
from src.local_deep_research.utilities.db_utils import get_db_session


def populate_search_engines():
    """Populate search engine settings in the database"""

    # Define search engines with their properties
    search_engines = {
        "searxng": {
            "display_name": "SearXNG",
            "description": "Privacy-focused metasearch engine",
            "strengths": ["Privacy", "No tracking", "Multiple sources"],
        },
        "google": {
            "display_name": "Google",
            "description": "Google search engine",
            "strengths": ["Comprehensive", "Fast", "Relevant results"],
        },
        "bing": {
            "display_name": "Bing",
            "description": "Microsoft Bing search engine",
            "strengths": ["Good for technical queries", "Image search", "News"],
        },
        "duckduckgo": {
            "display_name": "DuckDuckGo",
            "description": "Privacy-focused search engine",
            "strengths": ["Privacy", "No tracking", "Instant answers"],
        },
        "wikipedia": {
            "display_name": "Wikipedia",
            "description": "Wikipedia search",
            "strengths": ["Encyclopedic content", "Reliable", "Detailed"],
        },
        "arxiv": {
            "display_name": "arXiv",
            "description": "Scientific paper repository",
            "strengths": ["Academic papers", "Research", "Preprints"],
        },
        "pubmed": {
            "display_name": "PubMed",
            "description": "Medical research database",
            "strengths": ["Medical research", "Clinical studies", "Healthcare"],
        },
        "semantic_scholar": {
            "display_name": "Semantic Scholar",
            "description": "AI-powered research tool",
            "strengths": [
                "Academic search",
                "Citation analysis",
                "AI insights",
            ],
        },
    }

    # Also add the auto option
    auto_engine = {
        "auto": {
            "display_name": "Auto (Multiple Engines)",
            "description": "Automatically select best search engines",
            "strengths": ["Adaptive", "Multiple sources", "Best coverage"],
        }
    }

    session = get_db_session()

    try:
        # Add auto engine
        for key, props in auto_engine.items():
            # Display name
            display_setting = Setting(
                key="search.engine.auto.display_name",
                value=props["display_name"],
                type="SEARCH",
                category="search",
                name="Auto Display Name",
                description="Display name for auto search engine",
            )
            existing = (
                session.query(Setting)
                .filter_by(key=display_setting.key)
                .first()
            )
            if not existing:
                session.add(display_setting)
                logger.info(f"Added {display_setting.key}")

            # Description
            desc_setting = Setting(
                key="search.engine.auto.description",
                value=props["description"],
                type="SEARCH",
                category="search",
                name="Auto Description",
                description="Description for auto search engine",
            )
            existing = (
                session.query(Setting).filter_by(key=desc_setting.key).first()
            )
            if not existing:
                session.add(desc_setting)
                logger.info(f"Added {desc_setting.key}")

            # Strengths
            strengths_setting = Setting(
                key="search.engine.auto.strengths",
                value=props["strengths"],
                type="SEARCH",
                category="search",
                name="Auto Strengths",
                description="Strengths of auto search engine",
            )
            existing = (
                session.query(Setting)
                .filter_by(key=strengths_setting.key)
                .first()
            )
            if not existing:
                session.add(strengths_setting)
                logger.info(f"Added {strengths_setting.key}")

        # Add web search engines
        for engine_name, props in search_engines.items():
            # Display name
            display_setting = Setting(
                key=f"search.engine.web.{engine_name}.display_name",
                value=props["display_name"],
                type="SEARCH",
                category="search",
                name=f"{engine_name} Display Name",
                description=f"Display name for {engine_name}",
            )
            existing = (
                session.query(Setting)
                .filter_by(key=display_setting.key)
                .first()
            )
            if not existing:
                session.add(display_setting)
                logger.info(f"Added {display_setting.key}")

            # Description
            desc_setting = Setting(
                key=f"search.engine.web.{engine_name}.description",
                value=props["description"],
                type="SEARCH",
                category="search",
                name=f"{engine_name} Description",
                description=f"Description for {engine_name}",
            )
            existing = (
                session.query(Setting).filter_by(key=desc_setting.key).first()
            )
            if not existing:
                session.add(desc_setting)
                logger.info(f"Added {desc_setting.key}")

            # Strengths
            strengths_setting = Setting(
                key=f"search.engine.web.{engine_name}.strengths",
                value=props["strengths"],
                type="SEARCH",
                category="search",
                name=f"{engine_name} Strengths",
                description=f"Strengths of {engine_name}",
            )
            existing = (
                session.query(Setting)
                .filter_by(key=strengths_setting.key)
                .first()
            )
            if not existing:
                session.add(strengths_setting)
                logger.info(f"Added {strengths_setting.key}")

        # Commit all changes
        session.commit()
        logger.info("Successfully populated search engine settings")

        # Verify by querying
        search_settings = (
            session.query(Setting)
            .filter(
                Setting.type == "SEARCH", Setting.key.contains("display_name")
            )
            .all()
        )

        logger.info(f"Total search engine settings: {len(search_settings)}")
        for setting in search_settings:
            logger.info(f"  {setting.key}: {setting.value}")

    except Exception:
        logger.exception("Error populating search engines")
        session.rollback()
    finally:
        session.close()


if __name__ == "__main__":
    # Need to set this for test user database
    os.environ["LDR_CURRENT_USER"] = "testuser"
    populate_search_engines()
