"""
Comprehensive API Tests for Local Deep Research.

This test suite checks all API endpoints for proper authentication,
response formats, and basic functionality.
"""

import json
from loguru import logger


class TestAllAPIs:
    """Test suite for all API endpoints."""

    def test_auth_apis(self, authenticated_client):
        """Test authentication APIs."""
        # Check auth status
        response = authenticated_client.get("/auth/check")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["authenticated"] is True
        assert "username" in data

        # Test integrity check
        response = authenticated_client.get("/auth/integrity-check")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "integrity" in data
        assert "username" in data

    def test_settings_apis(self, authenticated_client):
        """Test settings APIs."""
        # Get all settings
        response = authenticated_client.get("/settings/api")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["status"] == "success"
        assert "settings" in data

        # Get categories
        response = authenticated_client.get("/settings/api/categories")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "categories" in data

        # Get types
        response = authenticated_client.get("/settings/api/types")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "types" in data

        # Get available models
        response = authenticated_client.get("/settings/api/available-models")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert (
            "providers" in data or "models" in data
        )  # API might return either

        # Get available search engines - THIS IS FAILING
        logger.info("Testing search engines API...")
        response = authenticated_client.get(
            "/settings/api/available-search-engines"
        )
        logger.info(f"Search engines response: {response.status_code}")
        if response.status_code != 200:
            logger.error(f"Search engines error: {response.data}")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "engines" in data or "engine_options" in data

        # Get warnings
        response = authenticated_client.get("/settings/api/warnings")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert isinstance(
            data, (list, dict)
        )  # API might return list or dict with warnings

        # Test get/set specific setting
        response = authenticated_client.get("/settings/api/llm.temperature")
        if response.status_code == 404:
            # Setting doesn't exist, create it
            response = authenticated_client.put(
                "/settings/api/llm.temperature",
                json={"value": 0.7},
                content_type="application/json",
            )
            assert response.status_code in [200, 201]

        # Get data location
        response = authenticated_client.get("/settings/api/data-location")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert (
            "data_directory" in data or "data_dir" in data
        )  # API might return either
        assert "is_custom" in data

    def test_research_apis(self, authenticated_client):
        """Test research APIs."""
        # Get research history
        response = authenticated_client.get("/history/api")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "items" in data or "history" in data  # API might return either

        # Start a test research
        research_data = {
            "query": "API test research query",
            "model": "gpt-3.5-turbo",
            "search_engines": ["searxng"],
            "local_context": 2000,
            "web_context": 2000,
            "temperature": 0.7,
        }

        response = authenticated_client.post(
            "/research/api/start",
            json=research_data,
            content_type="application/json",
        )

        # The research start API might fail in test environment due to
        # missing LLM configuration or other issues. We'll accept any
        # response as long as it's a valid HTTP response.
        assert response.status_code in [200, 400, 404, 500]

        # If successful, test the other endpoints
        if response.status_code == 200:
            data = json.loads(response.data)
            if "research_id" in data:
                research_id = data["research_id"]

                # Check status
                response = authenticated_client.get(
                    f"/research/api/research/{research_id}/status"
                )
                assert response.status_code == 200

                # Terminate research
                response = authenticated_client.post(
                    f"/research/api/research/{research_id}/terminate"
                )
                assert response.status_code in [200, 404]

    def test_metrics_apis(self, authenticated_client):
        """Test metrics APIs."""
        # Get metrics summary
        response = authenticated_client.get("/metrics/api/metrics")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "metrics" in data or "summary" in data  # API might return either

        # Get enhanced metrics
        response = authenticated_client.get("/metrics/api/metrics/enhanced")
        assert response.status_code == 200

        # Get star reviews
        response = authenticated_client.get("/metrics/api/star-reviews")
        assert response.status_code == 200

        # Get pricing info
        response = authenticated_client.get("/metrics/api/pricing")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "pricing" in data or "models" in data  # API might return either

        # Get cost analytics
        response = authenticated_client.get("/metrics/api/cost-analytics")
        assert response.status_code == 200

    def test_benchmark_apis(self, authenticated_client):
        """Test benchmark APIs."""
        # Get benchmark history
        response = authenticated_client.get("/benchmark/api/history")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "runs" in data or "history" in data  # API might return either

        # Get saved configs
        response = authenticated_client.get("/benchmark/api/configs")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "configs" in data

        # Check running benchmark
        response = authenticated_client.get("/benchmark/api/running")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert (
            "is_running" in data or "success" in data
        )  # API might return either

        # Validate config
        test_config = {
            "name": "Test Config",
            "queries": ["test query"],
            "models": ["gpt-3.5-turbo"],
            "search_engines": ["searxng"],
        }
        response = authenticated_client.post(
            "/benchmark/api/validate-config",
            json=test_config,
            content_type="application/json",
        )
        assert response.status_code == 200

    def test_history_apis(self, authenticated_client):
        """Test history APIs."""
        response = authenticated_client.get("/history/api")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "items" in data or "history" in data  # API might return either
        # Get the actual history list
        history_data = data.get("items", data.get("history", []))
        assert isinstance(history_data, list)

    def test_config_apis(self, authenticated_client):
        """Test configuration APIs."""
        # Get current config - api_bp is registered with /research/api prefix
        response = authenticated_client.get(
            "/research/api/settings/current-config"
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert "success" in data  # API returns success status
        if data.get("success"):
            assert "config" in data  # Config is nested under config key

        # Get public config - This endpoint may not exist
        response = authenticated_client.get("/api/config")
        if response.status_code == 404:
            # Endpoint doesn't exist, skip test
            pass
        else:
            assert response.status_code == 200
            data = json.loads(response.data)
            assert "version" in data or "available_models" in data

    def test_health_check_apis(self, authenticated_client):
        """Test health check APIs."""
        # Check Ollama status - the api_bp is registered with /research/api prefix
        response = authenticated_client.get("/research/api/check/ollama_status")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert (
            "running" in data or "available" in data
        )  # API might return either

        # Check Ollama model
        response = authenticated_client.get("/research/api/check/ollama_model")
        # This might return 400 if no model specified
        assert response.status_code in [200, 400]
