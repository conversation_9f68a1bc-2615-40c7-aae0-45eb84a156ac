"""Test CSRF with header."""

import time


class TestCSRFHeader:
    """Test CSRF protection with headers."""

    def test_csrf_with_header(self, client, app):
        """Test CSRF protection using headers."""
        # Since we disabled CSRF for testing, this test verifies the login flow
        # In production, CSRF would be required

        # Generate unique username to avoid conflicts
        test_username = f"testuser_csrf_{int(time.time() * 1000)}"

        # Get login page
        response = client.get("/auth/login")
        assert response.status_code == 200

        # In test mode, CSRF is disabled, so we can login without token
        login_data = {
            "username": test_username,
            "password": "testpassword123",
        }

        # First register the user
        register_response = client.post(
            "/auth/register",
            data={
                "username": test_username,
                "password": "testpassword123",
                "confirm_password": "testpassword123",
                "acknowledge": "true",
            },
        )
        assert register_response.status_code in [200, 302]

        # Now test login
        response = client.post(
            "/auth/login",
            data=login_data,
            follow_redirects=False,
        )

        assert response.status_code in [200, 302]

        if response.status_code == 302:
            # Login successful, redirecting
            assert "/" in response.location or "/research" in response.location

    def test_api_csrf_exemption(self, authenticated_client):
        """Test that API routes are exempt from CSRF."""
        # API routes should work without CSRF token
        response = authenticated_client.get("/api/v1/health")
        assert response.status_code == 200

        # Research API should also work
        response = authenticated_client.get("/research/api/history")
        assert response.status_code in [200, 404]  # 404 if no history yet
