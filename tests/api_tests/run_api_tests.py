#!/usr/bin/env python3
"""
Runner script for API tests.

Usage:
    python run_api_tests.py              # Run all API tests
    python run_api_tests.py search       # Run search engines test only
"""

import subprocess
import sys
from pathlib import Path


def run_all_tests():
    """Run all API tests."""
    print("Running all API tests...")
    result = subprocess.run(
        [sys.executable, "test_all_apis.py"], cwd=str(Path(__file__).parent)
    )
    return result.returncode


def run_search_engines_test():
    """Run search engines API test."""
    print("Running search engines API test...")
    result = subprocess.run(
        [sys.executable, "test_search_engines_api.py"],
        cwd=str(Path(__file__).parent),
    )
    return result.returncode


def main():
    """Main entry point."""
    if len(sys.argv) > 1 and sys.argv[1] == "search":
        return run_search_engines_test()
    else:
        return run_all_tests()


if __name__ == "__main__":
    sys.exit(main())
