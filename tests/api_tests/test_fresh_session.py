"""Test with completely fresh session."""

import pytest
import re
import time
from loguru import logger


class TestFreshSession:
    """Test authentication with fresh session."""

    def test_fresh_session_login(self, client):
        """Test login with completely fresh session."""
        # Get login page
        response = client.get("/auth/login")
        assert response.status_code == 200
        logger.info(f"GET /auth/login -> {response.status_code}")

        # Extract CSRF token
        csrf_match = re.search(
            r'name="csrf_token" value="([^"]+)"', response.data.decode()
        )
        csrf_token = csrf_match.group(1) if csrf_match else None
        logger.info(f"CSRF token: {csrf_token}")

        # Create unique test username to avoid conflicts
        test_username = f"testuser_fresh_{int(time.time() * 1000)}"
        test_password = "testpassword123"

        # Register user first (in case it doesn't exist)
        register_data = {
            "username": test_username,
            "password": test_password,
            "confirm_password": test_password,
            "acknowledge": "true",
            "csrf_token": csrf_token,
        }

        client.post("/auth/register", data=register_data)

        # Get fresh login page for new CSRF token
        response = client.get("/auth/login")
        csrf_match = re.search(
            r'name="csrf_token" value="([^"]+)"', response.data.decode()
        )
        csrf_token = csrf_match.group(1) if csrf_match else None

        # Login
        login_data = {
            "username": test_username,
            "password": test_password,
            "csrf_token": csrf_token,
        }

        logger.info("Posting login data...")
        response = client.post(
            "/auth/login", data=login_data, follow_redirects=False
        )

        logger.info(f"POST /auth/login -> {response.status_code}")

        if response.status_code == 302:
            logger.info("✅ Login successful! Got redirect")

            # Follow redirect
            location = response.headers.get("Location", "/")
            response = client.get(location, follow_redirects=False)
            logger.info(f"GET {location} -> {response.status_code}")

            if response.status_code == 200:
                logger.info("✅ Successfully accessed home page!")
            elif response.status_code == 302:
                logger.error(
                    f"Got redirected again to: {response.headers.get('Location')}"
                )

            # Test auth check
            response = client.get("/auth/check")
            logger.info(f"GET /auth/check -> {response.status_code}")
            assert response.status_code == 200

            data = response.get_json()
            assert data.get("authenticated") is True
            logger.info(f"✅ Authenticated as: {data.get('username')}")

        else:
            pytest.fail(
                f"Login failed with status code: {response.status_code}"
            )

    def test_fresh_session_api_access(self, client):
        """Test API access with fresh session."""
        # Try to access API without login
        response = client.get("/settings/api")
        assert response.status_code in [
            401,
            302,
        ]  # Should be unauthorized or redirect

        # Register and login
        response = client.get("/auth/login")
        csrf_match = re.search(
            r'name="csrf_token" value="([^"]+)"', response.data.decode()
        )
        csrf_token = csrf_match.group(1) if csrf_match else None

        # Create unique test username to avoid conflicts
        test_username = f"testuser_api_{int(time.time() * 1000)}"
        test_password = "testpassword123"

        register_data = {
            "username": test_username,
            "password": test_password,
            "confirm_password": test_password,
            "acknowledge": "true",
            "csrf_token": csrf_token,
        }

        client.post("/auth/register", data=register_data)

        # Get fresh login page
        response = client.get("/auth/login")
        csrf_match = re.search(
            r'name="csrf_token" value="([^"]+)"', response.data.decode()
        )
        csrf_token = csrf_match.group(1) if csrf_match else None

        login_data = {
            "username": test_username,
            "password": test_password,
            "csrf_token": csrf_token,
        }

        response = client.post(
            "/auth/login", data=login_data, follow_redirects=True
        )
        assert response.status_code == 200

        # Now try API access
        response = client.get("/settings/api")
        assert response.status_code == 200
        data = response.get_json()
        assert data["status"] == "success"
        assert "settings" in data
