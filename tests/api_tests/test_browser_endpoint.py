#!/usr/bin/env python3
"""
Test the browser's research creation endpoint specifically
"""

from flask import json


class TestBrowserEndpoint:
    """Test browser-specific endpoints."""

    def test_start_research_endpoint(self, authenticated_client):
        """Test the browser endpoint /api/start_research."""
        research_data = {
            "query": "Test from browser endpoint",
            "mode": "quick",
            "model_provider": "OLLAMA",
            "model": "llama2",
            "search_engine": "searxng",
            "max_results": 10,
            "time_period": "y",
            "iterations": 1,
            "questions_per_iteration": 3,
            "strategy": "source-based",
            "local_context": 2000,
            "web_context": 2000,
            "temperature": 0.7,
        }

        response = authenticated_client.post(
            "/api/start_research",
            json=research_data,
            content_type="application/json",
        )

        assert response.status_code in [200, 202]
        data = json.loads(response.data)

        if response.status_code == 200:
            assert data.get("status") in ["success", "processing"]
            if "research_id" in data:
                assert isinstance(data["research_id"], (str, int))

    def test_research_status_endpoint(self, authenticated_client):
        """Test research status endpoint."""
        # First create a research
        research_data = {
            "query": "Test research for status check",
            "mode": "quick",
            "model_provider": "OLLAMA",
            "model": "llama2",
            "search_engine": "wikipedia",
            "iterations": 1,
        }

        response = authenticated_client.post(
            "/api/start_research",
            json=research_data,
            content_type="application/json",
        )

        if response.status_code == 200:
            data = json.loads(response.data)
            research_id = data.get("research_id")

            if research_id:
                # Check status
                status_response = authenticated_client.get(
                    f"/research/api/status/{research_id}"
                )
                assert status_response.status_code in [200, 404]

                if status_response.status_code == 200:
                    status_data = json.loads(status_response.data)
                    assert "status" in status_data

    def test_endpoint_requires_authentication(self, client):
        """Test that endpoint requires authentication."""
        research_data = {
            "query": "Test without auth",
            "mode": "quick",
        }

        response = client.post(
            "/api/start_research",
            json=research_data,
            content_type="application/json",
        )

        # Should either redirect to login or return 401
        assert response.status_code in [302, 401]

        if response.status_code == 302:
            assert "/auth/login" in response.location
