"""
Basic API tests - only test endpoints that should respond quickly.
Focus on verifying the API is working without doing actual research.
"""

import pytest
from flask import json


class TestBasicAPI:
    """Test basic API functionality."""

    def test_health_check(self, authenticated_client):
        """Test health check endpoint."""
        response = authenticated_client.get("/api/v1/health")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["status"] == "ok"
        assert "timestamp" in data

    def test_api_documentation(self, authenticated_client):
        """Test API documentation endpoint."""
        response = authenticated_client.get("/api/v1/")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["api_version"] == "v1"
        assert "endpoints" in data
        assert len(data["endpoints"]) >= 3

    def test_error_handling(self, authenticated_client):
        """Test error handling for malformed requests."""
        # Test missing query parameter
        response = authenticated_client.post(
            "/api/v1/quick_summary",
            json={},
            content_type="application/json",
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert "error" in data
        assert "required" in data["error"].lower()

        # Test missing collection_name
        response = authenticated_client.post(
            "/api/v1/analyze_documents",
            json={"query": "test"},
            content_type="application/json",
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert "error" in data

    @pytest.mark.requires_llm
    def test_api_structure(
        self, authenticated_client, setup_database_for_all_tests
    ):
        """Test that API accepts properly formatted requests."""
        payload = {
            "query": "test",
            "search_tool": "wikipedia",
            "iterations": 1,
            "temperature": 0.7,
        }

        print(
            f"\n[DEBUG] Sending request to /api/v1/quick_summary with payload: {payload}"
        )

        # Send request with proper format
        response = authenticated_client.post(
            "/api/v1/quick_summary",
            json=payload,
            content_type="application/json",
        )

        print(f"[DEBUG] Response status code: {response.status_code}")
        print(
            f"[DEBUG] Response data: {response.data.decode()[:500]}"
        )  # First 500 chars

        if response.status_code == 500:
            # Print full error details for debugging
            try:
                error_data = json.loads(response.data)
                print(f"[DEBUG] Error response JSON: {error_data}")
            except:
                print(f"[DEBUG] Raw error response: {response.data.decode()}")

        # The API should accept the request format
        # It might return 200 with processing started, or 400 if there's a validation error
        assert response.status_code in [200, 202, 400]

        if response.status_code == 400:
            # Check that error message is informative
            data = json.loads(response.data)
            assert "error" in data

    def test_unauthenticated_access(self, client):
        """Test that unauthenticated requests are rejected."""
        response = client.get("/api/v1/health")
        # API endpoints might allow unauthenticated access for health checks
        # or might redirect to login
        assert response.status_code in [200, 302, 401]
