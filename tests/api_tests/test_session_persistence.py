"""Test session persistence after login."""

import json
import time
from loguru import logger


class TestSessionPersistence:
    """Test session persistence functionality."""

    def test_session_after_login(self, client):
        """Test that session persists after login."""
        # Create unique test username to avoid conflicts
        test_username = f"testuser_session_{int(time.time() * 1000)}"
        test_password = "testpassword123"

        # Get login page
        response = client.get("/auth/login")
        assert response.status_code == 200
        logger.info(f"GET /auth/login -> {response.status_code}")

        # Register/Login
        response = client.post(
            "/auth/register",
            data={
                "username": test_username,
                "password": test_password,
                "confirm_password": test_password,
                "acknowledge": "true",
            },
            follow_redirects=False,
        )

        # Login
        response = client.post(
            "/auth/login",
            data={
                "username": test_username,
                "password": test_password,
            },
            follow_redirects=False,
        )

        logger.info(f"POST /auth/login -> {response.status_code}")

        # Check if we're logged in by verifying successful redirect
        assert (
            response.status_code == 302
        )  # Should redirect after successful login

        # Access home page
        response = client.get("/", follow_redirects=False)
        logger.info(f"GET / -> {response.status_code}")

        # Should not redirect to login
        if response.status_code == 302:
            location = response.headers.get("Location", "")
            assert "/auth/login" not in location, (
                "Session was lost! Redirected back to login"
            )
        else:
            assert response.status_code == 200
            # Check if we're actually logged in
            assert (
                b"logout" in response.data.lower()
                or b"dashboard" in response.data.lower()
            )
            logger.info("✅ Successfully logged in and session preserved!")

    def test_auth_check_persistence(self, authenticated_client):
        """Test auth check endpoint with persistent session."""
        response = authenticated_client.get("/auth/check")
        assert response.status_code == 200
        logger.info(f"GET /auth/check -> {response.status_code}")

        data = json.loads(response.data)
        logger.info(f"Auth check data: {data}")
        assert data.get("authenticated") is True
        assert "username" in data
        logger.info(f"✅ Authenticated as: {data.get('username')}")

    def test_api_access_persistence(self, authenticated_client):
        """Test API access with persistent session."""
        response = authenticated_client.get("/settings/api")
        assert response.status_code == 200
        logger.info(f"GET /settings/api -> {response.status_code}")
        logger.info("✅ API access works!")

        data = json.loads(response.data)
        assert data["status"] == "success"
        assert "settings" in data

    def test_multiple_requests_session(self, authenticated_client):
        """Test that session persists across multiple requests."""
        endpoints = [
            "/auth/check",
            "/settings/api",
            "/history/api",
            "/metrics/api/metrics",
        ]

        for endpoint in endpoints:
            response = authenticated_client.get(endpoint)
            assert response.status_code == 200
            logger.info(f"✅ {endpoint} -> {response.status_code}")

    def test_session_after_page_navigation(self, authenticated_client):
        """Test session persists through page navigation."""
        pages = ["/", "/history", "/settings", "/metrics/"]

        for page in pages:
            response = authenticated_client.get(page)
            assert response.status_code == 200
            # Should not redirect to login
            assert b"login" not in response.request.url.encode()
            logger.info(f"✅ Successfully accessed {page}")

    def test_session_integrity_check(self, authenticated_client):
        """Test session integrity check endpoint."""
        response = authenticated_client.get("/auth/integrity-check")
        assert response.status_code == 200

        data = json.loads(response.data)
        assert "integrity" in data
        assert "username" in data
        logger.info(
            f"✅ Session integrity verified for user: {data['username']}"
        )

    def test_logout_clears_session(self, authenticated_client):
        """Test that logout properly clears the session."""
        # First verify we're logged in
        response = authenticated_client.get("/auth/check")
        data = json.loads(response.data)
        assert data["authenticated"] is True

        # Logout
        response = authenticated_client.post(
            "/auth/logout", follow_redirects=True
        )
        assert response.status_code == 200

        # Try to access protected endpoint
        response = authenticated_client.get("/settings/api")
        # Should either get 401 or redirect to login
        assert response.status_code in [401, 302]
        logger.info("✅ Session properly cleared after logout")
