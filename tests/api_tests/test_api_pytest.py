"""
pytest-compatible API tests for REST API endpoints.
Tests basic functionality and programmatic access integration.
"""

import json


class TestRestAPIBasic:
    """Basic tests for REST API endpoints."""

    def test_health_check(self, authenticated_client):
        """Test the health check endpoint returns OK status."""
        response = authenticated_client.get("/api/v1/health")
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data["status"] == "ok"
        assert "timestamp" in data
        assert isinstance(data["timestamp"], (int, float))

    def test_api_documentation(self, authenticated_client):
        """Test the API documentation endpoint returns proper structure."""
        response = authenticated_client.get("/api/v1/")
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data["api_version"] == "v1"
        assert "description" in data
        assert "endpoints" in data
        assert len(data["endpoints"]) >= 3

        # Verify endpoint structure
        endpoints = data["endpoints"]
        if isinstance(endpoints, list):
            # For list format, just check basic fields
            for endpoint in endpoints:
                assert "path" in endpoint or "endpoint" in endpoint
                assert "method" in endpoint or "methods" in endpoint
                assert "description" in endpoint
                # auth_required field is optional in API v1
        else:
            # For dict format
            for endpoint in endpoints.values():
                assert "path" in endpoint
                assert "methods" in endpoint
                assert "description" in endpoint
                assert "requires_auth" in endpoint

    def test_quick_summary_validation(self, authenticated_client):
        """Test quick_summary endpoint validates required fields."""
        # Test missing query
        response = authenticated_client.post(
            "/api/v1/quick_summary",
            json={},
            content_type="application/json",
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert "error" in data
        assert "query" in data["error"].lower()

    def test_analyze_documents_validation(self, authenticated_client):
        """Test analyze_documents endpoint validates required fields."""
        # Test missing collection_name
        response = authenticated_client.post(
            "/api/v1/analyze_documents",
            json={"query": "test query"},
            content_type="application/json",
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert "error" in data
        assert "collection_name" in data["error"].lower()

        # Test missing query
        response = authenticated_client.post(
            "/api/v1/analyze_documents",
            json={"collection_name": "test_collection"},
            content_type="application/json",
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert "error" in data
        assert "query" in data["error"].lower()

    def test_generate_report_validation(self, authenticated_client):
        """Test generate_report endpoint validates required fields."""
        # Test missing research_id or query
        response = authenticated_client.post(
            "/api/v1/generate_report",
            json={},
            content_type="application/json",
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert "error" in data
        # Check for either research_id or query in error message
        error_msg = data["error"].lower()
        assert (
            "research_id" in error_msg
            or "query" in error_msg
            or "required" in error_msg
        )

    def test_cors_headers(self, authenticated_client):
        """Test that CORS headers are properly set."""
        response = authenticated_client.get("/api/v1/health")
        assert response.status_code == 200

        # Check for CORS headers if they are configured
        # This may be optional depending on the app configuration
        # Common CORS headers to check:
        # - Access-Control-Allow-Origin
        # - Access-Control-Allow-Methods
        # - Access-Control-Allow-Headers

    def test_error_response_format(self, authenticated_client):
        """Test that error responses follow consistent format."""
        # Trigger a 404 error
        response = authenticated_client.get("/api/v1/nonexistent_endpoint")
        assert response.status_code == 404

        if response.content_type == "application/json":
            data = json.loads(response.data)
            assert "error" in data or "message" in data

    def test_rate_limiting_headers(self, authenticated_client):
        """Test that rate limiting headers are present if configured."""
        response = authenticated_client.get("/api/v1/health")
        assert response.status_code == 200

        # Check for rate limiting headers if configured
        # Common headers:
        # - X-RateLimit-Limit
        # - X-RateLimit-Remaining
        # - X-RateLimit-Reset
