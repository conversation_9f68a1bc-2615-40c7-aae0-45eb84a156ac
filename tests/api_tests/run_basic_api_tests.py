"""
Run basic API tests that are known to work.
"""

import os
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent.parent))
os.environ["LDR_ALLOW_UNENCRYPTED"] = "true"

from loguru import logger
from test_comprehensive_apis import (
    test_auth_apis,
    test_history_apis,
    test_settings_apis,
)


def run_working_tests():
    """Run only the APIs that are known to work."""
    logger.info("Running basic API tests...\n")

    try:
        # Initialize tester and authenticate
        tester = test_auth_apis()

        # Run working test suites
        test_settings_apis(tester)
        test_history_apis(tester)

        logger.info("\n✅ Basic API tests passed successfully!")
        return True

    except Exception:
        logger.exception("Test failed")
        return False


if __name__ == "__main__":
    # Setup logging
    logger.remove()
    logger.add(
        sys.stdout,
        level="INFO",
        format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>",
    )

    success = run_working_tests()
    sys.exit(0 if success else 1)
