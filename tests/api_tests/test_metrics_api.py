"""
Test metrics API endpoints specifically.
"""

import json
import pytest
from loguru import logger


class TestMetricsAPI:
    """Test metrics API endpoints."""

    def test_metrics_summary(self, authenticated_client):
        """Test metrics summary endpoint."""
        logger.info("Testing metrics summary...")

        response = authenticated_client.get("/metrics/api/metrics")
        assert response.status_code == 200

        data = json.loads(response.data)
        # The endpoint returns 'metrics' not 'summary'
        assert "metrics" in data
        logger.info("✅ Metrics summary passed")

    def test_enhanced_metrics(self, authenticated_client):
        """Test enhanced metrics endpoint."""
        logger.info("Testing enhanced metrics...")

        response = authenticated_client.get("/metrics/api/metrics/enhanced")
        assert response.status_code == 200
        logger.info("✅ Enhanced metrics passed")

    def test_pricing_info(self, authenticated_client):
        """Test pricing information endpoint."""
        logger.info("Testing pricing info...")

        response = authenticated_client.get("/metrics/api/pricing")
        assert response.status_code == 200

        data = json.loads(response.data)
        # The endpoint returns 'pricing' not 'models'
        assert "pricing" in data
        logger.info("✅ Pricing info passed")

    def test_cost_analytics(self, authenticated_client):
        """Test cost analytics endpoint."""
        logger.info("Testing cost analytics...")

        response = authenticated_client.get("/metrics/api/cost-analytics")
        assert response.status_code == 200
        logger.info("✅ Cost analytics passed")

    def test_cost_calculation(self, authenticated_client):
        """Test cost calculation endpoint."""
        logger.info("Testing cost calculation...")

        cost_data = {
            "model_name": "gpt-3.5-turbo",
            "prompt_tokens": 1000,
            "completion_tokens": 500,
        }

        response = authenticated_client.post(
            "/metrics/api/cost-calculation",
            json=cost_data,
            content_type="application/json",
        )
        assert response.status_code == 200

        data = json.loads(response.data)
        assert "cost" in data or "total_cost" in data
        logger.info("✅ Cost calculation passed")

    def test_star_reviews(self, authenticated_client):
        """Test star reviews endpoint."""
        logger.info("Testing star reviews...")

        response = authenticated_client.get("/metrics/api/star-reviews")
        assert response.status_code == 200
        logger.info("✅ Star reviews passed")

    def test_rate_limiting_metrics(self, authenticated_client):
        """Test rate limiting metrics endpoint."""
        logger.info("Testing rate limiting metrics...")

        response = authenticated_client.get("/metrics/api/rate-limiting")
        assert response.status_code == 200
        logger.info("✅ Rate limiting metrics passed")

    def test_current_rate_limits(self, authenticated_client):
        """Test current rate limits endpoint."""
        logger.info("Testing current rate limits...")

        response = authenticated_client.get(
            "/metrics/api/rate-limiting/current"
        )
        assert response.status_code == 200
        logger.info("✅ Current rate limits passed")

    def test_model_specific_pricing(self, authenticated_client):
        """Test model-specific pricing endpoint."""
        logger.info("Testing model-specific pricing...")

        # Test a specific model
        response = authenticated_client.get(
            "/metrics/api/pricing/gpt-3.5-turbo"
        )

        # This endpoint might not exist for all models
        if response.status_code == 200:
            data = json.loads(response.data)
            logger.info(f"Model pricing data: {data}")
            # Check for pricing fields based on actual response structure
            assert (
                "pricing" in data
                or "input" in data
                or "prompt" in data
                or "price" in data
            )
            logger.info("✅ Model-specific pricing passed")
        elif response.status_code == 404:
            logger.info(
                "⚠️ Model-specific pricing endpoint not found (expected)"
            )
        else:
            pytest.fail(f"Unexpected status code: {response.status_code}")

    def test_metrics_error_handling(self, authenticated_client):
        """Test metrics API error handling."""
        logger.info("Testing metrics error handling...")

        # Test cost calculation with missing data
        response = authenticated_client.post(
            "/metrics/api/cost-calculation",
            json={},
            content_type="application/json",
        )
        # The endpoint might return 200 with error in response body
        assert response.status_code in [200, 400, 422]

        # Test invalid model pricing
        response = authenticated_client.get(
            "/metrics/api/pricing/invalid-model-xyz"
        )
        # The endpoint returns 200 even for invalid models
        assert response.status_code in [200, 404, 400]

        logger.info("✅ Metrics error handling passed")
