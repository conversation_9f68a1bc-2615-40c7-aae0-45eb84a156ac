"""Simple authentication test."""

import json
import time
from loguru import logger


class TestSimpleAuth:
    """Simple authentication tests."""

    def test_login_flow(self, client):
        """Test the login flow."""
        # Create unique test username to avoid conflicts
        test_username = f"testuser_simple_{int(time.time() * 1000)}"
        test_password = "testpassword123"

        # 1. Get login page
        response = client.get("/auth/login")
        assert response.status_code == 200
        logger.info(f"GET /auth/login -> {response.status_code}")

        # 2. Register first (in case user doesn't exist)
        response = client.post(
            "/auth/register",
            data={
                "username": test_username,
                "password": test_password,
                "confirm_password": test_password,
                "acknowledge": "true",
            },
            follow_redirects=False,
        )
        # Either 302 (success) or error if already exists
        logger.info(f"POST /auth/register -> {response.status_code}")

        # 3. Login
        response = client.post(
            "/auth/login",
            data={
                "username": test_username,
                "password": test_password,
            },
            follow_redirects=True,
        )
        logger.info(
            f"POST /auth/login (with redirects) -> {response.status_code}"
        )
        logger.info(f"Final URL: {response.request.url}")

        # Should redirect to home page after successful login
        assert response.status_code == 200

    def test_auth_check(self, authenticated_client):
        """Test auth check endpoint."""
        response = authenticated_client.get("/auth/check")
        assert response.status_code == 200
        logger.info(f"GET /auth/check -> {response.status_code}")

        data = json.loads(response.data)
        logger.info(f"Auth check data: {data}")
        assert data["authenticated"] is True

    def test_access_home_page(self, authenticated_client):
        """Test accessing home page when authenticated."""
        response = authenticated_client.get("/")
        assert response.status_code == 200
        logger.info(f"GET / -> {response.status_code}")

    def test_api_access(self, authenticated_client):
        """Test API access when authenticated."""
        response = authenticated_client.get(
            "/settings/api/available-search-engines"
        )
        assert response.status_code == 200
        logger.info(
            f"GET /settings/api/available-search-engines -> {response.status_code}"
        )

        data = json.loads(response.data)
        logger.info(f"Search engines response has keys: {list(data.keys())}")

        if "engine_options" in data:
            logger.info(
                f"Found {len(data['engine_options'])} search engine options"
            )
            assert len(data["engine_options"]) > 0
        elif "engines" in data:
            logger.info(f"Found {len(data['engines'])} search engines")
            assert len(data["engines"]) > 0

    def test_unauthenticated_api_access(self, client):
        """Test API access without authentication."""
        # Should get 401 or redirect to login
        response = client.get("/settings/api/available-search-engines")
        assert response.status_code in [401, 302]
        logger.info(
            f"GET /settings/api/available-search-engines (unauth) -> {response.status_code}"
        )

    def test_logout(self, authenticated_client):
        """Test logout functionality."""
        # First verify we're authenticated
        response = authenticated_client.get("/auth/check")
        data = json.loads(response.data)
        assert data["authenticated"] is True

        # Logout
        print("\n[DEBUG] Testing logout endpoint with POST method")
        response = authenticated_client.post(
            "/auth/logout", follow_redirects=True
        )
        print(f"[DEBUG] Logout response status: {response.status_code}")
        print(f"[DEBUG] Logout response headers: {dict(response.headers)}")
        if response.status_code != 200:
            print(
                f"[DEBUG] Logout response data: {response.data.decode()[:500]}"
            )
        assert response.status_code == 200

        # Check we're no longer authenticated
        response = authenticated_client.get("/auth/check")
        assert response.status_code in [401, 200]
        if response.status_code == 200:
            data = json.loads(response.data)
            assert data["authenticated"] is False
