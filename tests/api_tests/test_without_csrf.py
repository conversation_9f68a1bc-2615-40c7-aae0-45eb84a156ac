"""Test authentication without CSRF to verify CSRF protection."""

from loguru import logger


class TestWithoutCSRF:
    """Test that authentication properly requires CSRF tokens."""

    def test_login_without_csrf_protection(self, app):
        """Test login behavior when CSRF protection is disabled."""
        # Temporarily disable CSRF for this test
        app.config["WTF_CSRF_ENABLED"] = False
        client = app.test_client()

        # Register user
        register_data = {
            "username": "testuser_nocsrf",
            "password": "testpassword123",
            "confirm_password": "testpassword123",
            "acknowledge": "true",
        }
        client.post("/auth/register", data=register_data)

        # Try login without CSRF token
        login_data = {
            "username": "testuser_nocsrf",
            "password": "testpassword123",
        }

        # Direct POST without getting the login page first
        response = client.post(
            "/auth/login", data=login_data, follow_redirects=False
        )

        logger.info(
            f"POST /auth/login (CSRF disabled) -> {response.status_code}"
        )

        # With CSRF disabled, login should work
        assert response.status_code == 302
        logger.info("✅ Login worked with CSRF disabled!")

        # Re-enable CSRF
        app.config["WTF_CSRF_ENABLED"] = True

    def test_login_requires_csrf_by_default(self, app):
        """Test that login requires CSRF token when enabled."""
        # Create a new client with CSRF enabled
        app.config["WTF_CSRF_ENABLED"] = True
        client = app.test_client()

        # Try login without CSRF token (CSRF should be enabled)
        login_data = {"username": "testuser", "password": "testpassword123"}

        # Direct POST without CSRF token
        response = client.post(
            "/auth/login", data=login_data, follow_redirects=False
        )

        logger.info(
            f"POST /auth/login (no CSRF token) -> {response.status_code}"
        )

        # Should fail because CSRF token is missing
        assert response.status_code != 302  # Should not redirect on success
        logger.info("✅ Login correctly requires CSRF token!")

    def test_api_endpoints_without_csrf(self, authenticated_client):
        """Test that API endpoints work without CSRF for authenticated users."""
        # API endpoints typically don't require CSRF for GET requests
        endpoints = [
            "/settings/api",
            "/history/api",
            "/metrics/api/cost-analytics",
        ]

        for endpoint in endpoints:
            response = authenticated_client.get(endpoint)
            assert response.status_code == 200
            logger.info(f"✅ GET {endpoint} works without CSRF token")

    def test_post_api_without_csrf(self, authenticated_client):
        """Test that POST API endpoints properly handle CSRF."""
        # Try to start research without CSRF token
        research_data = {"query": "test query", "mode": "quick"}

        # POST without CSRF should fail unless endpoint is exempt
        response = authenticated_client.post(
            "/api/start_research",
            json=research_data,  # JSON requests typically bypass CSRF
            content_type="application/json",
        )

        # JSON API endpoints often bypass CSRF protection
        logger.info(
            f"POST /api/start_research (JSON) -> {response.status_code}"
        )

        # Form POST without CSRF should fail
        response = authenticated_client.post(
            "/api/start_research",
            data=research_data,  # Form data should require CSRF
            follow_redirects=False,
        )

        logger.info(
            f"POST /api/start_research (form data, no CSRF) -> {response.status_code}"
        )
