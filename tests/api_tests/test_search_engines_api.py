"""
Focused test for the search engines API endpoint.

This test specifically debugs why the search engines dropdown is not loading.
"""

import json
import pytest
from loguru import logger


class TestSearchEnginesAPI:
    """Test the search engines API endpoint with detailed debugging."""

    def test_search_engines_api(self, authenticated_client):
        """Test the search engines API endpoint."""
        logger.info("Testing search engines API...")

        response = authenticated_client.get(
            "/settings/api/available-search-engines"
        )

        logger.info(f"Response status: {response.status_code}")

        assert response.status_code == 200
        data = json.loads(response.data)

        logger.info(f"Response data: {json.dumps(data, indent=2)}")

        # Check the structure
        if "engines" in data:
            logger.info(f"Found {len(data['engines'])} engines")
            assert len(data["engines"]) > 0
            for engine_name, engine_data in data["engines"].items():
                logger.info(f"  - {engine_name}: {engine_data}")
        elif "engine_options" in data:
            logger.info(f"Found {len(data['engine_options'])} engine options")
            assert len(data["engine_options"]) > 0
            for option in data["engine_options"]:
                logger.info(f"  - {option}")
        else:
            pytest.fail(
                "Unexpected data structure - neither 'engines' nor 'engine_options' found"
            )

    @pytest.mark.skip(
        reason="Search engine display_name settings are not loaded by default in test environment"
    )
    def test_related_search_settings(self, authenticated_client):
        """Check related search engine settings."""
        logger.info("Checking related settings...")

        # Check if search engine settings exist
        endpoints = [
            "/settings/api/search.engine.web.searxng.display_name",
            "/settings/api/search.engine.web.duckduckgo.display_name",
            "/settings/api/search.engine.web.google.display_name",
            "/settings/api/search.engine.web.bing.display_name",
        ]

        found_settings = 0
        for endpoint in endpoints:
            response = authenticated_client.get(endpoint)
            if response.status_code == 200:
                data = json.loads(response.data)
                logger.info(f"✓ {endpoint}: {data}")
                found_settings += 1
            else:
                logger.warning(f"✗ {endpoint}: {response.status_code}")

        # At least some search engines should be configured
        assert found_settings > 0, "No search engine settings found"

    @pytest.mark.skip(
        reason="Search settings are not loaded by default in test environment"
    )
    def test_all_search_settings(self, authenticated_client):
        """Check all settings to see what's available."""
        logger.info("Checking all settings...")

        response = authenticated_client.get("/settings/api")
        assert response.status_code == 200

        data = json.loads(response.data)
        assert data["status"] == "success"
        assert "settings" in data

        # Find search-related settings
        search_settings = {
            k: v for k, v in data["settings"].items() if "search" in k
        }

        logger.info(f"Found {len(search_settings)} search-related settings:")
        for key, value in search_settings.items():
            logger.info(f"  {key}: {value}")

        # Should have some search settings
        assert len(search_settings) > 0, "No search settings found"

    def test_search_engine_configuration(self, authenticated_client):
        """Test search engine configuration endpoints."""
        # Test getting search engine configuration
        response = authenticated_client.get(
            "/settings/api/search.default_search_engine"
        )

        if response.status_code == 200:
            data = json.loads(response.data)
            logger.info(f"Default search engine: {data}")
            assert "value" in data or "setting" in data
        else:
            logger.warning(
                f"No default search engine configured: {response.status_code}"
            )

        # Test search engine specific settings
        response = authenticated_client.get("/settings/api/search.max_results")
        if response.status_code == 200:
            data = json.loads(response.data)
            logger.info(f"Max search results: {data}")
            assert "value" in data or "setting" in data

    def test_search_engines_dropdown_data(self, authenticated_client):
        """Test the exact data structure needed for dropdown."""
        response = authenticated_client.get(
            "/settings/api/available-search-engines"
        )
        assert response.status_code == 200

        data = json.loads(response.data)

        # The frontend expects either:
        # 1. {"engines": {"name": {"display_name": "Name", "enabled": true}}}
        # 2. {"engine_options": ["engine1", "engine2"]}

        if "engines" in data:
            # Validate engines structure
            for engine_name, engine_data in data["engines"].items():
                assert isinstance(engine_data, dict), (
                    f"Engine {engine_name} data should be dict"
                )
                # Check for expected fields
                if "display_name" in engine_data:
                    assert isinstance(engine_data["display_name"], str)
                if "enabled" in engine_data:
                    assert isinstance(engine_data["enabled"], bool)
        elif "engine_options" in data:
            # Validate engine_options structure
            assert isinstance(data["engine_options"], list), (
                "engine_options should be list"
            )
            assert len(data["engine_options"]) > 0, (
                "engine_options should not be empty"
            )
            for option in data["engine_options"]:
                assert isinstance(option, (str, dict)), (
                    "Each option should be string or dict"
                )
