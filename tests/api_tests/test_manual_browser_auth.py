"""Test using manually created session from browser.

This test is skipped by default as it requires manual setup.
To run it, you need to provide a session cookie from the browser.
"""

import os
import pytest
from loguru import logger


@pytest.mark.skip(reason="Requires manual browser session setup")
class TestManualBrowserAuth:
    """Test authentication using browser session cookie."""

    def test_manual_session_cookie(self, client):
        """Test using manually provided session cookie.

        To use this test:
        1. Login via browser at http://127.0.0.1:5000/auth/login
        2. Open Developer Tools (F12)
        3. Go to Application/Storage/Cookies
        4. Copy the 'session' cookie value
        5. Set MANUAL_SESSION_COOKIE environment variable
        6. Run with: pytest -m "not skip" tests/api_tests/test_manual_browser_auth.py
        """
        # Get session cookie from environment
        session_cookie = os.environ.get("MANUAL_SESSION_COOKIE")
        if not session_cookie:
            pytest.skip("MANUAL_SESSION_COOKIE not set")

        # Set the cookie in the test client
        client.set_cookie(
            domain="localhost", key="session", value=session_cookie
        )

        logger.info("Testing with manual session cookie...")

        # Test auth check
        response = client.get("/auth/check")
        logger.info(f"GET /auth/check -> {response.status_code}")

        if response.status_code == 200:
            data = response.get_json()
            logger.info(f"Auth data: {data}")

            if data.get("authenticated"):
                logger.info(f"✅ Authenticated as: {data.get('username')}")

                # Test API endpoints
                response = client.get("/settings/api")
                logger.info(f"GET /settings/api -> {response.status_code}")
                assert response.status_code == 200

                response = client.get("/settings/api/available-search-engines")
                logger.info(
                    f"GET /settings/api/available-search-engines -> {response.status_code}"
                )
                assert response.status_code == 200

                data = response.get_json()
                if "engine_options" in data:
                    logger.info(
                        f"✅ Found {len(data['engine_options'])} search engines"
                    )
                    assert len(data["engine_options"]) > 0
            else:
                pytest.fail("Not authenticated - session cookie may be invalid")
        else:
            pytest.fail(f"Auth check failed with status {response.status_code}")

    def test_manual_session_api_operations(self, client):
        """Test API operations with manual session."""
        # This test is also skipped by default
        session_cookie = os.environ.get("MANUAL_SESSION_COOKIE")
        if not session_cookie:
            pytest.skip("MANUAL_SESSION_COOKIE not set")

        client.set_cookie(
            domain="localhost", key="session", value=session_cookie
        )

        # Test various API operations
        endpoints = [
            "/history/api",
            "/metrics/api/cost-analytics",
            "/settings/api/available-models",
        ]

        for endpoint in endpoints:
            response = client.get(endpoint)
            logger.info(f"GET {endpoint} -> {response.status_code}")
            assert response.status_code == 200
            data = response.get_json()
            assert data is not None
