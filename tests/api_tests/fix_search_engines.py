"""
Quick fix to ensure search engine settings are loaded for test user.
"""

import os
import sys
from pathlib import Path

from loguru import logger

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from src.local_deep_research.database.encrypted_db import db_manager
from src.local_deep_research.web.services.settings_manager import (
    SettingsManager,
)


def fix_search_engines_for_user(username: str, password: str):
    """Ensure search engine settings are loaded for a user."""

    logger.info(f"Fixing search engine settings for user: {username}")

    # Open user database
    engine = db_manager.open_user_database(username, password)
    if not engine:
        logger.error("Failed to open user database")
        return False

    # Get database session
    Session = db_manager.Session
    if not Session:
        logger.error("Failed to get database session factory")
        return False

    with Session() as db_session:
        try:
            # Create settings manager
            settings_manager = SettingsManager(db_session)

            # Check if we need to load defaults
            from src.local_deep_research.database.models import Setting

            search_engine_count = (
                db_session.query(Setting)
                .filter(Setting.key.like("search.engine.%.display_name"))
                .count()
            )

            if search_engine_count == 0:
                logger.info(
                    "No search engine settings found. Loading defaults..."
                )
                settings_manager.load_from_defaults_file(commit=True)
                logger.info("Default settings loaded successfully")
            else:
                logger.info(
                    f"Found {search_engine_count} search engine settings"
                )

            # Verify settings were loaded
            search_engines = (
                db_session.query(Setting)
                .filter(Setting.key.like("search.engine.%.display_name"))
                .all()
            )

            logger.info(f"Search engines after fix: {len(search_engines)}")
            for engine in search_engines[:5]:  # Show first 5
                logger.info(f"  - {engine.key}: {engine.value}")

            return True

        except Exception:
            logger.exception("Failed to fix search engines")
            db_session.rollback()
            return False


if __name__ == "__main__":
    import os

    os.environ["LDR_ALLOW_UNENCRYPTED"] = "true"

    # Fix for test user
    fix_search_engines_for_user(
        "testuser", "testpassword123"
    )  # pragma: allowlist secret

    # Also fix for any other common test users
    fix_search_engines_for_user("apitest_user", "apitest_pass123")
