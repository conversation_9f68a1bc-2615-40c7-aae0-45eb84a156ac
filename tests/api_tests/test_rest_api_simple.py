"""
Simple REST API tests with ultra-minimal queries and longer timeouts.
Focus on basic functionality verification.
"""

import json
import pytest

# Extended timeout for research operations
RESEARCH_TIMEOUT = 120  # 2 minutes


class TestRestAPISimple:
    """Simple REST API tests."""

    def test_health_and_docs(self, client):
        """Test basic non-research endpoints."""
        print("🔍 Testing health and documentation endpoints...")

        # Health check
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["status"] == "ok"
        print("✅ Health check passed")

        # API documentation
        response = client.get("/api/v1/")
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data["api_version"] == "v1"
        assert "endpoints" in data
        print("✅ API documentation passed")

    def test_error_handling(self, authenticated_client):
        """Test error handling for malformed requests."""
        print("🔍 Testing error handling...")

        # Missing query parameter
        response = authenticated_client.post(
            "/api/v1/quick_summary",
            json={},
            content_type="application/json",
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert "error" in data
        print("✅ Error handling for missing query passed")

        # Missing parameters for analyze_documents
        response = authenticated_client.post(
            "/api/v1/analyze_documents",
            json={"query": "test"},
            content_type="application/json",
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert "error" in data
        print("✅ Error handling for missing collection_name passed")

    @pytest.mark.requires_llm
    def test_quick_summary_ultra_minimal(self, authenticated_client):
        """Test quick summary with the most minimal possible query."""
        print("🔍 Testing quick summary with ultra-minimal query...")

        payload = {
            "query": "cat",  # Single word, very common
            "search_tool": "wikipedia",
            "iterations": 1,
            "temperature": 0.7,
        }

        print("Making request...")
        response = authenticated_client.post(
            "/api/v1/quick_summary",
            json=payload,
            content_type="application/json",
        )

        if response.status_code == 200:
            data = json.loads(response.data)

            # Basic structure validation
            required_fields = ["query", "summary", "findings"]
            for field in required_fields:
                assert field in data, f"Missing field: {field}"

            assert data["query"] == "cat"
            assert len(data["summary"]) > 0, "Summary should not be empty"
            assert isinstance(data["findings"], list), (
                "Findings should be a list"
            )

            print(
                f"✅ Quick summary passed - got {len(data['summary'])} chars of summary"
            )
            print(f"   Found {len(data['findings'])} findings")
        else:
            pytest.fail(
                f"Quick summary failed with status {response.status_code}"
            )
            print(f"   Response: {response.data[:200]}")

    @pytest.mark.requires_llm
    def test_quick_summary_test_minimal(self, authenticated_client):
        """Test the test endpoint with minimal query."""
        print("🔍 Testing quick summary test endpoint...")

        payload = {"query": "dog"}  # Another simple, common word

        print("Making request...")
        response = authenticated_client.post(
            "/api/v1/quick_summary_test",
            json=payload,
            content_type="application/json",
        )

        if response.status_code == 200:
            data = json.loads(response.data)

            # Basic structure validation
            assert "query" in data
            assert "summary" in data
            assert data["query"] == "dog"
            assert len(data["summary"]) > 0

            print(
                f"✅ Quick summary test passed - got {len(data['summary'])} chars of summary"
            )
        else:
            pytest.fail(
                f"Quick summary test failed with status {response.status_code}"
            )
            print(f"   Response: {response.data[:200]}")

    @pytest.mark.requires_llm
    def test_analyze_documents_minimal(self, authenticated_client):
        """Test analyze documents with minimal input."""
        print("🔍 Testing analyze documents with minimal input...")

        payload = {
            "documents": ["The cat sat on the mat."],
            "query": "Where is the cat?",
            "collection_name": "test_minimal",
        }

        response = authenticated_client.post(
            "/api/v1/analyze_documents",
            json=payload,
            content_type="application/json",
        )

        if response.status_code == 200:
            data = json.loads(response.data)

            # Basic structure validation
            assert "analysis" in data
            assert "processed_documents" in data
            assert data["processed_documents"] == 1

            print("✅ Analyze documents passed")
        else:
            # This endpoint might not be fully implemented
            print(f"⚠️ Analyze documents returned {response.status_code}")

    @pytest.mark.requires_llm
    def test_generate_report_minimal(self, authenticated_client):
        """Test generate report with minimal input."""
        print("🔍 Testing generate report with minimal input...")

        payload = {
            "query": "Sun",
            "research_type": "quick",
        }

        response = authenticated_client.post(
            "/api/v1/generate_report",
            json=payload,
            content_type="application/json",
        )

        # This endpoint might not be fully implemented
        if response.status_code == 200:
            print("✅ Generate report passed")
        elif response.status_code in [404, 500]:
            print(
                f"⚠️ Generate report endpoint not fully implemented ({response.status_code})"
            )
        else:
            pytest.fail(
                f"Generate report failed with unexpected status {response.status_code}"
            )
