# Infrastructure Tests

This directory contains tests for infrastructure and architectural components of the Local Deep Research application.

## Test Categories

### Route Management
- `test_route_registry.py` - Tests for the centralized route registry system
- `test_urls_js.py` - Python tests verifying JavaScript URL configuration matches backend routes
- `test_urls.test.js` - JavaScript unit tests for URL builder functionality

### Future Test Categories (to be added)
- **Configuration Tests** - Tests for settings management and configuration loading
- **Database Tests** - Schema validation, migration tests
- **Logging Tests** - Log formatting, sinks, and handlers
- **URL Management Tests** - URL utilities and validation
- **Cache Tests** - Caching layer functionality
- **Middleware Tests** - Request/response middleware components

## Running Infrastructure Tests

### Python Tests

Run all Python infrastructure tests:
```bash
pytest tests/infrastructure_tests/
```

Run a specific test file:
```bash
pytest tests/infrastructure_tests/test_route_registry.py -v
pytest tests/infrastructure_tests/test_urls_js.py -v
```

### JavaScript Tests

First, install JavaScript test dependencies:
```bash
cd tests/infrastructure_tests
npm install
```

Run JavaScript tests:
```bash
npm test
# or
npm run test:watch  # for watch mode
npm run test:coverage  # for coverage report
```

## Adding New Infrastructure Tests

When adding new infrastructure tests:
1. Create appropriately named test files (e.g., `test_<component>.py`)
2. Group related tests into test classes
3. Use descriptive test names that explain what is being tested
4. Add docstrings to test methods explaining the test purpose
5. Update this README with new test categories as they are added
