# Test Suite Optimization

## Overview
The test suite has been optimized to remove redundant test executions and improve performance.

## Changes Made

### 1. Renamed `run_full_pytest()` to `run_all_pytest_tests()`
- More descriptive name indicating it runs ALL pytest tests
- Fixed to use `pytest` directly instead of non-existent `run_tests.py`

### 2. Optimized "full" Profile
**Before:**
- Health checks
- All pytest tests (via run_full_pytest)
- Integration tests again (redundant)
- UI tests

**After:**
- Health checks
- All pytest tests (includes unit, integration, feature tests)
- UI tests only (require special setup)

**Time saved:** ~3-5 minutes by avoiding duplicate integration test runs

### 3. Added "comprehensive" Profile
- Kept as legacy option for backwards compatibility
- Runs tests the old way (with duplicates)
- Useful for extra thorough testing when needed

### 4. Test Profile Summary

| Profile | Duration | What it runs | Use case |
|---------|----------|--------------|----------|
| unit-only | < 2min | Unit tests only | Quick feedback during development |
| fast | < 30s | Health checks + unit tests | Pre-commit checks |
| standard | < 5min | Health + unit + UI tests | Normal development |
| full | < 10min | Health + all pytest + UI | Optimized comprehensive testing |
| comprehensive | < 15min | Same as full but with redundant runs | Legacy/extra thorough |
| ci | Varies | Health + unit tests | CI pipeline optimization |

## Integration Test Deduplication

Previously, integration tests were run twice in the full profile:
1. As part of `run_all_pytest_tests()` (which runs everything)
2. Separately via `run_integration_tests()`

Now they only run once as part of the all-pytest run, saving time while maintaining coverage.

## CI Pipeline Impact

The GitHub Actions workflow now uses the optimized "full" profile, reducing CI run time by approximately 3-5 minutes per run while maintaining the same test coverage.
