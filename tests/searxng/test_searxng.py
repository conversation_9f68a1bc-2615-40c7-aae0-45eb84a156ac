"""
Tests for SearXNG search engine integration.

This file provides basic tests for the SearXNG search functionality.
"""

import sys
from pathlib import Path

import pytest

# Handle import paths for testing
sys.path.append(str(Path(__file__).parent.parent.parent))


class TestSearXNGSearch:
    """Test SearXNG search engine functionality."""

    def test_searxng_import(self):
        """Test that SearXNG search engine can be imported."""
        try:
            from src.local_deep_research.web_search_engines.engines.search_engine_searxng import (
                SearXNGSearchEngine,
            )

            assert SearXNGSearchEngine is not None
        except ImportError:
            pytest.skip("SearXNG search engine not available")

    def test_searxng_initialization(self, monkeypatch):
        """Test SearXNG search engine initialization."""
        try:
            from src.local_deep_research.web_search_engines.engines.search_engine_searxng import (
                SearXNGSearchEngine,
            )

            # Mock any required environment variables
            monkeypatch.setenv("SEARXNG_URL", "http://localhost:8080")

            search = SearXNGSearchEngine()
            assert search is not None

        except ImportError:
            pytest.skip("SearXNG search engine not available")

    def test_searxng_search_mock(self, monkeypatch):
        """Test SearXNG search with mocked response."""
        try:
            from src.local_deep_research.web_search_engines.engines.search_engine_searxng import (
                SearXNGSearchEngine,
            )

            # Mock environment variables
            monkeypatch.setenv("SEARXNG_URL", "http://localhost:8080")

            # Mock the requests.get call
            def mock_get(*args, **kwargs):
                from unittest.mock import Mock

                mock_response = Mock()
                mock_response.status_code = 200
                mock_response.json.return_value = {
                    "results": [
                        {
                            "title": "Test SearXNG Result",
                            "url": "https://example.com/searxng-result",
                            "content": "This is a test result from SearXNG.",
                        }
                    ]
                }
                return mock_response

            monkeypatch.setattr("requests.get", mock_get)

            search = SearXNGSearchEngine()
            results = search.run("test query")

            # Basic validation
            assert isinstance(results, list)
            # Note: Actual result format depends on SearXNG implementation

        except ImportError:
            pytest.skip("SearXNG search engine not available")

    def test_searxng_error_handling(self, monkeypatch):
        """Test SearXNG error handling."""
        try:
            from src.local_deep_research.web_search_engines.engines.search_engine_searxng import (
                SearXNGSearchEngine,
            )

            # Mock environment variables
            monkeypatch.setenv("SEARXNG_URL", "http://localhost:8080")

            # Mock a failed response
            def mock_get(*args, **kwargs):
                from unittest.mock import Mock

                mock_response = Mock()
                mock_response.status_code = 500
                return mock_response

            monkeypatch.setattr("requests.get", mock_get)

            search = SearXNGSearchEngine()
            results = search.run("test query")

            # Should handle errors gracefully
            assert isinstance(results, list)

        except ImportError:
            pytest.skip("SearXNG search engine not available")

    def test_searxng_network_error(self, monkeypatch):
        """Test SearXNG network error handling."""
        try:
            from src.local_deep_research.web_search_engines.engines.search_engine_searxng import (
                SearXNGSearchEngine,
            )

            # Mock environment variables
            monkeypatch.setenv("SEARXNG_URL", "http://localhost:8080")

            # Mock the engine's __init__ method to avoid initialization network requests
            def mock_init(self, *args, **kwargs):
                # Set required attributes without network calls
                self.instance_url = "http://localhost:8080"
                self.max_results = 10
                self.is_available = True
                super(SearXNGSearchEngine, self).__init__(*args, **kwargs)

            monkeypatch.setattr(SearXNGSearchEngine, "__init__", mock_init)

            # Mock a network error for search requests
            def mock_get(*args, **kwargs):
                raise ConnectionError("Network error")

            monkeypatch.setattr("requests.get", mock_get)

            search = SearXNGSearchEngine()
            results = search.run("test query")

            # Should handle network errors gracefully
            assert isinstance(results, list)

        except ImportError:
            pytest.skip("SearXNG search engine not available")
