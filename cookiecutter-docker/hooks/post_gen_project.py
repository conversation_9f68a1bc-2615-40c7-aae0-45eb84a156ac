"""
Extracts the compose file from the cookiecutter directory.
"""

import shutil
from pathlib import Path

COMPOSE_FILE_NAME = "docker-compose.{{cookiecutter.config_name}}.yml"


def main():
    # Move the compose file one directory up.
    compose_path = Path(COMPOSE_FILE_NAME)
    output_dir = compose_path.parent.absolute()
    compose_path.rename(output_dir.parent / COMPOSE_FILE_NAME)

    # Delete the directory.
    shutil.rmtree(output_dir)


if __name__ == "__main__":
    main()
