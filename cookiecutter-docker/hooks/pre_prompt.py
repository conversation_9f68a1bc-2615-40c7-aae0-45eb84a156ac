import json
import os
import subprocess
from typing import Any, Dict

import cookiecutter.prompt


def run_command(command: str):
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    return result.stdout.strip()


def config_ollama(context: Dict[str, Any]) -> None:
    """
    Prompts the user for questions that are specific to Ollama. It is in a hook
    so that we can run it only if Ollama is enabled.

    """
    enable_ollama = cookiecutter.prompt.read_user_yes_no("enable_ollama", True)
    ollama_model = "gemma3:12b"
    if enable_ollama:
        # Ask ollama-specific questions.
        ollama_model = cookiecutter.prompt.read_user_variable(
            "ollama_model", ollama_model
        )

    context["_enable_ollama"] = enable_ollama
    context["_ollama_model"] = ollama_model


def check_gpu(context: Dict[str, Any]) -> None:
    """
    Check if the system has an NVIDIA or AMD GPU and set flags in the
    context.

    Args:
        context: The context dictionary to update with GPU information.

    """
    gpu_info = run_command("lspci | grep -i 'vga'")

    if "NVIDIA" in gpu_info:
        print("Detected an Nvidia GPU.")
        context["_nvidia_gpu"] = True
        context["_amd_gpu"] = False
        context["enable_gpu"] = True
    elif "AMD" in gpu_info:
        print("Detected an AMD GPU.")
        context["_amd_gpu"] = True
        context["_nvidia_gpu"] = False
        context["enable_gpu"] = True
    else:
        print("Did not detect any GPU.")
        context["_nvidia_gpu"] = False
        context["_amd_gpu"] = False
        context["enable_gpu"] = False


def main() -> None:
    # Load the context.
    with open("cookiecutter.json", "r") as config:
        context = json.load(config)

    # Check GPU information and update the context only if running on Linux.
    if os.name == "posix" and os.uname().sysname == "Linux":
        check_gpu(context)
    # Ollama-specific config.
    config_ollama(context)

    # Save the updated context back to cookiecutter.json.
    with open("cookiecutter.json", "w") as config:
        json.dump(context, config, indent=4)


if __name__ == "__main__":
    main()
