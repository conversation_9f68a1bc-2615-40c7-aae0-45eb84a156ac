# Important: Examples Updated for LDR v1.0

## Authentication Required

Starting with LDR v1.0, all API access requires authentication due to the new per-user encrypted database architecture.

## Updated Examples

The following examples have been updated for v1.0:

### ✅ Updated Examples:
- `http/simple_http_example.py` - Basic HTTP API usage with authentication
- `http/http_api_examples.py` - Comprehensive HTTP API examples with LDRClient class
- `programmatic/retriever_usage_example.py` - LangChain retriever integration with auth
- `programmatic/programmatic_access_v1.py` - NEW: Complete programmatic API examples

### ⚠️ Needs Manual Update:
- `programmatic/programmatic_access.ipynb` - Jupyter notebook (see programmatic_access_v1.py for reference)

## Quick Migration Guide

### Old Code (pre-v1.0):
```python
from local_deep_research.api import quick_summary
result = quick_summary("query")
```

### New Code (v1.0+):
```python
from local_deep_research.api import quick_summary
from local_deep_research.settings import CachedSettingsManager
from local_deep_research.database.session_context import get_user_db_session

with get_user_db_session(username="user", password="pass") as session:
    settings_manager = CachedSettingsManager(session, "user")
    settings_snapshot = settings_manager.get_all_settings()
    result = quick_summary("query", settings_snapshot=settings_snapshot)
```

## Before Running Examples

1. **Create an account**:
   ```bash
   python -m local_deep_research.web.app
   # Open http://localhost:5000 and register
   ```

2. **Configure LLM provider** in Settings (e.g., OpenAI, Anthropic, Ollama)

3. **Update credentials** in the example files:
   - Change `USERNAME = "your_username"` to your actual username
   - Change `PASSWORD = "your_password"` to your actual password

## Common Issues

- **"No settings context available"**: Pass `settings_snapshot` to API functions
- **"Encrypted database requires password"**: Use `get_user_db_session()` with credentials
- **"CSRF token missing"**: Get CSRF token before POST/PUT/DELETE requests
- **404 errors**: Check new endpoint paths (e.g., `/research/api/start`)

## Need Help?

- See [Migration Guide](../../docs/MIGRATION_GUIDE_v1.md) for detailed changes
- Check [API Quick Start](../../docs/api-quickstart.md) for authentication details
- Join our [Discord](https://discord.gg/ttcqQeFcJ3) for support
