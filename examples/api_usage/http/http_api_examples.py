#!/usr/bin/env python3
"""
HTTP API Examples for Local Deep Research v1.0+

This script demonstrates comprehensive usage of the LDR HTTP API with authentication.
Includes examples for research, settings management, and batch operations.

Requirements:
- LDR v1.0+ (with authentication features)
- User account created through web interface
- LDR server running: python -m local_deep_research.web.app
"""

import time
from typing import Any, Dict, List
import requests

# Configuration
BASE_URL = "http://localhost:5000"
USERNAME = "your_username"  # Change this!
PASSWORD = "your_password"  # Change this!


class LDRClient:
    """Client for interacting with LDR API v1.0+ with authentication"""

    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.csrf_token = None
        self.username = None

    def login(self, username: str, password: str) -> bool:
        """Authenticate with the LDR server."""
        response = self.session.post(
            f"{self.base_url}/auth/login",
            json={"username": username, "password": password},
        )

        if response.status_code == 200:
            self.username = username
            # Get CSRF token
            csrf_response = self.session.get(f"{self.base_url}/auth/csrf-token")
            self.csrf_token = csrf_response.json()["csrf_token"]
            return True
        return False

    def logout(self) -> None:
        """Logout from the server."""
        if self.csrf_token:
            self.session.post(
                f"{self.base_url}/auth/logout",
                headers={"X-CSRF-Token": self.csrf_token},
            )

    def _get_headers(self) -> Dict[str, str]:
        """Get headers with CSRF token."""
        return {"X-CSRF-Token": self.csrf_token} if self.csrf_token else {}

    def check_health(self) -> Dict[str, Any]:
        """Check API health status."""
        response = self.session.get(f"{self.base_url}/auth/check")
        return response.json()

    def start_research(self, query: str, **kwargs) -> Dict[str, Any]:
        """Start a new research task."""
        payload = {
            "query": query,
            "model": kwargs.get("model"),
            "search_engines": kwargs.get("search_engines", ["wikipedia"]),
            "iterations": kwargs.get("iterations", 2),
            "questions_per_iteration": kwargs.get("questions_per_iteration", 3),
            "temperature": kwargs.get("temperature", 0.7),
            "local_context": kwargs.get("local_context", 2000),
            "web_context": kwargs.get("web_context", 2000),
        }

        response = self.session.post(
            f"{self.base_url}/research/api/start",
            json=payload,
            headers=self._get_headers(),
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"Failed to start research: {response.text}")

    def get_research_status(self, research_id: str) -> Dict[str, Any]:
        """Get the status of a research task."""
        response = self.session.get(
            f"{self.base_url}/research/api/research/{research_id}/status"
        )
        return response.json()

    def get_research_result(self, research_id: str) -> Dict[str, Any]:
        """Get the results of a completed research task."""
        response = self.session.get(
            f"{self.base_url}/research/api/research/{research_id}/result"
        )
        return response.json()

    def wait_for_research(
        self, research_id: str, timeout: int = 300
    ) -> Dict[str, Any]:
        """Wait for research to complete and return results."""
        start_time = time.time()

        while time.time() - start_time < timeout:
            status = self.get_research_status(research_id)

            if status.get("status") == "completed":
                return self.get_research_result(research_id)
            elif status.get("status") == "failed":
                raise Exception(
                    f"Research failed: {status.get('error', 'Unknown error')}"
                )

            print(
                f"  Status: {status.get('status', 'unknown')} - {status.get('progress', 'N/A')}"
            )
            time.sleep(3)

        raise TimeoutError(
            f"Research {research_id} timed out after {timeout} seconds"
        )

    def get_settings(self) -> Dict[str, Any]:
        """Get all user settings."""
        response = self.session.get(f"{self.base_url}/settings/api")
        return response.json()

    def get_setting(self, key: str) -> Any:
        """Get a specific setting value."""
        response = self.session.get(f"{self.base_url}/settings/api/{key}")
        if response.status_code == 200:
            return response.json()
        return None

    def update_setting(self, key: str, value: Any) -> bool:
        """Update a setting value."""
        response = self.session.put(
            f"{self.base_url}/settings/api/{key}",
            json={"value": value},
            headers=self._get_headers(),
        )
        return response.status_code in [200, 201]

    def get_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get research history."""
        response = self.session.get(
            f"{self.base_url}/history/api", params={"limit": limit}
        )
        data = response.json()
        return data.get("items", data.get("history", []))

    def get_available_models(self) -> Dict[str, str]:
        """Get available LLM providers and models."""
        response = self.session.get(
            f"{self.base_url}/settings/api/available-models"
        )
        data = response.json()
        return data.get("providers", data.get("models", {}))

    def get_available_search_engines(self) -> List[str]:
        """Get available search engines."""
        response = self.session.get(
            f"{self.base_url}/settings/api/available-search-engines"
        )
        data = response.json()
        return data.get("engines", data.get("engine_options", []))


def example_quick_research(client: LDRClient) -> None:
    """Example: Quick research with minimal parameters."""
    print("\n=== Example 1: Quick Research ===")

    research = client.start_research(
        query="What are the key principles of machine learning?",
        iterations=1,
        questions_per_iteration=2,
    )

    print(f"Started research ID: {research['research_id']}")

    # Wait for completion
    result = client.wait_for_research(research["research_id"])

    print(f"\nSummary: {result['summary'][:500]}...")
    print(f"Sources: {len(result.get('sources', []))}")
    print(f"Findings: {len(result.get('findings', []))}")


def example_detailed_research(client: LDRClient) -> None:
    """Example: Detailed research with multiple search engines."""
    print("\n=== Example 2: Detailed Research ===")

    # Check available search engines
    engines = client.get_available_search_engines()
    print(f"Available search engines: {engines}")

    # Use multiple engines
    selected_engines = (
        ["wikipedia", "arxiv"] if "arxiv" in engines else ["wikipedia"]
    )

    research = client.start_research(
        query="Impact of climate change on global food security",
        search_engines=selected_engines,
        iterations=3,
        questions_per_iteration=4,
        temperature=0.7,
    )

    print(f"Started detailed research ID: {research['research_id']}")

    # Monitor progress
    result = client.wait_for_research(research["research_id"], timeout=600)

    print(f"\nTitle: {result.get('query', 'N/A')}")
    print(f"Summary length: {len(result['summary'])} characters")
    print(f"Sources: {len(result.get('sources', []))}")

    # Show some findings
    findings = result.get("findings", [])
    if findings:
        print("\nTop findings:")
        for i, finding in enumerate(findings[:3], 1):
            print(f"{i}. {finding.get('text', 'N/A')[:100]}...")


def example_settings_management(client: LDRClient) -> None:
    """Example: Managing user settings."""
    print("\n=== Example 3: Settings Management ===")

    # Get current settings
    settings = client.get_settings()
    settings_data = settings.get("settings", {})

    # Display current LLM configuration
    llm_provider = settings_data.get("llm.provider", {}).get("value", "Not set")
    llm_model = settings_data.get("llm.model", {}).get("value", "Not set")

    print(f"Current LLM Provider: {llm_provider}")
    print(f"Current LLM Model: {llm_model}")

    # Get available models
    models = client.get_available_models()
    print(f"\nAvailable providers: {list(models.keys())}")

    # Example: Update temperature setting
    current_temp = settings_data.get("llm.temperature", {}).get("value", 0.7)
    print(f"\nCurrent temperature: {current_temp}")

    # Update temperature (example - uncomment to actually update)
    # success = client.update_setting("llm.temperature", 0.5)
    # print(f"Temperature update: {'Success' if success else 'Failed'}")


def example_batch_research(client: LDRClient) -> None:
    """Example: Running multiple research tasks in batch."""
    print("\n=== Example 4: Batch Research ===")

    queries = [
        "What is quantum entanglement?",
        "How does CRISPR gene editing work?",
        "What are the applications of blockchain technology?",
    ]

    research_ids = []

    # Start all research tasks
    for query in queries:
        try:
            research = client.start_research(
                query=query, iterations=1, questions_per_iteration=2
            )
            research_ids.append(
                {
                    "id": research["research_id"],
                    "query": query,
                    "status": "started",
                }
            )
            print(f"Started: {query} (ID: {research['research_id']})")
        except Exception as e:
            print(f"Failed to start '{query}': {e}")

    # Wait for all to complete
    print("\nWaiting for batch completion...")
    completed = 0

    while completed < len(research_ids):
        for research in research_ids:
            if research["status"] != "completed":
                try:
                    status = client.get_research_status(research["id"])
                    if status.get("status") == "completed":
                        research["status"] = "completed"
                        completed += 1
                        print(f"✓ Completed: {research['query']}")
                except Exception:
                    pass

        if completed < len(research_ids):
            time.sleep(3)

    # Get all results
    print("\nBatch Results Summary:")
    for research in research_ids:
        try:
            result = client.get_research_result(research["id"])
            print(f"\n{research['query']}:")
            print(f"  - Summary: {result['summary'][:150]}...")
            print(f"  - Sources: {len(result.get('sources', []))}")
        except Exception as e:
            print(f"  - Error getting results: {e}")


def example_research_history(client: LDRClient) -> None:
    """Example: Viewing research history."""
    print("\n=== Example 5: Research History ===")

    history = client.get_history(limit=5)

    if not history:
        print("No research history found.")
        return

    print(f"Found {len(history)} recent research items:\n")

    for item in history:
        created = item.get("created_at", "Unknown date")
        query = item.get("query", "Unknown query")
        status = item.get("status", "Unknown")
        research_id = item.get("id", item.get("research_id", "N/A"))

        print(f"ID: {research_id}")
        print(f"Query: {query}")
        print(f"Date: {created}")
        print(f"Status: {status}")
        print("-" * 40)


def main():
    """Run all examples."""
    print("=== LDR HTTP API v1.0 Examples ===")

    # Create client
    client = LDRClient(BASE_URL)

    # Check if we need to update credentials
    if USERNAME == "your_username":
        print(
            "\n⚠️  WARNING: Please update USERNAME and PASSWORD in this script!"
        )
        print("Steps:")
        print("1. Start server: python -m local_deep_research.web.app")
        print("2. Open: http://localhost:5000")
        print("3. Register an account")
        print("4. Update USERNAME and PASSWORD in this script")
        return

    try:
        # Login
        print(f"\nLogging in as: {USERNAME}")
        if not client.login(USERNAME, PASSWORD):
            print("❌ Login failed! Please check your credentials.")
            return

        print("✅ Login successful")

        # Check health
        health = client.check_health()
        print(f"Authenticated: {health.get('authenticated', False)}")
        print(f"Username: {health.get('username', 'N/A')}")

        # Run examples
        example_quick_research(client)
        example_detailed_research(client)
        example_settings_management(client)
        example_batch_research(client)
        example_research_history(client)

    except requests.exceptions.ConnectionError:
        print("\n❌ Cannot connect to LDR server!")
        print("Make sure the server is running:")
        print("  python -m local_deep_research.web.app")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        # Always logout
        client.logout()
        print("\n✅ Logged out")


if __name__ == "__main__":
    main()
