#!/bin/bash
# HTTP API Examples using curl
# Make sure LDR server is running: python -m src.local_deep_research.web.app

API_URL="http://localhost:5000/api/v1"

echo "=== Health Check ==="
curl -s "$API_URL/health" | jq .

echo -e "\n=== Quick Summary ==="
curl -s -X POST "$API_URL/quick_summary" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is quantum computing?",
    "search_tool": "wikipedia",
    "iterations": 1
  }' | jq '.summary' | head -c 500

echo -e "\n\n=== Get Available Search Engines ==="
curl -s "$API_URL/search_engines" | jq 'keys'

echo -e "\n=== Detailed Research ==="
curl -s -X POST "$API_URL/detailed_research" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Future of electric vehicles",
    "iterations": 2,
    "questions_per_iteration": 3,
    "search_strategy": "source_based"
  }' | jq '{
    research_id: .research_id,
    summary_length: (.summary | length),
    sources_count: (.sources | length),
    metadata: .metadata
  }'

echo -e "\n=== Generate Report (Long Running) ==="
echo "Note: This can take several minutes. Uncomment to run:"
echo "# curl -X POST \"$API_URL/generate_report\" \\"
echo "#   -H \"Content-Type: application/json\" \\"
echo "#   -d '{\"query\": \"AI impact on education\"}' \\"
echo "#   -o \"ai_education_report.json\""
