#!/usr/bin/env python3
"""
Simple HTTP API Example for Local Deep Research v1.0+

This example shows how to use the LDR API with authentication.
Requires LDR v1.0+ with authentication features.
"""

import requests
import time
import sys

# Configuration
API_URL = "http://localhost:5000"
USERNAME = "your_username"  # Change this!
PASSWORD = "your_password"  # Change this!


def main():
    # Create a session to persist cookies
    session = requests.Session()

    print("=== LDR HTTP API Example ===")
    print(f"Connecting to: {API_URL}")

    # Step 1: Login
    print("\n1. Authenticating...")
    login_response = session.post(
        f"{API_URL}/auth/login",
        json={"username": USERNAME, "password": PASSWORD},
    )

    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.text}")
        print("\nPlease ensure:")
        print("- The server is running: python -m local_deep_research.web.app")
        print("- You have created an account through the web interface")
        print("- You have updated USERNAME and PASSWORD in this script")
        sys.exit(1)

    print("✅ Login successful")

    # Step 2: Get CSRF token
    print("\n2. Getting CSRF token...")
    csrf_response = session.get(f"{API_URL}/auth/csrf-token")
    csrf_token = csrf_response.json()["csrf_token"]
    headers = {"X-CSRF-Token": csrf_token}
    print("✅ CSRF token obtained")

    # Example 1: Quick Summary (using the start endpoint)
    print("\n=== Example 1: Quick Summary ===")
    research_request = {
        "query": "What is machine learning?",
        "model": None,  # Will use default from settings
        "search_engines": ["wikipedia"],  # Fast for demo
        "iterations": 1,
        "questions_per_iteration": 2,
    }

    # Start research
    start_response = session.post(
        f"{API_URL}/research/api/start", json=research_request, headers=headers
    )

    if start_response.status_code != 200:
        print(f"❌ Failed to start research: {start_response.text}")
        sys.exit(1)

    research_data = start_response.json()
    research_id = research_data["research_id"]
    print(f"✅ Research started with ID: {research_id}")

    # Poll for results
    print("\nWaiting for results...")
    while True:
        status_response = session.get(
            f"{API_URL}/research/api/research/{research_id}/status"
        )

        if status_response.status_code == 200:
            status = status_response.json()
            print(f"  Status: {status.get('status', 'unknown')}")

            if status.get("status") == "completed":
                break
            elif status.get("status") == "failed":
                print(
                    f"❌ Research failed: {status.get('error', 'Unknown error')}"
                )
                sys.exit(1)

        time.sleep(2)

    # Get results
    results_response = session.get(
        f"{API_URL}/research/api/research/{research_id}/result"
    )

    if results_response.status_code == 200:
        results = results_response.json()
        print(f"\n📝 Summary: {results['summary'][:300]}...")
        print(f"📚 Sources: {len(results.get('sources', []))} found")
        print(f"🔍 Findings: {len(results.get('findings', []))} findings")

    # Example 2: Check Settings
    print("\n=== Example 2: Current Settings ===")
    settings_response = session.get(f"{API_URL}/settings/api")

    if settings_response.status_code == 200:
        settings = settings_response.json()["settings"]

        # Show some key settings
        llm_provider = settings.get("llm.provider", {}).get("value", "Not set")
        llm_model = settings.get("llm.model", {}).get("value", "Not set")

        print(f"LLM Provider: {llm_provider}")
        print(f"LLM Model: {llm_model}")

    # Example 3: Get Research History
    print("\n=== Example 3: Research History ===")
    history_response = session.get(f"{API_URL}/history/api")

    if history_response.status_code == 200:
        history = history_response.json()
        items = history.get("items", history.get("history", []))

        print(f"Found {len(items)} research items")
        for item in items[:3]:  # Show first 3
            print(
                f"- {item.get('query', 'Unknown query')} ({item.get('created_at', 'Unknown date')})"
            )

    # Logout
    print("\n4. Logging out...")
    session.post(f"{API_URL}/auth/logout", headers=headers)
    print("✅ Logged out successfully")


if __name__ == "__main__":
    print("Make sure the LDR server is running:")
    print("  python -m local_deep_research.web.app\n")

    if USERNAME == "your_username":
        print("⚠️  WARNING: Please update USERNAME and PASSWORD in this script!")
        print("    Create an account through the web interface first.\n")

    main()
