#!/usr/bin/env python3
"""Test importing search_system directly without going through __init__.py"""

import sys

# Try importing the search_system module directly
try:
    print("Attempting to import search_system module directly...")
    from local_deep_research import search_system

    print("✓ search_system module imported!")

    # Now try to access AdvancedSearchSystem
    print("\nTrying to access AdvancedSearchSystem class...")
    AdvancedSearchSystem = search_system.AdvancedSearchSystem
    print("✓ Got AdvancedSearchSystem class!")

except Exception as e:
    print(f"✗ Failed: {e}")
    import traceback

    traceback.print_exc()

# Also try a more direct import
try:
    print("\nAttempting direct file import...")
    sys.path.insert(0, "src")
    print("✓ Direct import worked!")
except Exception as e:
    print(f"✗ Direct import failed: {e}")
