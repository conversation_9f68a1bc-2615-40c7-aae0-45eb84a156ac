#!/usr/bin/env python3
"""
Minimal working example for programmatic access to Local Deep Research.

This shows how to use the core functionality without database dependencies.
"""

from langchain_ollama import ChatOllama
from local_deep_research.search_system import AdvancedSearchSystem

# Re-enable logging after import (it gets disabled in __init__.py)
from loguru import logger
import sys

logger.remove()
logger.add(sys.stderr, level="WARNING", format="{time} {level} {message}")
logger.enable("local_deep_research")


class MinimalSearchEngine:
    """Minimal search engine that returns hardcoded results."""

    def __init__(self, settings_snapshot=None):
        self.settings_snapshot = settings_snapshot or {}

    def run(self, query, research_context=None):
        """Return some fake search results."""
        return [
            {
                "title": "Introduction to AI",
                "link": "https://example.com/ai-intro",
                "snippet": "Artificial Intelligence (AI) is the simulation of human intelligence...",
                "full_content": "Full article about AI basics...",
                "rank": 1,
            },
            {
                "title": "Machine Learning Explained",
                "link": "https://example.com/ml-explained",
                "snippet": "Machine learning is a subset of AI that enables systems to learn...",
                "full_content": "Detailed explanation of machine learning...",
                "rank": 2,
            },
        ]


def main():
    """Minimal example of programmatic access."""
    print("=== Minimal Local Deep Research Example ===\n")

    # 1. Create LLM
    print("1. Creating Ollama LLM...")
    llm = ChatOllama(model="gemma3:12b")

    # 2. Create minimal search engine
    print("2. Creating minimal search engine...")

    # Settings for search system (without programmatic_mode)
    settings = {
        "search.iterations": 1,
        "search.strategy": "direct",
    }

    search = MinimalSearchEngine(settings)

    # 3. Create search system
    print("3. Creating AdvancedSearchSystem...")
    # IMPORTANT: Pass programmatic_mode=True to avoid database dependencies
    system = AdvancedSearchSystem(
        llm=llm,
        search=search,
        settings_snapshot=settings,
        programmatic_mode=True,
    )

    # 4. Run a search
    print("\n4. Running search...")
    result = system.analyze_topic("What is artificial intelligence?")

    # 5. Show results
    print("\n=== RESULTS ===")
    print(f"Found {len(result['findings'])} findings")
    print(f"\nSummary:\n{result['current_knowledge']}")

    print("\n✓ Success! Programmatic access works without database.")


if __name__ == "__main__":
    main()
