#!/usr/bin/env python3
"""
Simple Programmatic API Example for Local Deep Research

Quick example showing how to use the LDR Python API directly.
"""

from local_deep_research.api import (
    detailed_research,
    quick_summary,
    generate_report,
)
from local_deep_research.api.settings_utils import (
    create_settings_snapshot,
)

# Use default settings with minimal overrides
# This provides all necessary settings with sensible defaults
settings_snapshot = create_settings_snapshot(
    overrides={
        "search.tool": "wikipedia",  # Use Wikipedia for this example
    }
)

# Alternative: Use completely default settings
# settings_snapshot = get_default_settings_snapshot()

# Example 1: Quick Summary
print("=== Quick Summary ===")
result = quick_summary(
    "What is machine learning?",
    settings_snapshot=settings_snapshot,
    programmatic_mode=True,
)
print(f"Summary: {result['summary'][:300]}...")
print(f"Found {len(result.get('findings', []))} findings")

# Example 2: Detailed Research with Custom Parameters
print("\n=== Detailed Research ===")
result = detailed_research(
    query="Impact of climate change on agriculture",
    iterations=2,
    search_tool="wikipedia",
    search_strategy="source_based",
    settings_snapshot=settings_snapshot,
    programmatic_mode=True,
)
print(f"Research ID: {result['research_id']}")
print(f"Summary length: {len(result['summary'])} characters")
print(f"Sources: {len(result.get('sources', []))}")

# Example 3: Using Custom Search Parameters
print("\n=== Custom Search Parameters ===")
result = quick_summary(
    query="renewable energy trends 2024",
    search_tool="auto",  # Auto-select best search engine
    iterations=1,
    questions_per_iteration=3,
    temperature=0.5,  # Lower temperature for focused results
    provider="openai_endpoint",  # Specify LLM provider
    model_name="llama-3.3-70b-instruct",  # Specify model
    settings_snapshot=settings_snapshot,
    programmatic_mode=True,
)
print(f"Completed {result['iterations']} iterations")
print(
    f"Generated {sum(len(qs) for qs in result.get('questions', {}).values())} questions"
)

# Example 4: Generate and Save a Report
print("\n=== Generate Report ===")
print("Note: Report generation can take several minutes")

# Generate a comprehensive report
report = generate_report(
    query="Future of artificial intelligence",
    output_file="ai_future_report.md",  # Save directly to file
    searches_per_section=2,
    iterations=1,
    settings_snapshot=settings_snapshot,  # Now works with programmatic mode!
)
print(f"Report saved to: {report.get('file_path', 'ai_future_report.md')}")
print(f"Report length: {len(report['content'])} characters")
print("Report preview (first 300 chars):")
print(report["content"][:300] + "...")
