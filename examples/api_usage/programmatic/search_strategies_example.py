#!/usr/bin/env python3
"""
Search Strategies Example for Local Deep Research

This example demonstrates the two main search strategies:
1. source-based: Comprehensive research with source citation
2. focused-iteration: Iterative refinement of research questions

Each strategy has different strengths and use cases.
"""

from local_deep_research.api import quick_summary, detailed_research
from local_deep_research.api.settings_utils import create_settings_snapshot


def demonstrate_source_based_strategy():
    """
    Source-based strategy:
    - Focuses on gathering and synthesizing information from multiple sources
    - Provides detailed citations and source tracking
    - Best for: Academic research, fact-checking, comprehensive reports
    """
    print("=" * 70)
    print("SOURCE-BASED STRATEGY")
    print("=" * 70)
    print("""
This strategy:
- Systematically searches for sources related to your topic
- Synthesizes information across multiple sources
- Provides detailed citations for all claims
- Ideal for research requiring source verification
    """)

    # Configure settings for programmatic mode
    settings = create_settings_snapshot(
        {
            "search.tool": "wikipedia",  # Using Wikipedia for demonstration
        }
    )

    # Run research with source-based strategy
    result = detailed_research(
        query="What are the main causes of climate change?",
        settings_snapshot=settings,
        search_strategy="source-based",  # Explicitly set strategy
        iterations=2,  # Number of research iterations
        questions_per_iteration=3,  # Questions to explore per iteration
        programmatic_mode=True,
    )

    print(f"Research ID: {result['research_id']}")
    print("\nSummary (first 500 chars):")
    print(result["summary"][:500] + "...")

    # Show sources found
    sources = result.get("sources", [])
    print(f"\nSources found: {len(sources)}")
    if sources:
        print("\nFirst 3 sources:")
        for i, source in enumerate(sources[:3], 1):
            print(f"  {i}. {source}")

    # Show the questions that were researched
    questions = result.get("questions", {})
    print(f"\nQuestions researched across {len(questions)} iterations:")
    for iteration, qs in questions.items():
        print(f"\n  Iteration {iteration}:")
        for q in qs[:2]:  # Show first 2 questions per iteration
            print(f"    - {q}")

    return result


def demonstrate_focused_iteration_strategy():
    """
    Focused-iteration strategy:
    - Iteratively refines the research based on previous findings
    - Adapts questions based on what's been learned
    - Best for: Deep dives, evolving research questions, exploratory research
    """
    print("\n" + "=" * 70)
    print("FOCUSED-ITERATION STRATEGY")
    print("=" * 70)
    print("""
This strategy:
- Starts with initial research on the topic
- Analyzes findings to generate more targeted questions
- Iteratively refines understanding through multiple rounds
- Ideal for complex topics requiring deep exploration
    """)

    # Configure settings
    settings = create_settings_snapshot(
        {
            "search.tool": "wikipedia",
        }
    )

    # Run research with focused-iteration strategy
    result = quick_summary(
        query="How do neural networks learn?",
        settings_snapshot=settings,
        search_strategy="focused-iteration",  # Use focused iteration
        iterations=3,  # More iterations for deeper exploration
        questions_per_iteration=2,  # Fewer but more focused questions
        temperature=0.7,  # Slightly higher for creative question generation
        programmatic_mode=True,
    )

    print("\nSummary (first 500 chars):")
    print(result["summary"][:500] + "...")

    # Show how questions evolved
    questions = result.get("questions", {})
    if questions:
        print("\nQuestion evolution across iterations:")
        for iteration, qs in questions.items():
            print(f"\n  Iteration {iteration}:")
            for q in qs:
                print(f"    - {q}")

    # Show findings
    findings = result.get("findings", [])
    print(f"\nKey findings: {len(findings)}")
    if findings:
        print("\nFirst 2 findings:")
        for i, finding in enumerate(findings[:2], 1):
            text = (
                finding.get("text", "N/A")
                if isinstance(finding, dict)
                else str(finding)
            )
            print(f"  {i}. {text[:150]}...")

    return result


def compare_strategies():
    """
    Direct comparison of both strategies on the same topic.
    """
    print("\n" + "=" * 70)
    print("STRATEGY COMPARISON")
    print("=" * 70)
    print(
        "\nComparing both strategies on the same topic: 'Quantum Computing Applications'\n"
    )

    settings = create_settings_snapshot(
        {
            "search.tool": "wikipedia",
        }
    )

    # Same topic, different strategies
    topic = "Quantum computing applications in cryptography"

    print("1. Source-based approach:")
    source_result = quick_summary(
        query=topic,
        settings_snapshot=settings,
        search_strategy="source-based",
        iterations=2,
        questions_per_iteration=3,
        programmatic_mode=True,
    )
    print(f"   - Sources found: {len(source_result.get('sources', []))}")
    print(f"   - Summary length: {len(source_result.get('summary', ''))} chars")
    print(f"   - Findings: {len(source_result.get('findings', []))}")

    print("\n2. Focused-iteration approach:")
    focused_result = quick_summary(
        query=topic,
        settings_snapshot=settings,
        search_strategy="focused-iteration",
        iterations=2,
        questions_per_iteration=3,
        programmatic_mode=True,
    )
    print(f"   - Sources found: {len(focused_result.get('sources', []))}")
    print(
        f"   - Summary length: {len(focused_result.get('summary', ''))} chars"
    )
    print(f"   - Findings: {len(focused_result.get('findings', []))}")

    print("\n" + "=" * 70)
    print("WHEN TO USE EACH STRATEGY")
    print("=" * 70)
    print("""
Use SOURCE-BASED when you need:
- Comprehensive coverage with citations
- Academic or professional research
- Fact-checking and verification
- Documentation with source tracking

Use FOCUSED-ITERATION when you need:
- Deep exploration of complex topics
- Adaptive research that evolves
- Discovery of unexpected connections
- Exploratory or investigative research
    """)


def main():
    """Run all demonstrations."""
    print("=" * 70)
    print("LOCAL DEEP RESEARCH - SEARCH STRATEGIES DEMONSTRATION")
    print("=" * 70)

    # Demonstrate each strategy
    demonstrate_source_based_strategy()
    demonstrate_focused_iteration_strategy()

    # Compare strategies
    compare_strategies()

    print("\n✓ Search strategies demonstration complete!")
    print("\nNote: Both strategies can be combined with different search tools")
    print(
        "(wikipedia, arxiv, searxng, etc.) and custom parameters for optimal results."
    )


if __name__ == "__main__":
    main()
