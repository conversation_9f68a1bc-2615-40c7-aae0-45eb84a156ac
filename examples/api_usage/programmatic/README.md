# Local Deep Research - Programmatic API Examples

This directory contains examples demonstrating how to use Local Deep Research programmatically without requiring authentication or database access.

## Quick Start

All examples use the programmatic API that bypasses authentication:

```python
from local_deep_research.api import quick_summary, detailed_research
from local_deep_research.api.settings_utils import create_settings_snapshot

# Create settings for programmatic mode
settings = create_settings_snapshot({
    "search.tool": "wikipedia"
})

# Run research
result = quick_summary(
    "your topic",
    settings_snapshot=settings,
    programmatic_mode=True
)
```

## Examples Overview

| Example | Purpose | Key Features | Difficulty |
|---------|---------|--------------|------------|
| **minimal_working_example.py** | Simplest possible example | Basic setup, minimal code | Beginner |
| **simple_programmatic_example.py** | Common use cases with the new API | quick_summary, detailed_research, generate_report, custom parameters | Beginner |
| **search_strategies_example.py** | Demonstrates search strategies | source-based vs focused-iteration strategies | Intermediate |
| **hybrid_search_example.py** | Combine multiple search sources | Multiple retrievers, web + retriever combo | Intermediate |
| **advanced_features_example.py** | Advanced programmatic features | generate_report, export formats, result analysis, keyword extraction | Advanced |
| **custom_llm_retriever_example.py** | Custom LLM and retriever integration | Ollama, custom retrievers, FAISS | Advanced |
| **searxng_example.py** | Web search with SearXNG | SearXNG integration, error handling | Advanced |

## Example Details

### minimal_working_example.py
**Purpose:** Show the absolute minimum code needed to use LDR programmatically.
- Creates a simple LLM and search engine
- Runs a basic search
- No external dependencies beyond Ollama

### simple_programmatic_example.py
**Purpose:** Demonstrate the main API functions with practical examples.
- `quick_summary()` - Fast research with summary
- `detailed_research()` - Comprehensive research with findings
- `generate_report()` - Create full markdown reports
- Custom search parameters
- Different search tools (Wikipedia, auto, etc.)

### search_strategies_example.py
**Purpose:** Explain and demonstrate the two main search strategies.
- **source-based**: Comprehensive research with detailed citations
- **focused-iteration**: Iterative refinement of research questions
- Side-by-side comparison of strategies
- When to use each strategy

### hybrid_search_example.py
**Purpose:** Show how to combine multiple search sources for comprehensive research.
- Multiple named retrievers for different document types
- Combining custom retrievers with web search
- Source analysis and tracking
- Meta search configuration

### advanced_features_example.py
**Purpose:** Demonstrate advanced programmatic features and analysis capabilities.
- `generate_report()` - Create comprehensive markdown reports
- Export formats - JSON, Markdown, custom formats
- Result analysis - Extract insights and patterns
- Keyword extraction - Identify key terms and concepts
- Batch research - Process multiple queries efficiently

### custom_llm_retriever_example.py
**Purpose:** Advanced integration with custom components.
- Custom LLM implementation (using Ollama)
- Custom retriever with embeddings
- Vector store integration (FAISS)
- Direct use of AdvancedSearchSystem

### searxng_example.py
**Purpose:** Web search integration using SearXNG.
- SearXNG configuration
- Error handling and fallbacks
- Real-time web search
- Direct use of search engines

## Key Concepts

### Programmatic Mode
All examples use `programmatic_mode=True` as an explicit parameter to bypass authentication:
```python
result = quick_summary(
    query="your topic",
    settings_snapshot=settings,
    programmatic_mode=True
)
```

### Search Strategies
- **source-based**: Best for academic research, fact-checking
- **focused-iteration**: Best for exploratory research, complex topics

### Search Tools
Available search tools include:
- `wikipedia` - Wikipedia search
- `arxiv` - Academic papers
- `searxng` - Web search via SearXNG
- `auto` - Automatically select best tool
- `meta` - Combine multiple tools

### Custom Retrievers
You can provide your own retrievers:
```python
result = quick_summary(
    query="topic",
    retrievers={"my_docs": custom_retriever},
    search_tool="my_docs",
    settings_snapshot=settings,
    programmatic_mode=True
)
```

## API Functions

### `quick_summary()`
Generate a quick research summary:
```python
from local_deep_research.api import quick_summary
from local_deep_research.api.settings_utils import create_settings_snapshot

settings = create_settings_snapshot({})
result = quick_summary(
    query="Your research question",
    settings_snapshot=settings,
    search_tool="wikipedia",
    iterations=2,
    programmatic_mode=True
)
```

### `detailed_research()`
Perform in-depth research with multiple iterations:
```python
from local_deep_research.api import detailed_research

result = detailed_research(
    query="Your research question",
    settings_snapshot=settings,
    search_strategy="source-based",
    iterations=3,
    questions_per_iteration=5,
    programmatic_mode=True
)
```

### `generate_report()`
Generate comprehensive markdown reports with structured sections:
```python
from local_deep_research.api import generate_report
from local_deep_research.api.settings_utils import create_settings_snapshot

settings = create_settings_snapshot(overrides={"programmatic_mode": True})
result = generate_report(
    query="Your research question",
    settings_snapshot=settings,
    output_file="report.md",
    searches_per_section=3
)
```

## Requirements

- Python 3.8+
- Local Deep Research installed
- Ollama (for most examples)
- SearXNG instance (for searxng_example.py)

## Running the Examples

1. Install Local Deep Research:
   ```bash
   pip install -e .
   ```

2. Start Ollama (if using Ollama examples):
   ```bash
   ollama serve
   ollama pull gemma3:12b
   ollama pull nomic-embed-text  # For embeddings
   ```

3. Run any example:
   ```bash
   python minimal_working_example.py
   python simple_programmatic_example.py
   python search_strategies_example.py
   ```

## Troubleshooting

### "No settings context available" Error
Make sure to pass `settings_snapshot` and `programmatic_mode` to all API functions:
```python
settings = create_settings_snapshot({})
result = quick_summary(
    "topic",
    settings_snapshot=settings,
    programmatic_mode=True
)
```

### Ollama Connection Error
Ensure Ollama is running:
```bash
ollama serve
```

### SearXNG Connection Error
Start a SearXNG instance or use the fallback in the example:
```bash
docker run -p 8080:8080 searxng/searxng
```


## Contributing

When adding new examples:
1. Focus on demonstrating specific features
2. Include clear comments explaining the code
3. Handle errors gracefully
4. Update this README with the new example

## License

See the main project LICENSE file.
