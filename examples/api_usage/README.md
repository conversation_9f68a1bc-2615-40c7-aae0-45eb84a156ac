# Local Deep Research API Examples

This directory contains examples for using LDR through different interfaces.

## Important: Authentication Required (v2.0+)

Since LDR v2.0, all API access requires authentication due to per-user encrypted databases. You must:

1. Create a user account through the web interface
2. Authenticate before making API calls
3. Pass settings_snapshot for programmatic access

## Directory Structure

- **`programmatic/`** - Direct Python API usage (import from `local_deep_research.api`)
  - `programmatic_access.ipynb` - Jupyter notebook with comprehensive examples
  - `retriever_usage_example.py` - Using LangChain retrievers with LDR

- **`http/`** - HTTP REST API usage (requires running server)
  - `simple_http_example.py` - Quick start example (needs updating for auth)
  - `http_api_examples.py` - Comprehensive examples including batch processing

## Quick Start

### Programmatic API (Python Package)

```python
from local_deep_research.api import quick_summary
from local_deep_research.settings import CachedSettingsManager
from local_deep_research.database.session_context import get_user_db_session

# Authenticate and get settings
with get_user_db_session(username="your_username", password="your_password") as session:
    settings_manager = CachedSettingsManager(session, "your_username")
    settings_snapshot = settings_manager.get_all_settings()

    # Use the API
    result = quick_summary(
        "What is quantum computing?",
        settings_snapshot=settings_snapshot
    )
    print(result["summary"])
```

### HTTP API (REST)

First, start the server:
```bash
python -m local_deep_research.web.app
```

Then authenticate and use the API:
```python
import requests

# Create session for cookie persistence
session = requests.Session()

# Login
session.post(
    "http://localhost:5000/auth/login",
    json={"username": "your_username", "password": "your_password"}
)

# Get CSRF token
csrf_token = session.get("http://localhost:5000/auth/csrf-token").json()["csrf_token"]

# Make API request
response = session.post(
    "http://localhost:5000/research/api/start",
    json={"query": "What is quantum computing?"},
    headers={"X-CSRF-Token": csrf_token}
)
print(response.json())
```

## Which API Should I Use?

- **Programmatic API**: Use when integrating LDR into your Python application
  - ✅ Direct access, no HTTP overhead
  - ✅ Full access to all features and parameters
  - ✅ Can pass Python objects (like LangChain retrievers)
  - ❌ Requires LDR to be installed in your environment
  - ❌ Requires database session and settings snapshot

- **HTTP API**: Use when accessing LDR from other languages or remote systems
  - ✅ Language agnostic - works with any HTTP client
  - ✅ Can run LDR on a separate server
  - ✅ Easy to scale and deploy
  - ❌ Limited to JSON-serializable parameters
  - ❌ Requires running the web server
  - ❌ Requires authentication and CSRF tokens

## API Changes in v2.0

### Breaking Changes

1. **Authentication Required**: All endpoints now require login
2. **Settings Snapshot**: Programmatic API needs `settings_snapshot` parameter
3. **New Endpoints**: API routes moved (e.g., `/api/v1/quick_summary` → `/research/api/start`)
4. **CSRF Protection**: POST/PUT/DELETE requests need CSRF token

### Migration Guide

#### Old (v1.x):
```python
# Programmatic
from local_deep_research.api import quick_summary
result = quick_summary("query")

# HTTP
curl -X POST http://localhost:5000/api/v1/quick_summary \
  -d '{"query": "test"}'
```

#### New (v2.0+):
```python
# Programmatic - with authentication and settings
with get_user_db_session(username, password) as session:
    settings_manager = CachedSettingsManager(session, username)
    settings_snapshot = settings_manager.get_all_settings()
    result = quick_summary("query", settings_snapshot=settings_snapshot)

# HTTP - with authentication and CSRF
# See examples above
```

## Running the Examples

### Prerequisites

1. Install LDR: `pip install local-deep-research`
2. Create a user account:
   - Start server: `python -m local_deep_research.web.app`
   - Open http://localhost:5000 and register
3. Configure your LLM provider in settings

### Programmatic Examples
```bash
# Update credentials in the example files first!
python examples/api_usage/programmatic/retriever_usage_example.py

# Or use the Jupyter notebook
jupyter notebook examples/api_usage/programmatic/programmatic_access.ipynb
```

### HTTP Examples
```bash
# First, start the LDR server
python -m local_deep_research.web.app

# In another terminal, run the examples
# Note: These need to be updated for v2.0 authentication!
python examples/api_usage/http/simple_http_example.py
python examples/api_usage/http/http_api_examples.py
```

## Need Help?

- See the [API Quick Start Guide](../../docs/api-quickstart.md)
- Check the [FAQ](../../docs/faq.md)
- Join our [Discord](https://discord.gg/ttcqQeFcJ3) for support
