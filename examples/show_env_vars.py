#!/usr/bin/env python3
"""
Example script showing all available environment variables for LDR configuration.
This demonstrates the centralized environment variable management in SettingsManager.
"""

import sys
import os
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from local_deep_research.settings.manager import SettingsManager


def main():
    print("=== Local Deep Research Environment Variables ===\n")

    all_env_vars = SettingsManager.get_all_env_vars()

    for category, vars_dict in all_env_vars.items():
        print(f"\n{category.upper()} VARIABLES:")
        print("-" * 50)

        for var_name, description in sorted(vars_dict.items()):
            # Check if currently set
            current_value = os.environ.get(var_name)
            if current_value:
                # Mask sensitive values
                if any(
                    sensitive in var_name
                    for sensitive in ["KEY", "PASSWORD", "SECRET"]
                ):
                    display_value = "***SET***"
                else:
                    display_value = current_value
                status = f" [Current: {display_value}]"
            else:
                status = ""

            print(f"  {var_name}")
            print(f"    {description}{status}")

    print("\n\n=== Environment Variable Formats ===")
    print("-" * 50)
    print(
        "Settings can be overridden via environment variables using this format:"
    )
    print("  Setting key: app.host")
    print("  Environment variable: LDR_APP__HOST")
    print(
        "\nNote: Use double underscores (__) to separate setting path components."
    )

    print("\n\n=== Bootstrap Variables ===")
    print("-" * 50)
    print("The following variables must be set before database access:")
    bootstrap_vars = SettingsManager.get_bootstrap_env_vars()
    for var in sorted(bootstrap_vars.keys()):
        print(f"  - {var}")


if __name__ == "__main__":
    main()
