"""
Advanced example of custom LLM integration with Local Deep Research.

This example demonstrates:
- Factory functions with configuration
- Error handling and retry logic
- Combining multiple LLMs
- Integration with custom retrievers
"""

import time
from typing import Any, Dict, List, Optional

from langchain_core.callbacks import CallbackManagerForLLMRun
from langchain_core.language_models import BaseChatModel
from langchain_core.messages import AIMessage, BaseMessage
from langchain_core.outputs import Chat<PERSON>eneration, ChatResult
from loguru import logger

from local_deep_research.api import detailed_research, quick_summary


class RetryLLM(BaseChatModel):
    """LLM wrapper that adds retry logic to any base LLM."""

    def __init__(
        self,
        base_llm: BaseChatModel,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ):
        super().__init__()
        self.base_llm = base_llm
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Generate with retry logic."""
        last_error = None

        for attempt in range(self.max_retries):
            try:
                return self.base_llm._generate(
                    messages, stop, run_manager, **kwargs
                )
            except Exception as e:
                last_error = e
                if attempt < self.max_retries - 1:
                    logger.warning(
                        f"Attempt {attempt + 1} failed, retrying in {self.retry_delay}s..."
                    )
                    time.sleep(self.retry_delay)
                    self.retry_delay *= 2  # Exponential backoff

        raise last_error

    @property
    def _llm_type(self) -> str:
        return f"retry_{self.base_llm._llm_type}"


class ConfigurableLLM(BaseChatModel):
    """LLM that can be configured with custom parameters."""

    def __init__(
        self,
        model_name: str = "configurable-v1",
        response_style: str = "technical",
        max_length: int = 500,
        include_confidence: bool = False,
        **kwargs,
    ):
        """
        Initialize ConfigurableLLM.

        Args:
            model_name: Name of the model to use
            response_style: Style of response ('technical', 'simple', or other)
            max_length: Maximum length of response in characters
            include_confidence: Whether to include confidence scores in responses
            **kwargs: Additional keyword arguments
        """
        super().__init__(**kwargs)
        self.model_name = model_name
        self.response_style = response_style
        self.max_length = max_length
        self.include_confidence = include_confidence

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Generate response based on configuration."""
        # Extract the query
        query = messages[-1].content if messages else "No query"

        # Build response based on style
        if self.response_style == "technical":
            response = (
                f"Technical Analysis ({self.model_name}): {query[:100]}..."
            )
        elif self.response_style == "simple":
            response = (
                f"Simple Answer: Based on the query about {query[:50]}..."
            )
        else:
            response = f"Response: Processing '{query[:50]}...'"

        # Limit length
        response = response[: self.max_length]

        # Add confidence if requested
        if self.include_confidence:
            response += "\n\nConfidence: High"  # Use descriptive confidence instead of hardcoded percentage

        message = AIMessage(content=response)
        generation = ChatGeneration(message=message)

        return ChatResult(generations=[generation])

    @property
    def _llm_type(self) -> str:
        return "configurable"


class DomainExpertLLM(BaseChatModel):
    """LLM that specializes in specific domains."""

    def __init__(self, domain: str = "general", expertise_level: float = 0.8):
        super().__init__()
        self.domain = domain
        self.expertise_level = expertise_level
        self.domain_knowledge = {
            "medical": ["diagnosis", "treatment", "symptoms", "medications"],
            "legal": ["contracts", "liability", "regulations", "compliance"],
            "technical": [
                "algorithms",
                "architecture",
                "performance",
                "scalability",
            ],
            "finance": ["investments", "risk", "portfolio", "markets"],
        }

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Generate domain-specific response."""
        query = messages[-1].content if messages else ""

        # Check if query matches domain
        domain_terms = self.domain_knowledge.get(self.domain, [])
        relevance = sum(
            1 for term in domain_terms if term.lower() in query.lower()
        )

        if relevance > 0:
            response = f"[{self.domain.upper()} EXPERT - High Relevance]: "
        else:
            response = f"[{self.domain.upper()} EXPERT - General]: "

        response += f"Based on my {self.domain} expertise (level: {self.expertise_level}), "
        response += f"regarding '{query[:100]}...': This requires specialized knowledge."

        message = AIMessage(content=response)
        generation = ChatGeneration(message=message)

        return ChatResult(generations=[generation])

    @property
    def _llm_type(self) -> str:
        return f"expert_{self.domain}"


def create_configured_llm(config: Dict[str, Any]) -> BaseChatModel:
    """Factory function that creates LLMs based on configuration."""
    llm_type = config.get("type", "basic")

    if llm_type == "retry":
        # Create base LLM first
        base_config = config.get("base_config", {})
        base_llm = create_configured_llm(base_config)

        # Wrap with retry
        return RetryLLM(
            base_llm=base_llm,
            max_retries=config.get("max_retries", 3),
            retry_delay=config.get("retry_delay", 1.0),
        )

    elif llm_type == "configurable":
        return ConfigurableLLM(
            model_name=config.get("model_name", "config-v1"),
            response_style=config.get("style", "technical"),
            max_length=config.get("max_length", 500),
            include_confidence=config.get("include_confidence", False),
        )

    elif llm_type == "expert":
        return DomainExpertLLM(
            domain=config.get("domain", "general"),
            expertise_level=config.get("expertise_level", 0.8),
        )

    else:
        # Default fallback
        return ConfigurableLLM()


def main():
    logger.info("Advanced Custom LLM Integration Examples")
    logger.info("=" * 60)

    # Example 1: Using a retry wrapper
    logger.info("\n1. Retry Wrapper Example:")
    base_llm = ConfigurableLLM(response_style="technical")
    retry_llm = RetryLLM(base_llm, max_retries=3)

    result = quick_summary(
        query="Explain quantum computing applications",
        llms={"retry_tech": retry_llm},
        provider="retry_tech",
        search_tool="wikipedia",
    )
    logger.info(f"Summary: {result['summary'][:200]}...")

    # Example 2: Multiple domain experts
    logger.info("\n\n2. Multiple Domain Experts:")
    experts = {
        "medical_expert": DomainExpertLLM(
            domain="medical", expertise_level=0.95
        ),
        "tech_expert": DomainExpertLLM(domain="technical", expertise_level=0.9),
        "finance_expert": DomainExpertLLM(
            domain="finance", expertise_level=0.85
        ),
    }

    # Medical query
    _ = quick_summary(
        query="What are the latest treatments for diabetes?",
        llms=experts,
        provider="medical_expert",
        search_tool="pubmed",
    )
    logger.info(
        "Medical summary retrieved successfully. Content not logged for privacy."
    )

    # Example 3: Factory with configuration
    logger.info("\n\n3. Factory Configuration Example:")

    # Configuration for a technical writer
    tech_writer_config = {
        "type": "configurable",
        "model_name": "tech-writer-v2",
        "style": "technical",
        "max_length": 1000,
        "include_confidence": True,
    }

    # Configuration for a retry wrapper around the technical writer
    robust_config = {
        "type": "retry",
        "max_retries": 5,
        "retry_delay": 0.5,
        "base_config": tech_writer_config,
    }

    result = quick_summary(
        query="How do neural networks learn?",
        llms={
            "robust_writer": lambda **kwargs: create_configured_llm(
                robust_config
            )
        },
        provider="robust_writer",
        search_tool="arxiv",
    )
    logger.info(f"Robust Writer: {result['summary'][:150]}...")

    # Example 4: Research pipeline with different LLMs
    logger.info("\n\n4. Multi-Stage Research Pipeline:")

    # Stage 1: Quick exploration with simple LLM
    simple_llm = ConfigurableLLM(response_style="simple", max_length=200)

    initial = quick_summary(
        query="Climate change impacts on agriculture",
        llms={"simple": simple_llm},
        provider="simple",
        iterations=1,
    )

    logger.info(f"Initial exploration: {initial['summary'][:100]}...")

    # Stage 2: Detailed research with expert
    expert_llm = DomainExpertLLM(domain="technical", expertise_level=0.95)

    detailed = detailed_research(
        query="Climate change impacts on agriculture: focus on technology solutions",
        llms={"expert": expert_llm},
        provider="expert",
        iterations=2,
    )

    logger.info(f"Expert analysis: {detailed['summary'][:150]}...")

    # Example 5: Combining custom LLMs with custom retrievers
    logger.info("\n\n5. Custom LLM + Retriever Combination:")

    # Mock retriever for demonstration
    class MockRetriever:
        def get_relevant_documents(self, query):
            return [
                {"page_content": f"Mock document about {query}", "metadata": {}}
            ]

    custom_llm = ConfigurableLLM(
        model_name="integrated-v1",
        response_style="technical",
        include_confidence=True,
    )

    result = quick_summary(
        query="Internal company policies on remote work",
        llms={"integrated": custom_llm},
        retrievers={"company_docs": MockRetriever()},
        provider="integrated",
        search_tool="company_docs",
    )

    logger.info(f"Integrated result: {result['summary'][:150]}...")


if __name__ == "__main__":
    main()
