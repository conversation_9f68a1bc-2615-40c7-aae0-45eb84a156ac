# Elasticsearch Integration Example

Quick setup guide to test Elasticsearch with Local Deep Research.

## Quick Start

1. **Start Elasticsearch**
   ```bash
   # From the examples/elasticsearch directory:
   sudo docker compose up -d

   # Or to see logs while it starts:
   sudo docker compose up
   ```

2. **Index sample documents**
   ```bash
   python search_example.py
   ```

3. **Configure in Web UI**
   - Go to Settings → Search Engines → Elasticsearch
   - Default settings should work (http://localhost:9200)
   - Index name: `documents`

4. **Stop Elasticsearch**
   ```bash
   sudo docker compose down
   ```

## Files

- `docker-compose.yml` - Elasticsearch container setup
- `search_example.py` - Example script to index and search documents
- `test_elasticsearch.sh` - Shell script to verify Elasticsearch is running

## Requirements

- Docker and Docker Compose
- Python with project dependencies installed
