#!/bin/bash

echo "==================================="
echo "Elasticsearch Local Testing Script"
echo "==================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if Docker is available
if command -v docker &> /dev/null; then
    echo -e "${GREEN}✓ Docker found${NC}"

    echo ""
    echo "Starting Elasticsearch with Docker..."
    echo "Running: docker-compose -f docker-compose.elasticsearch.yml up -d"

    docker compose -f docker-compose.elasticsearch.yml up -d 2>/dev/null || docker-compose -f docker-compose.elasticsearch.yml up -d

    echo ""
    echo "Waiting for Elasticsearch to be ready (this may take 30-60 seconds)..."

    # Wait for Elasticsearch to be ready
    for i in {1..30}; do
        if curl -s -o /dev/null -w "%{http_code}" http://localhost:9200 | grep -q "200"; then
            echo -e "${GREEN}✓ Elasticsearch is ready!${NC}"
            break
        fi
        echo -n "."
        sleep 2
    done
    echo ""

else
    echo -e "${YELLOW}⚠ Docker not found${NC}"
    echo ""
    echo "To test Elasticsearch, you need to either:"
    echo "1. Install Docker: https://docs.docker.com/get-docker/"
    echo "2. Install Elasticsearch directly:"
    echo "   wget -qO - https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo gpg --dearmor -o /usr/share/keyrings/elasticsearch-keyring.gpg"
    echo "   echo \"deb [signed-by=/usr/share/keyrings/elasticsearch-keyring.gpg] https://artifacts.elastic.co/packages/8.x/apt stable main\" | sudo tee /etc/apt/sources.list.d/elastic-8.x.list"
    echo "   sudo apt-get update && sudo apt-get install elasticsearch"
    echo ""
    echo "Checking if Elasticsearch is already running on localhost:9200..."
fi

# Test connection
echo ""
echo "Testing Elasticsearch connection..."
if curl -s -o /dev/null -w "%{http_code}" http://localhost:9200 | grep -q "200"; then
    echo -e "${GREEN}✓ Elasticsearch is running on http://localhost:9200${NC}"

    # Get cluster info
    echo ""
    echo "Cluster info:"
    curl -s http://localhost:9200 | python3 -m json.tool | head -10

    echo ""
    echo "Now you can:"
    echo "1. Run the example script to index sample documents:"
    echo "   ${GREEN}python examples/elasticsearch_search_example.py${NC}"
    echo ""
    echo "2. Configure Elasticsearch in the web UI:"
    echo "   - Go to Settings → Search Engines → Elasticsearch"
    echo "   - Set Host URLs to: [\"http://localhost:9200\"]"
    echo "   - Set Index Name to: documents"
    echo ""
    echo "3. To stop Elasticsearch (if using Docker):"
    echo "   ${YELLOW}docker compose -f docker-compose.elasticsearch.yml down${NC}"

else
    echo -e "${RED}✗ Elasticsearch is not accessible on http://localhost:9200${NC}"
    echo ""
    echo "Please ensure Elasticsearch is installed and running."
    exit 1
fi
