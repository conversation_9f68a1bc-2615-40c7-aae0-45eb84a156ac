#!/bin/bash
# Run a benchmark with Claude API grading integration

# Navigate to project root which is needed for proper imports
cd "$(dirname "$0")/../../.."
echo "Changed to project root: $(pwd)"

# Activate virtual environment if it exists
VENV_PATH=".venv/bin/activate"
if [ -f "$VENV_PATH" ]; then
    echo "Activating virtual environment..."
    source "$VENV_PATH"
else
    echo "Warning: Virtual environment not found at $VENV_PATH"
fi

# Create a timestamp for output directory
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="benchmark_results/claude_benchmark_${TIMESTAMP}"
mkdir -p "$OUTPUT_DIR"

echo "Running benchmark with Claude API grading..."
echo "Results will be saved to: $OUTPUT_DIR"

# Use a long timeout for comprehensive benchmarks
pdm run timeout 86400 python -m examples.benchmarks.claude_grading.benchmark "$@"

echo "Benchmark complete or timed out."
echo "Check $OUTPUT_DIR for results."
