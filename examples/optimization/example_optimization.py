# example_optimization.py - Quick Demo Version
"""
Full parameter optimization example for Local Deep Research.

This script demonstrates the full parameter optimization functionality.

Usage:
    # Install dependencies with PDM
    cd /path/to/local-deep-research
    pdm install

    # Run the script with PDM
    pdm run python examples/optimization/example_optimization.py
"""

import json
from datetime import datetime, UTC
from pathlib import Path


# Import the optimization functionality
from local_deep_research.benchmarks.optimization import (
    optimize_parameters,
)

# <PERSON><PERSON>ru automatically handles logging configuration


def main():
    # Create timestamp for unique output directory
    timestamp = datetime.now(UTC).strftime("%Y%m%d_%H%M%S")
    output_dir = str(
        Path("examples")
        / "optimization"
        / "results"
        / f"optimization_results_{timestamp}"
    )
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    print(
        f"Starting quick optimization demo - results will be saved to {output_dir}"
    )

    # Demo with just a single simple optimization
    print("\n=== Running quick demo optimization ===")

    # Create a very simple parameter set to test
    param_space = {
        "iterations": {
            "type": "int",
            "low": 1,
            "high": 2,
            "step": 1,
        },
        "questions_per_iteration": {
            "type": "int",
            "low": 1,
            "high": 2,
            "step": 1,
        },
        "search_strategy": {
            "type": "categorical",
            "choices": ["rapid"],  # Just use the fastest strategy
        },
    }

    balanced_params, balanced_score = optimize_parameters(
        query="SimpleQA quick demo",  # Task descriptor
        search_tool="searxng",  # Using SearXNG
        n_trials=2,  # Just 2 trials for quick demo
        output_dir=str(Path(output_dir) / "demo"),
        param_space=param_space,  # Limited parameter space
        metric_weights={"quality": 0.5, "speed": 0.5},
    )

    print(f"Best parameters: {balanced_params}")
    print(f"Best score: {balanced_score:.4f}")

    # Save demo results to a summary file
    summary = {
        "timestamp": timestamp,
        "demo": {"parameters": balanced_params, "score": balanced_score},
    }

    with open(Path(output_dir) / "optimization_summary.json", "w") as f:
        json.dump(summary, f, indent=2)

    print(f"\nDemo complete! Results saved to {output_dir}")
    print(f"Recommended parameters: {balanced_params}")


if __name__ == "__main__":
    main()
