# src/local_deep_research/api/__init__.py
"""
API module for programmatic access to Local Deep Research functionality.
"""

from .research_functions import (
    analyze_documents,
    detailed_research,
    generate_report,
    quick_summary,
)
from .settings_utils import (
    create_settings_snapshot,
    get_default_settings_snapshot,
    extract_setting_value,
)

from ..news import (
    get_news_feed,
    research_news_item,
    save_news_preferences,
    get_news_categories,
)

__all__ = [
    "analyze_documents",
    "detailed_research",
    "generate_report",
    "quick_summary",
    "create_settings_snapshot",
    "get_default_settings_snapshot",
    "extract_setting_value",
    "get_news_feed",
    "research_news_item",
    "save_news_preferences",
    "get_news_categories",
]
