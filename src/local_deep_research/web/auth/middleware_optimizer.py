"""
Middleware optimization to skip unnecessary checks for static files and public routes.
"""

from flask import request


def should_skip_database_middleware():
    """
    Determine if the current request should skip database middleware.
    Returns True for requests that don't need database access.
    """
    path = request.path

    # Skip for static files
    if path.startswith("/static/"):
        return True

    # Skip for favicon, robots.txt, etc
    if path in ["/favicon.ico", "/robots.txt", "/health"]:
        return True

    # Skip for Socket.IO polling/websocket
    if path.startswith("/socket.io/"):
        return True

    # Skip for public auth routes that don't need existing database
    if path in ["/auth/login", "/auth/register", "/auth/logout"]:
        return True

    # Skip for preflight CORS requests
    if request.method == "OPTIONS":
        return True

    return False


def should_skip_queue_checks():
    """
    Determine if the current request should skip queue processing checks.
    """
    # Skip all GET requests - they don't create new work
    if request.method == "GET":
        return True

    # Skip if already skipping database middleware
    if should_skip_database_middleware():
        return True

    return False


def should_skip_session_cleanup():
    """
    Determine if the current request should skip session cleanup.
    Session cleanup should only run occasionally, not on every request.
    """
    import random

    # Only run cleanup 1% of the time (1 in 100 requests)
    if random.randint(1, 100) > 1:
        return True

    # Or skip based on the same rules as database middleware
    return should_skip_database_middleware()
