"""
Middleware to process pending queue operations when user has active session.
"""

from flask import g
from loguru import logger

from ...database.encrypted_db import db_manager
from ...database.session_context import get_user_db_session
from ..queue.processor import queue_processor


def process_pending_queue_operations():
    """
    Process pending queue operations for the current user.
    This runs in request context where we have access to the encrypted database.
    """
    # Check if user is authenticated and has a database session
    if not hasattr(g, "current_user") or not g.current_user:
        return

    # g.current_user might be a string (username) or an object
    username = (
        g.current_user
        if isinstance(g.current_user, str)
        else g.current_user.username
    )

    # Check if user has an open database connection
    if username not in db_manager.connections:
        return

    try:
        # Use the session context manager to properly handle the session
        with get_user_db_session(username) as db_session:
            if not db_session:
                return

            # Process any pending operations for this user
            started_count = queue_processor.process_pending_operations_for_user(
                username, db_session
            )

            if started_count > 0:
                logger.info(
                    f"Started {started_count} queued researches for {username}"
                )

    except Exception:
        logger.exception(
            f"Error processing pending queue operations for {username}"
        )
