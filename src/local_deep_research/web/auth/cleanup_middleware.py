"""
Middleware to clean up completed research records.
Runs in request context where we have database access.
"""

from flask import g, session
from loguru import logger

from ...database.models import UserActiveResearch
from .middleware_optimizer import should_skip_database_middleware


def cleanup_completed_research():
    """
    Clean up completed research records for the current user.
    Called as a before_request handler.
    """
    # Skip for requests that don't need database access
    if should_skip_database_middleware():
        return

    username = session.get("username")

    if username and hasattr(g, "db_session") and g.db_session:
        try:
            # Find completed researches that haven't been cleaned up
            from ..routes.globals import active_research

            # Get all active records for this user
            active_records = (
                g.db_session.query(UserActiveResearch)
                .filter_by(username=username)
                .all()
            )

            cleaned_count = 0
            for record in active_records:
                # Check if this research is still active
                if record.research_id not in active_research:
                    # Research has completed, clean up the record
                    g.db_session.delete(record)
                    cleaned_count += 1
                    logger.debug(
                        f"Cleaned up completed research {record.research_id} "
                        f"for user {username}"
                    )

            if cleaned_count > 0:
                g.db_session.commit()
                logger.info(
                    f"Cleaned up {cleaned_count} completed research records "
                    f"for user {username}"
                )

        except Exception:
            # Don't let cleanup errors break the request
            logger.exception("Error cleaning up completed research")
            g.db_session.rollback()
