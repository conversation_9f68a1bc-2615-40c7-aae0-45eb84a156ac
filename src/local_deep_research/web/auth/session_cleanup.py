"""
Session cleanup middleware to handle stale sessions.
"""

from flask import session
from loguru import logger

from ...database.encrypted_db import db_manager
from .middleware_optimizer import should_skip_session_cleanup


def cleanup_stale_sessions():
    """
    Clean up stale sessions where user is authenticated but has no database connection.
    This runs as a before_request handler.
    """
    # Only run cleanup occasionally, not on every request
    if should_skip_session_cleanup():
        return

    username = session.get("username")
    if username and not db_manager.connections.get(username):
        # Check if we have any way to recover the session
        temp_auth_token = session.get("temp_auth_token")
        session_id = session.get("session_id")

        # If we have no recovery mechanism and the database is encrypted,
        # clear the session to force re-login
        if not temp_auth_token and db_manager.has_encryption:
            # Check if we have a session password stored
            if session_id:
                from ...database.session_passwords import session_password_store

                password = session_password_store.get_session_password(
                    username, session_id
                )
                if not password:
                    # No way to recover - clear the session
                    logger.info(
                        f"Clearing stale session for user {username} - no database connection available"
                    )
                    session.clear()
            else:
                # No session ID, can't recover
                logger.info(
                    f"Clearing stale session for user {username} - no recovery mechanism available"
                )
                session.clear()
