"""
Queue middleware v2 - notifies queue processor of user activity.
"""

from flask import g, session
from loguru import logger

from .middleware_optimizer import should_skip_queue_checks


def notify_queue_processor():
    """
    Notify the queue processor that this user is active.
    Called as a before_request handler.
    """
    # Skip for GET requests and other operations that don't need queue processing
    if should_skip_queue_checks():
        return

    username = session.get("username")
    session_id = session.get("session_id")

    if username and session_id:
        # Only notify if user has database connection (authenticated)
        if hasattr(g, "db_session") and g.db_session:
            try:
                from ..queue.processor_v2 import queue_processor

                # Notify the processor to check this user's queue
                queue_processor.notify_user_activity(username, session_id)

                # Also trigger immediate processing if needed
                queued_count = queue_processor.process_user_request(
                    username, session_id
                )
                if queued_count > 0:
                    logger.debug(
                        f"User {username} has {queued_count} queued items, "
                        f"processor notified"
                    )

            except Exception:
                # Don't let queue errors break the request
                logger.exception("Error notifying queue processor")
                pass
