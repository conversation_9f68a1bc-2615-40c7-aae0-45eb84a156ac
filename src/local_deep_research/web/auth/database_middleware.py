"""
Middleware to ensure database connections are available for authenticated users.
"""

from flask import g, session

from ...database.encrypted_db import db_manager
from ...database.thread_local_session import get_metrics_session
from .middleware_optimizer import should_skip_database_middleware


def ensure_user_database():
    """
    Ensure the user's database is open for the current request.
    This is called as a before_request handler.
    """
    # Skip for requests that don't need database access
    if should_skip_database_middleware():
        return

    # Check if we already have a session for this request
    if hasattr(g, "db_session") and g.db_session:
        return  # Already set up

    username = session.get("username")
    if username:
        # Try to get password from various sources
        password = None

        # Check for temporary auth token (post-registration/login)
        temp_auth_token = session.get("temp_auth_token")
        if temp_auth_token:
            from ...database.temp_auth import temp_auth_store

            auth_data = temp_auth_store.retrieve_auth(temp_auth_token)
            if auth_data:
                stored_username, password = auth_data
                if stored_username == username:
                    # Remove token from session after use
                    session.pop("temp_auth_token", None)

                    # Store in session password store for future requests
                    session_id = session.get("session_id")
                    if session_id:
                        from ...database.session_passwords import (
                            session_password_store,
                        )

                        session_password_store.store_session_password(
                            username, session_id, password
                        )

        # If no password from temp auth, try session password store
        if not password:
            session_id = session.get("session_id")
            if session_id:
                from ...database.session_passwords import session_password_store

                password = session_password_store.get_session_password(
                    username, session_id
                )

        # For unencrypted databases, use dummy password
        if not password and not db_manager.has_encryption:
            password = "dummy"

        # If we have a password, get or create thread-local session
        if password:
            try:
                # Use thread-local session manager for efficiency
                db_session = get_metrics_session(username, password)
                if db_session:
                    g.db_session = db_session
                    g.user_password = password
                    g.username = username
            except Exception:
                # Don't log exceptions here to avoid deadlock
                pass
