import atexit
from loguru import logger

from ..utilities.log_utils import config_logger, flush_log_queue
from .app_factory import create_app


@logger.catch
def main():
    """
    Entry point for the web application when run as a command.
    This function is needed for the package's entry point to work properly.
    """
    # Configure logging with milestone level
    config_logger("ldr_web")

    # Register atexit handler to flush logs on exit
    def flush_logs_on_exit():
        """Flush any pending logs when the program exits."""
        try:
            # Create a minimal Flask app context to allow database access
            from flask import Flask

            app = Flask(__name__)
            with app.app_context():
                flush_log_queue()
        except Exception:
            logger.exception("Failed to flush logs on exit")

    atexit.register(flush_logs_on_exit)
    logger.debug("Registered atexit handler for log flushing")

    # Create the Flask app and SocketIO instance
    app, socket_service = create_app()

    # Get web server settings from configuration file or environment
    # This allows settings to be configured through the web UI
    from .server_config import load_server_config

    config = load_server_config()
    host = config["host"]
    port = config["port"]
    debug = config["debug"]
    use_https = config["use_https"]

    if use_https:
        # For development, use self-signed certificate
        logger.info("Starting server with HTTPS (self-signed certificate)")
        # Note: SocketIOService doesn't support SSL context directly
        # For production, use a reverse proxy like nginx for HTTPS
        logger.warning(
            "HTTPS requested but not supported directly. Use a reverse proxy for HTTPS."
        )

    # Register shutdown handler for scheduler
    def shutdown_scheduler():
        if hasattr(app, "news_scheduler") and app.news_scheduler:
            try:
                app.news_scheduler.stop()
                logger.info("News subscription scheduler stopped gracefully")
            except Exception:
                logger.exception("Error stopping scheduler")

    atexit.register(shutdown_scheduler)

    # Use the SocketIOService's run method which properly runs the socketio server
    socket_service.run(host=host, port=port, debug=debug)


if __name__ == "__main__":
    main()
