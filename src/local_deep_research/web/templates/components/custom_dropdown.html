{% macro render_dropdown(input_id, dropdown_id, placeholder, label=None, help_text=None, allow_custom=False, show_refresh=False, refresh_aria_label="Refresh options", data_setting_key=None, data_initial_value=None) %}
<div class="form-group">
    {% if label %}
    <label for="{{ input_id }}">{{ label }}</label>
    {% endif %}

    {% if show_refresh %}
    <div class="custom-dropdown-with-refresh">
        <div class="custom-dropdown" id="{{ dropdown_id }}">
            <input type="text"
                   id="{{ input_id }}"
                   data-key="{{ data_setting_key or input_id }}"
                   {% if data_initial_value %}data-initial-value="{{ data_initial_value }}"{% endif %}
                   class="custom-dropdown-input"
                   placeholder="{{ placeholder }}"
                   autocomplete="off"
                   aria-haspopup="listbox">
            <!-- Hidden input that will be included in form submission -->
            <input type="hidden" name="{{ input_id }}" id="{{ input_id }}_hidden" value="{{ data_initial_value or '' }}">
            <div class="custom-dropdown-list" id="{{ dropdown_id }}-list"></div>
        </div>
        <button type="button"
                class="custom-dropdown-refresh-btn dropdown-refresh-button"
                id="{{ input_id }}-refresh"
                aria-label="{{ refresh_aria_label }}">
            <i class="fas fa-sync-alt"></i>
        </button>
    </div>
    {% else %}
    <div class="custom-dropdown" id="{{ dropdown_id }}">
        <input type="text"
               id="{{ input_id }}"
               data-key="{{ data_setting_key or input_id }}"
               {% if data_initial_value %}data-initial-value="{{ data_initial_value }}"{% endif %}
               class="custom-dropdown-input"
               placeholder="{{ placeholder }}"
               autocomplete="off"
               aria-haspopup="listbox">
        <!-- Hidden input that will be included in form submission -->
        <input type="hidden" name="{{ input_id }}" id="{{ input_id }}_hidden" value="{{ data_initial_value or '' }}">
        <div class="custom-dropdown-list" id="{{ dropdown_id }}-list"></div>
    </div>
    {% endif %}

    {% if help_text %}
    <span class="input-help">{{ help_text }}</span>
    {% endif %}
</div>
{% endmacro %}
