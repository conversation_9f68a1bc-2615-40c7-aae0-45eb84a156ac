<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}Deep Research System{% endblock %}</title>
    <!-- Vite HMR for development -->
    {{ vite_hmr() }}

    <!-- Load all vendor dependencies and styles through Vite -->
    {{ vite_asset('js/app.js') }}

    <!-- Keep local styles that aren't in npm packages -->
    <link rel="stylesheet" href="/static/css/styles.css">
    {% block extra_head %}{% endblock %}
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        {% include 'components/sidebar.html' %}

        <!-- Main Content -->
        <main class="main-content">
            {% if session.username %}
            <div class="top-bar">
                <div class="top-bar-right">
                    <div class="user-menu">
                        <span class="user-info">
                            <i class="fas fa-user"></i> {{ session.username }}
                        </span>
                        <form action="{{ url_for('auth.logout') }}" method="POST" class="logout-form" id="logout-form" style="display: inline;">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                            <a href="#" class="logout-btn" onclick="event.preventDefault(); document.getElementById('logout-form').submit(); return false;">
                                <i class="fas fa-sign-out-alt"></i> Logout
                            </a>
                        </form>
                    </div>
                </div>
            </div>
            {% endif %}

            {% block content %}{% endblock %}

            <!-- Collapsible Log Panel is included in specific pages -->
        </main>
    </div>

    <!-- Mobile Tab Bar -->
    {% include 'components/mobile_nav.html' %}

    <!-- Common Templates -->
    {% block templates %}{% endblock %}

    <!-- Scripts -->

    <!-- URL Configuration (must load first) -->
    <script src="/static/js/config/urls.js"></script>

    <!-- Logo click handler -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const logoLink = document.getElementById('logo-link');
            if (logoLink) {
                logoLink.addEventListener('click', function() {
                    window.location.href = '/';
                });
            }
        });
    </script>

    <!-- All vendor libraries are now loaded through Vite in the <head> section -->

    <!-- Core JS -->
    <script src="/static/js/services/formatting.js"></script>
    <script src="/static/js/services/ui.js"></script>
    <script src="/static/js/services/api.js"></script>
    <script src="/static/js/services/socket.js"></script>
    <script src="/static/js/services/keyboard.js"></script>

    <!-- Shared Components -->
    <script src="/static/js/components/logpanel.js"></script>

    <!-- Page-specific Components -->
    {% block component_scripts %}{% endblock %}

    <!-- Page-specific JS -->
    {% block page_scripts %}{% endblock %}

    <script>
        // Configure marked to not use eval
        if (typeof marked !== 'undefined') {
            marked.setOptions({
                headerIds: false,
                mangle: false,
                smartypants: false
            });
        }

        // Configure html2canvas to avoid using eval if possible
        if (typeof html2canvas !== 'undefined') {
            window.html2canvas_noSandbox = true;
        }
    </script>

    <script src="/static/js/components/settings_sync.js"></script>
</body>
</html>
