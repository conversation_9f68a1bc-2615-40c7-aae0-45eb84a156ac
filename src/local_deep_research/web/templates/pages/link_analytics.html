{% extends "base.html" %}

{% block title %}Link Analytics - Deep Research System{% endblock %}

{% block extra_head %}
<style>
    .link-analytics-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .metrics-header {
        background: var(--gradient-bg);
        color: var(--text-primary);
        padding: 2rem;
        border-radius: 0.75rem;
        margin-bottom: 2rem;
        text-align: center;
        border: 1px solid var(--border-color);
        box-shadow: var(--card-shadow);
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .metric-card {
        background: var(--bg-secondary);
        border-radius: 0.75rem;
        padding: 1.5rem;
        box-shadow: var(--card-shadow);
        border: 1px solid var(--border-color);
        transition: transform 0.2s ease;
    }

    .metric-card:hover {
        transform: translateY(-2px);
    }

    .metric-card h3 {
        margin-top: 0;
        color: var(--text-primary);
        border-bottom: 2px solid var(--border-color);
        padding-bottom: 0.75rem;
        margin-bottom: 1rem;
        font-size: 1.125rem;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 1rem;
    }

    .overall-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1.25rem;
        margin-bottom: 2rem;
    }

    .stat-item {
        text-align: center;
        padding: 1rem;
        background: var(--bg-tertiary);
        border-radius: 0.5rem;
        border: 1px solid var(--border-color);
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: var(--accent-primary);
        display: block;
    }

    .stat-label {
        font-size: 0.75rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .domain-list {
        max-height: 400px;
        overflow-y: auto;
    }

    .domain-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        border-bottom: 1px solid var(--border-color);
        transition: background-color 0.2s ease;
    }

    .domain-item:hover {
        background-color: var(--bg-tertiary);
    }

    .domain-item:last-child {
        border-bottom: none;
    }

    .domain-name {
        font-weight: 500;
        color: var(--text-primary);
        flex: 1;
    }

    .domain-count {
        background: var(--accent-primary);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        margin-left: 0.5rem;
    }

    .domain-percentage {
        color: var(--text-secondary);
        font-size: 0.875rem;
        margin-left: 0.5rem;
    }

    .domain-item-expanded {
        flex-direction: column;
        align-items: stretch;
        padding: 1rem;
        background: var(--bg-tertiary);
        border-radius: 0.5rem;
        margin-bottom: 0.75rem;
    }

    .domain-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .domain-stats {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .metric-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.5rem;
        background: var(--bg-secondary);
        color: var(--text-primary);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .metric-badge.frequency {
        background: linear-gradient(135deg, #6b46c1, #9061f9);
        color: white;
        border: none;
    }

    .metric-badge.diversity {
        background: linear-gradient(135deg, #3b82f6, #60a5fa);
        color: white;
        border: none;
    }

    .metric-badge.rank {
        background: var(--bg-tertiary);
        color: var(--text-secondary);
    }

    .research-links {
        margin-top: 0.5rem;
        padding-top: 0.5rem;
        border-top: 1px solid var(--border-color);
    }

    .research-links-title {
        font-size: 0.75rem;
        color: var(--text-secondary);
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .research-link {
        display: inline-block;
        margin: 0.25rem 0.5rem 0.25rem 0;
        padding: 0.25rem 0.5rem;
        background: var(--bg-secondary);
        border: 1px solid var(--border-color);
        border-radius: 0.25rem;
        color: var(--accent-primary);
        text-decoration: none;
        font-size: 0.8125rem;
        transition: all 0.2s;
    }

    .research-link:hover {
        background: var(--accent-primary);
        color: white;
        transform: translateY(-1px);
    }

    .source-type-bar {
        display: flex;
        align-items: center;
        margin: 0.5rem 0;
        gap: 0.75rem;
    }

    .source-type-label {
        min-width: 100px;
        font-weight: 500;
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .source-type-progress {
        flex: 1;
        height: 20px;
        background: var(--bg-tertiary);
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .source-type-fill {
        height: 100%;
        transition: width 0.3s ease;
        border-radius: 0.5rem;
    }

    .source-type-fill.academic {
        background: linear-gradient(90deg, #6b46c1, #9061f9);
    }

    .source-type-fill.news {
        background: linear-gradient(90deg, #f59e0b, #fbbf24);
    }

    .source-type-fill.reference {
        background: linear-gradient(90deg, #3b82f6, #60a5fa);
    }

    .source-type-fill.general {
        background: linear-gradient(90deg, #10b981, #34d399);
    }

    .source-type-count {
        font-weight: 500;
        min-width: 40px;
        text-align: right;
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .category-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
    }

    .category-card {
        background: var(--bg-tertiary);
        border-radius: 0.5rem;
        padding: 1rem;
        text-align: center;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .category-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .category-name {
        font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
    }

    .category-count {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
    }

    .category-percentage {
        font-size: 0.75rem;
        color: var(--text-muted);
        opacity: 0.8;
    }

    .period-selector {
        margin-bottom: 1.5rem;
    }

    .period-selector label {
        color: var(--text-secondary);
        margin-right: 0.5rem;
        font-weight: 500;
    }

    .period-selector select {
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        border-radius: 0.375rem;
        background: var(--bg-secondary);
        color: var(--text-primary);
        cursor: pointer;
        transition: border-color 0.2s ease;
    }

    .period-selector select:focus {
        outline: none;
        border-color: var(--accent-primary);
        box-shadow: 0 0 0 3px rgba(110, 79, 246, 0.1);
    }

    .loading {
        text-align: center;
        padding: 2.5rem;
        color: var(--text-secondary);
        background: var(--bg-secondary);
        border-radius: 0.5rem;
        border: 1px solid var(--border-color);
    }

    .error {
        background: rgba(250, 92, 124, 0.1);
        color: var(--error-color);
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 1.25rem 0;
        border: 1px solid rgba(250, 92, 124, 0.2);
    }

    @media (max-width: 768px) {
        .metrics-grid {
            grid-template-columns: 1fr;
        }

        .overall-stats {
            grid-template-columns: repeat(2, 1fr);
        }

        .link-analytics-container {
            padding: 1rem;
        }
    }

    /* Modal styles for classification dialog */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
    }

    .modal-content {
        position: relative;
        background-color: var(--bg-secondary);
        margin: 5% auto;
        padding: 2rem;
        border-radius: 0.75rem;
        width: 80%;
        max-width: 600px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        border: 1px solid var(--border-color);
    }

    .modal-header {
        margin-bottom: 1.5rem;
    }

    .modal-header h2 {
        margin: 0;
        color: var(--text-primary);
    }

    .close-modal {
        position: absolute;
        right: 1.5rem;
        top: 1.5rem;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--text-secondary);
        transition: color 0.2s;
    }

    .close-modal:hover {
        color: var(--text-primary);
    }

    .progress-bar {
        width: 100%;
        height: 30px;
        background: var(--bg-tertiary);
        border-radius: 0.5rem;
        overflow: hidden;
        margin: 1rem 0;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--accent-primary), #9061f9);
        transition: width 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 500;
    }

    .classification-log {
        max-height: 300px;
        overflow-y: auto;
        background: var(--bg-tertiary);
        border-radius: 0.5rem;
        padding: 1rem;
        margin-top: 1rem;
        font-size: 0.875rem;
    }

    .log-entry {
        padding: 0.5rem;
        border-bottom: 1px solid var(--border-color);
    }

    .log-entry:last-child {
        border-bottom: none;
    }

    .log-entry.success {
        color: #22c55e;
    }

    .log-entry.error {
        color: #ef4444;
    }

    .classified-badge {
        display: inline-block;
        padding: 0.2rem 0.5rem;
        background: linear-gradient(135deg, #22c55e, #34d399);
        color: white;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 500;
        margin-left: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="link-analytics-container">
    <div class="metrics-header">
        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
            <h1>🔗 Link Analytics Dashboard</h1>
            <a href="/metrics/" style="background: var(--bg-tertiary); color: var(--text-primary); padding: 0.5rem 1rem; border-radius: 0.375rem; text-decoration: none; font-size: 0.875rem; border: 1px solid var(--border-color); transition: background-color 0.2s ease;">
                ← Back to Metrics
            </a>
        </div>
        <p>Analyze sources and domains used in your research sessions</p>
    </div>

    <div class="period-selector" style="display: flex; justify-content: space-between; align-items: center;">
        <div>
            <label for="period-select">Time Period: </label>
            <select id="period-select">
                <option value="7d">Last 7 days</option>
                <option value="30d" selected>Last 30 days</option>
                <option value="90d">Last 3 months</option>
                <option value="365d">Last year</option>
                <option value="all">All time</option>
            </select>
        </div>
        <div>
            <button id="classify-domains-btn" class="btn" style="background: var(--accent-primary); color: white; padding: 0.5rem 1rem; border-radius: 0.375rem; border: none; cursor: pointer; font-size: 0.875rem;">
                <i class="fas fa-brain"></i> Classify Domains with AI
            </button>
            <span id="classification-status" style="margin-left: 1rem; font-size: 0.875rem; color: var(--text-secondary);"></span>
        </div>
    </div>

    <div id="loading" class="loading">
        <div>Loading link analytics data...</div>
    </div>

    <div id="error" class="error" style="display: none;"></div>

    <div id="content" style="display: none;">
        <!-- Overall Statistics -->
        <div class="metric-card">
            <h3>📊 Overall Link Statistics</h3>
            <div class="overall-stats">
                <div class="stat-item">
                    <span class="stat-value" id="total-links">0</span>
                    <div class="stat-label">Total Links</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="unique-domains">0</span>
                    <div class="stat-label">Unique Domains</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="avg-links">0</span>
                    <div class="stat-label">Avg Links/Research</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value" id="total-researches">0</span>
                    <div class="stat-label">Total Researches</div>
                </div>
            </div>
        </div>

        <div class="metrics-grid">
            <!-- Top Domains Chart -->
            <div class="metric-card">
                <h3>🌐 Top 10 Domains</h3>
                <div class="chart-container">
                    <canvas id="top-domains-chart"></canvas>
                </div>
            </div>

            <!-- Domain Distribution Pie Chart -->
            <div class="metric-card">
                <h3>📊 Domain Distribution</h3>
                <div class="chart-container">
                    <canvas id="domain-distribution-chart"></canvas>
                </div>
            </div>

            <!-- Temporal Trend Chart -->
            <div class="metric-card" style="grid-column: span 2;">
                <h3>📈 Links Over Time</h3>
                <div class="chart-container">
                    <canvas id="temporal-trend-chart"></canvas>
                </div>
            </div>

            <!-- Source Type Analysis -->
            <div class="metric-card">
                <h3>📚 Domain Category Analysis</h3>
                <div id="domain-category-grid" class="category-grid">
                    <!-- Dynamic category cards will be generated here -->
                </div>
                <div class="chart-container" style="margin-top: 1.5rem;">
                    <canvas id="source-type-chart"></canvas>
                </div>
            </div>

            <!-- Top Domains List with Research Info -->
            <div class="metric-card" style="grid-column: span 2;">
                <h3>🔍 Domain Details & Research Connections</h3>
                <div class="domain-list" id="domain-list" style="max-height: 600px;">
                    <!-- Domain items will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Domain Classification Modal -->
<div id="classification-modal" class="modal">
    <div class="modal-content">
        <span class="close-modal">&times;</span>
        <div class="modal-header">
            <h2><i class="fas fa-brain"></i> AI Domain Classification</h2>
            <p style="color: var(--text-secondary); margin-top: 0.5rem;">
                The AI will analyze and categorize each domain based on its content and purpose.
            </p>
        </div>

        <div id="classification-info">
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin-bottom: 1.5rem;">
                <div style="background: var(--bg-tertiary); padding: 1rem; border-radius: 0.5rem;">
                    <div style="font-size: 1.5rem; font-weight: bold; color: var(--accent-primary);" id="total-domains-count">0</div>
                    <div style="font-size: 0.875rem; color: var(--text-secondary);">Total Domains</div>
                </div>
                <div style="background: var(--bg-tertiary); padding: 1rem; border-radius: 0.5rem;">
                    <div style="font-size: 1.5rem; font-weight: bold; color: #22c55e;" id="classified-count">0</div>
                    <div style="font-size: 0.875rem; color: var(--text-secondary);">Already Classified</div>
                </div>
            </div>

            <div id="classification-options" style="margin-bottom: 1.5rem;">
                <label style="display: flex; align-items: center; cursor: pointer;">
                    <input type="checkbox" id="force-reclassify" style="margin-right: 0.5rem;">
                    <span>Force reclassification of all domains (overwrite existing)</span>
                </label>
            </div>

            <button id="start-classification" class="btn" style="width: 100%; background: var(--accent-primary); color: white; padding: 0.75rem; border-radius: 0.5rem; border: none; cursor: pointer; font-size: 1rem;">
                <i class="fas fa-play"></i> Start Classification
            </button>
        </div>

        <div id="classification-progress" style="display: none;">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 0%;">0%</div>
            </div>

            <div style="text-align: center; margin-bottom: 1rem;">
                <span id="current-domain" style="color: var(--text-secondary);"></span>
            </div>

            <div class="classification-log" id="classification-log">
                <!-- Log entries will be added here -->
            </div>

            <button id="stop-classification" class="btn" style="width: 100%; background: #ef4444; color: white; padding: 0.75rem; border-radius: 0.5rem; border: none; cursor: pointer; font-size: 1rem; margin-top: 1rem;">
                <i class="fas fa-stop"></i> Stop Classification
            </button>
        </div>

        <div id="classification-complete" style="display: none; text-align: center;">
            <i class="fas fa-check-circle" style="font-size: 3rem; color: #22c55e; margin-bottom: 1rem;"></i>
            <h3>Classification Complete!</h3>
            <p style="color: var(--text-secondary); margin: 1rem 0;">
                Successfully classified <span id="final-classified">0</span> domains.
            </p>
            <button id="close-complete" class="btn" style="background: var(--accent-primary); color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer;">
                <i class="fas fa-check"></i> Done
            </button>
        </div>
    </div>
</div>

<script>
let topDomainsChart = null;
let domainDistributionChart = null;
let sourceTypeChart = null;
let temporalTrendChart = null;

async function loadLinkAnalytics(period = '30d') {
    const loading = document.getElementById('loading');
    const content = document.getElementById('content');
    const error = document.getElementById('error');

    loading.style.display = 'block';
    content.style.display = 'none';
    error.style.display = 'none';

    try {
        const response = await fetch(`/metrics/api/link-analytics?period=${period}`);
        const result = await response.json();

        if (result.status === 'success') {
            const data = result.data;

            // Update overall statistics
            document.getElementById('total-links').textContent = data.total_links || 0;
            document.getElementById('unique-domains').textContent = data.total_unique_domains || 0;
            document.getElementById('avg-links').textContent = data.avg_links_per_research || 0;
            document.getElementById('total-researches').textContent = data.total_researches || 0;

            // Update domain category grid
            const domainCategories = data.domain_categories || {};
            generateCategoryGrid(domainCategories);

            // Update charts
            updateTopDomainsChart(data.top_domains || []);
            updateDomainDistributionChart(data.domain_distribution || {});
            updateSourceTypeChart(domainCategories);
            updateTemporalTrendChart(data.temporal_trend || []);

            // Update enhanced domain list with research info
            updateEnhancedDomainList(data.top_domains || [], data.domain_metrics || {});

            loading.style.display = 'none';
            content.style.display = 'block';
        } else {
            throw new Error(result.message || 'Failed to load data');
        }
    } catch (err) {
        console.error('Error loading link analytics:', err);
        error.textContent = 'Failed to load link analytics data. Please try again later.';
        error.style.display = 'block';
        loading.style.display = 'none';
    }
}

function generateCategoryGrid(domainCategories) {
    const gridContainer = document.getElementById('domain-category-grid');
    const categoryEntries = Object.entries(domainCategories);
    const total = Object.values(domainCategories).reduce((sum, count) => sum + count, 0) || 1;

    // Clear existing content
    gridContainer.innerHTML = '';

    // Generate colors for cards (same as pie chart)
    const colors = generateChartColors(categoryEntries.length);

    // Create cards for each category
    categoryEntries.forEach(([categoryName, count], index) => {
        const percentage = total > 0 ? ((count / total * 100).toFixed(1)) : 0;
        const cardColor = colors.background[index];

        const cardHtml = `
            <div class="category-card" style="border-left: 4px solid ${cardColor};">
                <div class="category-name">${categoryName}</div>
                <div class="category-count">${count}</div>
                <div class="category-percentage">${percentage}%</div>
            </div>
        `;

        gridContainer.innerHTML += cardHtml;
    });
}

function updateSourceTypeBar(type, count, total) {
    const percentage = total > 0 ? (count / total * 100) : 0;
    const fillElement = document.getElementById(`${type}-fill`);
    const countElement = document.getElementById(`${type}-count`);

    if (fillElement) {
        fillElement.style.width = `${percentage}%`;
    }
    if (countElement) {
        countElement.textContent = count;
    }
}

function updateTopDomainsChart(domains) {
    const ctx = document.getElementById('top-domains-chart').getContext('2d');

    if (topDomainsChart) {
        topDomainsChart.destroy();
    }

    topDomainsChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: domains.map(d => d.domain),
            datasets: [{
                label: 'Number of Links',
                data: domains.map(d => d.count),
                backgroundColor: 'rgba(107, 70, 193, 0.6)',
                borderColor: 'rgba(107, 70, 193, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            const domain = domains[context.dataIndex];
                            return `${domain.percentage}% of total`;
                        }
                    }
                }
            }
        }
    });
}

function updateDomainDistributionChart(distribution) {
    const ctx = document.getElementById('domain-distribution-chart').getContext('2d');

    if (domainDistributionChart) {
        domainDistributionChart.destroy();
    }

    const labels = [];
    const data = [];

    if (distribution.top_10 > 0) {
        labels.push('Top 10 Domains');
        data.push(distribution.top_10);
    }

    if (distribution.others > 0) {
        labels.push('Other Domains');
        data.push(distribution.others);
    }

    if (data.length === 0) {
        labels.push('No Data');
        data.push(1);
    }

    domainDistributionChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: [
                    'rgba(107, 70, 193, 0.8)',
                    'rgba(144, 97, 249, 0.8)'
                ],
                borderColor: [
                    'rgba(107, 70, 193, 1)',
                    'rgba(144, 97, 249, 1)'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function updateSourceTypeChart(domainCategories) {
    const ctx = document.getElementById('source-type-chart').getContext('2d');

    if (sourceTypeChart) {
        sourceTypeChart.destroy();
    }

    // Generate dynamic labels and data from whatever categories the LLM provides
    const labels = Object.keys(domainCategories);
    const data = Object.values(domainCategories);

    // Generate colors dynamically based on number of categories
    const colors = generateChartColors(labels.length);

    sourceTypeChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors.background,
                borderColor: colors.border,
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Helper function to generate colors for dynamic number of categories
function generateChartColors(count) {
    const baseColors = [
        'rgba(107, 70, 193, 0.8)',   // Purple
        'rgba(245, 158, 11, 0.8)',   // Orange
        'rgba(59, 130, 246, 0.8)',   // Blue
        'rgba(16, 185, 129, 0.8)',   // Green
        'rgba(239, 68, 68, 0.8)',    // Red
        'rgba(139, 69, 19, 0.8)',    // Brown
        'rgba(255, 192, 203, 0.8)',  // Pink
        'rgba(128, 128, 128, 0.8)',  // Gray
        'rgba(255, 165, 0, 0.8)',    // Orange
        'rgba(75, 0, 130, 0.8)'      // Indigo
    ];

    const background = [];
    const border = [];

    for (let i = 0; i < count; i++) {
        const colorIndex = i % baseColors.length;
        background.push(baseColors[colorIndex]);
        border.push(baseColors[colorIndex].replace('0.8', '1'));
    }

    return { background, border };
}

function updateTemporalTrendChart(trendData) {
    const ctx = document.getElementById('temporal-trend-chart').getContext('2d');

    if (temporalTrendChart) {
        temporalTrendChart.destroy();
    }

    if (trendData.length === 0) {
        return;
    }

    temporalTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: trendData.map(d => d.date),
            datasets: [{
                label: 'Links Collected',
                data: trendData.map(d => d.count),
                borderColor: 'rgba(107, 70, 193, 1)',
                backgroundColor: 'rgba(107, 70, 193, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.3,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                },
                x: {
                    ticks: {
                        maxRotation: 45,
                        minRotation: 0,
                        autoSkip: true,
                        maxTicksLimit: 15
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        title: function(context) {
                            return 'Date: ' + context[0].label;
                        },
                        label: function(context) {
                            return 'Links: ' + context.parsed.y;
                        }
                    }
                }
            }
        }
    });
}

function updateEnhancedDomainList(domains, domainMetrics) {
    const container = document.getElementById('domain-list');
    container.innerHTML = '';

    if (domains.length === 0) {
        container.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 1rem;">No domain data available</div>';
        return;
    }

    domains.forEach(domain => {
        const metrics = domainMetrics[domain.domain] || {};
        const usageCount = metrics.usage_count || domain.count || 0;
        const usagePercentage = metrics.usage_percentage || domain.percentage || 0;
        const researchDiversity = metrics.research_diversity || domain.research_count || 0;
        const rank = metrics.frequency_rank || 0;

        const item = document.createElement('div');
        item.className = 'domain-item-expanded';

        let researchLinksHtml = '';
        if (domain.recent_researches && domain.recent_researches.length > 0) {
            researchLinksHtml = `
                <div class="research-links">
                    <div class="research-links-title">Recent Researches (${researchDiversity} total)</div>
                    ${domain.recent_researches.map(r => `
                        <a href="/research/${r.id}" class="research-link" title="${r.query}">
                            ${r.query.length > 30 ? r.query.substring(0, 30) + '...' : r.query}
                        </a>
                    `).join('')}
                </div>
            `;
        }

        // Add classification if available
        let classificationHtml = '';
        if (domain.classification) {
            classificationHtml = `
                <span class="classified-badge" title="${domain.classification.subcategory} (${Math.round(domain.classification.confidence * 100)}% confidence)">
                    ${domain.classification.category}
                </span>
            `;
        }

        item.innerHTML = `
            <div class="domain-header">
                <span class="domain-name" style="font-size: 1rem; font-weight: 600;">
                    ${rank ? `#${rank}` : ''} <a href="https://${domain.domain}" target="_blank" rel="noopener noreferrer" style="color: var(--accent-primary); text-decoration: none; border-bottom: 1px dotted var(--accent-primary); transition: all 0.2s ease;" onmouseover="this.style.borderBottom='2px solid var(--accent-primary)'" onmouseout="this.style.borderBottom='1px dotted var(--accent-primary)'">${domain.domain}</a>
                    ${classificationHtml}
                </span>
                <div class="domain-stats">
                    <span class="metric-badge frequency">
                        📊 ${usageCount} uses (${usagePercentage}%)
                    </span>
                    <span class="metric-badge diversity">
                        🔍 ${researchDiversity} researches
                    </span>
                </div>
            </div>
            ${researchLinksHtml}
        `;
        container.appendChild(item);
    });
}

// Event listener for period selector
document.getElementById('period-select').addEventListener('change', (e) => {
    loadLinkAnalytics(e.target.value);
});

// Load initial data
loadLinkAnalytics('30d');

// Domain Classification functionality
let classificationInProgress = false;
let domainsToClassify = [];
let currentDomainIndex = 0;

// Modal elements
const modal = document.getElementById('classification-modal');
const classifyBtn = document.getElementById('classify-domains-btn');
const closeModal = document.querySelector('.close-modal');
const startBtn = document.getElementById('start-classification');
const stopBtn = document.getElementById('stop-classification');
const closeCompleteBtn = document.getElementById('close-complete');

// Show modal when classify button is clicked
classifyBtn.addEventListener('click', async () => {
    modal.style.display = 'block';
    await loadClassificationProgress();
});

// Close modal
closeModal.addEventListener('click', () => {
    if (!classificationInProgress) {
        modal.style.display = 'none';
    }
});

closeCompleteBtn.addEventListener('click', () => {
    modal.style.display = 'none';
    // Reload the page to show updated classifications
    loadLinkAnalytics(document.getElementById('period-select').value);
});

// Load classification progress
async function loadClassificationProgress() {
    try {
        const response = await fetch('/metrics/api/domain-classifications/progress');
        const result = await response.json();

        if (result.status === 'success') {
            const progress = result.progress;
            document.getElementById('total-domains-count').textContent = progress.total_domains;
            document.getElementById('classified-count').textContent = progress.classified;

            // Update status text
            const statusText = document.getElementById('classification-status');
            if (progress.classified > 0) {
                statusText.textContent = `${progress.classified}/${progress.total_domains} domains classified`;
                statusText.style.color = '#22c55e';
            }
        }
    } catch (error) {
        console.error('Error loading classification progress:', error);
    }
}

// Start classification
startBtn.addEventListener('click', async () => {
    const forceReclassify = document.getElementById('force-reclassify').checked;

    // Hide info, show progress
    document.getElementById('classification-info').style.display = 'none';
    document.getElementById('classification-progress').style.display = 'block';
    document.getElementById('classification-complete').style.display = 'none';

    classificationInProgress = true;

    // Get list of domains to classify
    try {
        const response = await fetch('/metrics/api/domain-classifications/progress');
        const result = await response.json();

        if (result.status === 'success') {
            const totalDomains = result.progress.total_domains;

            // Start classification process
            await classifyAllDomains(forceReclassify);
        }
    } catch (error) {
        console.error('Error starting classification:', error);
        addLogEntry(`Error: ${error.message}`, 'error');
    }
});

// Stop classification
stopBtn.addEventListener('click', () => {
    classificationInProgress = false;
    addLogEntry('Classification stopped by user', 'error');
});

// Classify all domains
async function classifyAllDomains(forceUpdate = false) {
    const logContainer = document.getElementById('classification-log');
    logContainer.innerHTML = '';

    try {
        // Get all domains from the progress endpoint which now includes all domains
        const progressResponse = await fetch('/metrics/api/domain-classifications/progress');
        const progressResult = await progressResponse.json();

        if (progressResult.status === 'success' && progressResult.progress.all_domains) {
            // Get all domains from the progress endpoint
            const allDomains = new Set(progressResult.progress.all_domains);

            // Get existing classifications to check which need updating
            if (!forceUpdate) {
                const classResponse = await fetch('/metrics/api/domain-classifications');
                const classResult = await classResponse.json();

                if (classResult.status === 'success') {
                    const classified = new Set(classResult.classifications.map(c => c.domain));
                    // Remove already classified domains
                    classified.forEach(d => allDomains.delete(d));
                }
            }

            domainsToClassify = Array.from(allDomains);
            const totalToClassify = domainsToClassify.length;

            if (totalToClassify === 0) {
                addLogEntry('All domains are already classified!', 'success');
                showClassificationComplete(0);
                return;
            }

            addLogEntry(`Starting classification of ${totalToClassify} domains...`, 'success');

            let classified = 0;
            let failed = 0;

            for (let i = 0; i < domainsToClassify.length && classificationInProgress; i++) {
                const domain = domainsToClassify[i];
                const progress = ((i + 1) / totalToClassify) * 100;

                // Update progress bar
                document.getElementById('progress-fill').style.width = `${progress}%`;
                document.getElementById('progress-fill').textContent = `${Math.round(progress)}%`;
                document.getElementById('current-domain').textContent = `Classifying: ${domain}`;

                try {
                    // Get CSRF token if it exists
                    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;

                    // Classify single domain
                    const headers = {
                        'Content-Type': 'application/json',
                    };

                    if (csrfToken) {
                        headers['X-CSRFToken'] = csrfToken;
                    }

                    const response = await fetch('/metrics/api/domain-classifications/classify', {
                        method: 'POST',
                        headers: headers,
                        credentials: 'same-origin', // Include cookies for session
                        body: JSON.stringify({
                            domain: domain,
                            force_update: forceUpdate
                        })
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        console.error(`HTTP error! status: ${response.status}, body: ${errorText}`);
                        failed++;
                        addLogEntry(`✗ ${domain}: HTTP ${response.status} error`, 'error');
                        continue;
                    }

                    const result = await response.json();

                    if (result.status === 'success') {
                        classified++;
                        const cat = result.classification.category;
                        const subcat = result.classification.subcategory;
                        addLogEntry(`✓ ${domain}: ${cat} / ${subcat}`, 'success');
                    } else {
                        failed++;
                        addLogEntry(`✗ ${domain}: ${result.message || 'Classification failed'}`, 'error');
                    }

                    // Small delay to avoid overwhelming the server
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Increased delay

                } catch (error) {
                    console.error(`Error classifying ${domain}:`, error);
                    failed++;
                    addLogEntry(`✗ ${domain}: ${error.message}`, 'error');
                }
            }

            if (classificationInProgress) {
                showClassificationComplete(classified);
            }

        }
    } catch (error) {
        console.error('Error during classification:', error);
        addLogEntry(`Fatal error: ${error.message}`, 'error');
    }

    classificationInProgress = false;
}

// Add log entry
function addLogEntry(message, type = 'info') {
    const logContainer = document.getElementById('classification-log');
    const entry = document.createElement('div');
    entry.className = `log-entry ${type}`;
    entry.textContent = message;
    logContainer.appendChild(entry);
    logContainer.scrollTop = logContainer.scrollHeight;
}

// Show classification complete
function showClassificationComplete(count) {
    document.getElementById('classification-progress').style.display = 'none';
    document.getElementById('classification-complete').style.display = 'block';
    document.getElementById('final-classified').textContent = count;
}

// Update domain display to show classifications
async function loadClassificationsAndUpdateDisplay() {
    try {
        const response = await fetch('/metrics/api/domain-classifications');
        const result = await response.json();

        if (result.status === 'success') {
            const classifications = {};
            result.classifications.forEach(c => {
                classifications[c.domain] = c;
            });

            // Update domain list to show classifications
            const domainItems = document.querySelectorAll('.domain-item-expanded');
            domainItems.forEach(item => {
                const domainName = item.querySelector('.domain-name');
                if (domainName) {
                    const domainText = domainName.textContent.replace(/^#\d+\s*/, '').trim();
                    if (classifications[domainText]) {
                        const classification = classifications[domainText];
                        // Add classification badge
                        if (!domainName.querySelector('.classified-badge')) {
                            const badge = document.createElement('span');
                            badge.className = 'classified-badge';
                            badge.textContent = classification.category;
                            badge.title = `${classification.subcategory} (${Math.round(classification.confidence * 100)}% confidence)`;
                            domainName.appendChild(badge);
                        }
                    }
                }
            });
        }
    } catch (error) {
        console.error('Error loading classifications:', error);
    }
}

// Load classifications when data is loaded
const originalUpdateEnhancedDomainList = updateEnhancedDomainList;
updateEnhancedDomainList = function(domains, domainMetrics) {
    originalUpdateEnhancedDomainList(domains, domainMetrics);
    // Load and display classifications
    loadClassificationsAndUpdateDisplay();
};
</script>
{% endblock %}
