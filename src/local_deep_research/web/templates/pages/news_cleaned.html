{% extends "base.html" %}

{% block title %}LDR News - AI-Powered Research Feed{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="/static/css/news.css">
{% endblock %}

{% block content %}
<!-- Alert Container -->
<div id="news-alert" class="settings-alert-container" style="display: none; position: fixed; top: 20px; right: 20px; z-index: 1000;"></div>

<div class="news-page-wrapper">
    <!-- Main Content Container with Right Sidebar -->
    <div class="news-container">
        <!-- News Feed Section -->
        <div class="news-feed">
            <!-- Header with integrated search -->
            <div class="feed-header-section">
                <div class="feed-header">
                    <h1><i class="bi bi-newspaper"></i> Research News Feed</h1>
                    <div class="news-search-box">
                        <input type="text" id="news-search" class="ldr-form-control" placeholder="Search news topics...">
                        <button id="search-btn" class="btn btn-primary">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>

                <!-- Quick actions row -->
                <div class="quick-actions-row">
                    <button class="btn btn-warning" id="refresh-feed-btn">
                        <i class="bi bi-arrow-clockwise"></i> Refresh Feed
                    </button>
                    <button class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#subscriptionsModal">
                        <i class="bi bi-bell"></i> Manage Subscriptions
                    </button>
                    <div class="form-check form-switch d-inline-block ms-3">
                        <input class="form-check-input" type="checkbox" id="auto-refresh" checked>
                        <label class="form-check-label" for="auto-refresh">
                            <i class="bi bi-arrow-clockwise"></i> Auto-refresh
                        </label>
                    </div>
                </div>
            </div>

            <!-- Filters Bar -->
            <div class="filters-bar">
                <div class="filter-section">
                    <div class="subscriptions-horizontal">
                        <select id="subscription-filter" class="form-select">
                            <option value="">All Topics</option>
                            <option value="ai">AI & Technology</option>
                            <option value="science">Science</option>
                            <option value="business">Business</option>
                            <option value="health">Health</option>
                            <option value="climate">Climate</option>
                        </select>
                    </div>

                    <div class="time-filter-group">
                        <button class="filter-btn active" data-time="today">Today</button>
                        <button class="filter-btn" data-time="week">This Week</button>
                        <button class="filter-btn" data-time="month">This Month</button>
                    </div>

                    <div class="impact-filter-group">
                        <label>Impact:</label>
                        <input type="range" class="impact-slider" min="0" max="10" value="0" id="impact-filter">
                        <span class="impact-value">0+</span>
                    </div>
                </div>
            </div>

            <!-- News Cards Container -->
            <div class="news-cards-container" id="news-container">
                <!-- Loading skeleton -->
                <div class="loading-skeleton">
                    <div class="skeleton-card"></div>
                    <div class="skeleton-card"></div>
                    <div class="skeleton-card"></div>
                </div>
            </div>

            <!-- Load More -->
            <div class="load-more-section">
                <button class="btn btn-outline-primary" id="load-more-btn">
                    <i class="bi bi-plus-circle"></i> Load More Stories
                </button>
            </div>
        </div>

        <!-- Right Sidebar - Recommendations -->
        <div class="recommendations">
            <h3><i class="bi bi-stars"></i> Recommended Topics</h3>
            <div class="recommendation-list" id="recommendations-list">
                <div class="recommendation-item">
                    <i class="bi bi-lightning-charge"></i>
                    <span>Quantum Computing Breakthroughs</span>
                    <button class="btn btn-sm btn-outline-primary">Follow</button>
                </div>
                <div class="recommendation-item">
                    <i class="bi bi-cpu"></i>
                    <span>AI Safety Research</span>
                    <button class="btn btn-sm btn-outline-primary">Follow</button>
                </div>
                <div class="recommendation-item">
                    <i class="bi bi-graph-up"></i>
                    <span>Tech Market Analysis</span>
                    <button class="btn btn-sm btn-outline-primary">Follow</button>
                </div>
            </div>

            <h3 class="mt-4"><i class="bi bi-clock-history"></i> Recent Searches</h3>
            <div class="recent-searches" id="recent-searches">
                <!-- Will be populated dynamically -->
            </div>
        </div>
    </div>
</div>

<!-- Subscriptions Modal -->
<div class="modal fade" id="subscriptionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-bell"></i> Manage Subscriptions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="subscription-section">
                    <h6>Your Subscriptions</h6>
                    <div id="current-subscriptions" class="subscriptions-list">
                        <!-- Will be populated dynamically -->
                    </div>

                    <h6 class="mt-4">Add New Subscription</h6>
                    <form id="add-subscription-form">
                        <div class="input-group">
                            <input type="text" class="ldr-form-control" placeholder="Enter topic or search query" id="new-subscription-input">
                            <button class="btn btn-primary" type="submit">
                                <i class="bi bi-plus"></i> Subscribe
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add marked.js for markdown rendering -->
<!-- Add UI services for markdown rendering -->
<script src="/static/js/services/ui.js"></script>

<!-- Add news page JavaScript -->
<script src="/static/js/pages/news.js"></script>

<!-- Add Bootstrap JavaScript -->
{% endblock %}
