{% extends "base.html" %}

{% block title %}Research in Progress - Deep Research System{% endblock %}

{% block content %}
<div class="page active" id="research-progress">
    <div class="page-header" id="research-progres-header">
        <h1>Research in Progress</h1>
    </div>
    <div class="ldr-card" id="research-progress-card">
        <div class="ldr-card-content">
            <div class="progress-info">
                <div class="current-query-container">
                    <div class="current-query-label">Current Query:</div>
                    <div id="current-query" class="current-query"></div>
                </div>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div id="progress-bar" class="progress-fill"></div>
                    </div>
                    <div id="progress-percentage" class="progress-percentage">0%</div>
                </div>
                <div class="status-container">
                    <div class="status-label">Status:</div>
                    <div id="status-text" class="status-indicator">Initializing</div>
                </div>
                <div class="task-container">
                    <div class="task-label">Current Task:</div>
                    <div id="current-task" class="task-text">Starting research...</div>
                </div>
                <div class="progress-actions">
                    <button id="cancel-research-btn" class="btn btn-outline terminate-btn">
                        <i class="fas fa-stop-circle"></i> Cancel Research
                    </button>
                    <a id="view-results-btn" class="btn btn-primary" style="display: none;">
                        <i class="fas fa-eye"></i> View Results
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% include "components/log_panel.html" %}
</div>
{% endblock %}

{% block page_scripts %}
<!-- Load required services -->
<script src="/static/js/services/audio.js"></script>
<script src="/static/js/services/ui.js"></script>
<script src="/static/js/services/formatting.js"></script>
<script src="/static/js/services/api.js"></script>
<script src="/static/js/services/socket.js"></script>

<!-- Then load the component -->
<script src="/static/js/components/progress.js"></script>
{% endblock %}
