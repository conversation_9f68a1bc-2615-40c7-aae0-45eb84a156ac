{% extends "base.html" %}

{% set active_page = 'metrics' %}

{% block title %}Context Overflow Analytics - Deep Research System{% endblock %}

{% block extra_head %}
<style>
    .overflow-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .overflow-card {
        padding: 1.5rem;
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        transition: transform 0.2s;
    }

    .overflow-card:hover {
        transform: translateY(-2px);
    }

    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-color);
        margin: 0.5rem 0;
    }

    .metric-label {
        color: var(--text-secondary);
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .metric-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--primary-color);
        color: white;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }

    .chart-container {
        position: relative;
        height: 400px;
        margin-top: 2rem;
    }

    .truncated-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.375rem;
        transition: background-color 0.2s;
        cursor: pointer;
    }

    .truncated-item:hover {
        background-color: var(--border-color);
    }

    .truncated-item.critical {
        border-left: 4px solid #f44336;
    }

    .truncated-item.warning {
        border-left: 4px solid #ff9800;
    }

    .truncation-badge {
        background: #f44336;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .model-card {
        padding: 1rem;
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }

    .model-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .model-name {
        font-weight: 600;
        color: var(--text-primary);
    }

    .model-provider {
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    .progress-bar {
        height: 20px;
        background: var(--bg-color);
        border-radius: 10px;
        overflow: hidden;
        margin-top: 0.5rem;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #4CAF50 0%, #FF9800 80%, #f44336 95%);
        transition: width 0.3s ease;
    }

    /* Time range selector */
    .time-range-selector {
        display: flex;
        gap: 0.5rem;
        align-items: center;
        margin-left: auto;
    }

    .time-range-btn {
        padding: 0.5rem 1rem;
        border: 1px solid var(--border-color);
        background: var(--card-bg);
        color: var(--text-secondary);
        border-radius: 0.375rem;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .time-range-btn:hover {
        background: var(--bg-color);
        color: var(--text-primary);
    }

    .time-range-btn.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .warning-banner {
        background: rgba(255, 152, 0, 0.1);
        border: 1px solid #ff9800;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .warning-banner i {
        color: #ff9800;
        font-size: 1.5rem;
    }

    .no-data-message {
        text-align: center;
        padding: 3rem;
        color: var(--text-secondary);
    }

    .no-data-message i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    .table-container {
        overflow-x: auto;
        margin-top: 1rem;
    }

    .data-table {
        width: 100%;
        border-collapse: collapse;
    }

    .data-table th {
        background: var(--bg-color);
        padding: 0.75rem;
        text-align: left;
        font-weight: 600;
        color: var(--text-primary);
        border-bottom: 2px solid var(--border-color);
    }

    .data-table td {
        padding: 0.75rem;
        border-bottom: 1px solid var(--border-color);
    }

    .data-table tr:hover {
        background: var(--bg-color);
    }

    .context-limit-badge {
        background: var(--primary-color);
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.75rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="page active" id="context-overflow">
    <div class="page-header" style="display: flex; align-items: center; justify-content: space-between;">
        <div style="display: flex; align-items: center; gap: 1rem;">
            <h1>Context Overflow Analytics</h1>
            <a href="/metrics" style="background: var(--primary-color); color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none; font-size: 0.875rem;">
                ← Back to Metrics
            </a>
        </div>
        <div class="time-range-selector">
            <span style="color: var(--text-secondary); margin-right: 0.5rem; font-size: 0.875rem;">Time Range:</span>
            <button class="time-range-btn" data-period="7d">7D</button>
            <button class="time-range-btn active" data-period="30d">30D</button>
            <button class="time-range-btn" data-period="3m">3M</button>
            <button class="time-range-btn" data-period="1y">1Y</button>
            <button class="time-range-btn" data-period="all">All</button>
        </div>
    </div>

    <div id="loading" class="loading-spinner" style="text-align: center; padding: 3rem;">
        <i class="fas fa-spinner fa-spin fa-2x"></i>
        <p>Loading context overflow data...</p>
    </div>

    <div id="content" style="display: none;">
        <!-- Warning Banner if high truncation rate -->
        <div id="warning-banner" class="warning-banner" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <div>
                <strong>High Context Truncation Detected!</strong>
                <p style="margin: 0.25rem 0 0 0; font-size: 0.875rem;">
                    <span id="warning-rate"></span>% of your requests are hitting context limits.
                    Consider adjusting your context window settings or using shorter prompts.
                </p>
            </div>
        </div>

        <!-- Overview Cards -->
        <div class="ldr-card">
            <div class="card-header">
                <h2><i class="fas fa-chart-line"></i> Context Usage Overview</h2>
            </div>
            <div class="ldr-card-content">
                <div class="overflow-grid">
                    <div class="overflow-card">
                        <div class="metric-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="metric-label">Truncation Rate</div>
                        <div class="metric-value" id="truncation-rate">0%</div>
                        <div style="font-size: 0.875rem; color: var(--text-secondary);">
                            <span id="truncated-count">0</span> of <span id="total-count">0</span> requests
                        </div>
                    </div>

                    <div class="overflow-card">
                        <div class="metric-icon">
                            <i class="fas fa-cut"></i>
                        </div>
                        <div class="metric-label">Avg Tokens Lost</div>
                        <div class="metric-value" id="avg-tokens-lost">0</div>
                        <div style="font-size: 0.875rem; color: var(--text-secondary);">
                            per truncated request
                        </div>
                    </div>

                    <div class="overflow-card">
                        <div class="metric-icon">
                            <i class="fas fa-microchip"></i>
                        </div>
                        <div class="metric-label">Models Tracked</div>
                        <div class="metric-value" id="models-tracked">0</div>
                        <div style="font-size: 0.875rem; color: var(--text-secondary);">
                            with context data
                        </div>
                    </div>

                    <div class="overflow-card">
                        <div class="metric-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <div class="metric-label">Data Coverage</div>
                        <div class="metric-value" id="data-coverage">0%</div>
                        <div style="font-size: 0.875rem; color: var(--text-secondary);">
                            requests with context info
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Chart: Request Size vs Context Limit -->
        <div class="ldr-card" style="margin-top: 2rem;">
            <div class="card-header">
                <h2><i class="fas fa-chart-area"></i> Request Size vs Context Limits</h2>
            </div>
            <div class="ldr-card-content">
                <div class="chart-container">
                    <canvas id="context-chart"></canvas>
                </div>
                <div style="margin-top: 1rem; padding: 1rem; background: var(--bg-color); border-radius: 0.375rem;">
                    <p style="margin: 0; color: var(--text-secondary); font-size: 0.875rem;">
                        <i class="fas fa-info-circle" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                        <strong>Chart Guide:</strong> Points above the red line indicate truncated requests.
                        The horizontal lines show different context limits configured for your models.
                    </p>
                </div>
            </div>
        </div>

        <!-- Model-Specific Stats -->
        <div class="ldr-card" style="margin-top: 2rem;">
            <div class="card-header">
                <h2><i class="fas fa-robot"></i> Model Context Usage</h2>
            </div>
            <div class="ldr-card-content">
                <div id="model-stats">
                    <!-- Populated dynamically -->
                </div>
            </div>
        </div>

        <!-- Recent Truncated Requests -->
        <div class="ldr-card" style="margin-top: 2rem;">
            <div class="card-header">
                <h2><i class="fas fa-exclamation-triangle"></i> Recent Truncated Requests</h2>
            </div>
            <div class="ldr-card-content">
                <div id="truncated-list">
                    <!-- Populated dynamically -->
                </div>
            </div>
        </div>

        <!-- Context Limit Distribution -->
        <div class="ldr-card" style="margin-top: 2rem;">
            <div class="card-header">
                <h2><i class="fas fa-chart-pie"></i> Context Limit Distribution</h2>
            </div>
            <div class="ldr-card-content">
                <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 2rem;">
                    <div>
                        <h3>Configured Limits</h3>
                        <div id="context-limits-list">
                            <!-- Populated dynamically -->
                        </div>
                    </div>
                    <div>
                        <canvas id="distribution-chart" style="max-height: 300px;"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
        <!-- All Requests Detailed Table -->
        <div class="ldr-card" style="margin-top: 2rem;">
            <div class="card-header">
                <h2><i class="fas fa-table"></i> All Requests Details</h2>
            </div>
            <div class="ldr-card-content">
                <div style="overflow-x: auto;">
                    <table id="requests-table" class="data-table" style="width: 100%; font-size: 0.9rem;">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Research ID</th>
                                <th>Model</th>
                                <th>Provider</th>
                                <th>Phase</th>
                                <th>Prompt Tokens</th>
                                <th>Completion Tokens</th>
                                <th>Total Tokens</th>
                                <th>Context Limit</th>
                                <th>Truncated</th>
                                <th>Tokens Lost</th>
                                <th>Query</th>
                            </tr>
                        </thead>
                        <tbody id="requests-tbody">
                            <!-- Populated dynamically -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block component_scripts %}
<script>
(function() {
    let contextChart = null;
    let distributionChart = null;
    let currentPeriod = '30d';

    // Format number with commas
    function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    // Load context overflow data
    async function loadContextData(period = currentPeriod) {
        console.log(`loadContextData called with period: ${period}`);
        try {
            // Show loading
            document.getElementById('loading').style.display = 'block';
            document.getElementById('content').style.display = 'none';

            console.log(`Fetching from: /metrics/api/context-overflow?period=${period}`);
            const response = await fetch(`/metrics/api/context-overflow?period=${period}`);
            console.log('Response status:', response.status);
            if (!response.ok) {
                console.error('Response not OK:', response.status, response.statusText);
                throw new Error('Failed to load data');
            }

            const data = await response.json();
            console.log('Response data:', data);

            if (data.status === 'success') {
                console.log('Data status is success, displaying data...');
                displayContextData(data);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('content').style.display = 'block';
            } else {
                console.error('Data status is not success:', data.status);
                showError();
            }
        } catch (error) {
            console.error('Error loading context data:', error);
            showError();
        }
    }

    // Display context overflow data
    function displayContextData(data) {
        console.log('displayContextData called with:', data);
        const { overview, model_stats, recent_truncated, chart_data, context_limits, all_requests } = data;
        console.log('Extracted data:', { overview, model_stats, recent_truncated, chart_data, context_limits, all_requests });

        // Update overview cards
        document.getElementById('truncation-rate').textContent = `${overview.truncation_rate}%`;
        document.getElementById('truncated-count').textContent = formatNumber(overview.truncated_requests);
        document.getElementById('total-count').textContent = formatNumber(overview.requests_with_context_data);
        document.getElementById('avg-tokens-lost').textContent = formatNumber(Math.round(overview.avg_tokens_truncated));
        document.getElementById('models-tracked').textContent = model_stats.length;

        const coverage = overview.total_requests > 0
            ? Math.round((overview.requests_with_context_data / overview.total_requests) * 100)
            : 0;
        document.getElementById('data-coverage').textContent = `${coverage}%`;

        // Show warning if high truncation rate
        if (overview.truncation_rate > 20) {
            document.getElementById('warning-banner').style.display = 'flex';
            document.getElementById('warning-rate').textContent = overview.truncation_rate.toFixed(1);
        } else {
            document.getElementById('warning-banner').style.display = 'none';
        }

        // Display model stats
        displayModelStats(model_stats);

        // Display recent truncated requests
        displayTruncatedRequests(recent_truncated);

        // Display context limits
        displayContextLimits(context_limits);

        // Create charts
        createContextChart(chart_data);
        createDistributionChart(context_limits);

        // Populate all requests table
        if (all_requests && all_requests.length > 0) {
            populateRequestsTable(all_requests);
        }
    }

    // Populate the detailed requests table
    function populateRequestsTable(requests) {
        const tbody = document.getElementById('requests-tbody');

        if (!requests || requests.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="12" style="text-align: center; padding: 2rem;">
                        No request data available
                    </td>
                </tr>
            `;
            return;
        }

        let tableRows = '';
        requests.forEach(req => {
            const timestamp = new Date(req.timestamp).toLocaleString();
            const truncatedBadge = req.context_truncated
                ? '<span style="color: #ff4444;">Yes</span>'
                : '<span style="color: #4CAF50;">No</span>';
            const contextLimit = req.context_limit || 'N/A';
            const tokensLost = req.tokens_truncated || 0;
            const truncationColor = tokensLost > 0 ? '#ff4444' : '#666';

            tableRows += `
                <tr>
                    <td style="white-space: nowrap;">${timestamp}</td>
                    <td style="font-family: monospace; font-size: 0.8rem;">${req.research_id ? req.research_id.substring(0, 8) : 'N/A'}</td>
                    <td>${req.model || 'N/A'}</td>
                    <td>${req.provider || 'N/A'}</td>
                    <td>${req.research_phase || 'N/A'}</td>
                    <td style="text-align: right;">${formatNumber(req.prompt_tokens)}</td>
                    <td style="text-align: right;">${formatNumber(req.completion_tokens)}</td>
                    <td style="text-align: right; font-weight: bold;">${formatNumber(req.total_tokens)}</td>
                    <td style="text-align: right;">${contextLimit}</td>
                    <td style="text-align: center;">${truncatedBadge}</td>
                    <td style="text-align: right; color: ${truncationColor};">${formatNumber(tokensLost)}</td>
                    <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;" title="${req.research_query || ''}">${req.research_query || 'N/A'}</td>
                </tr>
            `;
        });

        tbody.innerHTML = tableRows;
    }

    // Display model-specific stats
    function displayModelStats(modelStats) {
        const container = document.getElementById('model-stats');

        if (modelStats.length === 0) {
            container.innerHTML = `
                <div class="no-data-message">
                    <i class="fas fa-robot"></i>
                    <p>No model data available</p>
                </div>
            `;
            return;
        }

        container.innerHTML = '';
        modelStats.forEach(stat => {
            const truncationPercent = stat.truncation_rate;
            const card = document.createElement('div');
            card.className = 'model-card';

            card.innerHTML = `
                <div class="model-header">
                    <div>
                        <div class="model-name">${stat.model}</div>
                        <div class="model-provider">${stat.provider}</div>
                    </div>
                    <div style="text-align: right;">
                        <div style="font-size: 1.25rem; font-weight: 600; color: ${truncationPercent > 20 ? '#f44336' : truncationPercent > 10 ? '#ff9800' : '#4CAF50'};">
                            ${truncationPercent}%
                        </div>
                        <div style="font-size: 0.75rem; color: var(--text-secondary);">
                            truncation rate
                        </div>
                    </div>
                </div>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1rem; margin-top: 1rem;">
                    <div>
                        <div style="font-size: 0.75rem; color: var(--text-secondary);">Total Requests</div>
                        <div style="font-weight: 600;">${formatNumber(stat.total_requests)}</div>
                    </div>
                    <div>
                        <div style="font-size: 0.75rem; color: var(--text-secondary);">Truncated</div>
                        <div style="font-weight: 600; color: #f44336;">${formatNumber(stat.truncated_count)}</div>
                    </div>
                    <div>
                        <div style="font-size: 0.75rem; color: var(--text-secondary);">Avg Context</div>
                        <div style="font-weight: 600;">${stat.avg_context_limit ? formatNumber(stat.avg_context_limit) : 'N/A'}</div>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${Math.min(truncationPercent, 100)}%;"></div>
                </div>
            `;

            container.appendChild(card);
        });
    }

    // Display recent truncated requests
    function displayTruncatedRequests(requests) {
        const container = document.getElementById('truncated-list');

        if (requests.length === 0) {
            container.innerHTML = `
                <div class="no-data-message">
                    <i class="fas fa-check-circle" style="color: #4CAF50;"></i>
                    <p>No truncated requests found</p>
                    <p style="font-size: 0.875rem;">All your requests are within context limits!</p>
                </div>
            `;
            return;
        }

        container.innerHTML = '';
        const table = document.createElement('table');
        table.className = 'data-table';

        table.innerHTML = `
            <thead>
                <tr>
                    <th>Time</th>
                    <th>Query</th>
                    <th>Model</th>
                    <th>Prompt Tokens</th>
                    <th>Context Limit</th>
                    <th>Tokens Lost</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                ${requests.map(req => `
                    <tr>
                        <td>${new Date(req.timestamp).toLocaleString()}</td>
                        <td style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            ${req.research_query || 'N/A'}
                        </td>
                        <td>${req.model}</td>
                        <td>${formatNumber(req.actual_tokens || req.prompt_tokens)}</td>
                        <td><span class="context-limit-badge">${formatNumber(req.context_limit)}</span></td>
                        <td style="color: #f44336; font-weight: 600;">${formatNumber(req.tokens_truncated || 0)}</td>
                        <td>
                            <a href="/details/${req.research_id}" style="color: var(--primary-color);">
                                View Details →
                            </a>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        `;

        const tableContainer = document.createElement('div');
        tableContainer.className = 'table-container';
        tableContainer.appendChild(table);
        container.appendChild(tableContainer);
    }

    // Display context limits distribution
    function displayContextLimits(limits) {
        const container = document.getElementById('context-limits-list');

        if (limits.length === 0) {
            container.innerHTML = '<p style="color: var(--text-secondary);">No context limit data</p>';
            return;
        }

        container.innerHTML = '';
        limits.forEach(limit => {
            const item = document.createElement('div');
            item.style.cssText = 'display: flex; justify-content: space-between; padding: 0.5rem 0; border-bottom: 1px solid var(--border-color);';
            item.innerHTML = `
                <span>${formatNumber(limit.limit)} tokens</span>
                <span style="font-weight: 600;">${limit.count} requests</span>
            `;
            container.appendChild(item);
        });
    }

    // Create main context chart
    function createContextChart(chartData) {
        const ctx = document.getElementById('context-chart').getContext('2d');

        if (contextChart) {
            contextChart.destroy();
        }

        if (!chartData || chartData.length === 0) {
            // Show empty state
            contextChart = new Chart(ctx, {
                type: 'scatter',
                data: {
                    datasets: [{
                        label: 'No data',
                        data: []
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'No context data available yet'
                        }
                    }
                }
            });
            return;
        }

        // Get unique context limits for horizontal lines
        const contextLimits = [...new Set(chartData.map(d => d.context_limit).filter(l => l))];

        // Prepare datasets
        const normalData = chartData.filter(d => !d.truncated).map(d => ({
            x: new Date(d.timestamp),
            y: d.actual_prompt_tokens,
            research_id: d.research_id,
            model: d.model
        }));

        const truncatedData = chartData.filter(d => d.truncated).map(d => ({
            x: new Date(d.timestamp),
            y: d.actual_prompt_tokens,
            research_id: d.research_id,
            model: d.model,
            tokens_truncated: d.tokens_truncated
        }));

        const datasets = [
            {
                label: 'Normal Requests',
                data: normalData,
                backgroundColor: 'rgba(76, 175, 80, 0.6)',
                borderColor: 'rgba(76, 175, 80, 1)',
                pointRadius: 4,
                pointHoverRadius: 6
            },
            {
                label: 'Truncated Requests',
                data: truncatedData,
                backgroundColor: 'rgba(244, 67, 54, 0.6)',
                borderColor: 'rgba(244, 67, 54, 1)',
                pointRadius: 6,
                pointHoverRadius: 8
            }
        ];

        // Add horizontal lines for context limits
        contextLimits.forEach((limit, index) => {
            const color = `hsl(${index * 60}, 70%, 50%)`;
            datasets.push({
                label: `Context Limit: ${formatNumber(limit)}`,
                data: [
                    { x: new Date(Math.min(...chartData.map(d => new Date(d.timestamp)))), y: limit },
                    { x: new Date(Math.max(...chartData.map(d => new Date(d.timestamp)))), y: limit }
                ],
                type: 'line',
                borderColor: color,
                borderWidth: 2,
                borderDash: [10, 5],
                fill: false,
                pointRadius: 0,
                pointHoverRadius: 0,
                tension: 0
            });
        });

        contextChart = new Chart(ctx, {
            type: 'scatter',
            data: { datasets },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'time',
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Token Count'
                        },
                        beginAtZero: true
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const point = context.raw;
                                let label = `${context.dataset.label}: ${formatNumber(point.y)} tokens`;
                                if (point.model) {
                                    label += ` (${point.model})`;
                                }
                                if (point.tokens_truncated) {
                                    label += ` - Lost: ${formatNumber(point.tokens_truncated)} tokens`;
                                }
                                return label;
                            }
                        }
                    },
                    legend: {
                        position: 'bottom'
                    }
                },
                onClick: (event, elements) => {
                    if (elements.length > 0) {
                        const dataPoint = elements[0];
                        const dataset = contextChart.data.datasets[dataPoint.datasetIndex];
                        const point = dataset.data[dataPoint.index];
                        if (point.research_id) {
                            window.location.href = `/details/${point.research_id}`;
                        }
                    }
                }
            }
        });
    }

    // Create distribution chart
    function createDistributionChart(limits) {
        const ctx = document.getElementById('distribution-chart').getContext('2d');

        if (distributionChart) {
            distributionChart.destroy();
        }

        if (!limits || limits.length === 0) {
            return;
        }

        distributionChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: limits.map(l => `${formatNumber(l.limit)} tokens`),
                datasets: [{
                    data: limits.map(l => l.count),
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 205, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(153, 102, 255, 0.8)'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed / total) * 100).toFixed(1);
                                return `${context.label}: ${context.parsed} requests (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    // Show error state
    function showError() {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('content').innerHTML = `
            <div class="card">
                <div class="card-content">
                    <div class="no-data-message">
                        <i class="fas fa-exclamation-circle" style="color: #f44336;"></i>
                        <p>Error loading context overflow data</p>
                        <p style="font-size: 0.875rem;">Please try refreshing the page</p>
                    </div>
                </div>
            </div>
        `;
        document.getElementById('content').style.display = 'block';
    }

    // Handle time range changes
    function handleTimeRangeChange(period) {
        currentPeriod = period;

        // Update button states
        document.querySelectorAll('.time-range-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-period="${period}"]`).classList.add('active');

        // Reload data
        loadContextData(period);
    }

    // Initialize when DOM is loaded
    function initialize() {
        console.log('Initializing context overflow page...');

        // Set up time range buttons
        document.querySelectorAll('.time-range-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const period = this.getAttribute('data-period');
                handleTimeRangeChange(period);
            });
        });

        // Load initial data
        console.log('Loading context overflow data...');
        loadContextData();
    }

    // Check if DOM is already loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        // DOM is already loaded, initialize immediately
        initialize();
    }
})();
</script>
{% endblock %}
