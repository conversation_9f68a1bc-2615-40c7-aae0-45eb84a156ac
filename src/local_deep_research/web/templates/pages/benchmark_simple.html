{% extends "base.html" %}

{% set active_page = 'benchmark' %}

{% block title %}Benchmark - Deep Research System{% endblock %}

{% block extra_head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<style>
.benchmark-card {
    max-width: 700px;
    margin: 0 auto;
}

.current-config {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.config-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.config-item:last-child {
    border-bottom: none;
}

.config-label {
    font-weight: 500;
    color: var(--text-muted);
}

.config-value {
    font-weight: 600;
    color: var(--primary-color);
}

.dataset-selection {
    margin: 20px 0;
}

.dataset-option {
    display: flex;
    align-items: center;
    padding: 15px;
    margin: 10px 0;
    background: var(--card-bg);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
}

.dataset-option:hover {
    border-color: var(--primary-color);
}

.dataset-option.selected {
    border-color: var(--primary-color);
    background: var(--primary-light);
}

.dataset-option input[type="number"] {
    width: 80px;
    margin-left: auto;
}

.start-button {
    width: 100%;
    padding: 15px;
    font-size: 1.1rem;
}

.progress-section {
    display: none;
    margin-top: 30px;
}

.progress-bar {
    width: 100%;
    height: 30px;
    background: var(--bg-color);
    border-radius: 15px;
    overflow: hidden;
    margin: 20px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    width: 0%;
    transition: width 0.3s ease;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.stat-card {
    padding: 15px;
    background: var(--bg-color);
    border-radius: 8px;
    text-align: center;
}

.stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-top: 5px;
}
</style>
{% endblock %}

{% block content %}
<div class="page active" id="benchmark">
    <div class="page-header">
        <h1>Benchmark Your Configuration</h1>
        <p class="page-subtitle">Test your current settings against standard datasets</p>
    </div>

    <div id="benchmark-alert" class="settings-alert-container" style="display:none"></div>

    <div class="ldr-card benchmark-card">
        <div class="ldr-card-content">
            <!-- Current Configuration Display -->
            <div class="current-config">
                <h3>Current Configuration</h3>
                <div id="config-display">
                    <div class="config-item">
                        <span class="config-label">Loading...</span>
                        <span class="config-value"></span>
                    </div>
                </div>
            </div>

            <!-- Dataset Selection -->
            <div class="dataset-selection">
                <h3>Select Datasets to Test</h3>

                <div class="dataset-option selected" data-dataset="simpleqa">
                    <div>
                        <h4>SimpleQA</h4>
                        <p>Fact-based questions with clear answers</p>
                    </div>
                    <input type="number" id="simpleqa_count" value="20" min="1" max="500">
                </div>

                <div class="dataset-option selected" data-dataset="browsecomp">
                    <div>
                        <h4>BrowseComp</h4>
                        <p>Complex browsing and comparison tasks</p>
                    </div>
                    <input type="number" id="browsecomp_count" value="10" min="1" max="200">
                </div>
            </div>

            <!-- Start Button -->
            <button type="button" id="start-benchmark" class="btn btn-primary start-button">
                <i class="fas fa-play"></i> Start Benchmark
            </button>

            <!-- Progress Section -->
            <div class="progress-section" id="progress-section">
                <h3>Benchmark Progress</h3>

                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="stat-accuracy">--%</div>
                        <div class="stat-label">Accuracy</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="stat-completed">0</div>
                        <div class="stat-label">Completed</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="stat-rate">--</div>
                        <div class="stat-label">Per Minute</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="stat-remaining">--</div>
                        <div class="stat-label">Time Left</div>
                    </div>
                </div>

                <div style="margin-top: 20px; text-align: center;">
                    <button type="button" id="cancel-benchmark" class="btn btn-secondary">
                        <i class="fas fa-stop"></i> Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let currentBenchmarkId = null;
let progressInterval = null;

document.addEventListener('DOMContentLoaded', function() {
    loadCurrentConfig();
    setupEventListeners();
});

async function loadCurrentConfig() {
    try {
        // Get current settings from the database
        const response = await fetch('/research/settings/api');
        const data = await response.json();

        if (data.settings) {
            const config = {
                provider: data.settings['llm.provider']?.value || 'Not configured',
                model: data.settings['llm.model']?.value || 'Not configured',
                search_tool: data.settings['search.tool']?.value || 'searxng',
                iterations: data.settings['search.iterations']?.value || '8',
                questions_per_iteration: data.settings['search.questions_per_iteration']?.value || '5',
                search_strategy: data.settings['search.search_strategy']?.value || 'focused_iteration'
            };
            displayConfig(config);
        }
    } catch (error) {
        console.error('Error loading config:', error);
        // Use fallback display
        displayConfig({
            provider: 'Loading...',
            model: 'Loading...',
            search_tool: 'Loading...',
            iterations: '-'
        });
    }
}

function displayConfig(config) {
    const configDisplay = document.getElementById('config-display');
    configDisplay.innerHTML = `
        <div class="config-item">
            <span class="config-label">LLM Provider</span>
            <span class="config-value">${config.provider || 'Not set'}</span>
        </div>
        <div class="config-item">
            <span class="config-label">Model</span>
            <span class="config-value">${config.model || 'Not set'}</span>
        </div>
        <div class="config-item">
            <span class="config-label">Search Engine</span>
            <span class="config-value">${config.search_tool || 'searxng'}</span>
        </div>
        <div class="config-item">
            <span class="config-label">Search Iterations</span>
            <span class="config-value">${config.iterations || '8'}</span>
        </div>
    `;
}

function setupEventListeners() {
    // Dataset selection
    document.querySelectorAll('.dataset-option').forEach(option => {
        option.addEventListener('click', function(e) {
            if (!e.target.matches('input')) {
                this.classList.toggle('selected');
            }
        });
    });

    // Start benchmark
    document.getElementById('start-benchmark').addEventListener('click', startBenchmark);

    // Cancel benchmark
    document.getElementById('cancel-benchmark').addEventListener('click', cancelBenchmark);
}

async function startBenchmark() {
    // Get dataset configuration
    const datasets = {};
    document.querySelectorAll('.dataset-option.selected').forEach(option => {
        const dataset = option.getAttribute('data-dataset');
        const count = parseInt(option.querySelector('input').value) || 0;
        if (count > 0) {
            datasets[dataset] = { count: count };
        }
    });

    if (Object.keys(datasets).length === 0) {
        showAlert('Please select at least one dataset', 'error');
        return;
    }

    // Disable start button
    document.getElementById('start-benchmark').disabled = true;

    try {
        // Get CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        const response = await fetch('/benchmark/api/start-simple', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify({ datasets_config: datasets })
        });

        const data = await response.json();

        if (data.success) {
            currentBenchmarkId = data.benchmark_run_id;
            showProgress();
            startProgressTracking();
            showAlert('Benchmark started!', 'success');
        } else {
            showAlert('Error: ' + data.error, 'error');
            document.getElementById('start-benchmark').disabled = false;
        }
    } catch (error) {
        console.error('Error starting benchmark:', error);
        showAlert('Failed to start benchmark', 'error');
        document.getElementById('start-benchmark').disabled = false;
    }
}

function showProgress() {
    document.querySelector('.dataset-selection').style.display = 'none';
    document.getElementById('start-benchmark').style.display = 'none';
    document.getElementById('progress-section').style.display = 'block';
}

function hideProgress() {
    document.querySelector('.dataset-selection').style.display = 'block';
    document.getElementById('start-benchmark').style.display = 'block';
    document.getElementById('progress-section').style.display = 'none';
    document.getElementById('start-benchmark').disabled = false;
}

function startProgressTracking() {
    updateProgress(); // Initial update
    progressInterval = setInterval(updateProgress, 2000);
}

async function updateProgress() {
    if (!currentBenchmarkId) return;

    try {
        const response = await fetch(`/benchmark/api/status/${currentBenchmarkId}`);
        const data = await response.json();

        if (data.success) {
            const status = data.status;

            // Update progress bar
            const percentage = status.total_examples > 0 ?
                (status.completed_examples / status.total_examples * 100) : 0;
            document.getElementById('progress-fill').style.width = percentage + '%';

            // Update stats
            document.getElementById('stat-accuracy').textContent =
                status.overall_accuracy ? status.overall_accuracy.toFixed(1) + '%' : '--%';
            document.getElementById('stat-completed').textContent =
                `${status.completed_examples}/${status.total_examples}`;
            document.getElementById('stat-rate').textContent =
                status.processing_rate ? status.processing_rate.toFixed(1) : '--';

            // Estimate remaining time
            if (status.processing_rate > 0 && status.completed_examples < status.total_examples) {
                const remaining = status.total_examples - status.completed_examples;
                const minutes = Math.ceil(remaining / status.processing_rate);
                document.getElementById('stat-remaining').textContent = `${minutes}m`;
            } else {
                document.getElementById('stat-remaining').textContent = '--';
            }

            // Check if completed
            if (status.status === 'completed' || status.status === 'failed' || status.status === 'cancelled') {
                clearInterval(progressInterval);
                progressInterval = null;

                if (status.status === 'completed') {
                    showAlert(`Benchmark completed! Final accuracy: ${status.overall_accuracy.toFixed(1)}%`, 'success');
                } else {
                    showAlert(`Benchmark ${status.status}`, 'error');
                }

                setTimeout(() => {
                    hideProgress();
                    currentBenchmarkId = null;
                }, 3000);
            }
        }
    } catch (error) {
        console.error('Error updating progress:', error);
    }
}

async function cancelBenchmark() {
    if (!currentBenchmarkId) return;

    try {
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const response = await fetch(`/benchmark/api/cancel/${currentBenchmarkId}`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken
            }
        });

        const data = await response.json();
        if (data.success) {
            showAlert('Benchmark cancelled', 'info');
        }
    } catch (error) {
        console.error('Error cancelling benchmark:', error);
    }
}

function showAlert(message, type) {
    const alertContainer = document.getElementById('benchmark-alert');
    alertContainer.innerHTML = `
        <div class="settings-alert alert-${type}">
            <span>${message}</span>
            <button type="button" class="close-alert" onclick="this.parentElement.parentElement.style.display='none'">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    alertContainer.style.display = 'block';

    if (type === 'success') {
        setTimeout(() => {
            alertContainer.style.display = 'none';
        }, 5000);
    }
}
</script>
{% endblock %}
