{% extends "base.html" %}
{% set active_page = 'subscriptions' %}

{% block title %}Manage News Subscriptions - LDR{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="/static/css/subscriptions.css">
<style>
    /* Override Bootstrap defaults to ensure dark theme */
    body {
        background-color: #121212 !important;
        color: #f5f5f5 !important;
    }
    .main-content {
        background-color: #121212 !important;
    }
    .modal-content {
        background-color: #1e1e2d !important;
        color: #f5f5f5 !important;
    }
    .ldr-form-control, .form-select {
        background-color: #2a2a3a !important;
        border-color: #343452 !important;
        color: #f5f5f5 !important;
    }
    .ldr-form-control:focus, .form-select:focus {
        background-color: #2a2a3a !important;
        border-color: #6e4ff6 !important;
        color: #f5f5f5 !important;
        box-shadow: 0 0 0 0.2rem rgba(110, 79, 246, 0.25) !important;
    }
    .btn-secondary {
        background-color: #2a2a3a !important;
        border-color: #343452 !important;
    }
    .btn-secondary:hover {
        background-color: #343452 !important;
        border-color: #343452 !important;
    }
    .alert {
        background-color: #2a2a3a !important;
        border-color: #343452 !important;
        color: #f5f5f5 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="subscriptions-page">
    <!-- Header Section -->
    <div class="page-header">
        <div class="header-content">
            <h1><i class="bi bi-bell"></i> News Subscriptions</h1>
            <p class="subtitle">Manage your news topics and scheduled searches</p>
        </div>
        <div class="header-actions">
            <a href="/news" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to News Feed
            </a>
            <button id="create-subscription-btn" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> Create Subscription
            </button>
        </div>
    </div>

    <!-- Stats Overview -->
    <div class="stats-overview">
        <div class="stat-card">
            <div class="stat-value" id="total-subscriptions">0</div>
            <div class="stat-label">Total Subscriptions</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="active-subscriptions">0</div>
            <div class="stat-label">Active</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="paused-subscriptions">0</div>
            <div class="stat-label">Paused</div>
        </div>
        <div class="stat-card">
            <div class="stat-value" id="updates-today">0</div>
            <div class="stat-label">Updates Today</div>
        </div>
    </div>

    <!-- Scheduler Status Bar -->
    <div class="scheduler-status-bar">
        <div class="scheduler-info">
            <i class="bi bi-clock-history"></i>
            <span class="scheduler-label">Auto-Refresh Scheduler:</span>
            <span id="scheduler-status" class="scheduler-status">
                <span class="status-indicator" id="status-indicator"></span>
                <span id="status-text">Checking...</span>
            </span>
            <span class="scheduler-details" id="scheduler-details"></span>
        </div>
        <div class="scheduler-actions">
            <button class="btn btn-sm btn-outline-secondary" id="toggle-scheduler" style="display: none;">
                <i class="bi bi-play-fill"></i> Start Scheduler
            </button>
        </div>
    </div>

    <!-- Subscriptions List -->
    <div class="subscriptions-container">
        <!-- Folder/Category Sidebar -->
        <div class="folders-sidebar">
            <h3>Folders</h3>
            <div class="folder-list">
                <div class="folder-item active" data-folder="all">
                    <i class="bi bi-collection"></i> All Subscriptions
                    <span class="folder-count">0</span>
                </div>
                <div class="folder-item" data-folder="uncategorized">
                    <i class="bi bi-folder"></i> Uncategorized
                    <span class="folder-count">0</span>
                </div>
                <!-- Dynamic folders will be added here -->
            </div>
            <button class="btn btn-sm btn-outline-secondary mt-3" id="add-folder-btn">
                <i class="bi bi-plus"></i> New Folder
            </button>
        </div>

        <!-- Main Content Area -->
        <div class="subscriptions-main">
            <!-- Search and Filter Bar -->
            <div class="filter-bar">
                <div class="search-box">
                    <i class="bi bi-search"></i>
                    <input type="text" id="subscription-search" placeholder="Search subscriptions...">
                </div>
                <div class="filter-options">
                    <select id="status-filter" class="form-select form-select-sm">
                        <option value="all">All Status</option>
                        <option value="active">Active</option>
                        <option value="paused">Paused</option>
                    </select>
                    <select id="frequency-filter" class="form-select form-select-sm">
                        <option value="all">All Frequencies</option>
                        <option value="hourly">Hourly</option>
                        <option value="daily">Daily</option>
                        <option value="weekly">Weekly</option>
                    </select>
                </div>
            </div>

            <!-- Subscriptions Grid -->
            <div id="subscriptions-grid" class="subscriptions-grid">
                <!-- Subscription cards will be dynamically loaded here -->
                <div class="loading-placeholder">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading subscriptions...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals removed - using dedicated form pages instead -->

{% endblock %}

{% block page_scripts %}
<script src="/static/js/pages/subscriptions.js"></script>
{% endblock %}
