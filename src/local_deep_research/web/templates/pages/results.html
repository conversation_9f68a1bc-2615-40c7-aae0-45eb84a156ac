{% extends "base.html" %}

{% block title %}Research Results - Deep Research System{% endblock %}

{% block content %}
<div class="page active" id="research-results">
    <div class="page-header">
        <div class="results-header">
            <h1 class="page-title">Research Results</h1>
            <div class="results-actions">
                <button class="btn btn-primary" id="ask-followup-btn" onclick="window.followUpResearch ? followUpResearch.showFollowUpModal() : alert('Follow-up feature loading...')"><i class="fas fa-question-circle"></i> Ask Follow-up Question</button>
                <button class="btn btn-outline" id="view-metrics-btn"><i class="fas fa-chart-bar"></i> View Metrics</button>
                <button class="btn btn-outline" id="download-pdf-btn"><i class="fas fa-file-pdf"></i> Download PDF</button>
                <button class="btn btn-outline" id="export-markdown-btn"><i class="fas fa-file-alt"></i> Export Markdown</button>
                <button class="btn btn-outline" id="export-latex-btn"><i class="fas fa-file-code"></i> Export LaTeX</button>
                <button class="btn btn-outline" id="export-quarto-btn"><i class="fas fa-file-code"></i> Export Quarto</button>
                <button class="btn btn-outline" id="export-ris-btn"><i class="fas fa-file-export"></i> Export RIS (Zotero)</button>
                <button class="btn btn-outline" id="back-to-history"><i class="fas fa-arrow-left"></i> Back to History</button>
            </div>
        </div>
    </div>
    <div class="ldr-card results-card">
        <div class="ldr-card-content">
            <div class="results-metadata" id="research-metadata">
                <div class="metadata-item">
                    <span class="metadata-label">Query:</span>
                    <span id="result-query" class="metadata-value"></span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Generated:</span>
                    <span id="result-date" class="metadata-value"></span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Mode:</span>
                    <span id="result-mode" class="metadata-value"></span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Your Rating:</span>
                    <div class="star-rating" id="research-rating" title="Rate this research session to help improve your future searches. All rating data is stored locally on your device only and is never shared or sent anywhere.">
                        <span class="star" data-rating="1">★</span>
                        <span class="star" data-rating="2">★</span>
                        <span class="star" data-rating="3">★</span>
                        <span class="star" data-rating="4">★</span>
                        <span class="star" data-rating="5">★</span>
                    </div>
                </div>
            </div>
            <div class="results-content" id="results-content">
                <!-- Will be populated dynamically -->
                <div class="loading-spinner centered">
                    <div class="spinner"></div>
                </div>
            </div>
        </div>
    </div>

    {% include "components/log_panel.html" %}
</div>
{% endblock %}

{% block styles %}
{{ super() }}
<!-- Bootstrap CSS for modal -->
{% endblock %}

{% block page_scripts %}
<!-- Bootstrap JS for modal functionality -->
<!-- Fallback utilities in case main services are not available -->
<script src="/static/js/components/fallback/ui.js"></script>
<script src="/static/js/components/fallback/formatting.js"></script>

<!-- Main services -->
<script src="/static/js/services/ui.js"></script>
<script src="/static/js/services/formatting.js"></script>
<script src="/static/js/services/api.js"></script>
<script src="/static/js/services/pdf.js?v={{ range(1000, 9999) | random }}"></script>

<!-- Results component -->
<script src="/static/js/components/results.js"></script>

<!-- Follow-up Research component -->
<script src="/static/js/followup.js"></script>
{% endblock %}
