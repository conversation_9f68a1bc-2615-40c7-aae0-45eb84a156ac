{% extends "base.html" %}

{% set active_page = 'metrics' %}

{% block title %}Cost Analytics - Deep Research System{% endblock %}

{% block extra_head %}
<style>
    .cost-analytics-container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 1rem;
    }

    .cost-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 2rem;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .cost-title {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .cost-title h1 {
        margin: 0;
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .back-button {
        background: var(--bg-secondary);
        color: var(--text-primary);
        padding: 8px 16px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 0.875rem;
        transition: all 0.2s;
        border: 1px solid var(--border-color);
    }

    .back-button:hover {
        background: var(--border-color);
        transform: translateY(-1px);
    }

    .time-range-selector {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .time-range-btn {
        padding: 6px 12px;
        border: 1px solid var(--border-color);
        background: var(--bg-secondary);
        color: var(--text-secondary);
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.875rem;
        transition: all 0.2s;
    }

    .time-range-btn:hover,
    .time-range-btn.active {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    .cost-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .cost-card {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        padding: 1.5rem;
        transition: transform 0.2s;
    }

    .cost-card:hover {
        transform: translateY(-2px);
    }

    .cost-card-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .cost-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        color: white;
        border-radius: 50%;
        font-size: 1.25rem;
    }

    .cost-card-title {
        font-size: 0.875rem;
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin: 0;
    }

    .cost-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--primary-color);
        margin: 0.5rem 0;
    }

    .cost-subtitle {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin: 0;
    }

    .chart-container {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .chart-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .chart-canvas {
        position: relative;
        height: 400px;
        width: 100%;
    }

    .cost-table {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        overflow: hidden;
        margin-bottom: 2rem;
    }

    .cost-table-header {
        background: var(--bg-secondary);
        padding: 1rem 1.5rem;
        border-bottom: 1px solid var(--border-color);
    }

    .cost-table-title {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .cost-table-content {
        padding: 1rem;
    }

    .cost-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid var(--border-color);
    }

    .cost-item:last-child {
        border-bottom: none;
    }

    .cost-item-info {
        flex: 1;
    }

    .cost-item-name {
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
    }

    .cost-item-details {
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    .cost-item-value {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 1.1rem;
    }

    .cost-optimization {
        background: linear-gradient(135deg, #e8f5e8 0%, #f1f8f1 100%);
        border: 1px solid #4CAF50;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .cost-optimization h3 {
        color: #2e7d32;
        margin: 0 0 1rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .cost-optimization ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .cost-optimization li {
        margin-bottom: 0.75rem;
        padding-left: 1.5rem;
        position: relative;
        color: #2e7d32;
    }

    .cost-optimization li::before {
        content: '💡';
        position: absolute;
        left: 0;
    }

    .loading, .error, .no-data {
        text-align: center;
        padding: 3rem;
        color: var(--text-secondary);
    }

    .loading i, .error i, .no-data i {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
    }

    .no-data {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        margin: 2rem 0;
    }

    .research-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .research-link:hover {
        text-decoration: underline;
    }

    /* Tooltip Styles */
    .tooltip {
        position: relative;
        cursor: help;
        display: inline-block;
    }

    .tooltip::after {
        content: attr(data-tooltip);
        position: absolute;
        bottom: 120%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.95);
        color: white;
        padding: 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.75rem;
        line-height: 1.4;
        max-width: 280px;
        min-width: 200px;
        width: max-content;
        white-space: normal;
        word-wrap: break-word;
        text-align: center;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s, transform 0.2s;
        z-index: 1001;
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .tooltip::before {
        content: '';
        position: absolute;
        bottom: 105%;
        left: 50%;
        transform: translateX(-50%);
        border: 6px solid transparent;
        border-top-color: rgba(0, 0, 0, 0.95);
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s;
        z-index: 1001;
    }

    .tooltip:hover::after,
    .tooltip:hover::before {
        opacity: 1;
        transform: translateX(-50%) translateY(-2px);
    }

    .info-icon {
        color: var(--text-secondary);
        margin-left: 0.25rem;
        font-size: 0.875rem;
        cursor: help;
    }

    .info-icon:hover {
        color: var(--primary-color);
    }

    /* Page Information Section */
    .page-info {
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        padding: 2rem;
        margin-top: 3rem;
    }

    .page-info h3 {
        color: var(--text-primary);
        margin: 0 0 1.5rem 0;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.5rem;
    }

    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .info-section h4 {
        color: var(--primary-color);
        margin: 0 0 1rem 0;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .info-section ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .info-section li {
        margin-bottom: 0.75rem;
        padding-left: 1.5rem;
        position: relative;
        color: var(--text-secondary);
        line-height: 1.5;
    }

    .info-section li::before {
        content: '•';
        position: absolute;
        left: 0;
        color: var(--primary-color);
        font-weight: bold;
    }

    .info-section strong {
        color: var(--text-primary);
    }

    .info-note {
        background: var(--bg-secondary);
        border-left: 3px solid var(--primary-color);
        padding: 1.5rem;
        border-radius: 0.375rem;
        margin-top: 2rem;
    }

    .info-note p {
        margin: 0;
        color: var(--text-secondary);
        line-height: 1.6;
    }

    .info-note strong {
        color: var(--text-primary);
    }

    /* Dark theme adjustments */
    [data-theme="dark"] .cost-optimization {
        background: linear-gradient(135deg, #1a3a1a 0%, #2d4a2d 100%);
        border-color: #4CAF50;
    }

    [data-theme="dark"] .cost-optimization h3,
    [data-theme="dark"] .cost-optimization li {
        color: #81c784;
    }

    @media (max-width: 768px) {
        .cost-header {
            flex-direction: column;
            align-items: stretch;
        }

        .time-range-selector {
            justify-content: center;
            flex-wrap: wrap;
        }

        .cost-grid {
            grid-template-columns: 1fr;
        }

        .cost-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="page active" id="cost-analytics">
    <div class="cost-analytics-container">
        <!-- Header -->
        <div class="cost-header">
            <div class="cost-title">
                <h1><i class="fas fa-dollar-sign"></i> Cost Analytics <span style="color: var(--warning-color, #f39c12); font-size: 0.75em; font-weight: normal;">(Experimental)</span></h1>
                <a href="/metrics" class="back-button">
                    <i class="fas fa-arrow-left"></i> Back to Metrics
                </a>
            </div>

            <div class="time-range-selector">
                <span style="color: var(--text-secondary); margin-right: 0.5rem; font-size: 0.875rem;">Time Period:</span>
                <button class="time-range-btn active" data-period="7d">7 Days</button>
                <button class="time-range-btn" data-period="30d">30 Days</button>
                <button class="time-range-btn" data-period="90d">3 Months</button>
                <button class="time-range-btn" data-period="365d">1 Year</button>
                <button class="time-range-btn" data-period="all">All Time</button>
            </div>
        </div>

        <!-- Loading State -->
        <div id="loading" class="loading">
            <i class="fas fa-spinner fa-spin"></i>
            <h3>Loading Cost Analytics...</h3>
            <p>Calculating costs and generating insights</p>
        </div>

        <!-- Error State -->
        <div id="error" class="error" style="display: none;">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>Error Loading Cost Data</h3>
            <p>Unable to load cost analytics. Please try again later.</p>
        </div>

        <!-- Cost Analytics Content -->
        <div id="cost-content" style="display: none;">
            <!-- Overview Cards -->
            <div class="cost-grid">
                <div class="cost-card">
                    <div class="cost-card-header">
                        <div class="cost-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div>
                            <h3 class="cost-card-title tooltip" data-tooltip="Estimated total cost for all research sessions in the selected time period. Based on current model pricing and token usage.">
                                Total Cost
                                <i class="fas fa-info-circle info-icon"></i>
                            </h3>
                        </div>
                    </div>
                    <div class="cost-value" id="total-cost">$0.00</div>
                    <p class="cost-subtitle">Estimated total spending</p>
                </div>

                <div class="cost-card">
                    <div class="cost-card-header">
                        <div class="cost-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div>
                            <h3 class="cost-card-title tooltip" data-tooltip="Average cost per individual research session. This helps you understand the typical expense of running a research query.">
                                Average Per Research
                                <i class="fas fa-info-circle info-icon"></i>
                            </h3>
                        </div>
                    </div>
                    <div class="cost-value" id="avg-research-cost">$0.00</div>
                    <p class="cost-subtitle">Cost per research session</p>
                </div>

                <div class="cost-card">
                    <div class="cost-card-header">
                        <div class="cost-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div>
                            <h3 class="cost-card-title tooltip" data-tooltip="The LLM model that has cost you the most money in total during this time period. Consider using more cost-effective alternatives for routine tasks.">
                                Most Expensive Model
                                <i class="fas fa-info-circle info-icon"></i>
                            </h3>
                        </div>
                    </div>
                    <div class="cost-value" id="most-expensive-model">-</div>
                    <p class="cost-subtitle" id="most-expensive-model-cost">No data</p>
                </div>

                <div class="cost-card">
                    <div class="cost-card-header">
                        <div class="cost-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div>
                            <h3 class="cost-card-title tooltip" data-tooltip="Money saved by using local models instead of commercial alternatives. Local models like Ollama, LM Studio run for free.">
                                Local Models Savings
                                <i class="fas fa-info-circle info-icon"></i>
                            </h3>
                        </div>
                    </div>
                    <div class="cost-value" id="local-savings">$0.00</div>
                    <p class="cost-subtitle" id="local-savings-subtitle">Free local inference</p>
                </div>
            </div>

            <!-- Cost Trends Chart -->
            <div class="chart-container">
                <h3 class="chart-title tooltip" data-tooltip="Shows how your research costs have changed over time. Use this to identify spending patterns and budget more effectively for future research.">
                    <i class="fas fa-chart-area"></i>
                    Cost Trends Over Time
                    <i class="fas fa-info-circle info-icon"></i>
                </h3>
                <div class="chart-canvas">
                    <canvas id="cost-trends-chart"></canvas>
                </div>
            </div>

            <!-- Provider Breakdown Chart -->
            <div class="chart-container">
                <h3 class="chart-title tooltip" data-tooltip="Shows usage breakdown by provider (OpenAI, Anthropic, Ollama, etc). Helps you understand which providers you rely on most and identify opportunities to use more cost-effective local models.">
                    <i class="fas fa-chart-pie"></i>
                    Usage by Provider
                    <i class="fas fa-info-circle info-icon"></i>
                </h3>
                <div class="chart-canvas">
                    <canvas id="provider-chart"></canvas>
                </div>
            </div>

            <!-- Model Cost Comparison Chart -->
            <div class="chart-container">
                <h3 class="chart-title tooltip" data-tooltip="Compares the total cost spent on each LLM model with provider information. Local models (Ollama, LM Studio) show as $0.00 while commercial models show actual costs.">
                    <i class="fas fa-chart-bar"></i>
                    Cost by Model & Provider
                    <i class="fas fa-info-circle info-icon"></i>
                </h3>
                <div class="chart-canvas">
                    <canvas id="model-cost-chart"></canvas>
                </div>
            </div>

            <!-- Most Expensive Research Sessions -->
            <div class="cost-table">
                <div class="cost-table-header">
                    <h3 class="cost-table-title tooltip" data-tooltip="Your most expensive individual research sessions. Click on any session to view its detailed results and understand what drove the higher costs.">
                        <i class="fas fa-fire"></i>
                        Most Expensive Research Sessions
                        <i class="fas fa-info-circle info-icon"></i>
                    </h3>
                </div>
                <div class="cost-table-content" id="expensive-research-list">
                    <!-- Populated by JavaScript -->
                </div>
            </div>

            <!-- Cost Optimization Tips -->
            <div class="cost-optimization">
                <h3><i class="fas fa-lightbulb"></i> Cost Optimization Insights</h3>
                <ul id="optimization-tips">
                    <!-- Populated by JavaScript -->
                </ul>
            </div>
        </div>

        <!-- No Data State -->
        <div id="no-data" class="no-data" style="display: none;">
            <i class="fas fa-chart-pie"></i>
            <h3>No Cost Data Available</h3>
            <p>Start some research sessions to see cost analytics here.</p>
            <a href="/research" style="color: var(--primary-color); text-decoration: none; font-weight: 500;">
                Start Research Session →
            </a>
        </div>

        <!-- Page Information Section -->
        <div class="page-info">
            <h3><i class="fas fa-info-circle"></i> Understanding Cost Analytics</h3>

            <div class="info-grid">
                <!-- What We Track -->
                <div class="info-section">
                    <h4><i class="fas fa-chart-pie"></i> What We Track</h4>
                    <ul>
                        <li><strong>Token Usage:</strong> Input and output tokens for each LLM call</li>
                        <li><strong>Provider Information:</strong> Track which provider (OpenAI, Ollama, etc.) was used</li>
                        <li><strong>Model Pricing:</strong> Current market rates for each LLM provider</li>
                        <li><strong>Research Sessions:</strong> Individual costs per research query</li>
                        <li><strong>Local Model Savings:</strong> Estimated savings from using free local models</li>
                        <li><strong>Provider Breakdown:</strong> Usage distribution across different providers</li>
                        <li><strong>Time-based Analysis:</strong> Cost trends over different periods</li>
                    </ul>
                </div>

                <!-- How Costs Are Calculated -->
                <div class="info-section">
                    <h4><i class="fas fa-calculator"></i> How Costs Are Calculated</h4>
                    <ul>
                        <li><strong>Input Tokens:</strong> Charged at prompt pricing rate (usually lower)</li>
                        <li><strong>Output Tokens:</strong> Charged at completion pricing rate (usually higher)</li>
                        <li><strong>Total Cost:</strong> (Input tokens ÷ 1000) × Input rate + (Output tokens ÷ 1000) × Output rate</li>
                        <li><strong>Provider-Aware Pricing:</strong> Uses both model name and provider for accurate pricing</li>
                        <li><strong>Local Models:</strong> Ollama, LM Studio, vLLM, and self-hosted models are free ($0.00)</li>
                        <li><strong>Savings Calculation:</strong> Estimates money saved by comparing local usage to commercial equivalents</li>
                        <li><strong>Currency Format:</strong> Shows appropriate decimal places based on amount</li>
                    </ul>
                </div>

                <!-- Pricing Sources -->
                <div class="info-section">
                    <h4><i class="fas fa-database"></i> Pricing Sources</h4>
                    <ul>
                        <li><strong>OpenAI:</strong> GPT-4, GPT-4 Turbo, GPT-4o, GPT-3.5 Turbo pricing</li>
                        <li><strong>Anthropic:</strong> Claude-3 Opus, Sonnet, Haiku, Claude-3.5 pricing</li>
                        <li><strong>Google:</strong> Gemini Pro, Gemini 1.5 Pro/Flash pricing</li>
                        <li><strong>Local/Open Source:</strong> Ollama, LM Studio, vLLM, LlamaCpp (all free)</li>
                        <li><strong>Updates:</strong> Pricing data is updated regularly from official sources</li>
                    </ul>
                </div>

                <!-- Cost Optimization Tips -->
                <div class="info-section">
                    <h4><i class="fas fa-lightbulb"></i> Cost Optimization Strategies</h4>
                    <ul>
                        <li><strong>Model Selection:</strong> Use smaller models for simple tasks</li>
                        <li><strong>Research Mode:</strong> Choose "Quick" mode for basic queries</li>
                        <li><strong>Local Models:</strong> Consider Ollama for privacy and zero cost</li>
                        <li><strong>Batch Queries:</strong> Combine related questions in one session</li>
                        <li><strong>Monitor Trends:</strong> Track spending patterns to identify opportunities</li>
                    </ul>
                </div>

                <!-- Understanding the Data -->
                <div class="info-section">
                    <h4><i class="fas fa-chart-line"></i> Understanding the Data</h4>
                    <ul>
                        <li><strong>Estimates Only:</strong> Costs are estimates based on current pricing</li>
                        <li><strong>Real Usage:</strong> Data reflects your actual token consumption</li>
                        <li><strong>Time Filtering:</strong> All metrics update based on selected time period</li>
                        <li><strong>Research Links:</strong> Click session IDs to view detailed results</li>
                        <li><strong>Model Efficiency:</strong> Lower cost per token indicates better efficiency</li>
                    </ul>
                </div>

                <!-- Local Model Pricing Estimates -->
                <div class="info-section">
                    <h4><i class="fas fa-home"></i> Local Model Pricing Estimates</h4>
                    <ul>
                        <li><strong>Zero API Costs:</strong> Local models (Ollama, LM Studio, vLLM) have no API fees</li>
                        <li><strong>Savings Calculation:</strong> Compares your usage against equivalent commercial models</li>
                        <li><strong>Baseline Pricing:</strong> Uses conservative estimates (GPT-3.5/Claude Haiku rates)</li>
                        <li><strong>Rough Estimates:</strong> Actual commercial costs could be higher or lower</li>
                        <li><strong>Quality Differences:</strong> Local vs commercial models have different capabilities</li>
                        <li><strong>Hidden Costs:</strong> Does not include hardware, electricity, or setup time</li>
                    </ul>
                </div>

                <!-- Privacy & Data -->
                <div class="info-section">
                    <h4><i class="fas fa-shield-alt"></i> Privacy & Data</h4>
                    <ul>
                        <li><strong>Local Storage:</strong> All cost data is stored locally on your device</li>
                        <li><strong>No Sharing:</strong> Cost information is never shared or transmitted</li>
                        <li><strong>Real Costs:</strong> These are estimates - check provider bills for actual charges</li>
                        <li><strong>Accuracy:</strong> Pricing data is kept up-to-date but may vary</li>
                        <li><strong>Control:</strong> You have full control over your usage and spending</li>
                    </ul>
                </div>
            </div>

            <!-- Important Note -->
            <div class="info-note">
                <p>
                    <i class="fas fa-exclamation-triangle" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                    <strong>Important:</strong> These cost estimates are based on current public pricing from LLM providers and your actual token usage.
                    Actual costs may vary due to pricing changes, promotional rates, or billing adjustments.
                    Always refer to your official provider bills for exact charges.
                    Local models (Ollama, self-hosted) show $0.00 as they do not incur API costs.
                </p>
            </div>

            <!-- Local Model Pricing Disclaimer -->
            <div class="info-note" style="margin-top: 1rem; border-left-color: #FF9800;">
                <p>
                    <i class="fas fa-home" style="color: #FF9800; margin-right: 0.5rem;"></i>
                    <strong>Local Model Savings Disclaimer:</strong> The "Local Models Savings" calculation is a rough estimate comparing your local model usage to hypothetical commercial API costs.
                    This estimate uses conservative baseline pricing (~$0.0015 per 1K tokens) and should be viewed as an approximation only.
                    <br><br>
                    <strong>Important Caveats:</strong>
                    <br>• <strong>Quality Differences:</strong> Commercial models may provide different output quality, accuracy, or capabilities
                    <br>• <strong>Hidden Costs:</strong> Local models require hardware investment, electricity, maintenance, and setup time
                    <br>• <strong>Performance Variations:</strong> Speed, reliability, and availability differences between local and commercial models
                    <br>• <strong>Scale Considerations:</strong> Commercial APIs may be more cost-effective for very high or very low usage patterns
                    <br>• <strong>Feature Differences:</strong> Commercial services often include additional features, support, and guarantees
                    <br><br>
                    Use these estimates as a general guide only. The true value of local models includes privacy, control, and independence benefits beyond just cost savings.
                </p>
            </div>
        </div>
    </div>
</div>

<script>
// Cost Analytics JavaScript
(function() {
    let currentPeriod = '7d';
    let costData = null;
    let trendsChart = null;
    let modelChart = null;

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
        setupEventListeners();
        loadCostData();
    });

    function setupEventListeners() {
        // Time period buttons
        document.querySelectorAll('.time-range-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Update active state
                document.querySelectorAll('.time-range-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Update period and reload data
                currentPeriod = this.dataset.period;
                loadCostData();
            });
        });
    }

    async function loadCostData() {
        try {
            // Temporarily disable cost analytics until pricing logic is optimized
            showTemporarilyDisabled();
            return;

            showLoading();

            const response = await fetch(`/metrics/api/cost-analytics?period=${currentPeriod}`);
            if (!response.ok) {
                throw new Error(`API failed: ${response.status}`);
            }

            const data = await response.json();

            if (data.status === 'success') {
                costData = data;

                if (data.overview.total_calls > 0) {
                    displayCostData();
                    showContent();
                } else {
                    showNoData();
                }
            } else {
                throw new Error(data.message || 'Unknown error');
            }
        } catch (error) {
            console.error('Error loading cost data:', error);
            showError();
        }
    }

    function displayCostData() {
        const overview = costData.overview;

        // Update overview cards
        document.getElementById('total-cost').textContent = formatCurrency(overview.total_cost);
        document.getElementById('avg-research-cost').textContent = formatCurrency(overview.avg_cost_per_call);

        // Find most expensive model and calculate local savings
        const models = Object.entries(overview.model_breakdown || {});
        if (models.length > 0) {
            // Most expensive by total cost
            const mostExpensive = models.reduce((max, [name, data]) =>
                data.total_cost > max[1].total_cost ? [name, data] : max
            );
            document.getElementById('most-expensive-model').textContent = mostExpensive[0];
            document.getElementById('most-expensive-model-cost').textContent =
                `${formatCurrency(mostExpensive[1].total_cost)} total`;

            // Calculate local model savings
            let localTokens = 0;
            let localCalls = 0;

            for (const [modelName, data] of models) {
                if (data.total_cost === 0) { // Local models have zero cost
                    localTokens += data.prompt_tokens + data.completion_tokens;
                    localCalls += data.calls;
                }
            }

            // Estimate savings using GPT-3.5 pricing as baseline
            const estimatedSavings = (localTokens / 1000) * 0.0015; // ~$0.0015 per 1K tokens average

            if (localTokens > 0) {
                document.getElementById('local-savings').textContent = formatCurrency(estimatedSavings);
                document.getElementById('local-savings-subtitle').textContent =
                    `${localTokens.toLocaleString()} tokens, ${localCalls} calls`;
            } else {
                document.getElementById('local-savings').textContent = '$0.00';
                document.getElementById('local-savings-subtitle').textContent = 'No local model usage';
            }
        }

        // Load additional data for charts
        loadChartsData();
        displayExpensiveResearch();
        displayOptimizationTips();
    }

    async function loadChartsData() {
        // For now, create sample chart data
        // In a real implementation, you'd fetch time-series cost data
        createCostTrendsChart();
        createProviderChart();
        createModelCostChart();
    }

    function createCostTrendsChart() {
        const ctx = document.getElementById('cost-trends-chart');
        if (!ctx) return;

        if (trendsChart) {
            trendsChart.destroy();
        }

        // Sample data - replace with real time-series data
        const labels = ['Week 1', 'Week 2', 'Week 3', 'Week 4'];
        const costs = [0.02, 0.035, 0.028, 0.041];

        trendsChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Weekly Cost',
                    data: costs,
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Cost: ${formatCurrency(context.parsed.y)}`;
                            }
                        }
                    }
                }
            }
        });
    }

    function createProviderChart() {
        const ctx = document.getElementById('provider-chart');
        if (!ctx) return;

        if (window.providerChart) {
            window.providerChart.destroy();
        }

        // Calculate provider usage from model breakdown
        const models = Object.entries(costData.overview.model_breakdown || {});
        const providerStats = {};

        for (const [modelName, data] of models) {
            // Determine provider from model cost (0 = local, >0 = commercial)
            let provider = 'Unknown';
            if (data.total_cost === 0) {
                provider = 'Local (Ollama/LM Studio)';
            } else if (modelName.toLowerCase().includes('gpt')) {
                provider = 'OpenAI';
            } else if (modelName.toLowerCase().includes('claude')) {
                provider = 'Anthropic';
            } else if (modelName.toLowerCase().includes('gemini')) {
                provider = 'Google';
            }

            if (!providerStats[provider]) {
                providerStats[provider] = { calls: 0, tokens: 0, cost: 0 };
            }

            providerStats[provider].calls += data.calls;
            providerStats[provider].tokens += data.prompt_tokens + data.completion_tokens;
            providerStats[provider].cost += data.total_cost;
        }

        const labels = Object.keys(providerStats);
        const tokens = Object.values(providerStats).map(p => p.tokens);
        const colors = {
            'Local (Ollama/LM Studio)': '#4CAF50',
            'OpenAI': '#00A67E',
            'Anthropic': '#FF6B35',
            'Google': '#4285F4',
            'Unknown': '#9E9E9E'
        };

        window.providerChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Token Usage',
                    data: tokens,
                    backgroundColor: labels.map(label => colors[label] || '#9E9E9E'),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const provider = context.label;
                                const stats = providerStats[provider];
                                return [
                                    `${provider}`,
                                    `Tokens: ${stats.tokens.toLocaleString()}`,
                                    `Calls: ${stats.calls}`,
                                    `Cost: ${formatCurrency(stats.cost)}`
                                ];
                            }
                        }
                    }
                }
            }
        });
    }

    function createModelCostChart() {
        const ctx = document.getElementById('model-cost-chart');
        if (!ctx) return;

        if (modelChart) {
            modelChart.destroy();
        }

        const models = Object.entries(costData.overview.model_breakdown || {});
        const labels = models.map(([name, data]) => {
            // Add provider info to label
            let provider = data.total_cost === 0 ? '(Local)' : '(Commercial)';
            return `${name} ${provider}`;
        });
        const costs = models.map(([, data]) => data.total_cost);

        modelChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Total Cost',
                    data: costs,
                    backgroundColor: [
                        '#4CAF50',
                        '#2196F3',
                        '#FF9800',
                        '#9C27B0',
                        '#F44336'
                    ].slice(0, labels.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return formatCurrency(value);
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Cost: ${formatCurrency(context.parsed.y)}`;
                            }
                        }
                    }
                }
            }
        });
    }

    function displayExpensiveResearch() {
        const container = document.getElementById('expensive-research-list');
        const expensiveResearch = costData.top_expensive_research || [];

        if (expensiveResearch.length === 0) {
            container.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 2rem;">No research cost data available</p>';
            return;
        }

        container.innerHTML = '';
        expensiveResearch.slice(0, 10).forEach((item, index) => {
            const div = document.createElement('div');
            div.className = 'cost-item';
            div.innerHTML = `
                <div class="cost-item-info">
                    <div class="cost-item-name">
                        <a href="/results/${item.research_id}" class="research-link">
                            Research Session #${item.research_id}
                        </a>
                    </div>
                    <div class="cost-item-details">Rank #${index + 1} most expensive</div>
                </div>
                <div class="cost-item-value">${formatCurrency(item.total_cost)}</div>
            `;
            container.appendChild(div);
        });
    }

    function displayOptimizationTips() {
        const container = document.getElementById('optimization-tips');
        const tips = generateOptimizationTips();

        container.innerHTML = '';
        tips.forEach(tip => {
            const li = document.createElement('li');
            li.textContent = tip;
            container.appendChild(li);
        });
    }

    function generateOptimizationTips() {
        const tips = [];
        const overview = costData.overview;
        const models = Object.entries(overview.model_breakdown || {});

        // Count local vs commercial usage
        let localTokens = 0;
        let commercialTokens = 0;
        let hasLocalModels = false;

        for (const [modelName, data] of models) {
            if (data.total_cost === 0) {
                localTokens += data.prompt_tokens + data.completion_tokens;
                hasLocalModels = true;
            } else {
                commercialTokens += data.prompt_tokens + data.completion_tokens;
            }
        }

        // Generate informative tips (no judgmental cost warnings)
        const totalCost = overview.total_cost;
        const avgCostPerCall = overview.avg_cost_per_call;

        // Informative tips for local model users
        if (hasLocalModels && totalCost === 0) {
            tips.push('✅ Excellent! You are using 100% local models with zero inference costs');
            tips.push('🔒 Local models provide privacy benefits while keeping costs at $0.00');
            tips.push('🌟 You have complete control over your AI infrastructure');
        }

        // Mixed usage insights
        if (hasLocalModels && commercialTokens > 0) {
            const localPercent = Math.round((localTokens / (localTokens + commercialTokens)) * 100);
            tips.push('📊 You are using ' + localPercent + '% local models and ' + (100-localPercent) + '% commercial models');
            tips.push('💡 Consider evaluating which workloads could be shifted to local models');
        }

        // Pure commercial usage - neutral suggestion
        if (!hasLocalModels && totalCost > 0) {
            tips.push('💼 You are using commercial LLM APIs for all research');
            tips.push('🏠 Local models like Ollama are available as a zero-cost alternative');
        }

        // General optimization tips (no cost judgments)
        if (models.length > 1) {
            tips.push('⚖️ Compare model performance vs cost to optimize your model selection');
        }

        // Informative model breakdown
        if (models.length > 0 && totalCost > 0) {
            const mostUsed = models.reduce((max, [name, data]) =>
                (data.prompt_tokens + data.completion_tokens) > (max[1].prompt_tokens + max[1].completion_tokens) ? [name, data] : max
            );
            tips.push('📈 "' + mostUsed[0] + '" is your most used model (' + mostUsed[1].calls + ' calls)');
        }

        // Default neutral tips
        if (tips.length === 0) {
            tips.push('📊 Track your research costs and model usage over time');
            tips.push('💡 Explore different models to find the best fit for your use case');
        }

        return tips;
    }

    function formatCurrency(amount) {
        if (amount < 0.001) {
            return `$${amount.toFixed(6)}`;
        } else if (amount < 0.1) {
            return `$${amount.toFixed(4)}`;
        } else {
            return `$${amount.toFixed(2)}`;
        }
    }

    function showLoading() {
        document.getElementById('loading').style.display = 'block';
        document.getElementById('cost-content').style.display = 'none';
        document.getElementById('error').style.display = 'none';
        document.getElementById('no-data').style.display = 'none';
    }

    function showContent() {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('cost-content').style.display = 'block';
        document.getElementById('error').style.display = 'none';
        document.getElementById('no-data').style.display = 'none';
    }

    function showError() {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('cost-content').style.display = 'none';
        document.getElementById('error').style.display = 'block';
        document.getElementById('no-data').style.display = 'none';
    }

    function showNoData() {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('cost-content').style.display = 'none';
        document.getElementById('error').style.display = 'none';
        document.getElementById('no-data').style.display = 'block';
    }

    function showTemporarilyDisabled() {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('cost-content').style.display = 'none';
        document.getElementById('error').style.display = 'none';
        document.getElementById('no-data').style.display = 'block';

        // Update the no-data message to indicate temporary disable
        const noDataContainer = document.getElementById('no-data');
        if (noDataContainer) {
            noDataContainer.innerHTML = `
                <div class="no-data-container">
                    <div class="no-data-icon">⚠️</div>
                    <h3>Cost Analytics Temporarily Disabled</h3>
                    <p>Cost calculation is temporarily disabled while we optimize the pricing logic to ensure accuracy.</p>
                    <p>This prevents showing misleading cost information. The feature will be re-enabled once the pricing system is improved.</p>
                </div>
            `;
        }
    }
})();
</script>
{% endblock %}
