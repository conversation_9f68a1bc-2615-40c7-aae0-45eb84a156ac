{% extends "base.html" %}

{% block title %}Research Details - Deep Research System{% endblock %}

{% block extra_head %}
<style>
    .metrics-section {
        margin-top: 2rem;
    }

    .metric-card {
        background: var(--bg-color);
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid var(--border-color);
    }

    .metric-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
        opacity: 1 !important;
    }

    .metric-value {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;
    }

    .metric-card {
        padding: 1rem;
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        text-align: center;
    }

    .metric-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: var(--primary-color);
        margin: 0.5rem 0;
    }

    .metric-label {
        color: var(--text-secondary);
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        opacity: 1 !important;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin-top: 1rem;
    }

    .phase-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .phase-stat-item {
        padding: 0.75rem;
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.375rem;
        text-align: center;
    }

    .phase-name {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
    }

    .phase-tokens {
        color: var(--primary-color);
        font-weight: 500;
    }

    .phase-calls {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .search-engine-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.375rem;
        transition: background-color 0.2s;
    }

    .search-engine-item:hover {
        background-color: var(--border-color);
    }

    .search-engine-info {
        display: flex;
        flex-direction: column;
    }

    .search-engine-name {
        font-weight: 600;
        color: var(--text-primary);
    }

    .search-engine-stats {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
    }

    .search-timeline-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        background: var(--card-bg);
        border: 1px solid var(--border-color);
        border-radius: 0.375rem;
        border-left: 3px solid var(--primary-color);
    }

    .search-timeline-query {
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
    }

    .search-timeline-meta {
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    .search-timeline-results {
        text-align: right;
        font-size: 0.875rem;
    }

    .search-status-success {
        color: #22c55e;
    }

    .search-status-error {
        color: #ef4444;
    }

    .loading-spinner {
        text-align: center;
        padding: 3rem;
    }

    .error-message {
        text-align: center;
        padding: 2rem;
        color: var(--error-color, #dc3545);
    }

    .charts-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    @media (max-width: 768px) {
        .charts-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
    }

    /* Data table styling for better readability */
    .data-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        font-size: 0.9rem;
        background: var(--card-bg);
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    }

    .data-table thead {
        background: var(--bg-color);
    }

    .data-table th {
        padding: 1rem 0.75rem;
        text-align: left;
        font-weight: 600;
        color: var(--text-primary);
        border-bottom: 2px solid var(--border-color);
        white-space: nowrap;
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 0.05em;
    }

    .data-table td {
        padding: 0.875rem 0.75rem;
        border-bottom: 1px solid var(--border-color);
        color: var(--text-secondary);
        vertical-align: middle;
    }

    .data-table tbody tr {
        transition: background-color 0.2s;
    }

    .data-table tbody tr:hover {
        background-color: var(--bg-color);
    }

    .data-table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Specific column styling */
    .data-table td:first-child,
    .data-table th:first-child {
        padding-left: 1rem;
    }

    .data-table td:last-child,
    .data-table th:last-child {
        padding-right: 1rem;
    }

    /* Numeric columns alignment */
    .data-table td[style*="text-align: right"] {
        font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        font-size: 0.85rem;
    }

    /* Status badges in tables */
    .data-table .badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .data-table .badge-success {
        background-color: rgba(34, 197, 94, 0.2);
        color: #22c55e;
    }

    .data-table .badge-danger {
        background-color: rgba(239, 68, 68, 0.2);
        color: #ef4444;
    }
</style>
{% endblock %}

{% block content %}
<div class="page active" id="research-details">
    <div class="page-header">
        <div class="results-header">
            <h1>Research Details</h1>
            <div class="results-actions">
                <button class="btn btn-outline" id="view-results-btn"><i class="fas fa-file-text"></i> View Results</button>
                <button class="btn btn-outline" id="back-to-history"><i class="fas fa-arrow-left"></i> Back to History</button>
            </div>
        </div>
    </div>

    <div id="loading" class="loading-spinner">
        <i class="fas fa-spinner fa-spin fa-2x"></i>
        <p>Loading research details...</p>
    </div>

    <div id="error" class="error-message" style="display: none;">
        <i class="fas fa-exclamation-circle fa-2x"></i>
        <p>Error loading research details</p>
    </div>

    <div id="details-content" style="display: none;">
    <div class="ldr-card">
        <div class="ldr-card-content">
            <div class="research-metadata">
                <div class="metadata-item">
                    <span class="metadata-label">Query:</span>
                    <span id="research-query" class="metadata-value"></span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Mode:</span>
                    <span id="research-mode" class="metadata-value"></span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Date:</span>
                    <span id="research-date" class="metadata-value"></span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Model:</span>
                    <span id="model-used" class="metadata-value"></span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Strategy:</span>
                    <span id="research-strategy" class="metadata-value">-</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Total Cost:</span>
                    <span id="total-cost" class="metadata-value">$0.00</span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Progress:</span>
                    <div class="detail-progress-container">
                        <div class="detail-progress-bar">
                            <div class="detail-progress-fill" id="detail-progress-fill"></div>
                        </div>
                        <span id="detail-progress-percentage">0%</span>
                    </div>
                </div>
            </div>

            <div class="detail-actions" id="detail-actions">
                <!-- Conditionally shown based on research status -->
            </div>
        </div>
    </div>

    <!-- Token Metrics Section -->
    <div class="ldr-card metrics-section" id="token-metrics-section" style="display: none;">
        <div class="card-header">
            <h2><i class="fas fa-chart-line"></i> Token Usage Timeline</h2>
        </div>
        <div class="ldr-card-content">
            <!-- Metrics Summary -->
            <div class="metrics-grid" id="metrics-summary">
                <div class="metric-card">
                    <div class="metric-label">Total Tokens</div>
                    <div class="metric-value" id="total-tokens">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Input Tokens</div>
                    <div class="metric-value" id="prompt-tokens">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Output Tokens</div>
                    <div class="metric-value" id="completion-tokens">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">LLM Calls</div>
                    <div class="metric-value" id="llm-calls">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Avg Response Time</div>
                    <div class="metric-value" id="avg-response-time">0ms</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Success Rate</div>
                    <div class="metric-value" id="success-rate">0%</div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="charts-grid">
                <!-- Token Consumption Chart -->
                <div>
                    <h3><i class="fas fa-clock"></i> Token Consumption Over Time</h3>
                    <div class="chart-container" style="height: 250px;">
                        <canvas id="timeline-chart"></canvas>
                    </div>
                </div>

                <!-- Search Results Chart -->
                <div>
                    <h3><i class="fas fa-chart-line"></i> Search Results Over Time</h3>
                    <div class="chart-container" style="height: 250px;">
                        <canvas id="search-chart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Phase Breakdown -->
            <div style="margin-top: 2rem;">
                <h3><i class="fas fa-layer-group"></i> Breakdown by Research Phase</h3>
                <div class="phase-stats" id="phase-breakdown">
                    <!-- Populated dynamically -->
                </div>
            </div>

            <!-- Call Stack Analysis -->
            <div style="margin-top: 2rem;" id="call-stack-section">
                <h3><i class="fas fa-code"></i> LLM Call Stack Analysis</h3>
                <div style="margin-top: 1rem;">
                    <div id="call-stack-traces" style="max-height: 400px; overflow-y: auto;">
                        <!-- Populated dynamically -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Source Type Distribution Section (separate card for prominence) -->
    <div class="ldr-card metrics-section" id="source-distribution-section" style="display: none;">
        <div class="card-header">
            <h2><i class="fas fa-chart-pie"></i> Source Type Distribution</h2>
        </div>
        <div class="ldr-card-content">
            <div class="chart-container" style="height: 350px; max-width: 600px; margin: 0 auto;">
                <canvas id="source-type-chart"></canvas>
            </div>
        </div>
    </div>

    <!-- Search Analytics Section -->
    <div class="ldr-card metrics-section" id="search-metrics-section" style="display: none;">
        <div class="card-header">
            <h2><i class="fas fa-search"></i> Search Analytics</h2>
        </div>
        <div class="ldr-card-content">
            <!-- Search Summary -->
            <div class="metrics-grid" id="search-summary">
                <div class="metric-card">
                    <div class="metric-label">Total Searches</div>
                    <div class="metric-value" id="total-searches">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Total Results</div>
                    <div class="metric-value" id="total-search-results">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Avg Response Time</div>
                    <div class="metric-value" id="avg-search-response-time">0ms</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Search Success Rate</div>
                    <div class="metric-value" id="search-success-rate">0%</div>
                </div>
            </div>

            <!-- Search Engine Performance -->
            <div style="margin-top: 2rem;">
                <h3><i class="fas fa-tachometer-alt"></i> Search Engine Performance</h3>
                <div id="search-engine-performance" style="margin-top: 1rem;">
                    <!-- Populated dynamically -->
                </div>
            </div>

            <!-- Search Engine Breakdown -->
            <div style="margin-top: 2rem;">
                <h3><i class="fas fa-search"></i> Search Engine Performance</h3>
                <div id="search-engine-breakdown" style="margin-top: 1rem;">
                    <!-- Populated dynamically -->
                </div>
            </div>

            <!-- Search Timeline -->
            <div style="margin-top: 2rem;">
                <h3><i class="fas fa-clock"></i> Search Timeline</h3>
                <div id="search-timeline" style="margin-top: 1rem; max-height: 400px; overflow-y: auto;">
                    <!-- Populated dynamically -->
                </div>
            </div>
        </div>
    </div>

    <!-- Link Analytics Section -->
    <div class="ldr-card metrics-section" id="link-analytics-section" style="display: none;">
        <div class="card-header">
            <h2><i class="fas fa-link"></i> Link & Domain Analytics</h2>
        </div>
        <div class="ldr-card-content">
            <!-- Link Metrics Summary -->
            <div class="metrics-grid" id="link-metrics-summary">
                <div class="metric-card">
                    <div class="metric-label">Total Links</div>
                    <div class="metric-value" id="total-links">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Unique Domains</div>
                    <div class="metric-value" id="unique-domains">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Academic Sources</div>
                    <div class="metric-value" id="academic-sources">0</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">News Sources</div>
                    <div class="metric-value" id="news-sources">0</div>
                </div>
            </div>

            <!-- Domain Distribution -->
            <div style="margin-top: 2rem;">
                <h3><i class="fas fa-globe"></i> Top Domains Used</h3>
                <div id="domain-list" style="max-height: 400px; overflow-y: auto;">
                    <!-- Domain items will be populated here -->
                </div>
            </div>

            <!-- Recent Resources Sample -->
            <div style="margin-top: 2rem;">
                <h3><i class="fas fa-file-text"></i> Sample Resources</h3>
                <div id="resource-sample" style="max-height: 300px; overflow-y: auto;">
                    <!-- Resource samples will be populated here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Context Overflow Section -->
    <div class="ldr-card metrics-section" id="context-overflow-section" style="display: none; margin-top: 2rem;">
        <div class="card-header">
            <h2><i class="fas fa-layer-group"></i> Context & Token Usage Analysis</h2>
        </div>
        <div class="ldr-card-content">
            <!-- Overview Cards -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 2rem;">
                <div class="metric-card">
                    <div class="metric-label">Total Tokens</div>
                    <div class="metric-value" id="co-total-tokens">-</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Context Limit</div>
                    <div class="metric-value" id="co-context-limit">-</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Max Tokens Used</div>
                    <div class="metric-value" id="co-max-tokens">-</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Truncation Status</div>
                    <div class="metric-value" id="co-truncation-status">-</div>
                </div>
            </div>

            <!-- Token Usage Chart -->
            <div style="margin-top: 2rem;">
                <h3><i class="fas fa-chart-line"></i> Token Usage Over Time</h3>
                <div class="chart-container" style="height: 300px; margin-top: 1rem;">
                    <canvas id="co-usage-chart"></canvas>
                </div>
            </div>

            <!-- Phase Breakdown -->
            <div style="margin-top: 2rem;">
                <h3><i class="fas fa-tasks"></i> Token Usage by Phase</h3>
                <div id="co-phase-breakdown" style="margin-top: 1rem;">
                    <!-- Populated dynamically -->
                </div>
            </div>

            <!-- Detailed Requests Table -->
            <div style="margin-top: 2rem;">
                <h3 style="margin-bottom: 1rem;"><i class="fas fa-list"></i> Detailed Request Log</h3>
                <div style="overflow-x: auto; background: var(--card-bg); border-radius: 0.5rem; padding: 0.5rem;">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Phase</th>
                                <th>Function</th>
                                <th style="text-align: right;">Prompt</th>
                                <th style="text-align: right;">Completion</th>
                                <th style="text-align: right;">Total</th>
                                <th style="text-align: right;">Context Limit</th>
                                <th style="text-align: center;">Truncated</th>
                                <th>Response Time</th>
                            </tr>
                        </thead>
                        <tbody id="co-requests-table">
                            <!-- Populated dynamically -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    </div> <!-- End details-content -->
</div>
{% endblock %}

{% block templates %}
{% endblock %}

{% block page_scripts %}
<script src="/static/js/components/details.js"></script>
{% endblock %}
