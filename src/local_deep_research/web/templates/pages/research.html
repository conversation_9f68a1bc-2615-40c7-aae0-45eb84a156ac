{% extends "base.html" %}
{% from "components/custom_dropdown.html" import render_dropdown %}

{% set active_page = 'new-research' %}

{% block title %}New Research - Deep Research System{% endblock %}

{% block extra_head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<link rel="stylesheet" href="/static/css/custom_dropdown.css">
{% endblock %}

{% block content %}
<div class="page active" id="new-research">
    <div class="page-header">
        <h1>Start New Research</h1>
    </div>
    <!-- Add the alert container -->
    <div id="research-alert" class="settings-alert-container" style="display:none"></div>
    <div class="ldr-card research-card">
        <div class="ldr-card-content">
            <form id="research-form">
                <div class="form-group">
                    <label for="query">Research Query</label>
                    <textarea id="query" name="query" rows="3" placeholder="Enter your research topic or question..." autofocus></textarea>
                    <div class="search-hints">
                        <div class="hint-row">
                            <span class="hint-item">
                                <i class="fas fa-play hint-icon"></i>
                                <span class="hint-text"><kbd>Enter</kbd> to search</span>
                            </span>
                            <span class="hint-item">
                                <i class="fas fa-level-down-alt hint-icon"></i>
                                <span class="hint-text"><kbd>Shift</kbd>+<kbd>Enter</kbd> for new line</span>
                            </span>
                            <span class="hint-item">
                                <i class="fas fa-home hint-icon"></i>
                                <span class="hint-text"><kbd>Esc</kbd> returns here from anywhere</span>
                            </span>
                            <span class="hint-item">
                                <i class="fas fa-bars hint-icon"></i>
                                <span class="hint-text"><kbd>Ctrl</kbd>+<kbd>Shift</kbd>+<kbd>1-4</kbd> to navigate menu</span>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <fieldset>
                        <legend>Research Mode</legend>
                        <div class="mode-selection" role="radiogroup" aria-labelledby="mode-legend">
                            <input type="radio" id="mode-quick" name="research_mode" value="quick" checked class="sr-only">
                            <label for="mode-quick" class="mode-option active" data-mode="quick" role="radio" aria-checked="true" tabindex="0">
                                <div class="mode-icon"><i class="fas fa-bolt" aria-hidden="true"></i></div>
                                <div class="mode-info">
                                    <h2>Quick Summary</h2>
                                    <p>Generated in a few minutes</p>
                                </div>
                            </label>

                            <input type="radio" id="mode-detailed" name="research_mode" value="detailed" class="sr-only">
                            <label for="mode-detailed" class="mode-option" data-mode="detailed" role="radio" aria-checked="false" tabindex="-1">
                                <div class="mode-icon"><i class="fas fa-microscope" aria-hidden="true"></i></div>
                                <div class="mode-info">
                                    <h2>Detailed Report</h2>
                                    <p>In-depth analysis (takes longer)</p>
                                </div>
                            </label>
                        </div>
                    </fieldset>
                </div>

                <!-- Advanced Options - positioned before start for configuration -->
                <button type="button" class="advanced-options-toggle" aria-expanded="false" aria-controls="advanced-options-panel">
                    <span class="toggle-text">Advanced Options</span>
                    <i class="fas fa-chevron-down" aria-hidden="true"></i>
                    <span class="sr-only">Click to expand advanced options</span>
                </button>

                <div class="advanced-options-panel" id="advanced-options-panel" aria-labelledby="advanced-options-toggle">
                    <div class="form-row">
                        <!-- Model Provider Selection -->
                        <div class="form-group half">
                            <label for="model_provider">Model Provider</label>
                            <select id="model_provider" name="model_provider" class="ldr-form-control" data-initial-value="{{ settings.llm_provider }}">
                                <!-- Will be populated dynamically -->
                                <option value="">Loading providers...</option>
                            </select>
                            <span class="input-help">Select the LLM provider to use</span>
                        </div>

                        <!-- Custom Endpoint (hidden by default) -->
                        <div class="form-group half" id="endpoint_container" style="display: none;">
                            <label for="custom_endpoint">Custom Endpoint</label>
                            <input type="text" id="custom_endpoint" name="custom_endpoint" class="ldr-form-control" placeholder="https://your-endpoint-url/v1" value="{{ settings.llm_openai_endpoint_url }}">
                            <span class="input-help">Enter the OpenAI-compatible API endpoint URL</span>
                        </div>
                    </div>

                    <div class="form-row">
                        <!-- Model Selection -->
                        <div class="form-group half">
                            {{ render_dropdown(
                                input_id="model",
                                dropdown_id="model-dropdown",
                                placeholder="Enter or select a model",
                                label="Language Model",
                                help_text="Select or enter a custom model name",
                                show_refresh=True,
                                refresh_aria_label="Refresh model list",
                                data_initial_value=settings.llm_model
                            ) }}
                        </div>

                        <!-- Search Engine Selection -->
                        <div class="form-group half">
                            {{ render_dropdown(
                                input_id="search_engine",
                                dropdown_id="search-engine-dropdown",
                                placeholder="Select a search engine",
                                label="Search Engine",
                                help_text="Select the search engine to use for research",
                                show_refresh=True,
                                refresh_aria_label="Refresh search engine list",
                                data_initial_value=settings.search_tool
                            ) }}
                        </div>
                    </div>

                    <div class="form-row">
                        <!-- Search Iterations -->
                        <div class="form-group half">
                            <label for="iterations">Search Iterations</label>
                            <input type="number" id="iterations" name="iterations" class="ldr-form-control" min="1" max="5" value="{{ settings.search_iterations }}">
                            <span class="input-help">Number of research cycles to perform</span>
                        </div>

                        <!-- Questions Per Iteration -->
                        <div class="form-group half">
                            <label for="questions_per_iteration">Questions Per Iteration</label>
                            <input type="number" id="questions_per_iteration" name="questions_per_iteration" class="ldr-form-control" min="1" max="10" value="{{ settings.search_questions_per_iteration }}">
                            <span class="input-help">Follow-up questions in each cycle</span>
                        </div>
                    </div>

                    <div class="form-row">
                        <!-- Search Strategy -->
                        <div class="form-group half">
                            <label for="strategy">Search Strategy</label>
                            <select id="strategy" name="strategy" class="ldr-form-control">
                                <option value="source-based" {% if settings.search_strategy == 'source-based' %}selected{% endif %}>Source-Based (~70% SimpleQA, Comprehensive Research & Text Creation)</option>
                                <option value="focused-iteration" {% if settings.search_strategy == 'focused-iteration' %}selected{% endif %}>Focused Iteration (~95% SimpleQA, Fast & Precise Q&A)</option>
                            </select>
                            <span class="input-help">Source-Based: Proven strategy for detailed research. Focused Iteration: 2x faster for direct answers. Performance varies with LLM and configuration.</span>
                        </div>

                        <!-- Empty div for layout balance -->
                        <div class="form-group half"></div>
                    </div>
                </div>

                <!-- Start Research Button -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" id="start-research-btn"><i class="fas fa-rocket"></i> Start Research</button>
                </div>

                <!-- Audio Notifications -->
                <div class="form-options">
                    <div class="form-option">
                        <label for="notification-toggle" class="checkbox-label">
                            <input type="checkbox" id="notification-toggle" checked>
                            <span class="checkbox-text">Sound notifications when complete</span>
                        </label>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block component_scripts %}
<script src="/static/js/components/custom_dropdown.js"></script>
<script src="/static/js/components/research.js"></script>
<script src="/static/js/research_form.js"></script>
{% endblock %}
