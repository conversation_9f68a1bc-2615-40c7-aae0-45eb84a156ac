{% extends "base.html" %}
{% from "components/custom_dropdown.html" import render_dropdown %}

{% block title %}LDR News - AI-Powered Research Feed{% endblock %}

{% block extra_head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<link rel="stylesheet" href="/static/css/custom_dropdown.css">
<link rel="stylesheet" href="/static/css/news.css">
<link rel="stylesheet" href="/static/css/news-enhanced.css">
<style>
    /* Override Bootstrap defaults to ensure dark theme */
    body {
        background-color: #121212 !important;
        color: #f5f5f5 !important;
    }
    .main-content {
        background-color: #121212 !important;
    }

    /* Define the button styles */
    .filter-btn {
        background-color: var(--accent-tertiary);
        color: white;              /* White text color */
        padding: 5px 10px;        /* Padding inside the button */
        border: none;              /* Remove borders */
        border-radius: 5px;        /* Rounded corners */
        cursor: pointer;           /* Change cursor to a pointer on hover */
    }

    /* Add some styles for when the user hovers over the button */
    .filter-btn:hover {
        background-color: #87ceeb;
    }
</style>
{% endblock %}

{% block content %}
<!-- Alert Container -->
<div id="news-alert" class="settings-alert-container"></div>

<div class="news-page-wrapper">
    <!-- Main Content Container with Right Sidebar -->
    <div class="news-container">
        <!-- News Feed Section -->
        <div class="news-feed">
            <!-- Header with integrated search -->
            <div class="feed-header-section">
                <div class="feed-header">
                    <h1><i class="bi bi-newspaper"></i> Research News Feed</h1>
                </div>

                <!-- Search box below header -->
                <div class="news-search-container">
                    <div class="news-search-box">
                        <i class="bi bi-search search-icon"></i>
                        <input type="text" id="news-search" class="ldr-form-control" placeholder="Search news topics, headlines, or keywords...">
                        <button id="search-btn" class="btn btn-primary">
                            Search
                        </button>
                    </div>
                </div>

                <!-- Quick actions row -->
                <div class="quick-actions-row">
                    <button class="btn btn-warning" id="refresh-feed-btn">
                        <i class="bi bi-arrow-clockwise"></i> Refresh Feed
                    </button>
                    <a href="/news/subscriptions/new" class="btn btn-outline-primary">
                        <i class="bi bi-plus-circle"></i> Create Subscription
                    </a>
                    <a href="/news/subscriptions" class="btn btn-outline-secondary">
                        <i class="bi bi-bell"></i> Manage Subscriptions
                    </a>
                    <div class="form-check form-switch d-inline-block ms-3">
                        <input class="form-check-input" type="checkbox" id="auto-refresh" checked>
                        <label class="form-check-label" for="auto-refresh">
                            <i class="bi bi-arrow-clockwise"></i> Auto-refresh
                        </label>
                    </div>
                </div>
            </div>

            <!-- Priority Status Alert -->
            <div id="priority-status" class="priority-alert" style="display: none;">
                <i class="bi bi-info-circle"></i>
                <span id="priority-message"></span>
            </div>

            <!-- Filters Bar -->
            <div class="filters-bar">
                <div class="filter-section">
                    <div class="subscriptions-horizontal">
                        <select id="subscription-filter" class="form-select">
                            <option value="">All News</option>
                        </select>
                    </div>

                    <div class="time-filter-group">
                        <button class="filter-btn" data-time="today">Today</button>
                        <button class="filter-btn" data-time="week">This Week</button>
                        <button class="filter-btn" data-time="month">This Month</button>
                    </div>

                    <div class="impact-filter-group">
                        <label>Impact:</label>
                        <input type="range" class="impact-slider" min="0" max="10" value="0" id="impact-filter">
                        <span class="impact-value">0+</span>
                    </div>
                </div>
            </div>

            <!-- News Cards Container -->
            <div class="news-cards-container">
                <div id="news-feed-content">
                    <!-- This is where news items will be displayed -->
                </div>
            </div>

            <!-- Load More -->
            <div class="load-more-section">
                <button class="btn btn-outline-primary" id="load-more-btn">
                    <i class="bi bi-plus-circle"></i> Load More Stories
                </button>
            </div>
        </div>

        <!-- Right Sidebar - Recommendations -->
        <div class="recommendations">
            <!-- News Templates -->
            <h3><i class="bi bi-table"></i> News Templates</h3>
            <div class="news-templates">
                <button class="template-btn" onclick="useNewsTemplate('breaking-news')">
                    <i class="bi bi-newspaper"></i>
                    <span>Breaking News Table</span>
                </button>
                <button class="template-btn" onclick="useNewsTemplate('market-analysis')">
                    <i class="bi bi-graph-up-arrow"></i>
                    <span>Market Analysis</span>
                </button>
                <button class="template-btn" onclick="useNewsTemplate('tech-updates')">
                    <i class="bi bi-cpu"></i>
                    <span>Tech Updates</span>
                </button>
                <button class="template-btn" onclick="useNewsTemplate('local-news')">
                    <i class="bi bi-geo-alt"></i>
                    <span>Local News</span>
                </button>
                <button class="template-btn" onclick="useNewsTemplate('topic-news')">
                    <i class="bi bi-bookmark-star"></i>
                    <span>Topic News</span>
                </button>
                <button class="template-btn" onclick="useNewsTemplate('product-prices')">
                    <i class="bi bi-tag"></i>
                    <span>Product Prices</span>
                </button>
                <button class="template-btn" onclick="useNewsTemplate('custom')">
                    <i class="bi bi-pencil-square"></i>
                    <span>Custom Template</span>
                </button>
            </div>

            <!-- Trending Topics -->
            <h3 class="mt-4"><i class="bi bi-trending-up"></i> Trending Topics</h3>
            <div id="trending-topics" class="topic-tags">
                <!-- Will be populated with trending topics -->
            </div>

            <h3 class="mt-4 d-flex justify-content-between align-items-center">
                <span><i class="bi bi-clock-history"></i> Recent Searches</span>
                <button class="btn btn-sm btn-outline-secondary" onclick="clearSearchHistory()" title="Clear search history">
                    <i class="bi bi-trash"></i>
                </button>
            </h3>
            <div class="recent-searches" id="recent-searches">
                <!-- Will be populated dynamically -->
            </div>

            <!-- Query Templates -->
            <div id="query-templates" class="mt-4">
                <!-- Will be populated with query templates -->
            </div>
        </div>
    </div>
</div>

<!-- Subscriptions Modal -->
<div class="modal fade" id="subscriptionsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Manage News Subscriptions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Subscription Stats -->
                <div class="subscription-stats mb-3">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 id="total-subscriptions">0</h3>
                                <p>Total Subscriptions</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 id="active-subscriptions">0</h3>
                                <p>Active</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 id="total-folders">0</h3>
                                <p>Folders</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-card">
                                <h3 id="next-refresh-time">--</h3>
                                <p>Next Update</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Folder Tabs -->
                <ul class="nav nav-tabs mb-3" id="folderTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" data-folder="all" type="button">
                            All Subscriptions
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" data-folder="Unfiled" type="button">
                            📁 Unfiled
                        </button>
                    </li>
                    <!-- Dynamic folder tabs will be added here -->
                    <li class="nav-item ms-auto" role="presentation">
                        <button class="btn btn-sm btn-outline-primary" id="create-folder-btn">
                            <i class="bi bi-plus"></i> New Folder
                        </button>
                    </li>
                </ul>

                <!-- Subscriptions List -->
                <div id="subscriptions-list" class="subscriptions-container">
                    <div class="text-center p-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- News Subscription Modal -->
<div class="modal fade" id="newsSubscriptionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-bell-fill"></i> Create News Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="news-subscription-form" onsubmit="handleNewsSubscriptionSubmit(event)">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="news-subscription-query" class="form-label">Search Query</label>
                        <textarea class="ldr-form-control" id="news-subscription-query" rows="3" required
                                  placeholder="e.g., AI breakthroughs, climate news, tech updates..."></textarea>
                        <small class="text-muted">Enter keywords or topics you want to track</small>
                    </div>

                    <div class="mb-3">
                        <label for="news-subscription-name" class="form-label">Subscription Name</label>
                        <input type="text" class="ldr-form-control" id="news-subscription-name"
                               placeholder="Optional - defaults to query">
                    </div>

                    <div class="mb-3">
                        <label for="news-subscription-frequency" class="form-label">Update Frequency</label>
                        <select class="form-select" id="news-subscription-frequency">
                            <option value="1">Every hour</option>
                            <option value="2">Every 2 hours</option>
                            <option value="4" selected>Every 4 hours</option>
                            <option value="8">Every 8 hours</option>
                            <option value="24">Daily</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="news-subscription-folder" class="form-label">Folder</label>
                        <select class="form-select" id="news-subscription-folder">
                            <option value="">Uncategorized</option>
                        </select>
                    </div>

                    <!-- Model Configuration Section -->
                    <div class="mb-3">
                        {{ render_dropdown(
                            input_id="news-subscription-model",
                            dropdown_id="news-model-dropdown",
                            placeholder="Enter or select a model",
                            label="Language Model",
                            help_text="Select model for this subscription (leave empty for default)",
                            show_refresh=False
                        ) }}
                    </div>

                    <div class="mb-3">
                        <label for="news-subscription-strategy" class="form-label">Search Strategy</label>
                        <select class="form-select" id="news-subscription-strategy">
                            <option value="news_aggregation">News Aggregation</option>
                            <option value="source-based">Source Based</option>
                            <option value="focused-iteration">Focused Iteration</option>
                            <option value="rapid">Rapid Search</option>
                        </select>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="news-subscription-active" checked>
                        <label class="form-check-label" for="news-subscription-active">
                            Active
                        </label>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="news-subscription-run-now">
                        <label class="form-check-label" for="news-subscription-run-now">
                            Run search immediately
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="run-template-btn">Run Once</button>
                    <button type="submit" class="btn btn-primary">Create Subscription</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add marked.js for markdown rendering -->
<!-- Add UI services for markdown rendering -->
<script src="/static/js/services/ui.js"></script>

<!-- Add custom dropdown component -->
<script src="/static/js/components/custom_dropdown.js"></script>

<!-- Add news page JavaScript -->
<script src="/static/js/pages/news.js"></script>

<!-- Add subscription manager -->
<script src="/static/js/components/subscription-manager.js"></script>

<!-- Add Bootstrap JavaScript -->
<!-- Add design enhancements -->
<script src="/static/js/news-enhancements.js"></script>
{% endblock %}
