{% extends "base.html" %}
{% from "components/custom_dropdown.html" import render_dropdown %}

{% block title %}{{ 'Edit' if subscription else 'Create' }} News Subscription - LDR{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="/static/css/custom_dropdown.css">
<link rel="stylesheet" href="/static/css/news.css">
<style>
    body {
        background-color: #121212 !important;
        color: #f5f5f5 !important;
    }
    .subscription-form-container {
        max-width: 800px;
        margin: 2rem auto;
        padding: 0 1rem;
    }
    .form-card {
        background-color: #1e1e1e;
        border: 1px solid #333;
        border-radius: 8px;
        padding: 2rem;
        margin-bottom: 2rem;
    }
    .form-section {
        margin-bottom: 2rem;
    }
    .form-section:last-child {
        margin-bottom: 0;
    }
    .section-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: #f5f5f5;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    .form-label {
        color: #e0e0e0;
        margin-bottom: 0.5rem;
    }
    .ldr-form-control, .form-select {
        background-color: #2a2a2a;
        border: 1px solid #444;
        color: #f5f5f5;
    }
    .ldr-form-control:focus, .form-select:focus {
        background-color: #2a2a2a;
        border-color: #007bff;
        color: #f5f5f5;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    .form-text {
        color: #999;
        font-size: 0.875rem;
    }
    .btn-group-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 2rem;
    }
    .preview-box {
        background-color: #2a2a2a;
        border: 1px solid #444;
        border-radius: 4px;
        padding: 1rem;
        margin-top: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="subscription-form-container">
    <div class="page-header">
        <h1>
            <i class="bi bi-bell-fill"></i>
            {{ 'Edit' if subscription else 'Create' }} News Subscription
        </h1>
    </div>

    <!-- Alert Container -->
    <div id="subscription-alert" class="settings-alert-container"></div>

    <form id="subscription-form" class="form-card">
        <!-- Basic Information Section -->
        <div class="form-section">
            <h2 class="section-title">
                <i class="bi bi-info-circle"></i> Basic Information
            </h2>

            <div class="mb-3">
                <label for="subscription-query" class="form-label">Search Query or Topic</label>
                <textarea class="form-control" id="subscription-query" rows="4" required
                          placeholder="e.g., AI breakthroughs in healthcare, climate change impacts on agriculture, latest cryptocurrency regulations">{% if subscription %}{{ subscription.query_or_topic }}{% endif %}</textarea>
                <div class="form-text">
                    Enter keywords, topics, or detailed search queries. You can use natural language or specific terms.
                    Use YYYY-MM-DD for dynamic date replacement.
                </div>
            </div>

            <div class="mb-3">
                <label for="subscription-name" class="form-label">Subscription Name</label>
                <input type="text" class="form-control" id="subscription-name"
                       placeholder="e.g., AI Healthcare News, Climate Agriculture Updates"
                       {% if subscription %}value="{{ subscription.name or '' }}"{% endif %}>
                <div class="form-text">Optional - give your subscription a memorable name</div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="subscription-interval" class="form-label">Update Frequency (minutes)</label>
                    <input type="number" class="form-control" id="subscription-interval"
                           min="1" step="1" value="{% if subscription %}{{ subscription.refresh_interval_minutes|int if subscription.refresh_interval_minutes else 1440 }}{% else %}1440{% endif %}" required>
                    <div class="form-text">
                        Common values: 1 (every minute), 60 (hourly), 1440 (daily), 10080 (weekly)
                        <br>
                        <small class="text-muted">A random jitter will be added to prevent all subscriptions from running at once</small>
                    </div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="subscription-folder" class="form-label">Folder</label>
                    <select class="form-select" id="subscription-folder">
                        <option value="">Uncategorized</option>
                        <!-- Folders will be loaded dynamically -->
                    </select>
                </div>
            </div>
        </div>

        <!-- Model Configuration Section -->
        <div class="form-section">
            <h2 class="section-title">
                <i class="bi bi-cpu"></i> Model Configuration
            </h2>

            <div class="mb-3">
                <label for="subscription-provider" class="form-label">Model Provider</label>
                <select class="form-select" id="subscription-provider">
                    <option value="">Loading providers...</option>
                </select>
                <div class="form-text">Select the LLM provider to use</div>
            </div>

            <div class="mb-3" id="custom-endpoint-container" style="display: none;">
                <label for="subscription-custom-endpoint" class="form-label">Custom Endpoint</label>
                <input type="url" class="form-control" id="subscription-custom-endpoint"
                       placeholder="https://api.example.com/v1"
                       value="{% if subscription and subscription.custom_endpoint %}{{ subscription.custom_endpoint }}{% else %}{{ default_settings.custom_endpoint }}{% endif %}">
                <div class="form-text">Enter the OpenAI-compatible API endpoint URL</div>
            </div>

            <div class="mb-3">
                {{ render_dropdown(
                    input_id="subscription-model",
                    dropdown_id="subscription-model-dropdown",
                    placeholder="Enter or select a model",
                    label="Language Model",
                    help_text="Select the AI model to use for this subscription (leave empty for default)",
                    show_refresh=True,
                    data_initial_value=(subscription.model if subscription else '')
                ) }}
            </div>

            <div class="mb-3">
                {{ render_dropdown(
                    input_id="subscription-search-engine",
                    dropdown_id="subscription-search-engine-dropdown",
                    placeholder="Select the search engine to use",
                    label="Search Engine",
                    help_text="Select the search engine to use for research",
                    data_initial_value=(subscription.search_engine if subscription else default_settings.search_engine)
                ) }}
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="subscription-iterations" class="form-label">Search Iterations</label>
                    <input type="number" class="form-control" id="subscription-iterations"
                           min="1" max="10" value="{{ subscription.search_iterations if subscription else default_settings.iterations }}">
                    <div class="form-text">Number of research cycles to perform</div>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="subscription-questions" class="form-label">Questions Per Iteration</label>
                    <input type="number" class="form-control" id="subscription-questions"
                           min="1" max="20" value="{{ subscription.questions_per_iteration if subscription else default_settings.questions_per_iteration }}">
                    <div class="form-text">Follow-up questions in each cycle</div>
                </div>
            </div>

            <div class="mb-3">
                <label for="subscription-strategy" class="form-label">Search Strategy</label>
                <select class="form-select" id="subscription-strategy">
                    <option value="news_aggregation" {% if (subscription and subscription.search_strategy == 'news_aggregation') or (not subscription and default_settings.search_strategy == 'news_aggregation') %}selected{% endif %}>News Aggregation (Optimized for news)</option>
                    <option value="source-based" {% if (subscription and subscription.search_strategy == 'source-based') or (not subscription and default_settings.search_strategy == 'source-based') %}selected{% endif %}>Source Based (Comprehensive research)</option>
                    <option value="focused-iteration" {% if (subscription and subscription.search_strategy == 'focused-iteration') or (not subscription and default_settings.search_strategy == 'focused-iteration') %}selected{% endif %}>Focused Iteration (Fast Q&A)</option>
                </select>
                <div class="form-text">Choose the search strategy that best fits your needs</div>
            </div>
        </div>

        <!-- Status Section -->
        <div class="form-section">
            <h2 class="section-title">
                <i class="bi bi-toggle-on"></i> Status & Options
            </h2>

            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="subscription-active"
                       {% if not subscription or subscription.is_active %}checked{% endif %}>
                <label class="form-check-label" for="subscription-active">
                    Active (subscription will run automatically)
                </label>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="btn-group-actions">
            <a href="/news" class="btn btn-secondary">
                <i class="bi bi-arrow-left"></i> Cancel
            </a>
            <button type="button" class="btn btn-info" id="test-subscription-btn">
                <i class="bi bi-play-circle"></i> Test Run
            </button>
            {% if not subscription %}
            <button type="button" class="btn btn-success" id="create-and-run-btn">
                <i class="bi bi-rocket-takeoff"></i> Create & Run Now
            </button>
            {% endif %}
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-save"></i> {{ 'Update' if subscription else 'Create' }} Subscription
            </button>
        </div>
    </form>
</div>

<script src="/static/js/components/custom_dropdown.js"></script>
<script>
// Global variables
let currentProvider = '{{ subscription.model_provider if subscription else default_settings.model_provider }}';
let defaultModel = '{{ subscription.model if subscription else default_settings.model }}';
let defaultSearchEngine = '{{ subscription.search_engine if subscription else default_settings.search_engine }}';
let defaultCustomEndpoint = '{{ subscription.custom_endpoint if subscription else default_settings.custom_endpoint }}';
let modelOptions = [];
let modelDropdownInstance = null;
let searchEngineOptions = [];
let searchEngineDropdownInstance = null;

// Debug: Log initial values from server
console.log('Initial values from server:', {
    currentProvider: currentProvider,
    defaultModel: defaultModel,
    defaultSearchEngine: defaultSearchEngine,
    defaultCustomEndpoint: defaultCustomEndpoint
});

// Get model options function for dropdown
function getModelOptions() {
    return modelOptions;
}

// Get search engine options function for dropdown
function getSearchEngineOptions() {
    return searchEngineOptions;
}

// Initialize dropdowns
function initializeDropdowns() {
    console.log('Initializing dropdowns...');
    console.log('setupCustomDropdown available?', typeof setupCustomDropdown);
    console.log('Default values - Model:', defaultModel, 'Search Engine:', defaultSearchEngine, 'Provider:', currentProvider);
    console.log('Available search engines:', searchEngineOptions);

    if (typeof setupCustomDropdown !== 'undefined') {
        // Model dropdown
        const modelInput = document.getElementById('subscription-model');
        const modelDropdownList = document.getElementById('subscription-model-dropdown-list');

        if (modelInput && modelDropdownList) {
            console.log('Initializing model dropdown');
            modelDropdownInstance = setupCustomDropdown(
                modelInput,
                modelDropdownList,
                getModelOptions,
                function(value, item) {
                    console.log('Model selected:', value);
                },
                true,  // allowCustomValues
                'No models available'
            );
            console.log('Model dropdown instance created:', modelDropdownInstance);

            // Set initial value if available
            if (defaultModel && modelDropdownInstance.setValue) {
                console.log('Setting initial model value:', defaultModel);
                modelDropdownInstance.setValue(defaultModel, false);
            }
        } else {
            console.error('Model dropdown elements not found');
        }

        // Search engine dropdown
        const searchEngineInput = document.getElementById('subscription-search-engine');
        const searchEngineDropdownList = document.getElementById('subscription-search-engine-dropdown-list');

        if (searchEngineInput && searchEngineDropdownList) {
            console.log('Initializing search engine dropdown');
            searchEngineDropdownInstance = setupCustomDropdown(
                searchEngineInput,
                searchEngineDropdownList,
                getSearchEngineOptions,
                function(value, item) {
                    console.log('Search engine selected:', value);
                },
                false,  // don't allow custom values
                'No search engines available'
            );
            console.log('Search engine dropdown instance created:', searchEngineDropdownInstance);

            // Set initial value if available
            if (defaultSearchEngine && searchEngineDropdownInstance.setValue) {
                console.log('Setting initial search engine value:', defaultSearchEngine);
                // Try exact match first
                searchEngineDropdownInstance.setValue(defaultSearchEngine, false);

                // Check if the value was set by looking at the input
                const input = document.getElementById('subscription-search-engine');
                if (!input.value && searchEngineOptions.length > 0) {
                    // Try case-insensitive match
                    const matchingOption = searchEngineOptions.find(opt =>
                        opt.value.toLowerCase() === defaultSearchEngine.toLowerCase()
                    );
                    if (matchingOption) {
                        console.log('Found case-insensitive match:', matchingOption);
                        searchEngineDropdownInstance.setValue(matchingOption.value, false);
                    }
                }
            }
        } else {
            console.error('Search engine dropdown elements not found');
        }
    } else {
        console.error('setupCustomDropdown is not available');
        // Try again in a bit
        setTimeout(initializeDropdowns, 500);
    }
}

// Load search engines
async function loadSearchEngines() {
    try {
        const response = await fetch('/settings/api/available-search-engines', {
            credentials: 'same-origin'
        });
        const data = await response.json();

        if (data.engine_options) {
            searchEngineOptions = data.engine_options;

            // Update dropdown if it exists
            if (window.updateDropdownOptions) {
                const searchEngineInput = document.getElementById('subscription-search-engine');
                window.updateDropdownOptions(searchEngineInput, searchEngineOptions);
            }
        }
    } catch (error) {
        console.error('Error loading search engines:', error);
        searchEngineOptions = [{ value: 'auto', label: 'Auto' }];
    }
}

// Load models for a specific provider
async function loadModelsForProvider(provider) {
    console.log('Loading models for provider:', provider);

    try {
        // Fetch available models
        const response = await fetch('/settings/api/available-models', {
            credentials: 'same-origin'
        });
        const data = await response.json();

        if (data.providers) {
            // Get models for the selected provider
            const providerKey = `${provider.toLowerCase()}_models`;
            const models = data.providers[providerKey] || [];

            console.log('Available providers:', Object.keys(data.providers));
            console.log(`Looking for key: ${providerKey}`);
            console.log(`Found ${models.length} models for ${provider}`);

            // Update model options
            modelOptions = models.length > 0 ? models : [{ value: '', label: 'No models available' }];
            console.log('Model options for provider', provider, ':', modelOptions);

            // Update dropdown if it exists
            if (window.updateDropdownOptions) {
                const modelInput = document.getElementById('subscription-model');
                window.updateDropdownOptions(modelInput, modelOptions);

                // After updating options, restore the default model value if it matches the provider
                if (modelDropdownInstance && defaultModel) {
                    // Check if the default model exists in the new options
                    const modelExists = modelOptions.some(m => m.value === defaultModel);
                    if (modelExists) {
                        console.log('Restoring model value after provider change:', defaultModel);
                        modelDropdownInstance.setValue(defaultModel, false);
                    }
                }
            }
        }
    } catch (error) {
        console.error('Error loading models:', error);
        modelOptions = [{ value: '', label: 'Error loading models' }];

        // Update dropdown with error state
        if (window.updateDropdownOptions) {
            const modelInput = document.getElementById('subscription-model');
            window.updateDropdownOptions(modelInput, modelOptions);
        }
    }
}

// Load providers
async function loadProviders() {
    const providerSelect = document.getElementById('subscription-provider');

    try {
        const response = await fetch('/settings/api/available-models', {
            credentials: 'same-origin'
        });
        const data = await response.json();

        if (data.provider_options) {
            providerSelect.innerHTML = '<option value="">Select a provider</option>';

            data.provider_options.forEach(provider => {
                const option = document.createElement('option');
                option.value = provider.value;
                option.textContent = provider.label;
                // Handle case-insensitive comparison for provider names
                option.selected = provider.value.toUpperCase() === currentProvider.toUpperCase() ||
                                 (!currentProvider && provider.default);
                providerSelect.appendChild(option);
            });

            // Add change handler
            providerSelect.addEventListener('change', async (e) => {
                currentProvider = e.target.value;

                // Show/hide custom endpoint field
                const customEndpointContainer = document.getElementById('custom-endpoint-container');
                if (currentProvider.toUpperCase() === 'OPENAI_ENDPOINT' ||
                    currentProvider === 'custom' ||
                    currentProvider === 'openai_compatible') {
                    customEndpointContainer.style.display = 'block';
                } else {
                    customEndpointContainer.style.display = 'none';
                }

                // Reload models for new provider
                await loadModelsForProvider(currentProvider);
            });

            // Load models for initial provider
            if (currentProvider || data.provider_options.some(p => p.default)) {
                const initialProvider = currentProvider || data.provider_options.find(p => p.default)?.value;
                if (initialProvider) {
                    // Check if we need to show custom endpoint field on load
                    const customEndpointContainer = document.getElementById('custom-endpoint-container');
                    if (initialProvider.toUpperCase() === 'OPENAI_ENDPOINT' ||
                        initialProvider === 'custom' ||
                        initialProvider === 'openai_compatible') {
                        customEndpointContainer.style.display = 'block';
                    }

                    await loadModelsForProvider(initialProvider);
                }
            }

            // Set default selected value for provider
            if (currentProvider) {
                providerSelect.value = currentProvider;
            }
        }
    } catch (error) {
        console.error('Error loading providers:', error);
        providerSelect.innerHTML = '<option value="">Error loading providers</option>';
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', async function() {
    console.log('DOMContentLoaded fired for subscription form');
    console.log('setupCustomDropdown available?', typeof setupCustomDropdown);
    console.log('window.setupCustomDropdown available?', typeof window.setupCustomDropdown);

    // Log subscription data if editing
    {% if subscription %}
    console.log('Editing subscription - Search Engine:', '{{ subscription.search_engine }}');
    console.log('Full subscription data:', {{ subscription | tojson }});
    {% endif %}

    try {
        // Initialize interval for existing subscription
        {% if subscription and subscription.refresh_interval_minutes %}
        // Already handled in the input value attribute
        {% endif %}

        // Load jitter info
        loadJitterInfo();
        // Initialize dropdowns after a short delay to ensure DOM is ready
        setTimeout(() => {
            initializeDropdowns();
        }, 100);

    // Load providers, models, and search engines
    await loadProviders();
    await loadSearchEngines();

    // Re-initialize dropdowns with loaded options
    setTimeout(() => {
        console.log('Re-initializing dropdowns with loaded data');
        initializeDropdowns();

        // After reinitializing, set the default values again
        // This ensures the user's saved settings are properly displayed
        if (modelDropdownInstance && defaultModel) {
            console.log('Restoring model value after reinitialization:', defaultModel);
            modelDropdownInstance.setValue(defaultModel, false);
        }

        if (searchEngineDropdownInstance && defaultSearchEngine) {
            console.log('Restoring search engine value after reinitialization:', defaultSearchEngine);
            searchEngineDropdownInstance.setValue(defaultSearchEngine, false);
        }
    }, 500);

    // Load folders
    loadSubscriptionFolders();

    // Handle form submission
    document.getElementById('subscription-form').addEventListener('submit', handleSubscriptionSubmit);

    // Handle test run
    const testBtn = document.getElementById('test-subscription-btn');
    if (testBtn) {
        console.log('Adding click handler to test button');
        testBtn.addEventListener('click', handleTestRun);
    } else {
        console.error('Test button not found!');
    }

    // Handle create and run button
    const createAndRunBtn = document.getElementById('create-and-run-btn');
    if (createAndRunBtn) {
        console.log('Adding click handler to create and run button');
        createAndRunBtn.addEventListener('click', handleCreateAndRun);
    }

    // Handle model refresh button
    const modelRefreshBtn = document.querySelector('.custom-dropdown-container .refresh-btn');
    if (modelRefreshBtn) {
        modelRefreshBtn.addEventListener('click', async function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Add loading class
            modelRefreshBtn.classList.add('loading');

            try {
                // Reload models for current provider
                if (currentProvider) {
                    await loadModelsForProvider(currentProvider);
                }

                // Remove loading class
                modelRefreshBtn.classList.remove('loading');

                // Click the input to show refreshed options
                const modelInput = document.getElementById('subscription-model');
                if (modelInput) {
                    modelInput.focus();
                    modelInput.click();
                }
            } catch (error) {
                console.error('Error refreshing models:', error);
                modelRefreshBtn.classList.remove('loading');
            }
        });
    }

    // Handle URL parameters for prefilling
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('query')) {
        document.getElementById('subscription-query').value = urlParams.get('query');
    }
    if (urlParams.has('name')) {
        document.getElementById('subscription-name').value = urlParams.get('name');
    }
    // Store source_id if provided (from history page)
    if (urlParams.has('source_id')) {
        window.sourceResearchId = urlParams.get('source_id');
    }

    console.log('Subscription form initialization complete');
    } catch (error) {
        console.error('Error initializing subscription form:', error);
        showAlert('Error initializing form: ' + error.message, 'error');
    }
});

// Load subscription folders
async function loadSubscriptionFolders() {
    try {
        const response = await fetch('/news/api/folders', {
            credentials: 'same-origin'
        });
        if (response.ok) {
            const data = await response.json();
            const select = document.getElementById('subscription-folder');

            // Clear existing options except first
            while (select.options.length > 1) {
                select.remove(1);
            }

            // Add folders
            data.folders.forEach(folder => {
                const option = document.createElement('option');
                option.value = folder.id;
                option.textContent = folder.name;
                select.appendChild(option);
            });

            // Set selected folder if editing
            {% if subscription and subscription.folder_id %}
            select.value = "{{ subscription.folder_id }}";
            {% endif %}
        }
    } catch (error) {
        console.error('Error loading folders:', error);
    }
}

// Handle form submission
async function handleSubscriptionSubmit(e) {
    e.preventDefault();

    const query = document.getElementById('subscription-query').value;
    const name = document.getElementById('subscription-name').value || query.substring(0, 50);
    const intervalMinutes = parseInt(document.getElementById('subscription-interval').value);
    const folder = document.getElementById('subscription-folder').value;
    const isActive = document.getElementById('subscription-active').checked;

    // Model configuration
    const modelHidden = document.getElementById('subscription-model_hidden');
    const model = modelHidden ? modelHidden.value : '';
    const provider = document.getElementById('subscription-provider').value;
    const customEndpoint = document.getElementById('subscription-custom-endpoint').value;
    if (model && model.includes(':')) {
        const parts = model.split(':');
        modelProvider = parts[0];
    }

    // Search configuration
    const searchEngineHidden = document.getElementById('subscription-search-engine_hidden');
    const searchEngine = searchEngineHidden ? searchEngineHidden.value : 'auto';
    const iterations = document.getElementById('subscription-iterations').value;
    const questionsPerIteration = document.getElementById('subscription-questions').value;
    const strategy = document.getElementById('subscription-strategy').value;

    try {
        const url = {% if subscription %}'/news/api/subscriptions/{{ subscription.id }}'{% else %}'/news/api/subscribe'{% endif %};
        const method = {% if subscription %}'PUT'{% else %}'POST'{% endif %};

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({
                user_id: localStorage.getItem('userId') || 'anonymous',
                query: query,
                name: name,
                subscription_type: 'search',
                refresh_minutes: intervalMinutes, // Convert minutes to hours (can be fractional)
                folder_id: folder || null,
                is_active: isActive,
                model_provider: provider,
                model: model,
                custom_endpoint: customEndpoint || null,
                search_engine: searchEngine,
                search_iterations: parseInt(iterations),
                questions_per_iteration: parseInt(questionsPerIteration),
                search_strategy: strategy,
                source_id: window.sourceResearchId || null
            })
        });

        if (response.ok) {
            const data = await response.json();
            showAlert('Subscription {{ "updated" if subscription else "created" }} successfully!', 'success');

            // Redirect to appropriate page after a short delay
            setTimeout(() => {
                {% if subscription %}
                // Editing - go back to subscriptions page
                window.location.href = '/news/subscriptions';
                {% else %}
                // Creating - go to news feed
                window.location.href = '/news';
                {% endif %}
            }, 1500);
        } else {
            const error = await response.json();
            showAlert('Error: ' + (error.error || 'Failed to save subscription'), 'error');
        }
    } catch (error) {
        showAlert('Error: ' + error.message, 'error');
    }
}

// Handle test run
async function handleTestRun(event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    console.log('Test Run button clicked');

    try {
        const query = document.getElementById('subscription-query').value;
        console.log('Query value:', query);
        if (!query) {
            showAlert('Please enter a query first', 'warning');
            return;
        }

    // Collect all configuration
    const provider = document.getElementById('subscription-provider').value;
    const modelHidden = document.getElementById('subscription-model_hidden');
    const model = modelHidden ? modelHidden.value : '';
    const customEndpoint = document.getElementById('subscription-custom-endpoint').value;
    const searchEngineHidden = document.getElementById('subscription-search-engine_hidden');
    const searchEngine = searchEngineHidden ? searchEngineHidden.value : 'auto';
    const iterations = document.getElementById('subscription-iterations').value;
    const questions = document.getElementById('subscription-questions').value;
    const strategy = document.getElementById('subscription-strategy').value || 'source-based';

    console.log('Query length:', query.length);

    // Show loading state
    showAlert('Starting test run...', 'info');

    try {
        // Prepare the request data
        const requestData = {
            query: query,
            mode: 'quick',
            strategy: strategy,
            metadata: {
                is_news_search: true,
                search_type: 'news_analysis',
                display_in: 'news_feed',
                triggered_by: 'subscription_test_run'
            }
        };

        // Add optional parameters
        if (provider) requestData.model_provider = provider;
        if (model) requestData.model = model;
        if (customEndpoint) requestData.custom_endpoint = customEndpoint;
        if (searchEngine) requestData.search_engine = searchEngine;
        if (iterations) requestData.iterations = parseInt(iterations);
        if (questions) requestData.questions_per_iteration = parseInt(questions);

        console.log('Sending research request:', requestData);

        // POST directly to the research API
        const response = await fetch('/api/start_research', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify(requestData)
        });

        console.log('Research API response status:', response.status);

        if (!response.ok) {
            let errorMessage = 'Error starting research';
            try {
                const errorData = await response.json();
                errorMessage = errorData.error || errorData.message || errorMessage;
            } catch (e) {
                errorMessage = `${errorMessage}: ${response.statusText}`;
            }

            if (response.status === 401) {
                showAlert('Please log in to test subscriptions. Redirecting to login...', 'error');
                setTimeout(() => {
                    window.location.href = '/auth/login?next=' + encodeURIComponent(window.location.href);
                }, 2000);
                return;
            }

            showAlert(errorMessage, 'error');
            return;
        }

        const data = await response.json();
        console.log('Research started:', data);

        if (data.status === 'success' && data.research_id) {
            showAlert('Research started successfully! Redirecting to news page...', 'success');

            // Store the research ID and query so the news page can track it
            sessionStorage.setItem('activeTestRunResearchId', data.research_id);
            sessionStorage.setItem('activeTestRunQuery', query);

            // Redirect to news page after a short delay
            setTimeout(() => {
                window.location.href = '/news';
            }, 1500);
        } else {
            showAlert('Unexpected response from server', 'error');
        }

    } catch (error) {
        console.error('Error starting test run:', error);
        showAlert('Error: ' + error.message, 'error');
    }
    } catch (outerError) {
        console.error('Error in handleTestRun:', outerError);
        showAlert('An unexpected error occurred', 'error');
    }
}

// Handle create and run button click
async function handleCreateAndRun(event) {
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }

    console.log('Create and Run button clicked');

    try {
        // First create the subscription
        const query = document.getElementById('subscription-query').value;
        const name = document.getElementById('subscription-name').value || query.substring(0, 50);
        const intervalMinutes = parseInt(document.getElementById('subscription-interval').value);
        const folder = document.getElementById('subscription-folder').value;
        const isActive = document.getElementById('subscription-active').checked;

        // Model configuration
        const modelHidden = document.getElementById('subscription-model_hidden');
        const model = modelHidden ? modelHidden.value : '';
        const provider = document.getElementById('subscription-provider').value;
        const customEndpoint = document.getElementById('subscription-custom-endpoint').value;

        // Search configuration
        const searchEngineHidden = document.getElementById('subscription-search-engine_hidden');
        const searchEngine = searchEngineHidden ? searchEngineHidden.value : 'auto';
        const iterations = document.getElementById('subscription-iterations').value;
        const questionsPerIteration = document.getElementById('subscription-questions').value;
        const strategy = document.getElementById('subscription-strategy').value;

        // Show loading state
        showAlert('Creating subscription and starting research...', 'info');

        // Create subscription
        const createResponse = await fetch('/news/api/subscribe', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({
                user_id: localStorage.getItem('userId') || 'anonymous',
                query: query,
                name: name,
                subscription_type: 'search',
                refresh_minutes: intervalMinutes,
                folder_id: folder || null,
                is_active: isActive,
                model_provider: provider,
                model: model,
                custom_endpoint: customEndpoint || null,
                search_engine: searchEngine,
                search_iterations: parseInt(iterations),
                questions_per_iteration: parseInt(questionsPerIteration),
                search_strategy: strategy,
                source_id: window.sourceResearchId || null
            })
        });

        if (!createResponse.ok) {
            const error = await createResponse.json();
            showAlert('Error: ' + (error.error || 'Failed to create subscription'), 'error');
            return;
        }

        const createData = await createResponse.json();
        console.log('Subscription created:', createData);
        console.log('Full response data:', JSON.stringify(createData, null, 2));

        // Now run the subscription using the test run functionality
        console.log('Subscription created, now starting immediate research run');
        await handleTestRun();

    } catch (error) {
        console.error('Error in create and run:', error);
        showAlert('Error: ' + error.message, 'error');
    }
}

// Show alert
function showAlert(message, type) {
    const alertContainer = document.getElementById('subscription-alert');
    alertContainer.innerHTML = `
        <div class="alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    alertContainer.style.display = 'block';
}

// Get CSRF token
function getCSRFToken() {
    const token = document.querySelector('meta[name="csrf-token"]');
    return token ? token.content : '';
}

// Load jitter settings from server
async function loadJitterInfo() {
    try {
        const response = await fetch('/news/api/scheduler/status', {
            credentials: 'same-origin',
            headers: {
                'X-CSRFToken': getCSRFToken()
            }
        });
        if (response.ok) {
            const data = await response.json();
            const maxJitter = data.settings?.max_jitter_seconds || 300;

            // Calculate jitter range in human-readable format
            const formatJitter = (seconds) => {
                if (seconds === 0) return '0';
                if (seconds < 60) return `${seconds} second${seconds !== 1 ? 's' : ''}`;
                const minutes = Math.round(seconds/60);
                if (minutes < 60) return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
                const hours = Math.round(seconds/3600);
                return `${hours} hour${hours !== 1 ? 's' : ''}`;
            };

            let jitterText;
            if (maxJitter === 0) {
                jitterText = `<i class="bi bi-info-circle"></i> No jitter configured. Subscriptions will run exactly at scheduled times.`;
            } else {
                jitterText = `<i class="bi bi-shuffle"></i> A random delay up to <strong>${formatJitter(maxJitter)}</strong> will be added to prevent all subscriptions from running simultaneously.`;
            }

            jitterText += ` <a href="/settings#news_scheduler" class="text-muted" style="font-size: 0.85em;"><i class="bi bi-gear"></i> Change in settings</a>`;

            const element = document.getElementById('subscription-jitter-info');
            if (element) {
                element.innerHTML = `<small class="text-muted">${jitterText}</small>`;
            }
        }
    } catch (error) {
        console.error('Error loading jitter info:', error);
    }
}
</script>
{% endblock %}
