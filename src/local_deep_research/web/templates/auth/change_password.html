<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Change Password - Local Deep Research</title>

    <!-- Vite HMR for development -->
    {{ vite_hmr() }}

    <!-- Load all vendor dependencies and styles through Vite -->
    {{ vite_asset('js/app.js') }}
    <style>
        body {
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
        }
        .change-password-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 450px;
        }
        .change-password-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .change-password-header h1 {
            color: #333;
            font-size: 1.8rem;
            margin-bottom: 0.5rem;
        }
        .change-password-header p {
            color: #666;
            margin: 0;
        }
        .form-floating {
            margin-bottom: 1rem;
        }
        .btn-change {
            width: 100%;
            padding: 0.75rem;
            font-size: 1.1rem;
        }
        .alert {
            margin-bottom: 1rem;
        }
        .warning-box {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        .warning-box i {
            color: #1976d2;
        }
        .process-info {
            background-color: #f5f5f5;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1.5rem;
            font-size: 0.85rem;
        }
        .process-info ul {
            margin-bottom: 0;
            padding-left: 1.5rem;
        }
        .auth-links {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid #dee2e6;
        }
        .password-strength {
            height: 4px;
            border-radius: 2px;
            margin-top: 0.5rem;
            transition: all 0.3s ease;
        }
        .strength-weak { background-color: #f44336; width: 33%; }
        .strength-medium { background-color: #ff9800; width: 66%; }
        .strength-strong { background-color: #4caf50; width: 100%; }
    </style>
</head>
<body>
    <div class="change-password-container">
        <div class="change-password-header">
            <h1><i class="fas fa-key"></i> Change Password</h1>
            <p>Update your encryption password</p>
        </div>

        <div class="warning-box">
            <i class="fas fa-info-circle"></i>
            <strong>What happens when you change your password:</strong><br>
            Your entire database will be re-encrypted with the new password. This process may take a few moments.
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="post" action="{{ url_for('auth.change_password') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

            <div class="form-floating">
                <input type="password"
                       class="ldr-form-control"
                       id="current_password"
                       name="current_password"
                       placeholder="Current Password"
                       autocomplete="off"
                       required
                       autofocus>
                <label for="current_password">Current Password</label>
            </div>

            <hr class="my-3">

            <div class="process-info">
                <strong>New Password Requirements:</strong>
                <ul>
                    <li>At least 8 characters long</li>
                    <li>Different from your current password</li>
                    <li>Remember to update your password manager</li>
                </ul>
            </div>

            <div class="form-floating">
                <input type="password"
                       class="ldr-form-control"
                       id="new_password"
                       name="new_password"
                       placeholder="New Password"
                       autocomplete="off"
                       required
                       minlength="8"
                       onkeyup="checkPasswordStrength(this.value)">
                <label for="new_password">New Password</label>
                <div id="password-strength" class="password-strength" style="display: none;"></div>
            </div>

            <div class="form-floating">
                <input type="password"
                       class="ldr-form-control"
                       id="confirm_password"
                       name="confirm_password"
                       placeholder="Confirm New Password"
                       autocomplete="off"
                       required
                       minlength="8">
                <label for="confirm_password">Confirm New Password</label>
            </div>

            <button type="submit" class="btn btn-primary btn-change">
                <i class="fas fa-sync-alt"></i> Change Password
            </button>
        </form>

        <div class="auth-links">
            <a href="{{ url_for('research.index') }}">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </a>
            <p class="text-muted small mt-3">
                <i class="fas fa-shield-alt"></i>
                Your data remains encrypted during the password change
            </p>
        </div>
    </div>

    <script>
        function checkPasswordStrength(password) {
            const strengthBar = document.getElementById('password-strength');

            if (password.length === 0) {
                strengthBar.style.display = 'none';
                return;
            }

            strengthBar.style.display = 'block';

            let strength = 0;

            // Length check
            if (password.length >= 8) strength++;
            if (password.length >= 12) strength++;

            // Character variety
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^a-zA-Z0-9]/.test(password)) strength++;

            // Update strength bar
            strengthBar.className = 'password-strength';
            if (strength <= 2) {
                strengthBar.classList.add('strength-weak');
            } else if (strength <= 4) {
                strengthBar.classList.add('strength-medium');
            } else {
                strengthBar.classList.add('strength-strong');
            }
        }

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const currentPassword = document.getElementById('current_password').value;
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (newPassword !== confirmPassword) {
                e.preventDefault();
                alert('New passwords do not match!');
                return false;
            }

            if (currentPassword === newPassword) {
                e.preventDefault();
                alert('New password must be different from current password!');
                return false;
            }
        });
    </script>
</body>
</html>
