<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Local Deep Research</title>

    <!-- Vite HMR for development -->
    {{ vite_hmr() }}

    <!-- Load all vendor dependencies and styles through Vite -->
    {{ vite_asset('js/app.js') }}

    <!-- Keep local styles -->
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        /* Override main layout for auth pages */
        body {
            min-height: 100vh;
            background: var(--bg-primary);
            background-image:
                radial-gradient(ellipse at top left, rgba(110, 79, 246, 0.1) 0%, transparent 50%),
                radial-gradient(ellipse at bottom right, rgba(64, 191, 255, 0.1) 0%, transparent 50%);
            padding: 0;
            margin: 0;
            overflow-x: hidden;
        }

        .auth-wrapper {
            min-height: 100vh;
            width: 100%;
            display: flex !important;
            flex-direction: row !important;
            align-items: center;
            justify-content: center;
            gap: 4rem;
            padding: 2rem;
        }

        .auth-visual {
            flex: 1 0 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 3rem;
            opacity: 0.8;
        }

        .visual-container {
            position: relative;
            width: 300px;
            height: 300px;
        }

        /* DNA Helix Animation */
        .dna-helix {
            position: absolute;
            width: 100%;
            height: 100%;
            transform: rotateX(60deg) rotateZ(45deg);
        }

        .helix-line {
            position: absolute;
            width: 2px;
            height: 100%;
            background: linear-gradient(to bottom,
                transparent 0%,
                var(--accent-primary) 20%,
                var(--accent-secondary) 50%,
                var(--accent-tertiary) 80%,
                transparent 100%);
            left: 50%;
            transform: translateX(-50%);
        }

        .helix-1 {
            animation: rotate-helix 8s linear infinite;
        }

        .helix-2 {
            animation: rotate-helix 8s linear infinite;
            animation-delay: -4s;
        }

        .helix-dot {
            position: absolute;
            width: 8px;
            height: 8px;
            background: var(--accent-primary);
            border-radius: 50%;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 0 20px var(--accent-primary);
        }

        .dot-1 { top: 10%; animation: helix-dots 8s linear infinite; }
        .dot-2 { top: 30%; animation: helix-dots 8s linear infinite 1.3s; }
        .dot-3 { top: 50%; animation: helix-dots 8s linear infinite 2.6s; }
        .dot-4 { top: 70%; animation: helix-dots 8s linear infinite 3.9s; }
        .dot-5 { top: 90%; animation: helix-dots 8s linear infinite 5.2s; }
        .dot-6 { top: 50%; animation: helix-dots 8s linear infinite 6.5s; }

        @keyframes rotate-helix {
            0% { transform: translateX(-50%) rotateY(0deg); }
            100% { transform: translateX(-50%) rotateY(360deg); }
        }

        @keyframes helix-dots {
            0%, 100% {
                transform: translateX(-50%) translateZ(30px) scale(1);
                background: var(--accent-primary);
            }
            33% {
                transform: translateX(-50%) translateZ(-30px) scale(0.8);
                background: var(--accent-secondary);
            }
            66% {
                transform: translateX(-50%) translateZ(30px) scale(1.2);
                background: var(--accent-tertiary);
            }
        }

        /* Neural Network */
        .neural-network {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0.6;
        }

        .network-svg {
            width: 100%;
            height: 100%;
        }

        .node {
            animation: pulse-node 3s ease-in-out infinite;
        }

        .node:nth-child(odd) {
            animation-delay: 0.5s;
        }

        @keyframes pulse-node {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.5); opacity: 1; }
        }

        /* Floating Symbols */
        .floating-symbols {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .symbol {
            position: absolute;
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--accent-secondary);
            opacity: 0.3;
            font-family: 'Courier New', monospace;
        }

        .symbol-1 {
            top: 10%;
            left: 10%;
            animation: float-symbol 6s ease-in-out infinite;
        }
        .symbol-2 {
            top: 20%;
            right: 15%;
            animation: float-symbol 8s ease-in-out infinite 1s;
        }
        .symbol-3 {
            bottom: 30%;
            left: 20%;
            animation: float-symbol 7s ease-in-out infinite 2s;
        }
        .symbol-4 {
            bottom: 20%;
            right: 10%;
            animation: float-symbol 9s ease-in-out infinite 1.5s;
        }
        .symbol-5 {
            top: 50%;
            left: 5%;
            animation: float-symbol 8s ease-in-out infinite 3s;
        }
        .symbol-6 {
            top: 40%;
            right: 5%;
            animation: float-symbol 7s ease-in-out infinite 2.5s;
        }

        @keyframes float-symbol {
            0%, 100% {
                transform: translateY(0) rotate(0deg);
                opacity: 0.3;
            }
            25% {
                transform: translateY(-20px) rotate(90deg);
                opacity: 0.6;
            }
            50% {
                transform: translateY(10px) rotate(180deg);
                opacity: 0.4;
            }
            75% {
                transform: translateY(-10px) rotate(270deg);
                opacity: 0.5;
            }
        }

        .visual-text {
            text-align: center;
        }

        .text-line {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            letter-spacing: 2px;
            text-transform: uppercase;
            opacity: 0.7;
        }

        .text-line:last-child {
            font-size: 1rem;
            letter-spacing: 1px;
        }

        /* Responsive - stack on very small screens */
        @media (max-width: 900px) {
            .auth-wrapper {
                flex-direction: column !important;
            }

            .auth-visual {
                display: none;
            }

            .auth-container {
                max-width: 550px;
                margin: 0 auto;
            }
        }

        .auth-container {
            flex: 0 0 550px;
            width: 550px;
            max-width: 90%;
        }

        .auth-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .auth-header h1 {
            font-size: 2.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .auth-header h1 i {
            color: var(--accent-primary);
            font-size: 2.25rem;
        }

        .auth-header p {
            color: var(--text-secondary);
            font-size: 1.25rem;
        }

        .ldr-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            overflow: hidden;
        }

        .ldr-card-content {
            padding: 3rem;
        }

        .encryption-banner {
            background: linear-gradient(135deg, rgba(110, 79, 246, 0.1) 0%, rgba(64, 191, 255, 0.1) 100%);
            border: 1px solid rgba(110, 79, 246, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 1.25rem;
        }

        .encryption-banner i {
            color: var(--accent-primary);
            font-size: 2rem;
        }

        .encryption-banner-content {
            flex: 1;
        }

        .encryption-banner-content strong {
            color: var(--text-primary);
            display: block;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .encryption-banner-content span {
            color: var(--text-secondary);
            font-size: 1rem;
            line-height: 1.5;
        }

        .security-warning {
            background: rgba(249, 188, 11, 0.1);
            border: 1px solid rgba(249, 188, 11, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            color: var(--warning-color);
        }

        .security-warning i {
            margin-right: 0.5rem;
        }

        .alert {
            background: rgba(250, 92, 124, 0.1);
            border: 1px solid rgba(250, 92, 124, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            color: var(--error-color);
            position: relative;
        }

        .alert-success {
            background: rgba(10, 207, 151, 0.1);
            border-color: rgba(10, 207, 151, 0.3);
            color: var(--success-color);
        }

        .alert-info {
            background: rgba(64, 191, 255, 0.1);
            border-color: rgba(64, 191, 255, 0.3);
            color: var(--accent-tertiary);
        }

        .alert-dismissible {
            padding-right: 3rem;
        }

        .btn-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            color: inherit;
            opacity: 0.5;
            cursor: pointer;
            font-size: 1.2rem;
            line-height: 1;
            padding: 0;
            width: auto;
            height: auto;
        }

        .btn-close:hover {
            opacity: 1;
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
            font-weight: 500;
            font-size: 1.1rem;
        }

        .ldr-form-control {
            width: 100%;
            padding: 1rem 1.25rem;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            color: var(--text-primary);
            font-size: 1.1rem;
            transition: all 0.2s;
        }

        .ldr-form-control:focus {
            outline: none;
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 3px rgba(110, 79, 246, 0.1);
        }

        .ldr-form-control::placeholder {
            color: var(--text-muted);
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            gap: 1rem;
            cursor: pointer;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            font-size: 1.05rem;
        }

        .checkbox-label input[type="checkbox"] {
            width: 1.5rem;
            height: 1.5rem;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            flex-shrink: 0;
        }

        .checkbox-label input[type="checkbox"]:checked {
            background: var(--accent-primary);
            border-color: var(--accent-primary);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            padding: 1.25rem 2.5rem;
            background: var(--accent-primary);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.15rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            width: 100%;
        }

        .btn:hover {
            background: var(--accent-secondary);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(110, 79, 246, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .auth-links {
            text-align: center;
            margin-top: 2.5rem;
            padding-top: 2.5rem;
            border-top: 1px solid var(--border-color);
        }

        .auth-links p {
            color: var(--text-secondary);
            margin-bottom: 0.75rem;
            font-size: 1.05rem;
        }

        .auth-links a {
            color: var(--accent-tertiary);
            text-decoration: none;
            font-weight: 500;
            font-size: 1.05rem;
        }

        .auth-links a:hover {
            color: var(--accent-secondary);
        }

        .browser-hint {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-top: 1rem;
        }

        .browser-hint i {
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <div class="auth-wrapper">
        <div class="auth-container">
            <div class="auth-header">
                <h1><i class="fas fa-flask"></i> Local Deep Research</h1>
                <p>Secure Research Platform</p>
            </div>

            <div class="ldr-card">
                <div class="ldr-card-content">
                    <div class="encryption-banner">
                        <i class="fas fa-shield-alt"></i>
                        <div class="encryption-banner-content">
                            <strong>Your data is encrypted</strong>
                            <span>Each user has their own encrypted database protected by their password</span>
                        </div>
                    </div>

                {% if not has_encryption %}
                    <div class="security-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Security Warning:</strong>
                        Database encryption is not available. Your data will be stored unencrypted.
                        <br><small>Install SQLCipher for secure storage.</small>
                    </div>
                {% endif %}

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'success' if category == 'success' else 'info' if category == 'info' else '' }} alert-dismissible">
                                {{ message }}
                                <button type="button" class="btn-close" onclick="this.parentElement.style.display='none'">×</button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="post" action="{{ url_for('auth.login') }}" autocomplete="on">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text"
                               class="ldr-form-control"
                               id="username"
                               name="username"
                               placeholder="Enter your username"
                               autocomplete="username"
                               required
                               autofocus>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password"
                               class="ldr-form-control"
                               id="password"
                               name="password"
                               placeholder="Enter your password"
                               autocomplete="current-password"
                               required>
                    </div>

                    <label class="checkbox-label">
                        <input type="checkbox"
                               id="remember"
                               name="remember"
                               value="true">
                        <span>Remember me for 30 days</span>
                    </label>

                    <button type="submit" class="btn">
                        <i class="fas fa-sign-in-alt"></i>
                        Login
                    </button>
                </form>

                <div class="auth-links">
                    <p>Don't have an account? <a href="{{ url_for('auth.register') }}">Register here</a></p>
                    <div class="browser-hint">
                        <i class="fas fa-info-circle"></i>
                        <span>Your browser can save your password for easy login</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="auth-visual">
            <div class="visual-container">
                <div class="dna-helix">
                    <div class="helix-line helix-1"></div>
                    <div class="helix-line helix-2"></div>
                    <div class="helix-dot dot-1"></div>
                    <div class="helix-dot dot-2"></div>
                    <div class="helix-dot dot-3"></div>
                    <div class="helix-dot dot-4"></div>
                    <div class="helix-dot dot-5"></div>
                    <div class="helix-dot dot-6"></div>
                </div>
                <div class="neural-network">
                    <svg viewBox="0 0 200 200" class="network-svg">
                        <defs>
                            <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                                <stop offset="0%" style="stop-color:var(--accent-primary);stop-opacity:0.6" />
                                <stop offset="100%" style="stop-color:var(--accent-tertiary);stop-opacity:0.6" />
                            </linearGradient>
                        </defs>
                        <!-- Network connections -->
                        <line x1="50" y1="50" x2="100" y2="30" stroke="url(#gradient1)" stroke-width="1" opacity="0.5" />
                        <line x1="50" y1="50" x2="100" y2="100" stroke="url(#gradient1)" stroke-width="1" opacity="0.5" />
                        <line x1="50" y1="150" x2="100" y2="100" stroke="url(#gradient1)" stroke-width="1" opacity="0.5" />
                        <line x1="50" y1="150" x2="100" y2="170" stroke="url(#gradient1)" stroke-width="1" opacity="0.5" />
                        <line x1="100" y1="30" x2="150" y2="75" stroke="url(#gradient1)" stroke-width="1" opacity="0.5" />
                        <line x1="100" y1="100" x2="150" y2="75" stroke="url(#gradient1)" stroke-width="1" opacity="0.5" />
                        <line x1="100" y1="100" x2="150" y2="125" stroke="url(#gradient1)" stroke-width="1" opacity="0.5" />
                        <line x1="100" y1="170" x2="150" y2="125" stroke="url(#gradient1)" stroke-width="1" opacity="0.5" />

                        <!-- Network nodes -->
                        <circle cx="50" cy="50" r="4" fill="var(--accent-primary)" class="node" />
                        <circle cx="50" cy="150" r="4" fill="var(--accent-primary)" class="node" />
                        <circle cx="100" cy="30" r="4" fill="var(--accent-secondary)" class="node" />
                        <circle cx="100" cy="100" r="4" fill="var(--accent-secondary)" class="node" />
                        <circle cx="100" cy="170" r="4" fill="var(--accent-secondary)" class="node" />
                        <circle cx="150" cy="75" r="4" fill="var(--accent-tertiary)" class="node" />
                        <circle cx="150" cy="125" r="4" fill="var(--accent-tertiary)" class="node" />
                    </svg>
                </div>
                <div class="floating-symbols">
                    <div class="symbol symbol-1">{ }</div>
                    <div class="symbol symbol-2">&lt;/&gt;</div>
                    <div class="symbol symbol-3">∞</div>
                    <div class="symbol symbol-4">λ</div>
                    <div class="symbol symbol-5">∇</div>
                    <div class="symbol symbol-6">Σ</div>
                </div>
            </div>
            <div class="visual-text">
                <div class="text-line">Local Deep Research</div>
                <div class="text-line">LDR Engine</div>
                <div class="text-line">Encrypted • Secure • Private</div>
            </div>
        </div>
    </div>
</body>
</html>
