<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Local Deep Research</title>

    <!-- Vite HMR for development -->
    {{ vite_hmr() }}

    <!-- Load all vendor dependencies and styles through Vite -->
    {{ vite_asset('js/app.js') }}

    <!-- Keep local styles -->
    <link rel="stylesheet" href="/static/css/styles.css">
    <style>
        /* Override main layout for auth pages */
        body {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background: var(--bg-primary);
            background-image:
                radial-gradient(ellipse at top left, rgba(110, 79, 246, 0.1) 0%, transparent 50%),
                radial-gradient(ellipse at bottom right, rgba(64, 191, 255, 0.1) 0%, transparent 50%);
        }

        .auth-container {
            width: 100%;
            max-width: 600px;
            padding: 2rem;
        }

        .auth-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .auth-header h1 {
            font-size: 2.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
        }

        .auth-header h1 i {
            color: var(--accent-primary);
            font-size: 2.25rem;
        }

        .auth-header p {
            color: var(--text-secondary);
            font-size: 1.25rem;
        }

        .ldr-card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            box-shadow: var(--card-shadow);
            overflow: hidden;
        }

        .ldr-card-content {
            padding: 3rem;
        }

        .warning-banner {
            background: rgba(249, 188, 11, 0.1);
            border: 1px solid rgba(249, 188, 11, 0.3);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            display: flex;
            align-items: flex-start;
            gap: 1.25rem;
        }

        .warning-banner i {
            color: var(--warning-color);
            font-size: 2rem;
            margin-top: 0.25rem;
        }

        .warning-banner-content {
            flex: 1;
        }

        .warning-banner-content strong {
            color: var(--warning-color);
            display: block;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }

        .warning-banner-content span {
            color: var(--text-secondary);
            font-size: 1rem;
            line-height: 1.6;
        }

        .danger-alert {
            background: rgba(250, 92, 124, 0.1);
            border: 1px solid rgba(250, 92, 124, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            color: var(--error-color);
        }

        .danger-alert strong {
            display: block;
            margin-bottom: 0.5rem;
        }

        .alert {
            background: rgba(250, 92, 124, 0.1);
            border: 1px solid rgba(250, 92, 124, 0.3);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            color: var(--error-color);
            position: relative;
        }

        .alert-dismissible {
            padding-right: 3rem;
        }

        .btn-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: none;
            color: inherit;
            opacity: 0.5;
            cursor: pointer;
            font-size: 1.2rem;
            line-height: 1;
            padding: 0;
            width: auto;
            height: auto;
        }

        .btn-close:hover {
            opacity: 1;
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
            font-weight: 500;
            font-size: 1.1rem;
        }

        .form-group small {
            display: block;
            margin-top: 0.5rem;
            color: var(--text-muted);
            font-size: 0.95rem;
        }

        .ldr-form-control {
            width: 100%;
            padding: 1rem 1.25rem;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            color: var(--text-primary);
            font-size: 1.1rem;
            transition: all 0.2s;
        }

        .ldr-form-control:focus {
            outline: none;
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 3px rgba(110, 79, 246, 0.1);
        }

        .ldr-form-control::placeholder {
            color: var(--text-muted);
        }

        .password-requirements {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group.password-field {
            margin-bottom: 1rem;
        }

        .password-requirements strong {
            color: var(--text-primary);
            display: block;
            margin-bottom: 0.75rem;
            font-size: 1.05rem;
        }

        .password-requirements ul {
            margin: 0;
            padding-left: 1.75rem;
            color: var(--text-secondary);
            font-size: 1rem;
        }

        .password-requirements li {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .password-strength {
            height: 4px;
            border-radius: 2px;
            margin-top: 0.5rem;
            transition: all 0.3s ease;
            background: var(--bg-tertiary);
        }

        .strength-weak { background-color: var(--error-color); width: 33%; }
        .strength-medium { background-color: var(--warning-color); width: 66%; }
        .strength-strong { background-color: var(--success-color); width: 100%; }

        .acknowledge-box {
            background: rgba(250, 92, 124, 0.05);
            border: 1px solid rgba(250, 92, 124, 0.2);
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .checkbox-label {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            cursor: pointer;
            color: var(--text-secondary);
            font-size: 1.05rem;
        }

        .checkbox-label input[type="checkbox"] {
            width: 1.5rem;
            height: 1.5rem;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            flex-shrink: 0;
            margin-top: 0.125rem;
        }

        .checkbox-label input[type="checkbox"]:checked {
            background: var(--accent-primary);
            border-color: var(--accent-primary);
        }

        .checkbox-label span {
            line-height: 1.5;
        }

        .checkbox-label strong {
            color: var(--error-color);
            font-weight: 600;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            padding: 1.25rem 2.5rem;
            background: var(--accent-primary);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.15rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            width: 100%;
        }

        .btn:hover {
            background: var(--accent-secondary);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(110, 79, 246, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .auth-links {
            text-align: center;
            margin-top: 2.5rem;
            padding-top: 2.5rem;
            border-top: 1px solid var(--border-color);
        }

        .auth-links p {
            color: var(--text-secondary);
            margin-bottom: 0.75rem;
            font-size: 1.05rem;
        }

        .auth-links a {
            color: var(--accent-tertiary);
            text-decoration: none;
            font-weight: 500;
            font-size: 1.05rem;
        }

        .auth-links a:hover {
            color: var(--accent-secondary);
        }

        .browser-hint {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-top: 1rem;
        }

        .browser-hint i {
            font-size: 1rem;
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-header">
            <h1><i class="fas fa-user-plus"></i> Create Account</h1>
            <p>Join Local Deep Research</p>
        </div>

        <div class="card">
            <div class="card-content">

                {% if has_encryption %}
                    <div class="warning-banner">
                        <i class="fas fa-exclamation-triangle"></i>
                        <div class="warning-banner-content">
                            <strong>Important Security Notice</strong>
                            <span>Your database will be encrypted with your password. There is NO way to recover your data if you forget your password. Please use a password manager.</span>
                        </div>
                    </div>
                {% else %}
                    <div class="danger-alert">
                        <i class="fas fa-shield-alt"></i>
                        <strong>Security Alert: No Encryption Available</strong>
                        SQLCipher is not installed. Your database will be created WITHOUT encryption.
                        Your API keys and data will be stored in plain text.
                        <br><br>
                        <strong>This is NOT recommended for production use.</strong>
                        <br>
                        Install SQLCipher before creating accounts for secure operation.
                    </div>
                {% endif %}

                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-dismissible">
                                {{ message }}
                                <button type="button" class="btn-close" onclick="this.parentElement.style.display='none'">×</button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <form method="post" action="{{ url_for('auth.register') }}" autocomplete="on">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>

                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text"
                               class="form-control"
                               id="username"
                               name="username"
                               placeholder="Choose a username"
                               autocomplete="username"
                               required
                               autofocus
                               minlength="3"
                               pattern="[a-zA-Z0-9_\-]+">
                        <small>Letters, numbers, underscores, and hyphens only</small>
                    </div>

                    <div class="form-group password-field">
                        <label for="password">Password</label>
                        <input type="password"
                               class="form-control"
                               id="password"
                               name="password"
                               placeholder="Create a strong password"
                               autocomplete="new-password"
                               required
                               minlength="8"
                               onkeyup="checkPasswordStrength(this.value)">
                        <div id="password-strength" class="password-strength" style="display: none;"></div>
                    </div>

                    <div class="password-requirements">
                        <strong>Password Requirements:</strong>
                        <ul>
                            <li>At least 8 characters long</li>
                            <li>Use a mix of letters, numbers, and symbols</li>
                            <li>Store it in your browser's password manager</li>
                        </ul>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password">Confirm Password</label>
                        <input type="password"
                               class="form-control"
                               id="confirm_password"
                               name="confirm_password"
                               placeholder="Re-enter your password"
                               autocomplete="new-password"
                               required
                               minlength="8">
                    </div>

                    <div class="acknowledge-box">
                        <label class="checkbox-label">
                            <input type="checkbox"
                                   id="acknowledge"
                                   name="acknowledge"
                                   value="true"
                                   required>
                            <span>
                                <i class="fas fa-lock"></i> I understand that <strong>if I forget my password,
                                I will permanently lose access to all my data</strong>. There is no password recovery.
                            </span>
                        </label>
                    </div>

                    <button type="submit" class="btn">
                        <i class="fas fa-shield-alt"></i>
                        Create Encrypted Account
                    </button>
                </form>

                <div class="auth-links">
                    <p>Already have an account? <a href="{{ url_for('auth.login') }}">Login here</a></p>
                    <div class="browser-hint">
                        <i class="fas fa-info-circle"></i>
                        <span>Your browser will offer to save your password</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        function checkPasswordStrength(password) {
            const strengthBar = document.getElementById('password-strength');

            if (password.length === 0) {
                strengthBar.style.display = 'none';
                return;
            }

            strengthBar.style.display = 'block';

            let strength = 0;

            // Length check
            if (password.length >= 8) strength++;
            if (password.length >= 12) strength++;

            // Character variety
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^a-zA-Z0-9]/.test(password)) strength++;

            // Update strength bar
            strengthBar.className = 'password-strength';
            if (strength <= 2) {
                strengthBar.classList.add('strength-weak');
            } else if (strength <= 4) {
                strengthBar.classList.add('strength-medium');
            } else {
                strengthBar.classList.add('strength-strong');
            }
        }

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match!');
                return false;
            }
        });
    </script>
</body>
</html>
