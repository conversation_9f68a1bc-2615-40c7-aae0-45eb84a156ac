/**
 * Custom Dropdown Component
 * Styling for reusable dropdown component
 */

.custom-dropdown {
    position: relative;
    width: 100%;
    z-index: 5; /* Base z-index for the dropdown container */
}

.custom-dropdown-input {
    width: 100%;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid var(--border-color, #343452);
    background-color: var(--bg-secondary, #1e1e2d);
    color: var(--text-primary, #f5f5f5);
    font-size: 0.9rem;
    cursor: pointer;
    position: relative;
    z-index: 6; /* Higher than container for stacking context */
}

.custom-dropdown-input:focus {
    outline: none;
    border-color: var(--accent-primary, #6e4ff6);
    box-shadow: 0 0 0 3px rgba(110, 79, 246, 0.15);
}

.custom-dropdown-list {
    position: absolute;
    width: 100%;
    max-height: 250px;
    overflow-y: auto;
    z-index: 9999; /* High z-index for default state */
    background-color: var(--bg-tertiary, #2a2a3a);
    border: 1px solid var(--border-color, #343452);
    border-radius: 6px;
    margin-top: 5px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
    display: none;
}

/* Styles for when the dropdown is active */
.custom-dropdown-list.dropdown-active {
    z-index: 99999 !important; /* Extremely high z-index */
    margin-top: 0 !important; /* Reset margin as top/left are calculated */
    max-height: 40vh !important; /* Cap at 40% of viewport height */
    overflow-y: auto !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5) !important; /* Stronger shadow */
    visibility: visible !important;
    opacity: 1 !important;
    clip: auto !important;
    clip-path: none !important;
    transform: none !important;
    pointer-events: auto !important;

    /* Ensure contrast with background */
    background-color: var(--bg-tertiary, #2a2a3a) !important;
    color: var(--text-primary, #f5f5f5) !important;
    border: 2px solid var(--accent-primary, #6e4ff6) !important; /* More visible border */

    /* Width, top, left are set by JS */
}

/* Explicitly override any ancestor overflow properties - more targeted to avoid side effects */
body * {
    overflow: visible !important;
}

/* Immediately revert the blanket override for essential elements */
html, body {
    overflow: auto !important;
}
.custom-dropdown-list,
.custom-dropdown-list.dropdown-fixed {
    overflow-y: auto !important;
}

/* Add a special class to the body when a dropdown is active */
body.dropdown-active {
    /* Apply a higher z-index to ensure body doesn't create a stacking context that traps our dropdown */
    z-index: auto !important;
    position: relative !important;
}

.custom-dropdown-item {
    padding: 10px 15px;
    cursor: pointer;
    color: var(--text-primary, #f5f5f5);
    transition: background-color 0.2s;
}

.custom-dropdown-item:hover,
.custom-dropdown-item.active {
    background-color: rgba(110, 79, 246, 0.1);
}

.custom-dropdown-item .highlight {
    background-color: rgba(110, 79, 246, 0.3);
    border-radius: 2px;
    padding: 0 2px;
}

.custom-dropdown-no-results {
    padding: 10px 15px;
    color: var(--text-secondary, #c0c0cc);
    font-style: italic;
}

.custom-dropdown-footer {
    padding: 10px 15px;
    border-top: 1px solid var(--border-color, #343452);
    color: var(--accent-tertiary, #40bfff);
    font-size: 0.85rem;
    background-color: rgba(64, 191, 255, 0.08);
}

/* Custom dropdown with refresh button */
.custom-dropdown-with-refresh {
    display: flex;
    align-items: center; /* Center items vertically */
    gap: 8px;
    width: 100%;
}

.custom-dropdown-with-refresh .custom-dropdown {
    flex: 1;
}

.custom-dropdown-refresh-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 38px;
    background-color: var(--bg-tertiary, #2a2a3a);
    border: 1px solid var(--border-color, #343452);
    border-radius: 6px;
    color: var(--text-secondary, #c0c0cc);
    cursor: pointer;
    transition: all 0.2s;
    flex-shrink: 0; /* Prevent button from shrinking */
}

.custom-dropdown-refresh-btn:hover {
    background-color: var(--bg-secondary, #1e1e2d);
    color: var(--accent-primary, #6e4ff6);
    border-color: var(--accent-primary, #6e4ff6);
}

.custom-dropdown-refresh-btn.loading {
    pointer-events: none;
}

/* Remove the inner dropdown loader - we'll use JS to only show it in the button */
.dropdown-loading-indicator {
    display: none;
}

/* Only show loader inside button when needed */
.custom-dropdown-refresh-btn.loading i {
    display: none;
}

.custom-dropdown-refresh-btn.loading:before {
    content: "";
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: var(--accent-primary, #6e4ff6);
    animation: spin 0.8s linear infinite;
    display: block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Advanced Options Panel Styles */
.advanced-options-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding: 0.8rem 1rem;
    background-color: var(--bg-tertiary, rgba(40, 45, 60, 0.7));
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
    border: 1px solid var(--border-color, rgba(255, 255, 255, 0.1));
}

.advanced-options-toggle:hover {
    background-color: rgba(50, 55, 70, 0.8);
}

.advanced-options-toggle.open {
    margin-bottom: 0.5rem;
}

.toggle-text {
    font-weight: 600;
    color: var(--text-primary, #e1e2e4);
}

.advanced-options-panel {
    position: relative;
    z-index: 1; /* Lower than dropdowns */
    transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out, margin 0.3s ease-in-out;
    max-height: 0;
    overflow: hidden; /* Keep hidden when collapsed */
    opacity: 0;
    margin-top: 0;
    margin-bottom: 0;
    display: block !important; /* Always display but use max-height/opacity for animation */
}

.advanced-options-panel.expanded {
    max-height: 1000px; /* Large enough to fit all content */
    opacity: 1;
    margin-bottom: 1.5rem;
    margin-top: 0.5rem;
    overflow: visible; /* Allow dropdowns to overflow when expanded */
}

/* Make dropdowns in advanced panel appear more quickly when expanded */
.advanced-options-panel.expanded .form-group {
    animation: fadeIn 0.3s ease forwards;
}

/* Stagger animation for form groups */
.advanced-options-panel.expanded .form-row:nth-child(1) .form-group {
    animation-delay: 0.05s;
}

.advanced-options-panel.expanded .form-row:nth-child(2) .form-group {
    animation-delay: 0.1s;
}

.advanced-options-panel.expanded .form-row:nth-child(3) .form-group {
    animation-delay: 0.15s;
}

@keyframes fadeIn {
    from { opacity: 0; /* transform: translateY(-5px); REMOVED */ }
    to { opacity: 1; /* transform: translateY(0); REMOVED */ }
}

.advanced-options-toggle i {
    transition: transform 0.3s ease;
}

.advanced-options-toggle.open i {
    transform: rotate(180deg);
}

/* Loading state for dropdowns */
.custom-dropdown.loading input,
.form-group.loading input {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='%236e4ff6' d='M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z' opacity='0.3'/%3E%3Cpath fill='%236e4ff6' d='M20 12h2A10 10 0 0 0 12 2v2a8 8 0 0 1 8 8z'%3E%3CanimateTransform attributeName='transform' dur='1s' from='0 12 12' repeatCount='indefinite' to='360 12 12' type='rotate'/%3E%3C/path%3E%3C/svg%3E");
    background-position: right 10px center;
    background-repeat: no-repeat;
    background-size: 16px 16px;
}

/* Add special handling for dropdowns in the advanced panel */
/* .advanced-options-panel .custom-dropdown {
    z-index: 100;
} */

.advanced-options-panel .custom-dropdown-list {
    z-index: 10000; /* Even higher z-index for dropdowns in the advanced panel */
}

/* Accessibility improvements */
.sr-only {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Enhanced search hints styling */
.search-hints {
    margin-top: 0.75rem;
    padding: 0.75rem;
    background-color: rgba(64, 191, 255, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(64, 191, 255, 0.1);
}

.hint-row {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 0.5rem;
}

.hint-row:last-child {
    margin-bottom: 0;
}

.hint-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: var(--text-secondary, #c0c0cc);
}

.hint-icon {
    color: var(--accent-tertiary, #40bfff);
    font-size: 0.8rem;
    width: 12px;
    text-align: center;
}

.hint-text {
    font-size: 0.85rem;
    color: var(--text-secondary, #c0c0cc);
}

/* Legacy keyboard hint styling for other components */
.keyboard-hint {
    margin-top: 6px;
}

.keyboard-hint .hint-text {
    font-size: 0.8rem;
    color: var(--text-secondary, #c0c0cc);
    font-style: italic;
}

/* Responsive design for hints */
@media (max-width: 767px) {
    .hint-row {
        flex-direction: column;
        gap: 0.5rem;
    }

    .hint-item {
        font-size: 0.8rem;
    }
}

/* Mode selection accessibility improvements */
.mode-selection {
    outline: none;
}

/* Override label default styles for mode options */
label.mode-option {
    display: flex !important;
    align-items: center !important;
    width: 100%;
    margin: 0;
    font-weight: normal;
}

.mode-option {
    position: relative;
    transition: all 0.2s ease;
}

.mode-option:focus {
    outline: 2px solid var(--accent-primary, #6e4ff6);
    outline-offset: 2px;
}

.mode-option:focus-visible {
    outline: 2px solid var(--accent-primary, #6e4ff6);
    outline-offset: 2px;
}

/* Ensure proper keyboard navigation */
.mode-option[tabindex="0"] {
    cursor: pointer;
}

.mode-option[tabindex="-1"] {
    cursor: pointer;
}

/* Enhanced focus states for better accessibility */
fieldset {
    border: none;
    margin: 0;
    padding: 0;
}

legend {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary, #e1e2e4);
}

/* Keyboard shortcut hint styling */
.keyboard-shortcut-hint {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1rem;
    padding: 0.5rem;
    animation: fadeIn 0.5s ease-in;
}

.keyboard-shortcut-hint .hint-text {
    font-size: 0.9rem;
    color: var(--text-secondary, #c0c0cc);
}

.keyboard-shortcut-hint kbd {
    display: inline-block;
    padding: 0.2rem 0.4rem;
    font-size: 0.875rem;
    font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
    line-height: 1;
    color: var(--text-primary, #e1e2e4);
    background-color: var(--bg-tertiary, #2a2a3a);
    border: 1px solid var(--border-color, #343452);
    border-radius: 4px;
    box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25);
    font-weight: 600;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Results page keyboard hint */
.results-keyboard-hint {
    text-align: right;
    margin-top: 0.5rem;
    padding: 0.25rem 0;
    animation: fadeIn 0.5s ease-in;
}

.results-keyboard-hint .hint-text {
    font-size: 0.85rem;
    color: var(--text-muted, #8a8aa0);
}
