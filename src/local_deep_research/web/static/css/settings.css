/* Settings Dashboard Styles */

/* Container */
.settings-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0;
}

.settings-header {
    margin-bottom: 1.5rem;
}

.settings-header h1 {
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
    font-weight: 600;
}

.settings-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

/* Card styling improvements */
.ldr-card {
    background-color: var(--bg-secondary);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.ldr-card-content {
    padding: 1.75rem;
}

/* Form */
.settings-form {
    margin-top: 1rem;
}

/* Tabs */
.settings-tabs {
    display: flex;
    margin: 0 0 1.5rem 0;
    overflow-x: auto;
    white-space: nowrap;
    gap: 0.75rem;
}

.settings-tab {
    padding: 0.6rem 1.2rem;
    cursor: pointer;
    color: var(--text-secondary);
    font-weight: 500;
    background-color: var(--bg-tertiary);
    border-radius: 2rem;
    transition: all 0.2s;
    text-align: center;
    border: 1px solid var(--border-color);
}

.settings-tab:hover {
    color: var(--text-primary);
    background-color: rgba(110, 79, 246, 0.1);
}

.settings-tab.active {
    color: var(--text-primary);
    background-color: var(--accent-primary);
    box-shadow: 0 0 10px rgba(110, 79, 246, 0.3);
    border-color: var(--accent-primary);
}

/* Remove the underline since we're using background color */
.settings-tab.active::after {
    display: none;
}

/* Cards */
.settings-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.settings-card {
    background-color: rgba(30, 35, 45, 0.7);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.settings-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
}

.settings-card-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    color: var(--text-primary);
    background-color: rgba(255, 255, 255, 0.1);
}

.settings-card h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.settings-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.settings-card-footer {
    margin-top: auto;
    display: flex;
    justify-content: flex-end;
}

/* Sections */
.settings-section {
    margin-bottom: 1.5rem;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.settings-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.25rem;
    cursor: pointer;
    background-color: rgba(40, 45, 60, 0.5);
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.2s;
}

.settings-section-header:hover {
    background-color: rgba(50, 55, 70, 0.9);
}

.settings-section-title {
    font-weight: 600;
    font-size: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    max-width: 80%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.settings-section-title i {
    margin-right: 0.5rem;
    color: var(--accent-primary);
    opacity: 0.8;
}

.settings-toggle-icon {
    transition: transform 0.3s;
    color: var(--accent-primary);
}

.settings-section-header.collapsed .settings-toggle-icon {
    transform: rotate(-90deg);
}

.settings-section-body {
    padding: 1.25rem;
    border-top: none;
}

.settings-group {
    margin-bottom: 1.5rem;
}

.settings-group-title {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1rem;
    color: var(--text-primary);
    display: flex;
    align-items: center;
}

.settings-group-title i {
    margin-right: 8px;
}

.settings-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.settings-item {
    margin-bottom: 1.25rem;
    padding-bottom: 1.25rem;
    border-bottom: 1px solid var(--border-color);
}

.settings-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.settings-item-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    min-height: 28px;
}

.settings-item-header label {
    font-weight: 600;
    margin-right: 0.5rem;
    color: var(--text-primary);
    max-width: 400px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.settings-item-header label[title] {
    cursor: help;
}

.settings-item label {
    font-weight: 600;
    margin-right: 0.5rem;
    color: var(--text-primary);
}

.settings-tooltip {
    color: var(--text-secondary);
    cursor: help;
}

.settings-tooltip:hover::after {
    content: attr(title);
    position: absolute;
    top: 100%;
    right: 0;
    background-color: rgba(0, 0, 0, 0.9);
    color: #fff;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    width: 200px;
    white-space: normal;
    z-index: 10;
    margin-top: 5px;
}

.settings-tooltip:hover::before {
    content: '';
    position: absolute;
    top: 100%;
    right: 5px;
    border-width: 5px;
    border-style: solid;
    border-color: transparent transparent rgba(0, 0, 0, 0.9) transparent;
    z-index: 10;
}

/* Inputs */
.settings-input,
.settings-textarea,
.settings-select {
    width: 100%;
    padding: 0.75rem;
    font-size: 0.9rem;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.settings-textarea {
    min-height: 120px;
    font-family: monospace;
    white-space: pre;
}

.settings-textarea.json-content {
    font-family: 'Courier New', Courier, monospace;
}

.settings-input:focus,
.settings-textarea:focus,
.settings-select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(110, 79, 246, 0.3);
}

.settings-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%236b7280' viewBox='0 0 16 16'%3E%3Cpath d='M8 11l-6-6 1.5-1.5L8 8l4.5-4.5L14 5l-6 6z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    padding-right: 2.5rem;
}

.settings-checkbox-container {
    margin: 0.5rem 0;
}

.settings-checkbox-container .checkbox-label {
    margin-bottom: 0.25rem;
}

.json-property-item .checkbox-label {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
}

.settings-checkbox {
    width: 18px;
    height: 18px;
    background-color: rgba(20, 25, 35, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-range {
    flex: 1;
}

.settings-range-container {
    display: flex;
    align-items: center;
}

.settings-range-value {
    min-width: 40px;
    margin-left: 1rem;
    padding: 0.25rem 0.5rem;
    background-color: var(--accent-primary);
    color: white;
    border-radius: 0.25rem;
    text-align: center;
    font-size: 0.85rem;
}

.settings-help {
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
    padding-left: 24px;
}

.settings-error {
    border-color: var(--error-color) !important;
}

.settings-error-message {
    color: var(--error-color);
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.settings-actions {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 1.5rem;
}

.settings-modified {
    position: relative;
}

.json-content {
    font-family: monospace;
    white-space: pre-wrap;
    overflow-x: auto;
    font-size: 0.9rem;
    background-color: rgba(20, 25, 35, 0.7);
}

.settings-json-editor {
    position: relative;
    display: flex;
    flex-direction: column;
}

.settings-json-toolbar {
    display: flex;
    justify-content: flex-end;
    gap: 5px;
    margin-bottom: 5px;
    background-color: rgba(40, 45, 55, 0.9);
    padding: 5px;
    border-radius: 4px;
}

.json-toolbar-btn {
    background-color: rgba(30, 35, 45, 0.7);
    border: none;
    color: var(--text-primary);
    padding: 2px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.json-toolbar-btn:hover {
    background-color: rgba(50, 55, 65, 0.9);
}

.json-format-btn {
    display: flex;
    align-items: center;
}

.json-copy-btn {
    display: flex;
    align-items: center;
}

/* Alert Messages */
.alert {
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border-radius: 0.375rem;
    display: flex;
    align-items: center;
}

.alert i {
    margin-right: 0.75rem;
}

.alert-success {
    background-color: rgba(10, 207, 151, 0.2);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-error, .alert-danger {
    background-color: rgba(250, 92, 124, 0.2);
    color: var(--error-color);
    border-left: 4px solid var(--error-color);
}

.alert-info {
    background-color: rgba(64, 191, 255, 0.2);
    color: var(--accent-tertiary);
    border-left: 4px solid var(--accent-tertiary);
}

.alert-warning {
    background-color: rgba(249, 188, 11, 0.2);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

/* Misc */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: var(--text-secondary);
}

/* Loading indicator */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

.loading-spinner.centered {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    border-top-color: var(--accent-primary);
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 1rem;
    position: relative;
    left: 0;
    top: 0;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Additional styles for main settings page */
.settings-section h2 {
    margin-bottom: 1rem;
    color: var(--accent-primary);
    font-size: 1.3rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.ldr-form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

.form-row {
    display: flex;
    gap: 1rem;
}

.form-group.half {
    flex: 1;
}

.input-help {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-top: 0.3rem;
    line-height: 1.3;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group input {
    margin: 0;
}

.checkbox-group label {
    margin: 0;
}

.range-value {
    display: inline-block;
    margin-left: 0.5rem;
}

.config-raw {
    margin-top: 2rem;
    background-color: var(--bg-quaternary);
    padding: 1rem;
    border-radius: 4px;
    overflow: auto;
    font-family: monospace;
    font-size: 0.9rem;
    color: var(--text-primary);
    white-space: pre;
    min-height: 200px;
    max-height: 600px;
    border: 1px solid var(--border-color);
}

.toggle-raw-config {
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--accent-primary);
    font-weight: 500;
    padding: 0.5rem 1rem;
    background-color: rgba(74, 108, 247, 0.1);
    border-radius: 4px;
    width: fit-content;
}

.toggle-raw-config:hover {
    background-color: rgba(74, 108, 247, 0.2);
}

.config-raw-editor {
    background-color: var(--bg-quaternary);
    color: var(--text-primary);
    font-family: monospace;
    font-size: 0.9rem;
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    width: 100%;
    min-height: 400px;
    resize: vertical;
    display: block !important;
}

.editor-header {
    background-color: var(--bg-tertiary);
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.editor-header h3 {
    margin: 0;
    font-size: 1rem;
    color: var(--text-primary);
}

.editor-lang {
    background-color: var(--accent-primary);
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

/* Search controls */
.search-controls {
    margin-bottom: 1.5rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    border: 1px solid var(--border-color);
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: all 0.2s;
}

.search-input:focus {
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(110, 79, 246, 0.2);
    outline: none;
}

/* Settings-specific colorful info boxes */
.provider-info,
.search-info,
.report-info,
.app-info {
    margin-bottom: 1.5rem;
    padding: 1rem;
    border-radius: 0.375rem;
    border-left-width: 4px;
    border-left-style: solid;
}

.provider-info {
    background-color: rgba(110, 79, 246, 0.1);
    border-left-color: var(--accent-primary);
}

.provider-info h3 {
    color: var(--accent-primary);
}

.search-info {
    background-color: rgba(249, 188, 11, 0.1);
    border-left-color: var(--warning-color);
}

.search-info h3 {
    color: var(--warning-color);
}

.report-info {
    background-color: rgba(10, 207, 151, 0.1);
    border-left-color: var(--success-color);
}

.report-info h3 {
    color: var(--success-color);
}

.app-info {
    background-color: rgba(110, 79, 246, 0.15);
    border-left-color: var(--accent-secondary);
}

.app-info h3 {
    color: var(--accent-secondary);
}

.provider-info h3,
.search-info h3,
.report-info h3,
.app-info h3 {
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.provider-info p,
.search-info p,
.report-info p,
.app-info p {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.provider-info ul,
.search-info ul,
.report-info ul,
.app-info ul {
    margin-bottom: 0;
    padding-left: 1.5rem;
}

.provider-info li,
.search-info li,
.report-info li,
.app-info li {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

/* Collections config specific */
.file-path {
    background-color: var(--bg-secondary);
    padding: 0.75rem;
    border-radius: 4px;
    font-family: monospace;
    margin: 1rem 0;
    word-break: break-all;
    color: var(--text-primary);
}

.config-example {
    background-color: var(--bg-quaternary);
    color: var(--text-primary);
    font-family: monospace;
    font-size: 0.9rem;
    padding: 1rem;
    border-radius: 4px;
    overflow: auto;
    white-space: pre;
    margin: 1rem 0;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: 2rem;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
    gap: 0.5rem;
}

.btn i {
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--accent-primary);
    color: var(--text-primary);
    box-shadow: 0 0 10px rgba(110, 79, 246, 0.3);
}

.btn-primary:hover {
    background-color: var(--accent-secondary);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(110, 79, 246, 0.4);
}

.btn-outline {
    background-color: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-outline:hover {
    color: var(--text-primary);
    border-color: var(--accent-primary);
    background-color: rgba(110, 79, 246, 0.1);
}

/* Raw JSON Configuration */
.raw-config-section {
    margin-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    padding-top: 1.5rem;
}

.section-header {
    margin-bottom: 1.5rem;
}

.section-header h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
}

.section-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.json-editor {
    width: 100%;
    min-height: 300px;
    padding: 1rem;
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    resize: vertical;
    white-space: pre;
    overflow-x: auto;
}

.json-editor:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(110, 79, 246, 0.2);
}

.toggle-raw-config {
    display: inline-flex;
    align-items: center;
    padding: 0.6rem 1.2rem;
    margin-top: 2rem;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 2rem;
    cursor: pointer;
    color: var(--text-secondary);
    font-weight: 500;
    transition: all 0.2s;
    gap: 0.5rem;
}

.toggle-raw-config:hover {
    color: var(--text-primary);
    background-color: rgba(110, 79, 246, 0.1);
    border-color: var(--accent-primary);
}

/* JSON Expanded Controls */
.json-expanded-controls {
    background-color: rgba(30, 35, 45, 0.5);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
}

.json-property-controls {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.json-property-item {
    margin-bottom: 0.5rem;
    padding: 1rem;
    background-color: rgba(25, 30, 40, 0.8);
    border-radius: 6px;
    border: 1px solid var(--border-color);
    transition: transform 0.2s, box-shadow 0.2s, border-color 0.2s;
}

.json-property-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-color: rgba(110, 79, 246, 0.4);
}

/* Property label with ellipsis for long text */
.json-property-item .property-label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.95rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    cursor: pointer;
}

/* Add explicit title for hover tooltip on long labels */
.json-property-item .property-label:hover {
    color: var(--accent-primary);
}

/* Checkbox wrapper for clickable area */
.checkbox-wrapper {
    cursor: pointer;
    padding: 0.5rem;
    margin: -0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.checkbox-wrapper:hover {
    background-color: rgba(110, 79, 246, 0.1);
}

.json-property-item .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.5rem;
    cursor: pointer;
}

.json-property-item .checkbox-text {
    font-size: 0.9rem;
    color: var(--text-primary);
    cursor: pointer;
}

.json-property-item .settings-input,
.json-property-item .settings-select {
    width: 100%;
    padding: 0.7rem;
    font-size: 0.9rem;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: 4px;
}

.json-property-item .settings-input:focus,
.json-property-item .settings-select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 2px rgba(110, 79, 246, 0.3);
}

.json-property-item .settings-help {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin-top: 0.5rem;
    opacity: 0.8;
}

/* Animations for expanded controls */
.json-expanded-controls {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Checkbox styling */
.checkbox-label {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding-left: 0;
    cursor: pointer;
    position: relative;
}

.checkbox-label input[type="checkbox"] {
    position: relative;
    width: 18px;
    height: 18px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    border: 2px solid var(--accent-primary);
    border-radius: 3px;
    background-color: var(--bg-surface);
    cursor: pointer;
    margin: 0 8px 0 0;
    padding: 0;
    vertical-align: middle;
    transition: all 0.2s;
}

.checkbox-label input[type="checkbox"]:checked {
    background-color: var(--accent-primary);
    border-color: var(--accent-primary);
}

.checkbox-label input[type="checkbox"]:checked::after {
    content: '✓';
    color: white;
    font-size: 12px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.checkbox-label input[type="checkbox"]:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(110, 79, 246, 0.3);
}

.checkbox-text {
    margin-left: 0;
    color: var(--text-primary);
    font-size: 14px;
    vertical-align: middle;
}

.settings-item .checkbox-label {
    margin-top: 0;
    padding-left: 0;
    display: flex;
    align-items: center;
}

/* Boolean property items */
.json-property-item.boolean-property {
    cursor: pointer;
    transition: background-color 0.2s, transform 0.2s, box-shadow 0.2s, border-color 0.2s;
    position: relative;
}

.json-property-item.boolean-property:hover {
    background-color: rgba(25, 35, 55, 0.9);
    border-color: var(--accent-primary);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25);
}

.json-property-item.boolean-property:active {
    transform: translateY(0px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.25);
}

.json-property-item.boolean-property:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    cursor: pointer;
}

.json-property-item.boolean-property .checkbox-wrapper,
.json-property-item.boolean-property .checkbox-label {
    background-color: transparent;
    cursor: pointer;
    padding: 0;
    margin: 0;
    position: relative;
    z-index: 2;
}

.json-property-item.boolean-property .checkbox-wrapper:hover {
    background-color: transparent;
}

.json-property-item.boolean-property .checkbox-text {
    cursor: pointer;
    position: relative;
    z-index: 2;
}

.json-property-item.boolean-property .property-label {
    cursor: pointer;
    position: relative;
    z-index: 2;
}

.json-property-item.boolean-property input[type="checkbox"] {
    cursor: pointer;
    margin-right: 8px;
    position: relative;
    z-index: 3;
}

/* Add styling for Reset to Defaults button */
#reset-to-defaults-button {
    margin-left: 10px;
    background-color: #e74c3c;
    color: #fff;
    border: none;
}

#reset-to-defaults-button:hover {
    background-color: #c0392b;
}

/* Add styling for Fix Corrupted Settings button */
#fix-corrupted-button {
    margin-left: 10px;
    background-color: #3498db;
    color: #fff;
    border: none;
}

#fix-corrupted-button:hover {
    background-color: #2980b9;
}

.settings-item .checkbox-label {
    margin-top: 8px;
    padding-left: 0;
}

/* Saving indicator styles */
.saving {
    position: relative;
}

.saving:before {
    content: '';
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 10px;
    height: 10px;
    border: 2px solid rgba(100, 100, 255, 0.3);
    border-radius: 50%;
    border-top-color: #6060ff;
    animation: spinner 0.8s linear infinite;
    z-index: 10;
}

.checkbox-label.saving:before {
    left: -20px;
    top: 50%;
}

.settings-item.saving:before {
    left: -20px;
}

.form-group.saving:before {
    left: -20px;
}

@keyframes spinner {
    to {transform: translateY(-50%) rotate(360deg);}
}

/* Add styles for alert container and close button */
.settings-alert-container {
    margin-bottom: 1rem;
    width: 100%;
}

.settings-alert-container .alert {
    position: relative;
    padding-right: 30px;
}

.alert-close {
    position: absolute;
    right: 10px;
    top: 8px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    opacity: 0.7;
}

.alert-close:hover {
    opacity: 1;
}

/* Data Location Section Styles */
.data-location-section {
    background-color: var(--bg-secondary) !important;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

/* Ensure all child elements don't have white backgrounds */
.data-location-section * {
    background-color: transparent;
}

.data-location-section > div {
    background-color: transparent !important;
}

.data-location-section .alert-warning {
    background: linear-gradient(135deg, rgba(110, 79, 246, 0.15) 0%, rgba(145, 121, 240, 0.15) 100%) !important;
    background-color: transparent !important;
    border-left: 4px solid var(--accent-primary);
    color: var(--text-primary) !important;
    border-radius: 0.375rem;
    box-shadow: 0 2px 8px rgba(110, 79, 246, 0.2);
}

/* Override any default alert styles */
.data-location-section .alert {
    background-color: transparent !important;
}

.data-location-section .alert-warning strong {
    color: var(--accent-secondary);
}

.data-location-section .alert-warning i {
    color: var(--accent-primary);
}

.data-location-section .alert-warning small {
    color: var(--text-secondary);
    display: block;
    margin-top: 0.25rem;
}

.data-location-details {
    padding: 1rem;
}

.data-location-details .settings-item {
    padding: 0.75rem 1rem;
    margin-bottom: 0.75rem;
    background-color: var(--bg-tertiary) !important;
    border-radius: 0.375rem;
    border: 1px solid var(--border-color);
}

/* Override any card or container styles within data location */
.data-location-section .ldr-card,
.data-location-section .card-body,
.data-location-section .ldr-card-content {
    background-color: transparent !important;
}

.data-location-details .settings-label {
    color: var(--accent-secondary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.data-location-details .settings-label i {
    color: var(--accent-primary);
}

.data-location-details .settings-value code {
    background-color: rgba(0, 0, 0, 0.5);
    color: var(--accent-tertiary);
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.data-location-details .platform-defaults {
    list-style: none;
    padding: 0;
    margin-top: 0.5rem;
}

.data-location-details .platform-defaults li {
    padding: 0.375rem 0;
    color: var(--text-secondary);
}

.data-location-details .platform-defaults strong {
    color: var(--text-primary);
    margin-right: 0.5rem;
}

.data-location-details .badge-info {
    background-color: var(--accent-primary);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.data-location-details .text-success {
    color: var(--success-color);
}

.data-location-details .text-muted {
    color: var(--text-muted);
    font-style: italic;
}
