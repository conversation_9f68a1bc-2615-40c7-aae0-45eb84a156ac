/* Modern AI-themed dark interface */
:root {
  --bg-primary: #121212;
  --bg-secondary: #1e1e2d;
  --bg-tertiary: #2a2a3a;
  --accent-primary: #6e4ff6;
  --accent-secondary: #9179f0;
  --accent-tertiary: #40bfff;
  --text-primary: #f5f5f5;
  --text-secondary: #c0c0cc;
  --text-muted: #8a8aa0;
  --border-color: #343452;
  --success-color: #0acf97;
  --warning-color: #f9bc0b;
  --error-color: #fa5c7c;
  --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  --glow-effect: 0 0 15px rgba(110, 79, 246, 0.3);
  --gradient-bg: linear-gradient(135deg, #2a2a3a 0%, #1e1e2d 100%);

  /* Aliases for commonly used variables */
  --primary-color: var(--accent-primary);
  --card-bg: var(--bg-secondary);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  overflow-x: hidden;
}

a {
  color: var(--accent-tertiary);
  text-decoration: none;
  transition: color 0.2s;
}

a:hover {
  color: var(--accent-secondary);
}

/* Layout */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar */
.sidebar {
  width: 240px;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  z-index: 10;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-header h2 {
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 1.25rem;
  gap: 0.5rem;
  color: var(--accent-secondary);
}

.sidebar-header h2 i {
  color: var(--accent-primary);
}

.sidebar-nav {
  flex: 1;
  padding: 1rem 0;
}

.sidebar-nav ul {
  list-style: none;
  position: relative;
}

.sidebar-nav li {
  padding: 0.75rem 1.5rem;
  margin-bottom: 0.25rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--text-secondary);
  border-left: 3px solid transparent;
  transition: all 0.2s;
  position: relative;
  min-height: 52px; /* Ensures a reasonable clickable height */
}

.sidebar-nav li a {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1.5rem;
  color: inherit;
  text-decoration: none;
  z-index: 2;
}

.sidebar-nav li i {
  width: 20px;
  color: inherit;
}

.sidebar-nav li:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.sidebar-nav li a:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: -2px;
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

.sidebar-nav li a:focus .nav-shortcut {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

.sidebar-nav li.active {
  color: var(--accent-primary);
  background-color: rgba(110, 79, 246, 0.1);
  border-left-color: var(--accent-primary);
}

.sidebar-nav li.active i,
.sidebar-nav li.active a {
  color: inherit;
}

/* Navigation shortcuts */
.nav-shortcut {
  margin-left: auto;
  background-color: var(--bg-tertiary);
  color: var(--text-muted);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 1.5rem;
  text-align: center;
  transition: all 0.2s;
}

.sidebar-nav li:hover .nav-shortcut {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

.sidebar-nav li.active .nav-shortcut {
  background-color: var(--accent-primary);
  color: var(--text-primary);
}

.sidebar-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  color: var(--text-muted);
  font-size: 0.875rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.nav-hint {
  font-size: 0.75rem;
  opacity: 0.7;
  margin: 0;
}

.nav-hint kbd {
  background-color: var(--bg-tertiary);
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
  font-size: 0.7rem;
  border: 1px solid var(--border-color);
}

/* Main Content */
.main-content {
  flex: 1;
  padding: 5rem 2rem 2rem 2rem; /* Top padding accounts for fixed top bar */
  margin-left: 240px;
  width: calc(100% - 240px);
  position: relative;
}

/* Top Bar with User Info and Logout */
.top-bar {
  position: fixed;
  top: 0;
  right: 0;
  left: 240px;
  height: 60px;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 2rem;
  z-index: 100;
}

.top-bar-right {
  display: flex;
  align-items: center;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info {
  color: var(--text-secondary);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logout-form {
  display: inline;
}

.logout-btn {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.logout-btn:hover {
  background-color: var(--error-color);
  color: white;
  border-color: var(--error-color);
  text-decoration: none;
}

/* Add padding to bottom of main content when log panel is visible */
.page#research-progress.active ~ .main-content,
.page#research-results.active ~ .main-content {
  padding-bottom: 42vh; /* Log panel max height + some extra */
}

.page {
  display: none;
}

.page.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.page-header {
  margin-bottom: 1.5rem;
}

.page-header h1 {
  font-weight: 600;
  font-size: 1.75rem;
  color: var(--text-primary);
}

/* Cards */
.ldr-card {
  background-color: var(--bg-secondary);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  margin-bottom: 1.5rem;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.ldr-card-content {
  padding: 1.5rem;
}

/* Form Elements */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-secondary);
}

textarea, input[type="text"] {
  width: 100%;
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  font-family: inherit;
  font-size: 0.95rem;
  resize: vertical;
  transition: border-color 0.2s, box-shadow 0.2s;
}

textarea:focus, input[type="text"]:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(110, 79, 246, 0.2);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
}

.btn-primary {
  background-color: var(--accent-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--accent-secondary);
  box-shadow: var(--glow-effect);
  color: white;
}

.btn-outline {
  background-color: transparent;
  color: var(--accent-tertiary);
  border: 1px solid var(--accent-tertiary);
}

.btn-outline:hover {
  background-color: rgba(64, 191, 255, 0.1);
}

.delete-btn {
  color: var(--error-color);
  border-color: var(--error-color);
}

.delete-btn:hover {
  background-color: rgba(250, 92, 124, 0.1);
}

.terminate-btn {
  color: #e74c3c;
  border-color: #e74c3c;
}

.terminate-btn:hover {
  background-color: rgba(231, 76, 60, 0.1);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
}

/* Mode Selection */
.mode-selection {
  display: flex;
  gap: 1rem;
}

.mode-option {
  flex: 1;
  background-color: var(--bg-tertiary);
  border-radius: 8px;
  padding: 1.25rem;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mode-option:hover {
  background-color: var(--bg-primary);
}

.mode-option.active {
  border-color: var(--accent-primary);
  background-color: rgba(110, 79, 246, 0.1);
}

.mode-icon {
  width: 40px;
  height: 40px;
  background-color: var(--accent-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.mode-option.active .mode-icon {
  background-color: var(--accent-secondary);
  box-shadow: var(--glow-effect);
}

.mode-info h3 {
  margin-bottom: 0.25rem;
  color: var(--text-primary);
}

.mode-info p {
  color: var(--text-muted);
  font-size: 0.85rem;
}

/* Progress Page */
.progress-info {
  padding: 1rem 0;
}

.current-query-container {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.current-query-label {
  font-weight: 600;
  color: var(--text-secondary);
  margin-right: 0.5rem;
  min-width: 120px;
}

.current-query {
  flex: 1;
  color: var(--text-primary);
  padding: 0.5rem;
  background-color: var(--bg-tertiary);
  border-radius: 6px;
  font-family: 'JetBrains Mono', monospace;
  font-size: 0.9rem;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

.progress-container {
  margin: 1.5rem 0;
  position: relative;
}

.progress-bar {
  width: 100%;
  height: 10px;
  background-color: var(--bg-tertiary);
  border-radius: 5px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background-color: var(--accent-primary);
  border-radius: 5px;
  transition: width 0.5s ease;
}

.progress-percentage {
  position: absolute;
  right: 0;
  top: -1.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
}

.status-container, .task-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.status-label, .task-label {
  font-weight: 600;
  color: var(--text-secondary);
  margin-right: 0.5rem;
  min-width: 120px;
}

.status-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
}

.status-indicator.status-completed {
  background-color: rgba(10, 207, 151, 0.2);
  color: var(--success-color);
}

.status-indicator.status-failed,
.status-indicator.status-error {
  background-color: rgba(250, 92, 124, 0.2);
  color: var(--error-color);
}

.status-indicator.status-in_progress {
  background-color: rgba(64, 191, 255, 0.2);
  color: var(--accent-tertiary);
}

.task-text {
  flex: 1;
  color: var(--text-primary);
  line-height: 1.4;
}

.progress-actions {
  margin-top: 1.5rem;
  display: flex;
  gap: 1rem;
}

/* Notification settings and audio test button */
.notification-settings {
  margin: 15px 0;
  display: flex;
  justify-content: flex-end;
}

.audio-test {
  position: relative;
  display: inline-flex;
  align-items: center;
}

.audio-test .tooltip-text {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-left: 10px;
}

#test-audio-btn {
  padding: 5px 10px;
  font-size: 0.9rem;
}

/* Sound test dialog styling */
.sound-test-dialog {
  font-size: 0.9rem;
}

.sound-test-content {
  display: flex;
  flex-direction: column;
}

.sound-test-content h3 {
  margin-top: 0;
  margin-bottom: 15px;
}

.sound-test-content ul {
  list-style: none;
  padding: 0;
  margin: 15px 0;
}

.sound-test-content li {
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
}

.sound-test-content .test-tip {
  background-color: rgba(64, 191, 255, 0.1);
  border-radius: 6px;
  padding: 10px;
  margin: 10px 0;
  font-size: 0.85rem;
}

.sound-test-content button {
  margin-top: 10px;
  margin-right: 8px;
}

/* History List */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  background-color: var(--bg-tertiary);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--border-color);
  transition: all 0.2s;
  cursor: pointer;
  position: relative;
}

.history-item:hover {
  border-color: var(--accent-tertiary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.history-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.history-item-title {
  font-weight: 500;
  color: var(--text-primary);
}

.history-item-status {
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 500;
}

.status-completed {
  background-color: rgba(10, 207, 151, 0.15);
  color: var(--success-color);
}

.status-in-progress {
  background-color: rgba(64, 191, 255, 0.15);
  color: var(--accent-tertiary);
  animation: pulse 2s infinite;
}

.status-failed {
  background-color: rgba(250, 92, 124, 0.15);
  color: var(--error-color);
}

.status-suspended {
  background-color: rgba(243, 156, 18, 0.15);
  color: #ffc107;
}

.status-terminating {
  background-color: rgba(231, 76, 60, 0.15);
  color: #e74c3c;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.history-item-meta {
  display: flex;
  color: var(--text-muted);
  font-size: 0.85rem;
  margin-bottom: 0.75rem;
}

.history-item-date {
  margin-right: 1rem;
}

.history-item-mode {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.history-item-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  position: relative;
  z-index: 5;
}

.history-item-actions button {
  position: relative;
  z-index: 10;
}

/* Results Page */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-metadata {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.metadata-item {
  display: flex;
  flex-direction: column;
}

.metadata-label {
  font-size: 0.85rem;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
  opacity: 1 !important;
}

.metadata-value {
  color: var(--text-primary);
  font-weight: 500;
}

.results-content {
  line-height: 1.7;
}

.results-content h1 {
  font-size: 1.75rem;
  margin: 1.5rem 0 1rem;
  color: var(--text-primary);
}

.results-content h2 {
  font-size: 1.5rem;
  margin: 1.25rem 0 0.75rem;
  color: var(--accent-tertiary);
}

.results-content h3 {
  font-size: 1.25rem;
  margin: 1rem 0 0.5rem;
  color: var(--accent-secondary);
}

.results-content p {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

.results-content ul,
.results-content ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
  color: var(--text-secondary);
}

.results-content li {
  margin-bottom: 0.5rem;
}

.results-content code {
  background-color: var(--bg-tertiary);
  padding: 0.15rem 0.3rem;
  border-radius: 4px;
  font-family: 'SF Mono', 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
}

.results-content pre {
  background-color: var(--bg-tertiary);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  overflow-x: auto;
}

.results-content blockquote {
  border-left: 3px solid var(--accent-primary);
  padding-left: 1rem;
  margin-left: 0;
  margin-bottom: 1rem;
  color: var(--text-muted);
}

.results-content table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.results-content th {
  background-color: var(--bg-tertiary);
  text-align: left;
  padding: 0.75rem;
  border-bottom: 2px solid var(--border-color);
  color: var(--accent-tertiary);
}

.results-content td {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  color: var(--text-secondary);
}

/* Research Details Page */
.research-metadata {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.detail-progress-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.detail-progress-bar {
  flex: 1;
  height: 8px;
  background-color: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
}

.detail-progress-fill {
  height: 100%;
  width: 0%;
  background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
  border-radius: 4px;
  transition: width 0.5s ease;
}

.research-log-container {
  margin-top: 1.5rem;
}

.research-log-container h3 {
  margin-bottom: 1rem;
  color: var(--text-secondary);
  font-size: 1.1rem;
  font-weight: 500;
}

.research-log {
  max-height: 500px;
  overflow-y: auto;
  padding: 1rem;
  background-color: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.log-entry {
  padding: 0.75rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  gap: 1rem;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-entry-time {
  font-size: 0.8rem;
  color: var(--text-muted);
  min-width: 80px;
}

.log-entry-content {
  flex: 1;
}

.log-entry-message {
  color: var(--text-secondary);
  margin-bottom: 0.25rem;
}

.log-entry-progress {
  font-size: 0.85rem;
  color: var(--accent-secondary);
  font-weight: 500;
}

.detail-actions {
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.phase-highlight {
  color: var(--accent-tertiary);
  font-weight: 500;
}

.phase-init {
  color: var(--accent-tertiary);
}

.phase-search {
  color: var(--accent-secondary);
}

.phase-analysis {
  color: var(--warning-color);
}

.phase-complete {
  color: var(--success-color);
}

.phase-error {
  color: var(--error-color);
}

.phase-termination {
  color: #e74c3c;
  font-weight: bold;
}

.empty-state {
  padding: 2rem;
  text-align: center;
  color: var(--text-muted);
  font-style: italic;
}

.error-message {
  padding: 1rem;
  background-color: rgba(250, 92, 124, 0.1);
  border: 1px solid var(--error-color);
  border-radius: 8px;
  color: var(--error-color);
  margin: 1rem 0;
  font-size: 0.9rem;
}

/* Loading Spinner */
.loading-spinner {
  padding: 2rem 0;
}

.loading-spinner.centered {
  display: flex;
  justify-content: center;
  align-items: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(110, 79, 246, 0.2);
  border-left-color: var(--accent-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Mobile Tab Bar */
.mobile-tab-bar {
  display: none; /* Hidden by default */
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  z-index: 1000;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
}

.mobile-tab-bar ul {
  display: flex;
  height: 100%;
  list-style: none;
  padding: 0;
  margin: 0;
  width: 100%;
}

.mobile-tab-bar li {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: var(--text-secondary);
  transition: all 0.2s;
  padding: 0.5rem 0;
  min-width: 90px;
}

.mobile-tab-bar li i {
  font-size: 1.3rem;
  margin-bottom: 8px;
}

.mobile-tab-bar li span {
  font-size: 0.75rem;
  text-align: center;
  width: 100%;
}

.mobile-tab-bar li.active {
  color: var(--accent-primary);
}

.mobile-tab-bar li:hover {
  color: var(--text-primary);
}

/* External link styling for sidebar and mobile navigation - more subtle */
.sidebar-nav ul li.external-link {
    color: var(--text-primary);
    border-left: 3px solid #607d8b;
    margin-bottom: 10px;
    padding-left: 17px;
    opacity: 0.75;
}

.sidebar-nav ul li.external-link:hover {
    background-color: rgba(96, 125, 139, 0.1);
    opacity: 1;
    cursor: pointer;
}

.mobile-tab-bar ul li.external-link {
    color: var(--text-primary);
    opacity: 0.75;
}

.mobile-tab-bar ul li.external-link i {
    color: var(--text-primary);
}

.mobile-tab-bar ul li.external-link:hover {
    opacity: 1;
}

/* Responsive */
@media (max-width: 991px) {
  .sidebar {
    width: 60px;
    overflow: hidden;
  }

  /* Adjust top bar position for narrow sidebar */
  .top-bar {
    left: 60px;
  }

  /* Hide all text in the logo and center icon */
  .sidebar-header {
    padding: 1rem 0;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
  }

  .sidebar-header h2 {
    font-size: 0;
    display: flex;
    justify-content: center;
    width: 100%;
  }

  .sidebar-header h2 i {
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Hide all text in nav items and center icons */
  .sidebar-nav {
    display: flex;
    flex-direction: column;
    padding-top: 0.5rem;
  }

  .sidebar-nav ul {
    width: 100%;
  }

  .sidebar-nav li {
    text-indent: -9999px;
    white-space: nowrap;
    padding: 1rem 0;
    text-align: center;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sidebar-nav li i {
    text-indent: 0;
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Hide navigation shortcuts on small screens */
  .nav-shortcut {
    display: none;
  }

  /* Active state fix for icon view */
  .sidebar-nav li.active {
    border-left-width: 3px;
  }

  /* Hide footer */
  .sidebar-footer {
    display: none;
  }

  /* Adjust main content */
  .main-content {
    margin-left: 60px;
    width: calc(100% - 60px);
  }

  .mode-selection {
    flex-direction: column;
  }
}

@media (max-width: 767px) {
  body {
    padding-bottom: 60px; /* Add padding to body for the tab bar */
  }

  .main-content {
    margin-left: 0;
    width: 100%;
    padding-bottom: 70px; /* Account for mobile tab bar */
  }

  /* Adjust top bar for mobile */
  .top-bar {
    left: 0;
    padding: 0 1rem;
    height: 50px;
  }

  .user-info {
    font-size: 0.85rem;
  }

  .logout-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  /* Adjust padding for mobile when log panel is visible */
  .page#research-progress.active ~ .main-content,
  .page#research-results.active ~ .main-content {
    padding-bottom: calc(42vh + 60px); /* Log panel + mobile tab bar */
  }

  .sidebar {
    width: 0;
    transform: translateX(-100%);
  }

  .sidebar.active {
    width: 240px;
    transform: translateX(0);
  }

  /* Restore text in logo when sidebar is active on mobile */
  .sidebar.active .sidebar-header h2 {
    font-size: 1.25rem;
  }

  /* Restore text in nav items when sidebar is active on mobile */
  .sidebar.active .sidebar-nav li {
    text-indent: 0;
    padding: 0.75rem 1.5rem;
    text-align: left;
  }

  .sidebar.active .sidebar-nav li i {
    display: inline;
    width: 20px;
    margin-right: 0.75rem;
    font-size: 1rem;
  }

  .sidebar.active .sidebar-footer {
    display: flex;
  }

  /* Show mobile tab bar */
  .mobile-tab-bar {
    display: flex !important; /* Use !important to ensure it displays */
  }
}

.progress-status {
  margin-bottom: 1.25rem;
  padding: 0.5rem 0;
  font-weight: 500;
}

.progress-actions {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

/* File path styling from the first version */
.file-path {
  background-color: #1a1a1a;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  margin: 15px 0;
  word-break: break-all;
  border: 1px solid #333;
}

.config-example {
  background-color: #0a0a0a;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  margin: 15px 0;
  overflow-x: auto;
  border: 1px solid #333;
  color: #e6e6e6;
}

.mt-4 {
  margin-top: 20px;
}

/* Try again button styling from the second version */
#try-again-btn {
  margin-top: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

#try-again-btn:hover {
  background-color: #2980b9;
}

#try-again-btn i {
  margin-right: 5px;
}

/* Collapsible Log Panel styles from the third version */
.collapsible-log-panel {
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-tertiary);
    display: flex;
    flex-direction: column;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
    padding: 0;
    margin: 0;
    /* Instead of hiding, make it visible by default */
    display: flex;
}

/* Updated selector to ensure it's visible when needed */
.page#research-progress.active ~ .collapsible-log-panel,
.page#research-results.active ~ .collapsible-log-panel,
.page#research-progress .collapsible-log-panel,
.page#research-results .collapsible-log-panel {
    display: flex !important; /* Force display with !important */
}

/* Adjust for smaller screens */
@media (max-width: 991px) {
    .collapsible-log-panel {
        left: 60px; /* Adjust for collapsed sidebar */
    }
}

@media (max-width: 767px) {
    .collapsible-log-panel {
        left: 0; /* Full width on mobile */
        bottom: 60px; /* Account for mobile tab bar */
    }
}

.log-panel-header {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background: var(--gradient-bg);
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s;
    z-index: 101;
    border-radius: 8px 8px 0 0;
}

.log-panel-header:hover {
    background-color: var(--bg-secondary);
}

.log-panel-header .toggle-icon {
    margin-right: 0.75rem;
    transition: transform 0.3s ease;
}

.log-panel-header.collapsed .toggle-icon {
    transform: rotate(-90deg);
}

.log-indicator {
    margin-left: auto;
    background-color: var(--accent-primary);
    color: white;
    border-radius: 12px;
    padding: 0.1rem 0.5rem;
    font-size: 0.75rem;
    min-width: 1.5rem;
    text-align: center;
}

.log-panel-content {
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: height 0.3s ease;
}

.log-panel-content.collapsed {
    height: 0 !important;
    overflow: hidden;
    padding: 0;
    margin: 0;
    border: none;
}

.log-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    border-bottom: 1px solid var(--border-color);
    background-color: rgba(0, 0, 0, 0.2);
}

.log-filter {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.log-filter select {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.console-log {
    padding: 0.5rem;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.85rem;
    line-height: 1.4;
    overflow-y: auto;
    height: 100%;
}

.console-log-entry {
    padding: 0.5rem;
    display: flex;
    align-items: flex-start;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.console-log-entry:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

.console-log-entry .log-timestamp {
    color: var(--text-muted);
    min-width: 4.5rem;
    margin-right: 0.5rem;
    font-size: 0.75rem;
}

.console-log-entry .log-badge {
    display: inline-block;
    padding: 0 0.4rem;
    margin-right: 0.5rem;
    border-radius: 3px;
    font-size: 0.7rem;
    text-transform: uppercase;
    font-weight: bold;
    line-height: 1.4;
}

.console-log-entry .log-message {
    flex: 1;
    word-break: break-word;
    padding-right: 0.5rem;
    padding-left: 0.5rem;
}

/* Duplicate counter badge for logs */
.console-log-entry .duplicate-counter {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-left: 0.5rem;
    padding: 0.1rem 0.4rem;
    background-color: rgba(0, 0, 0, 0.15);
    border-radius: 10px;
    display: inline-block;
}

.console-log-entry.log-milestone .log-badge {
    background-color: var(--accent-primary);
    color: white;
}

.console-log-entry.log-error .log-badge {
    background-color: var(--error-color);
    color: white;
}

.console-log-entry.log-warning .log-badge {
  background-color: var(--warning-color);
  color: white;
}

.console-log-entry.log-debug .log-badge {
  background-color: var(--text-muted);
  color: white;
}

.console-log-entry.log-info .log-badge {
    background-color: var(--accent-tertiary);
    color: white;
}

.empty-log-message {
    color: var(--text-muted);
    padding: 1rem;
    text-align: center;
    font-style: italic;
}

/* Add warning message styling */
.warning-message {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border-color: rgba(255, 193, 7, 0.2);
}

/* Styling for search engine selection log entries */
.console-log-entry[data-log-type="info"][data-engine-selected="true"] {
    background-color: rgba(64, 191, 255, 0.05);
    border-left: 2px solid var(--accent-tertiary);
    padding-left: calc(0.5rem - 2px);
    font-weight: 500;
}

.console-log-entry[data-log-type="info"][data-engine-selected="true"] .log-message {
    color: var(--accent-tertiary);
}

/* Update existing error message styling if needed */
.error-message {
    display: none;
    margin: 15px 0;
    padding: 10px 15px;
    border-radius: 4px;
    border-left: 4px solid;
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border-color: rgba(220, 53, 69, 0.2);
    font-weight: 500;
}

.filter-buttons {
    display: flex;
    gap: 4px;
    justify-content: flex-start;
    margin-top: 0;
}

.small-btn {
    font-size: 0.75rem;
    padding: 4px 8px;
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.small-btn:hover {
    background-color: rgba(110, 79, 246, 0.2);
    color: var(--text-primary);
}

.small-btn.selected {
    background-color: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

/* Settings card styles */
.settings-card {
  background-color: var(--bg-secondary);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s;
  border: 1px solid var(--border-color);
}

.settings-card:hover {
  background-color: var(--bg-tertiary);
  border-color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: var(--card-shadow);
}

.settings-card .btn {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.settings-card .btn:hover {
  background-color: var(--accent-primary);
  color: white;
  border-color: var(--accent-primary);
}

/* Search Controls */
.search-controls {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 1rem;
}

.search-input {
  flex: 1;
  padding: 0.6rem 1rem;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  font-size: 0.9rem;
}

.search-input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(110, 79, 246, 0.2);
}

/* Toast Notifications */
#toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.toast {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    max-width: 350px;
    transform: translateX(120%);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    border-left: 4px solid transparent;
}

.toast.visible {
    transform: translateX(0);
    opacity: 1;
}

.toast i {
    font-size: 1.2rem;
}

.toast-success {
    border-left-color: var(--success-color);
}

.toast-success i {
    color: var(--success-color);
}

.toast-error {
    border-left-color: var(--error-color);
}

.toast-error i {
    color: var(--error-color);
}

.toast-info {
    border-left-color: var(--accent-tertiary);
}

.toast-info i {
    color: var(--accent-tertiary);
}

.toast-warning {
    border-left-color: var(--warning-color);
}

.toast-warning i {
    color: var(--warning-color);
}

/* Advanced Options */
.advanced-options-toggle {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin: 1.5rem 0 1rem;
    padding: 0.75rem 1rem;
    cursor: pointer;
    color: var(--accent-tertiary);
    font-weight: 600;
    transition: all 0.2s;
    border-radius: 8px;
    background-color: rgba(64, 191, 255, 0.05);
    border: 1px solid rgba(64, 191, 255, 0.1);
    width: 100%;
    text-align: left;
    font-family: inherit;
    font-size: inherit;
}

.advanced-options-toggle:hover {
    color: var(--accent-secondary);
    background-color: rgba(110, 79, 246, 0.1);
    border-color: rgba(110, 79, 246, 0.2);
}

.advanced-options-toggle .toggle-text {
    margin-right: 0.5rem;
}

.advanced-options-toggle i {
    transition: transform 0.3s ease;
    margin-left: auto;
}

.advanced-options-toggle.open i {
    transform: rotate(180deg);
}

.advanced-options-panel {
    background-color: rgba(42, 42, 58, 0.5);
    border-radius: 8px;
    padding: 0 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.1);
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    transition: all 0.3s ease;
}

/* Allow form controls to be interactive even when panel is collapsed */
.advanced-options-panel select,
.advanced-options-panel input,
.advanced-options-panel button,
.advanced-options-panel textarea {
    pointer-events: auto;
}

.advanced-options-toggle.open + .advanced-options-panel,
.advanced-options-panel.expanded {
    max-height: 1000px;
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
    padding: 1.5rem;
}

.form-row {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group.half {
    flex: 1;
}

.input-help {
    display: block;
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: 0.5rem;
}

.form-options {
    margin: 1.5rem 0;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.checkbox-label:hover {
    background-color: rgba(64, 191, 255, 0.05);
}

.checkbox-label input[type="checkbox"] {
    margin-right: 0.75rem;
}

/* Improved form controls for advanced options */
.ldr-form-control {
    width: 100%;
    padding: 0.75rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-family: inherit;
    font-size: 0.95rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

select.ldr-form-control {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%238a8aa0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
}

input[type="number"].ldr-form-control {
    appearance: textfield;
    -moz-appearance: textfield;
}

input[type="number"].ldr-form-control::-webkit-outer-spin-button,
input[type="number"].ldr-form-control::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.ldr-form-control:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(110, 79, 246, 0.15);
}

/* Notification Group Styles from app-refactoring */
.notification-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.notification-group .checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.notification-group .btn-sm {
  padding: 0.35rem 0.75rem;
  font-size: 0.85rem;
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.notification-group .btn-sm:hover {
  background-color: var(--accent-primary);
  color: white;
}

/* Custom Dropdown Component */
.custom-dropdown {
    position: relative;
    width: 100%;
    font-family: inherit;
    margin-bottom: 0.25rem;
}

.custom-dropdown-input {
    width: 100%;
    padding: 8px 12px;
    font-size: 0.95rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    cursor: pointer;
}

.custom-dropdown-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(110, 79, 246, 0.2);
}

.custom-dropdown-list {
    position: absolute;
    width: 100%;
    max-height: 220px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--bg-secondary);
    display: none;
    z-index: 1000;
    margin-top: 4px;
    box-shadow: var(--card-shadow);
}

.custom-dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    color: var(--text-primary);
    transition: background-color 0.15s ease;
}

.custom-dropdown-item:hover {
    background-color: var(--bg-tertiary);
}

.custom-dropdown-item.active {
    background-color: rgba(110, 79, 246, 0.2);
}

.custom-dropdown-item .highlight {
    color: var(--accent-primary);
    font-weight: 500;
}

.custom-dropdown-no-results {
    padding: 8px 12px;
    color: var(--text-muted);
    font-style: italic;
}

.custom-dropdown-footer {
    padding: 8px 12px;
    border-top: 1px solid var(--border-color);
    color: var(--accent-tertiary);
    font-size: 0.9em;
    background-color: rgba(110, 79, 246, 0.05);
}

.model-warning {
    color: var(--warning-color);
    font-size: 0.85rem;
    margin-top: 5px;
    display: none;
}

/* Form Layout Helpers */
.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group.half {
    flex: 1;
}

.input-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: var(--text-muted);
}

/* Star Rating Component */
.star-rating {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    cursor: pointer;
    font-size: 1.2rem;
}

.star-rating .star {
    color: #444;
    transition: color 0.2s ease;
    user-select: none;
    cursor: pointer;
}

.star-rating .star.active {
    color: #ffd700;
}

.star-rating .star.hover {
    color: #ffd700;
}

.metadata-item .star-rating {
    margin-left: 0.5rem;
}

/* History Page Enhancements */
.news-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.125rem 0.5rem;
    background: var(--accent-primary);
    color: white;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    margin-left: 0.5rem;
}

.news-indicator i {
    font-size: 0.875rem;
}

.subscribe-btn {
    background: transparent;
    border: 1px solid var(--accent-primary);
    color: var(--accent-primary);
    transition: all 0.2s ease;
}

.subscribe-btn:hover {
    background: var(--accent-primary);
    color: white;
    transform: translateY(-1px);
}

.subscribe-btn.subscribed {
    background: var(--success-color);
    border-color: var(--success-color);
    color: white;
    cursor: default;
}

.subscribe-btn.subscribed:hover {
    transform: none;
}
