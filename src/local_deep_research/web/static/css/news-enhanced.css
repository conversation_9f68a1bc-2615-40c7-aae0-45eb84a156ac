/* Enhanced News Page Styles - Modern Design Update */

/* Import base news styles */
@import 'news.css';

/* Enhanced Color Variables */
:root {
    --news-gradient-1: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --news-gradient-2: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --news-gradient-3: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --news-gradient-4: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --news-card-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    --news-card-hover-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    --news-border-radius: 16px;
}

/* Enhanced News Container */
.news-container {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--bg-primary) 0%, rgba(100, 108, 255, 0.02) 100%);
}

/* Enhanced News Cards */
.news-item {
    background: var(--bg-secondary);
    border-radius: var(--news-border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid transparent;
    box-shadow: var(--news-card-shadow);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.news-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--news-gradient-1);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.news-item:hover::before {
    opacity: 1;
}

.news-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--news-card-hover-shadow);
    border-color: rgba(100, 108, 255, 0.2);
}

/* Enhanced Headlines */
.news-headline {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--text-primary) 0%, rgba(100, 108, 255, 0.9) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1.5;
    transition: all 0.3s ease;
}

.news-item:hover .news-headline {
    background: var(--news-gradient-1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Meta Information */
.news-meta {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.news-category {
    background: var(--news-gradient-1);
    color: white;
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.news-impact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.impact-bar {
    width: 60px;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.impact-fill {
    height: 100%;
    background: var(--news-gradient-2);
    border-radius: 3px;
    transition: width 0.6s ease;
}

/* Enhanced News Content */
.news-content {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    padding: 1.25rem;
    margin: 1rem 0;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.news-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    overflow: hidden;
    border-radius: 8px;
}

.news-table th {
    background: linear-gradient(135deg, rgba(100, 108, 255, 0.1) 0%, rgba(100, 108, 255, 0.05) 100%);
    padding: 0.75rem 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid rgba(100, 108, 255, 0.2);
}

.news-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.news-table tr:hover td {
    background: rgba(100, 108, 255, 0.05);
}

/* Enhanced Source Links */
.news-sources {
    background: rgba(100, 108, 255, 0.05);
    border-radius: 12px;
    padding: 1rem;
    margin: 1rem 0;
    border: 1px solid rgba(100, 108, 255, 0.1);
}

.news-sources h6 {
    color: var(--accent-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.source-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
}

.source-link:hover {
    color: var(--accent-primary);
    transform: translateX(4px);
}

.source-link i {
    font-size: 0.85rem;
    opacity: 0.7;
}

/* Enhanced Action Buttons */
.news-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.vote-buttons {
    display: flex;
    gap: 1rem;
}

.vote-btn {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.vote-btn:hover {
    background: rgba(100, 108, 255, 0.1);
    border-color: rgba(100, 108, 255, 0.3);
    color: var(--accent-primary);
    transform: translateY(-2px);
}

.vote-btn.active {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.vote-count {
    font-weight: 600;
    margin-left: 0.25rem;
}

/* Enhanced View Report Button */
.btn-primary {
    background: var(--news-gradient-1);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

/* Enhanced Sidebar */
.subscriptions-card {
    background: var(--bg-secondary);
    border-radius: var(--news-border-radius);
    padding: 1.5rem;
    box-shadow: var(--news-card-shadow);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.subscription-item {
    background: rgba(100, 108, 255, 0.05);
    border: 1px solid rgba(100, 108, 255, 0.1);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.subscription-item:hover {
    background: rgba(100, 108, 255, 0.1);
    border-color: rgba(100, 108, 255, 0.3);
    transform: translateX(4px);
}

.subscription-item.active {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

/* Enhanced Search Box */
.news-search-box {
    background: var(--bg-secondary);
    border-radius: var(--news-border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--news-card-shadow);
}

.search-input-group {
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 0.5rem;
    transition: all 0.3s ease;
}

.search-input-group:focus-within {
    background: rgba(100, 108, 255, 0.05);
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 4px rgba(100, 108, 255, 0.1);
}

/* Enhanced Topic Pills */
.topic-pill {
    background: linear-gradient(135deg, rgba(100, 108, 255, 0.1) 0%, rgba(100, 108, 255, 0.05) 100%);
    color: var(--accent-primary);
    padding: 0.4rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    border: 1px solid rgba(100, 108, 255, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.topic-pill:hover {
    background: var(--accent-primary);
    color: white;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(100, 108, 255, 0.3);
}

/* Enhanced Loading States */
.skeleton-loader {
    background: linear-gradient(90deg,
        rgba(255, 255, 255, 0.05) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        rgba(255, 255, 255, 0.05) 100%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s ease-in-out infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Enhanced Priority Indicators */
.news-item.priority-high {
    border-left: 4px solid transparent;
    background: linear-gradient(90deg, rgba(250, 112, 154, 0.1) 0%, var(--bg-secondary) 10%);
}

.news-item.priority-high::after {
    content: '⚡';
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.2rem;
    opacity: 0.8;
}

.news-item.priority-medium {
    border-left: 4px solid transparent;
    background: linear-gradient(90deg, rgba(254, 225, 64, 0.1) 0%, var(--bg-secondary) 10%);
}

.news-item.priority-low {
    border-left: 4px solid transparent;
    background: linear-gradient(90deg, rgba(0, 242, 254, 0.1) 0%, var(--bg-secondary) 10%);
}

/* Enhanced Animations */
.news-item {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.news-item:nth-child(1) { animation-delay: 0.1s; }
.news-item:nth-child(2) { animation-delay: 0.2s; }
.news-item:nth-child(3) { animation-delay: 0.3s; }
.news-item:nth-child(4) { animation-delay: 0.4s; }
.news-item:nth-child(5) { animation-delay: 0.5s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .news-item {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .news-headline {
        font-size: 1.2rem;
    }

    .news-meta {
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    .news-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .vote-buttons {
        width: 100%;
        justify-content: space-around;
    }
}
