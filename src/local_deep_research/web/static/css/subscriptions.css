/* Subscriptions Management Page Styles */

/* Import LDR color variables */
:root {
    --bg-primary: #121212;
    --bg-secondary: #1e1e2d;
    --bg-tertiary: #2a2a3a;
    --accent-primary: #6e4ff6;
    --accent-secondary: #9179f0;
    --accent-tertiary: #40bfff;
    --text-primary: #f5f5f5;
    --text-secondary: #c0c0cc;
    --text-muted: #8a8aa0;
    --border-color: #343452;
}

.subscriptions-page {
    padding: 20px;
    max-width: 1400px;
    margin: 0 auto;
    background-color: var(--bg-primary);
    min-height: 100vh;
    color: var(--text-primary);
}

/* Header Section */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.header-content h1 {
    margin: 0;
    font-size: 2rem;
    color: var(--text-primary);
}

.header-content .subtitle {
    margin: 5px 0 0 0;
    color: var(--text-secondary);
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* Stats Overview */
.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #2c5aa0;
    margin-bottom: 5px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Scheduler Status Bar */
.scheduler-status-bar {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.scheduler-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--text-primary);
}

.scheduler-label {
    font-weight: 500;
}

.scheduler-status {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--text-muted);
    display: inline-block;
}

.status-indicator.active {
    background: #4caf50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
}

.status-indicator.inactive {
    background: #f44336;
}

.status-indicator.checking {
    background: #ff9800;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.scheduler-details {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-left: 10px;
}

.scheduler-actions {
    display: flex;
    gap: 10px;
}

/* Main Container */
.subscriptions-container {
    display: flex;
    gap: 20px;
}

/* Folders Sidebar */
.folders-sidebar {
    width: 250px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    height: fit-content;
}

.folders-sidebar h3 {
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    color: var(--text-primary);
}

.folder-list {
    margin: 0 0 15px 0;
}

.folder-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    margin: 0 -15px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.folder-item:hover {
    background-color: var(--bg-tertiary);
}

.folder-item.active {
    background-color: #e3f2fd;
    color: #2c5aa0;
}

.folder-count {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
}

.folder-item.active .folder-count {
    background: #2c5aa0;
    color: white;
}

/* Main Content Area */
.subscriptions-main {
    flex: 1;
}

/* Filter Bar */
.filter-bar {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    gap: 15px;
    align-items: center;
}

.search-box {
    flex: 1;
    position: relative;
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-box input {
    width: 100%;
    padding: 8px 15px 8px 35px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    outline: none;
}

.search-box input:focus {
    border-color: #2c5aa0;
}

.filter-options {
    display: flex;
    gap: 10px;
}

.filter-options select {
    min-width: 150px;
}

/* Subscriptions Grid */
.subscriptions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

/* Subscription Card */
.subscription-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    transition: box-shadow 0.2s;
}

.subscription-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    gap: 15px;
}

.card-header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--text-primary);
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
}

.card-actions {
    display: flex;
    gap: 5px;
    flex-shrink: 0;
}

.btn-icon {
    background: none;
    border: none;
    padding: 5px 8px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: color 0.2s;
}

.btn-icon:hover {
    color: #2c5aa0;
}

.btn-icon.btn-danger {
    color: #dc3545;
}

.btn-icon.btn-danger:hover {
    color: #c82333;
    background-color: rgba(220, 53, 69, 0.1);
    border-radius: 4px;
}

.card-body {
    padding: 15px;
}

.query-text {
    color: var(--text-secondary);
    margin-bottom: 15px;
    font-style: italic;
}

.subscription-meta {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.status-badge,
.frequency-badge,
.folder-badge {
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 0.85rem;
}

.status-badge.status-active {
    background: #d4edda;
    color: #155724;
}

.status-badge.status-paused {
    background: #f8d7da;
    color: #721c24;
}

.frequency-badge {
    background: #e2e3e5;
    color: #383d41;
}

.folder-badge {
    background: #d1ecf1;
    color: #0c5460;
}

.subscription-stats {
    margin: 15px 0;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.stat-item {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.stat-item i {
    margin-right: 5px;
}

.source-link {
    color: #2c5aa0;
    text-decoration: none;
}

.source-link:hover {
    text-decoration: underline;
}

.last-updated,
.next-update {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.last-updated i,
.next-update i {
    margin-right: 5px;
}

/* History Modal Styles */
.history-summary {
    background: var(--bg-tertiary);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.history-summary p {
    margin: 5px 0;
}

.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 10px;
    transition: background-color 0.2s;
}

.history-item:hover {
    background-color: var(--bg-tertiary);
}

.history-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.history-item-header a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
}

.history-item-header a:hover {
    color: #2c5aa0;
}

.history-date {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.history-item-meta {
    display: flex;
    gap: 10px;
}

.duration {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.topics-list {
    display: flex;
    gap: 5px;
    margin-top: 5px;
    flex-wrap: wrap;
}

.topic-badge {
    background: var(--accent-primary);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 4rem;
    color: var(--text-muted);
    margin-bottom: 20px;
}

.empty-state h3 {
    margin: 0 0 10px 0;
}

/* Loading State */
.loading-placeholder {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

/* Modal Customizations */
.modal-body .form-text {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .subscriptions-container {
        flex-direction: column;
    }

    .folders-sidebar {
        width: 100%;
        margin-bottom: 20px;
    }

    .stats-overview {
        grid-template-columns: repeat(2, 1fr);
    }

    .subscriptions-grid {
        grid-template-columns: 1fr;
    }

    .filter-bar {
        flex-direction: column;
    }

    .filter-options {
        width: 100%;
        flex-direction: column;
    }

    .filter-options select {
        width: 100%;
    }
}
