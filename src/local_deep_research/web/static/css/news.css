/* News Page Styles - Following LDR Design System */

/* Import LDR color variables */
:root {
    --bg-primary: #121212;
    --bg-secondary: #1e1e2d;
    --bg-tertiary: #2a2a3a;
    --accent-primary: #6e4ff6;
    --accent-secondary: #9179f0;
    --accent-tertiary: #40bfff;
    --text-primary: #f5f5f5;
    --text-secondary: #c0c0cc;
    --text-muted: #8a8aa0;
    --border-color: #343452;
}

/* Alert Container */
.settings-alert-container {
    display: none;
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

/* Page wrapper */
.news-page-wrapper {
    width: 100%;
    padding: 0;
    box-sizing: border-box;
    background-color: var(--bg-primary);
    min-height: 100vh;
}

/* Override Bootstrap white backgrounds */
.modal-content {
    background-color: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
}

.dropdown-menu {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-color) !important;
}

.dropdown-item {
    color: var(--text-primary) !important;
}

.dropdown-item:hover {
    background-color: var(--bg-tertiary) !important;
}

.ldr-form-control, .form-select {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--border-color) !important;
    color: var(--text-primary) !important;
}

.ldr-form-control:focus, .form-select:focus {
    background-color: var(--bg-tertiary) !important;
    border-color: var(--accent-primary) !important;
    color: var(--text-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(110, 79, 246, 0.25) !important;
}

/* Layout - Two column without left sidebar */
.news-container {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 1.5rem;
    height: calc(100vh - 140px);
    margin-top: 1rem;
    width: calc(100% - 2rem);
    max-width: calc(100vw - 2rem);
    box-sizing: border-box;
    margin-left: 1rem;
    margin-right: 1rem;
}

@media (max-width: 1400px) {
    .news-container {
        grid-template-columns: 1fr 280px;
        gap: 1rem;
    }
}

@media (max-width: 1200px) {
    .news-container {
        grid-template-columns: 1fr;
    }

    .subscriptions-card {
        display: none;
    }
}

/* Integrated Search Section */
.integrated-search-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

.search-input-group {
    display: flex;
    align-items: center;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.25rem;
    transition: all 0.3s ease;
}

.search-input-group:focus-within {
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(100, 108, 255, 0.1);
}

.search-icon {
    padding: 0 0.75rem;
    color: var(--text-secondary);
}

.search-input {
    flex: 1;
    background: transparent !important;
    border: none !important;
    padding: 0.5rem 0;
    color: var(--text-primary);
}

.search-input:focus {
    box-shadow: none !important;
    outline: none;
}

.search-controls {
    display: flex;
    gap: 0.25rem;
    padding-right: 0.25rem;
}

/* Removed search-type-select styles as dropdown was removed */

.quick-actions-row {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.quick-actions-row .btn {
    min-width: 140px;
    font-size: 0.9rem;
}

/* Query Template */
.query-template {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: 6px;
    border: 1px solid var(--border-color);
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.template-header h3 {
    font-size: 0.9rem;
    margin: 0;
}

.template-content {
    font-size: 0.75rem;
    max-height: 200px;
    overflow-y: auto;
    margin: 0.5rem 0;
    padding: 0.5rem;
    background: var(--bg-primary);
    border-radius: 4px;
}

.template-actions {
    display: flex;
    gap: 0.5rem;
}

/* View Options */
.view-options {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.view-options h3 {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.option-group {
    margin-bottom: 1rem;
}

.option-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* Toggle Switch */
.toggle-switch {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.toggle-switch input {
    display: none;
}

.toggle-slider {
    width: 40px;
    height: 20px;
    background: var(--bg-tertiary);
    border-radius: 20px;
    position: relative;
    transition: background 0.3s;
    margin-right: 0.5rem;
}

.toggle-slider::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    background: var(--text-secondary);
    border-radius: 50%;
    top: 2px;
    left: 2px;
    transition: transform 0.3s;
}

.toggle-switch input:checked + .toggle-slider {
    background: var(--accent-primary);
}

.toggle-switch input:checked + .toggle-slider::after {
    transform: translateX(20px);
    background: var(--text-primary);
}

/* Feed Section */
.news-feed-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    height: 100%;
    overflow: hidden;
}

.feed-header-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
}

.feed-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.feed-header h1 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--text-primary);
}

/* Search Container */
.news-search-container {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.news-search-box {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
    background: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.news-search-box:focus-within {
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(110, 79, 246, 0.1);
}

.news-search-box .search-icon {
    position: absolute;
    left: 1rem;
    color: var(--text-muted);
    pointer-events: none;
}

.news-search-box input {
    flex: 1;
    background: transparent;
    border: none;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.news-search-box input:focus {
    outline: none;
    box-shadow: none;
}

.news-search-box input::placeholder {
    color: var(--text-muted);
}

#search-btn {
    background: var(--accent-primary);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0 8px 8px 0;
    font-weight: 500;
    transition: all 0.3s ease;
}

#search-btn:hover {
    background: var(--accent-secondary);
}

.search-type-select {
    width: auto;
    min-width: 150px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
}

.feed-controls {
    display: flex;
    gap: 0.5rem;
}

/* Horizontal Filters Bar */
.filters-bar {
    background: var(--bg-tertiary);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.filter-section {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.subscriptions-section {
    flex: 1;
}

/* Horizontal subscriptions */
.subscriptions-horizontal {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
    padding: 0.25rem 0;
    flex: 1;
    scrollbar-width: thin;
    scrollbar-color: var(--accent-primary) var(--bg-secondary);
}

.subscriptions-horizontal::-webkit-scrollbar {
    height: 6px;
}

.subscriptions-horizontal::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 3px;
}

.subscriptions-horizontal::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 3px;
}

.subscriptions-horizontal .subscription-item {
    padding: 0.375rem 0.75rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
    flex-shrink: 0;
}

.subscriptions-horizontal .subscription-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--accent-primary);
    transform: translateY(-1px);
}

.subscriptions-horizontal .subscription-item.active {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

/* Time and impact filters inline */
.time-filter-group,
.impact-filter-group {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.impact-slider {
    width: 120px;
}

/* Query template integrated */
.query-template-integrated {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--accent-primary);
    border-radius: 8px;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Priority Alert */
.priority-alert {
    padding: 1rem;
    background: var(--warning-color);
    color: #000;  /* Black text on warning background */
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

/* News Items */
.news-items-card {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.news-items-card .ldr-card-content {
    flex: 1;
    overflow-y: auto;
}

.news-items {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Active research card */
.active-research-card {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(110, 79, 246, 0.1) 100%);
    border: 2px solid var(--accent-primary);
    box-shadow: 0 0 20px rgba(110, 79, 246, 0.2);
    position: relative;
    overflow: hidden;
}

.active-research-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg,
        var(--accent-primary) 0%,
        var(--accent-secondary) 50%,
        var(--accent-primary) 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.active-research-card .progress {
    margin: 1rem 0;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
}

.active-research-card .progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    transition: width 0.5s ease;
}

.spinning {
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* News Card */
.news-item {
    padding: 2.5rem;
    background: var(--bg-tertiary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    transition: all 0.2s;
    position: relative;
    cursor: pointer;
    min-height: 350px;
}

/* Read/Unread states */
.news-item.is-unread {
    border-left: 4px solid var(--accent-primary);
}

.news-item.is-read {
    opacity: 0.85;
}

.news-item.is-read .news-headline {
    color: var(--text-secondary);
}

/* News item header */
.news-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.news-actions-menu {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.news-actions-menu .dropdown {
    position: relative;
}

.news-actions-menu .dropdown-menu {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 180px;
}

.news-actions-menu .dropdown-item {
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.2s;
}

.news-actions-menu .dropdown-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.news-actions-menu .dropdown-item i {
    width: 1.2rem;
    text-align: center;
}

.news-actions-menu .dropdown-divider {
    border-color: var(--border-color);
    margin: 0.5rem 0;
}

.btn-ghost {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
    border-radius: 4px;
}

.btn-ghost:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Saved items indicator */
.save-btn i.bi-bookmark-fill {
    color: var(--warning-color);
}

.subscription-item .subscription-type {
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    opacity: 0.7;
}

.expand-icon {
    transition: transform 0.3s;
}

.news-item:hover {
    border-color: var(--accent-primary);
    box-shadow: var(--glow-effect);
    transform: translateY(-2px);
}

/* New item styling */
.news-item.is-new {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, rgba(100, 108, 255, 0.05) 100%);
    border-color: rgba(100, 108, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.news-item.is-new::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--accent-primary), rgba(100, 108, 255, 0.3));
    animation: newItemPulse 2s ease-in-out infinite;
}

.news-item.is-new .new-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 0.75rem;
    color: var(--accent-primary);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.news-item.is-new .new-indicator::before {
    content: '•';
    font-size: 1.2rem;
    animation: pulseDot 1.5s ease-in-out infinite;
}

@keyframes newItemPulse {
    0%, 100% {
        opacity: 1;
        transform: scaleY(1);
    }
    50% {
        opacity: 0.7;
        transform: scaleY(0.98);
    }
}

@keyframes pulseDot {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(0.8);
    }
}

/* Subtle animation when new items appear */
.news-item.fade-in-new {
    animation: fadeInNew 0.6s ease-out forwards;
}

@keyframes fadeInNew {
    from {
        opacity: 0;
        transform: translateY(10px) scale(0.98);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.news-item.priority-high {
    border-left: 4px solid var(--error-color);
}

.news-item.priority-medium {
    border-left: 4px solid var(--warning-color);
}

.news-item.priority-low {
    border-left: 4px solid var(--success-color);
}

.news-headline {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
    line-height: 1.5;
}

.news-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.95rem;
    color: var(--text-secondary);
}

/* News content styles */
.news-findings {
    color: var(--text-primary);
    line-height: 1.8;
    margin: 1.5rem 0;
    /* Removed max-height and overflow as items are always expanded */
    position: relative;
    font-size: 1.1rem;
    transition: max-height 0.5s ease;
}

/* News card styles for JavaScript-generated content */
.news-card {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 1.25rem;
    position: relative;
    min-height: 450px;
}

.news-title {
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
    line-height: 1.5;
}

.news-summary {
    color: var(--text-primary);
    line-height: 1.7;
    margin: 1rem 0;
    font-size: 0.95rem;
}

.news-summary p {
    color: var(--text-primary);
    margin: 0;
}

/* Ensure proper text color inheritance */
#news-container,
#news-feed-content {
    color: var(--text-primary);
}

.news-card * {
    color: inherit;
}

/* Expand/Collapse button - Commented out as news items are now always expanded */
/*
.expand-toggle-btn {
    position: absolute;
    bottom: 1.5rem;
    right: 1.5rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--accent-primary);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    transition: all 0.2s;
    z-index: 10;
}

.expand-toggle-btn:hover {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
    transform: translateY(-2px);
}

.expand-toggle-btn i {
    transition: transform 0.3s;
}

.news-item.is-expanded .expand-toggle-btn i,
.news-card.is-expanded .expand-toggle-btn i {
    transform: rotate(180deg);
}
*/

/* Expanded state styles */
.news-item.is-expanded,
.news-card.is-expanded {
    min-height: auto;
}

.news-item.is-expanded .news-findings,
.news-card.is-expanded .news-findings,
.news-item.is-expanded .news-summary,
.news-card.is-expanded .news-summary {
    max-height: none;
    overflow: visible;
}

.news-item.is-expanded .news-findings::after,
.news-card.is-expanded .news-findings::after,
.news-item.is-expanded .news-summary::after,
.news-card.is-expanded .news-summary::after {
    display: none;
}

.news-header h2 {
    color: var(--text-primary);
}

.news-actions {
    color: var(--text-primary);
}

/* Subscription Management Styles */
.subscription-stats {
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid var(--border-color);
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-card h3 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--accent-primary);
    margin-bottom: 0.5rem;
}

.stat-card p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.subscriptions-container {
    max-height: 500px;
    overflow-y: auto;
    padding: 1rem;
}

.subscription-card {
    background: var(--bg-secondary);
    padding: 1.25rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.subscription-card:hover {
    border-color: var(--accent-primary);
    box-shadow: 0 2px 8px rgba(110, 79, 246, 0.1);
}

.subscription-actions {
    display: flex;
    gap: 0.5rem;
}

.subscription-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

#folderTabs .nav-link {
    color: var(--text-secondary);
    border: 1px solid transparent;
    background: transparent;
    padding: 0.5rem 1rem;
    border-radius: 8px 8px 0 0;
}

#folderTabs .nav-link:hover {
    color: var(--text-primary);
    background: var(--bg-tertiary);
}

#folderTabs .nav-link.active {
    color: var(--accent-primary);
    background: var(--bg-secondary);
    border-color: var(--border-color);
    border-bottom-color: var(--bg-secondary);
}

#folderTabs .badge {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
}

/* Fade-out effect - commented out as items are always expanded */
/*
.news-findings::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background: linear-gradient(to bottom, transparent, var(--bg-tertiary));
    pointer-events: none;
    transition: opacity 0.3s;
}
*/

/* Expanded state */
.news-item.is-expanded .news-findings {
    max-height: none;
}

.news-item.is-expanded .news-findings::after {
    opacity: 0;
}

.news-findings p {
    margin: 0.5rem 0;
}

.news-findings ul, .news-findings ol {
    margin: 0.5rem 0;
    padding-left: 1.5rem;
}

.news-findings strong {
    font-weight: 600;
    color: var(--text-primary);
}

.news-summary {
    color: var(--text-secondary);
    line-height: 1.8;
    margin: 1.25rem 0;
    font-size: 1.05rem;
    /* Removed max-height and overflow as items are always expanded */
    position: relative;
}

/* Fade-out effect - commented out as items are always expanded */
/*
.news-summary::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px;
    background: linear-gradient(to bottom, transparent, var(--bg-tertiary));
    pointer-events: none;
}
*/

.news-category {
    padding: 0.25rem 0.5rem;
    background: var(--bg-secondary);
    border-radius: 4px;
    font-weight: 500;
}

.impact-indicator {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.impact-bar {
    width: 60px;
    height: 4px;
    background: var(--bg-secondary);
    border-radius: 2px;
    overflow: hidden;
}

.impact-fill {
    height: 100%;
    background: linear-gradient(to right, var(--success-color), var(--warning-color), var(--error-color));
}

.news-summary {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: 1rem;
}

.news-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.vote-buttons {
    display: flex;
    gap: 0.5rem;
}

.vote-btn {
    padding: 0.4rem 1rem;
    background: var(--bg-secondary) !important;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary) !important;  /* Changed from text-secondary for better contrast */
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.2s;
    position: relative;
    z-index: 2;
}

.vote-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.vote-btn.voted {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-buttons .btn,
.action-buttons a,
.action-buttons button {
    position: relative;
    z-index: 2;
}

/* Search History */
.recent-search-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
}

.recent-search-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--accent-primary);
    transform: translateX(4px);
}

.recent-search-item i:first-child {
    color: var(--accent-primary);
    font-size: 1.1rem;
}

.recent-search-item i:last-child {
    color: var(--text-secondary);
    opacity: 0;
    transition: opacity 0.2s;
}

.recent-search-item:hover i:last-child {
    opacity: 1;
}

.recent-search-item .search-query {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.recent-search-item .search-meta {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Table View */
.news-table-container {
    overflow-x: auto;
}

.news-table {
    width: 100%;
    border-collapse: collapse;
}

.news-table th {
    background: var(--bg-tertiary);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 2px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.news-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
    vertical-align: top;
}

.news-table tr:hover td {
    background: var(--bg-tertiary);
}

.impact-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 600;
}

.impact-high {
    background: var(--error-color);
    color: white;
}

.impact-medium {
    background: var(--warning-color);
    color: #000;  /* Black text on warning background */
}

.impact-low {
    background: var(--success-color);
    color: white;
}

/* Subscriptions */
.subscriptions-card {
    height: fit-content;
    max-height: calc(100vh - 160px);
    overflow-y: auto;
}

.subscriptions-list {
    margin-bottom: 1rem;
    max-height: 300px;
    overflow-y: auto;
}

.subscription-item {
    padding: 0.75rem;
    background: var(--bg-tertiary);
    border-radius: 6px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
}

.subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.subscription-item:hover {
    border-color: var(--accent-primary);
}

.subscription-item.active {
    background: var(--accent-primary);
    color: white;
}

.subscription-type {
    font-size: 0.75rem;
    opacity: 0.7;
    text-transform: uppercase;
}

.subscription-query {
    font-weight: 500;
    margin: 0.25rem 0;
}

.subscription-meta {
    font-size: 0.8rem;
    opacity: 0.7;
}

/* News Topics */
.news-topics {
    margin: 0.5rem 0;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* Filter chips */
.filter-group {
    margin-bottom: 1.5rem;
}

.filter-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.filter-chip {
    padding: 0.4rem 0.8rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s;
    color: var(--text-primary);
}

.filter-chip:hover {
    background: var(--bg-tertiary);
    border-color: var(--accent-primary);
}

.filter-chip.active {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

/* Filter status bar */
.filter-status-bar {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 0.75rem;
    margin-top: 1rem;
    border: 1px solid var(--border-color);
}

.active-filters {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.active-filters > span:first-child {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-right: 0.5rem;
}

.filter-badge {
    padding: 0.25rem 0.75rem;
    background: var(--bg-tertiary);
    border-radius: 20px;
    font-size: 0.8rem;
    color: var(--text-primary);
    border: 1px solid var(--accent-primary);
}

.filter-status-bar .btn-link {
    margin-left: auto;
    color: var(--accent-primary);
    text-decoration: none;
    padding: 0.25rem 0.5rem;
}

.filter-status-bar .btn-link:hover {
    color: var(--accent-secondary);
}

/* Bulk actions bar */
.bulk-actions-bar {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
}

.bulk-actions-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.news-stats {
    display: flex;
    gap: 1.5rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.stat-item i {
    color: var(--accent-primary);
}

.bulk-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.bulk-actions .btn {
    font-size: 0.85rem;
}

/* Auto-refresh indicator */
.text-warning {
    color: var(--warning-color) !important;
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.6;
    }
}

.form-check-label[for="auto-refresh"] {
    font-size: 0.9rem;
    transition: color 0.3s;
}

/* Refresh button animation */
button[onclick="refreshFeed()"] i {
    transition: transform 0.3s, color 0.3s;
}

button[onclick="refreshFeed()"]:hover i {
    transform: rotate(180deg);
}

/* Trending Topics */
.trending-section {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.trending-section h3 {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

#trending-topics {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

#trending-topics .topic-tag {
    font-size: 0.8rem;
    padding: 0.3rem 0.7rem;
}

.topic-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.topic-tag {
    padding: 0.25rem 0.75rem;
    background: var(--bg-tertiary) !important;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    font-size: 0.85rem;
    color: var(--text-primary) !important;  /* Ensure text color is set */
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    z-index: 2;
    display: inline-block;
}

.topic-tag:hover {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}

.topic-tag.active {
    background: var(--accent-primary) !important;
    color: white !important;
    border-color: var(--accent-primary) !important;
}

/* Topic count badge */
.topic-count {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    padding: 0.1rem 0.4rem;
    border-radius: 10px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-left: 0.3rem;
    display: inline-block;
    min-width: 1.2rem;
    text-align: center;
}

.topic-tag:hover .topic-count {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

.topic-tag.active .topic-count {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

/* Source links */
.news-sources {
    margin: 1rem 0;
    padding: 0.75rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.sources-label {
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.sources-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.source-link {
    color: var(--accent-primary) !important;
    text-decoration: none;
    font-size: 0.8rem;
    display: inline-flex;
    align-items: center;
    gap: 0.2rem;
    padding: 0.2rem 0.6rem;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    transition: all 0.2s;
    white-space: nowrap;
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
}

.source-link:hover {
    background: var(--accent-primary);
    color: white !important;
    border-color: var(--accent-primary);
    transform: translateY(-1px);
}

.source-link i {
    font-size: 0.9rem;
    opacity: 0.7;
}

/* Make the entire sources section more compact */
.news-sources {
    margin-top: 0.8rem;
    padding: 0.8rem;
    background: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

/* Active filter bar */
.active-filter-bar {
    background: var(--bg-tertiary);
    border: 1px solid var(--accent-primary);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.filter-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
}

.filter-info strong {
    color: var(--accent-primary);
}

.filter-info button {
    margin-left: auto;
    color: var(--text-secondary);
    text-decoration: none;
}

.filter-info button:hover {
    color: var(--accent-primary);
}

/* News-specific button styles */
.news-item .btn-primary {
    padding: 0.5rem 1rem;
    background: var(--accent-primary);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    transition: all 0.2s;
}

.news-item .btn-primary:hover {
    background: var(--accent-primary-hover, #0056b3);
    transform: translateY(-1px);
}

/* Button Extensions */
.btn-full {
    width: 100%;
}

.btn-icon {
    padding: 0.5rem;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s;
}

.btn-icon:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Loading States */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: var(--text-muted);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--bg-tertiary);
    border-top-color: var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Subscription History Modal */
.subscription-stats {
    display: flex;
    gap: 1.5rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: 8px;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
}

.stat-item i {
    color: var(--accent-primary);
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    padding: 1rem;
    background: var(--bg-tertiary);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.history-status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.history-status.status-completed {
    background: var(--success-color);
    color: white;
}

.history-status.status-in_progress {
    background: var(--warning-color);
    color: #000;
}

.history-status.status-failed,
.history-status.status-error {
    background: var(--error-color);
    color: white;
}

.history-time {
    font-size: 0.85rem;
    color: var(--text-muted);
}

.history-query {
    margin: 0.5rem 0;
    font-weight: 500;
    color: var(--text-primary);
}

.history-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 0.5rem;
}

.history-actions .duration {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

/* Modal overrides for dark theme */
.modal-content {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.modal-header {
    border-bottom-color: var(--border-color);
}

.modal-title small {
    display: block;
    font-size: 0.85rem;
    font-weight: normal;
    margin-top: 0.25rem;
}

/* News Templates */
.news-templates {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 20px;
}

.template-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    color: var(--text-primary);
    font-size: 0.9rem;
    text-align: left;
}

.template-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
}

.template-btn i {
    font-size: 1.1rem;
    flex-shrink: 0;
}

/* Progress card specific styles */
.news-card.active-research-card .news-title i {
    color: var(--accent-primary);
    margin-right: 0.5rem;
}

.news-card.active-research-card .btn-outline-primary {
    border-color: var(--accent-primary);
    color: var(--accent-primary);
}

.news-card.active-research-card .btn-outline-primary:hover {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
}
