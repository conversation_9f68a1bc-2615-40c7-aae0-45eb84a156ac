// Subscriptions Management Page
let subscriptions = [];
let folders = [];
let currentFilter = {
    folder: 'all',
    status: 'all',
    frequency: 'all',
    search: ''
};

// Default fetch options to include credentials
const fetchOptions = {
    credentials: 'same-origin'
};

// Format next update time correctly
function formatNextUpdate(dateString) {
    // The date string from the database is in UTC
    // If it contains 'Z' or '+00:00', it's UTC
    // Otherwise, assume it's already in local time

    const date = new Date(dateString);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
        return 'Invalid date';
    }

    // If the date string doesn't contain timezone info, it might be interpreted incorrectly
    // Let's check if it's in the past (more than 5 minutes ago)
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    if (date < fiveMinutesAgo) {
        // The date is in the past, which likely means it was interpreted in the wrong timezone
        // Add the local timezone offset to correct it
        const offset = now.getTimezoneOffset() * 60 * 1000;
        const correctedDate = new Date(date.getTime() - offset);
        return correctedDate.toLocaleString();
    }

    return date.toLocaleString();
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadSubscriptions();
    loadFolders();
    setupEventListeners();
    checkSchedulerStatus();
    // Jitter info now handled by dedicated form pages
});

// Setup event listeners
function setupEventListeners() {
    // Search box
    document.getElementById('subscription-search').addEventListener('input', (e) => {
        currentFilter.search = e.target.value;
        renderSubscriptions();
    });

    // Filter dropdowns
    document.getElementById('status-filter').addEventListener('change', (e) => {
        currentFilter.status = e.target.value;
        renderSubscriptions();
    });

    document.getElementById('frequency-filter').addEventListener('change', (e) => {
        currentFilter.frequency = e.target.value;
        renderSubscriptions();
    });

    // Folder navigation
    document.addEventListener('click', (e) => {
        if (e.target.closest('.folder-item')) {
            const folderItem = e.target.closest('.folder-item');
            const folderId = folderItem.dataset.folder;
            selectFolder(folderId);
        }
    });

    // Form handlers removed - now handled by dedicated form pages

    // Add folder button
    document.getElementById('add-folder-btn').addEventListener('click', createNewFolder);

    // Create subscription button - redirect to form page
    document.getElementById('create-subscription-btn').addEventListener('click', () => {
        window.location.href = '/news/subscriptions/new';
    });
}

// Load subscriptions from API
async function loadSubscriptions() {
    console.log('Loading subscriptions...');
    try {
        const response = await fetch('/news/api/subscriptions/current', {
            credentials: 'same-origin'
        });

        if (response.ok) {
            const data = await response.json();
            subscriptions = data.subscriptions || [];
            console.log('Loaded subscriptions:', subscriptions);

            // Log the 3090 subscription specifically
            const sub3090 = subscriptions.find(s => s.query && s.query.includes('3090'));
            if (sub3090) {
                console.log('3090 subscription data:', {
                    id: sub3090.id,
                    refresh_minutes: sub3090.refresh_minutes,
                    next_refresh: sub3090.next_refresh,
                    last_refreshed: sub3090.last_refreshed
                });
            }

            renderSubscriptions();
            updateStats();
        } else {
            console.error('Failed to load subscriptions:', response.status, response.statusText);
            showAlert('Failed to load subscriptions', 'error');
        }
    } catch (error) {
        console.error('Error loading subscriptions:', error);
        showAlert('Failed to load subscriptions', 'error');
    }
}

// Load folders
async function loadFolders() {
    try {
        const response = await fetch('/news/api/subscription/folders', {
            credentials: 'same-origin'
        });
        if (response.ok) {
            const data = await response.json();
            folders = Array.isArray(data) ? data : (data.folders || []);
            renderFolders();
        } else {
            console.error('Failed to load folders:', response.status, response.statusText);
        }
    } catch (error) {
        console.error('Error loading folders:', error);
    }
}

// Render subscriptions grid
function renderSubscriptions() {
    const grid = document.getElementById('subscriptions-grid');
    const filtered = filterSubscriptions();

    if (filtered.length === 0) {
        grid.innerHTML = `
            <div class="empty-state">
                <i class="bi bi-bell-slash"></i>
                <h3>No subscriptions found</h3>
                <p>Create your first subscription to start tracking news topics</p>
            </div>
        `;
        return;
    }

    grid.innerHTML = filtered.map(sub => createSubscriptionCard(sub)).join('');
}

// Create subscription card HTML
function createSubscriptionCard(subscription) {
    const statusClass = subscription.is_active ? 'active' : 'paused';
    const statusIcon = subscription.is_active ? 'bi-play-circle' : 'bi-pause-circle';
    const lastUpdated = subscription.last_refreshed ?
        new Date(subscription.last_refreshed).toLocaleString() : 'Never';

    // Use refresh_minutes directly
    const refreshMinutes = subscription.refresh_minutes;
    let refreshInterval;
    if (refreshMinutes === 1) {
        refreshInterval = 'Every minute';
    } else if (refreshMinutes < 60) {
        refreshInterval = `Every ${refreshMinutes} minutes`;
    } else if (refreshMinutes === 60) {
        refreshInterval = 'Hourly';
    } else if (refreshMinutes === 1440) {
        refreshInterval = 'Daily';
    } else if (refreshMinutes === 10080) {
        refreshInterval = 'Weekly';
    } else if (refreshMinutes % 1440 === 0) {
        refreshInterval = `Every ${refreshMinutes / 1440} days`;
    } else if (refreshMinutes % 60 === 0) {
        refreshInterval = `Every ${refreshMinutes / 60} hours`;
    } else {
        refreshInterval = `Every ${refreshMinutes} minutes`;
    }

    // Truncate long text
    const displayName = subscription.name || subscription.query;
    const truncatedName = displayName.length > 50 ? displayName.substring(0, 50) + '...' : displayName;
    const truncatedQuery = subscription.query.length > 80 ? subscription.query.substring(0, 80) + '...' : subscription.query;

    return `
        <div class="subscription-card" data-subscription-id="${subscription.id}" onclick="viewSubscriptionHistory('${subscription.id}')" style="cursor: pointer;">
            <div class="card-header">
                <h4 title="${displayName}">${truncatedName}</h4>
                <div class="card-actions">
                    <button class="btn btn-sm btn-icon" onclick="event.stopPropagation(); runSubscriptionNow('${subscription.id}')" title="Run now">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                    <button class="btn btn-sm btn-icon" onclick="event.stopPropagation(); toggleSubscription('${subscription.id}')" title="${statusClass === 'active' ? 'Pause' : 'Resume'}">
                        <i class="bi ${statusIcon}"></i>
                    </button>
                    <button class="btn btn-sm btn-icon" onclick="event.stopPropagation(); viewSubscriptionHistory('${subscription.id}')" title="View history">
                        <i class="bi bi-clock-history"></i>
                    </button>
                    <a href="/news/subscriptions/${subscription.id}/edit" class="btn btn-sm btn-icon" onclick="event.stopPropagation();" title="Edit">
                        <i class="bi bi-pencil"></i>
                    </a>
                    <button class="btn btn-sm btn-icon btn-danger" onclick="event.stopPropagation(); deleteSubscriptionDirect('${subscription.id}')" title="Delete">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="query-text" title="${subscription.query}">${truncatedQuery}</div>
                <div class="subscription-meta">
                    <span class="status-badge status-${statusClass}">${statusClass}</span>
                    <span class="frequency-badge">${refreshInterval}</span>
                    ${subscription.folder_id ? `<span class="folder-badge">${getFolderName(subscription.folder_id)}</span>` : ''}
                </div>
                <div class="subscription-stats">
                    <div class="stat-item">
                        <i class="bi bi-file-text"></i>
                        <span>${subscription.total_runs || 0} research runs</span>
                    </div>
                    ${subscription.source_id ? `
                        <div class="stat-item">
                            <i class="bi bi-link-45deg"></i>
                            <a href="/progress/${subscription.source_id}" class="source-link">View original research</a>
                        </div>
                    ` : ''}
                </div>
                <div class="last-updated">
                    <i class="bi bi-clock"></i> Last updated: ${lastUpdated}
                </div>
                ${subscription.next_refresh ? `
                    <div class="next-update">
                        <i class="bi bi-arrow-clockwise"></i> Next update: ${formatNextUpdate(subscription.next_refresh)}
                    </div>
                ` : ''}
            </div>
        </div>
    `;
}

// Filter subscriptions based on current filters
function filterSubscriptions() {
    return subscriptions.filter(sub => {
        // Folder filter
        if (currentFilter.folder !== 'all') {
            if (currentFilter.folder === 'uncategorized' && sub.folder_id) return false;
            if (currentFilter.folder !== 'uncategorized' && sub.folder_id !== currentFilter.folder) return false;
        }

        // Status filter
        if (currentFilter.status !== 'all') {
            const isActive = sub.is_active;
            if (currentFilter.status === 'active' && !isActive) return false;
            if (currentFilter.status === 'paused' && isActive) return false;
        }

        // Frequency filter
        if (currentFilter.frequency !== 'all' && sub.refresh_interval !== currentFilter.frequency) {
            return false;
        }

        // Search filter
        if (currentFilter.search) {
            const searchLower = currentFilter.search.toLowerCase();
            const nameMatch = (sub.name || '').toLowerCase().includes(searchLower);
            const queryMatch = sub.query.toLowerCase().includes(searchLower);
            if (!nameMatch && !queryMatch) return false;
        }

        return true;
    });
}

// New subscription creation now handled by dedicated form page

// Run subscription now - uses the same research system as news page
async function runSubscriptionNow(subscriptionId) {
    const subscription = subscriptions.find(s => s.id === subscriptionId);
    if (!subscription) return;

    try {
        const query = subscription.query || subscription.query_or_topic || '';
        console.log('Running subscription:', subscriptionId, 'with query:', query);
        showAlert('Starting research for: ' + query, 'info');

        const requestData = {
            query: query,
            mode: 'quick',
            metadata: {
                is_news_search: true,
                search_type: 'news_analysis',
                display_in: 'news_feed',
                subscription_id: subscriptionId,
                triggered_by: 'manual_run'
            }
        };
        console.log('Sending research request:', requestData);

        // Use the same research endpoint as the news page
        const response = await fetch('/api/start_research', {
            ...fetchOptions,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify(requestData)
        });

        if (response.ok) {
            const data = await response.json();
            console.log('Research API response:', data);

            if (data.status === 'success' && data.research_id) {
                showAlert(`Research started! <a href="/progress/${data.research_id}" style="color: white; text-decoration: underline;">View progress</a>`, 'success');

                // Store active research in localStorage so news page can show progress
                localStorage.setItem('active_news_research', JSON.stringify({
                    researchId: data.research_id,
                    query: query,
                    startTime: new Date().toISOString()
                }));

                // Update the subscription's last run time
                subscription.last_run = new Date().toISOString();
                renderSubscriptions();

                // Refresh subscription data after a delay
                setTimeout(() => loadSubscriptions(), 2000);

                // Optional: Open news page to show progress
                // window.open('/news', '_blank');
            } else {
                console.error('Unexpected response:', data);
                showAlert('Failed to start research: ' + (data.message || 'Unknown error'), 'error');
            }
        } else {
            console.error('Research API error:', response.status, response.statusText);
            const errorData = await response.json().catch(() => ({}));
            console.error('Error data:', errorData);
            showAlert(errorData.message || 'Failed to start research', 'error');
        }
    } catch (error) {
        console.error('Error running subscription:', error);
        showAlert('Failed to start research', 'error');
    }
}

// Toggle subscription active/paused
async function toggleSubscription(subscriptionId) {
    const subscription = subscriptions.find(s => s.id === subscriptionId);
    if (!subscription) return;

    try {
        const response = await fetch(`/news/api/subscriptions/${subscriptionId}`, {
            ...fetchOptions,
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({
                is_active: !subscription.is_active
            })
        });

        if (response.ok) {
            subscription.is_active = !subscription.is_active;
            renderSubscriptions();
            updateStats();
        }
    } catch (error) {
        console.error('Error toggling subscription:', error);
        showAlert('Failed to update subscription', 'error');
    }
}

// View subscription history
async function viewSubscriptionHistory(subscriptionId) {
    const subscription = subscriptions.find(s => s.id === subscriptionId);
    if (!subscription) return;

    try {
        // Fetch subscription history
        const response = await fetch(`/news/api/subscriptions/${subscriptionId}/history`, fetchOptions);
        if (response.ok) {
            const data = await response.json();
            showSubscriptionHistoryModal(subscription, data);
        } else {
            showAlert('Failed to load subscription history', 'error');
        }
    } catch (error) {
        console.error('Error loading subscription history:', error);
        showAlert('Failed to load subscription history', 'error');
    }
}

// Show subscription history modal
function showSubscriptionHistoryModal(subscription, historyData) {
    // Create modal content
    const modalContent = `
        <div class="modal fade" id="historyModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-clock-history"></i> History: ${subscription.name || subscription.query}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="history-summary">
                            <p><strong>Total research runs:</strong> ${historyData.total_runs || 0}</p>
                            <p><strong>Created:</strong> ${new Date(subscription.created_at).toLocaleString()}</p>
                            ${subscription.source_id ? `
                                <p><strong>Created from:</strong>
                                    <a href="/progress/${subscription.source_id}" target="_blank">
                                        Original research <i class="bi bi-box-arrow-up-right"></i>
                                    </a>
                                </p>
                            ` : ''}
                        </div>

                        <h6 class="mt-4">Recent Research Runs</h6>
                        <div class="history-list" style="max-height: 400px; overflow-y: auto;">
                            ${historyData.history && historyData.history.length > 0 ?
                                historyData.history.map(item => `
                                    <div class="history-item">
                                        <div class="history-item-header">
                                            <a href="/progress/${item.research_id}" target="_blank">
                                                ${item.headline || '[No headline]'} <i class="bi bi-box-arrow-up-right"></i>
                                            </a>
                                            <span class="history-date">${new Date(item.created_at).toLocaleString()}</span>
                                        </div>
                                        <div class="history-item-meta">
                                            <span class="status-badge status-${item.status}">${item.status}</span>
                                            ${item.duration_seconds ? `<span class="duration">${item.duration_seconds}s</span>` : ''}
                                            ${item.topics && item.topics.length > 0 ?
                                                `<div class="topics-list">${item.topics.slice(0, 3).map(topic =>
                                                    `<span class="topic-badge">${topic}</span>`
                                                ).join('')}</div>` : ''
                                            }
                                        </div>
                                    </div>
                                `).join('') :
                                '<p class="text-muted">No research runs yet</p>'
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing history modal if any
    const existingModal = document.getElementById('historyModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalContent);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('historyModal'));
    modal.show();

    // Clean up when modal is hidden
    document.getElementById('historyModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Edit subscription redirects to dedicated form page

// Delete subscription directly from card
async function deleteSubscriptionDirect(subscriptionId) {
    const subscription = subscriptions.find(s => s.id === subscriptionId);

    if (!subscription || !confirm(`Are you sure you want to delete the subscription "${subscription.name || subscription.query}"?`)) {
        return;
    }

    try {
        const response = await fetch(`/news/api/subscriptions/${subscriptionId}`, {
            ...fetchOptions,
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCSRFToken()
            }
        });

        if (response.ok) {
            showAlert('Subscription deleted successfully', 'success');
            // Reload subscriptions
            await loadSubscriptions();
        } else {
            const error = await response.json();
            showAlert(error.error || 'Failed to delete subscription', 'error');
        }
    } catch (error) {
        console.error('Error deleting subscription:', error);
        showAlert('Failed to delete subscription', 'error');
    }
}

// Update statistics
function updateStats() {
    const total = subscriptions.length;
    const active = subscriptions.filter(s => s.is_active).length;
    const paused = total - active;
    const updatedToday = subscriptions.filter(s => {
        if (!s.last_refreshed) return false;
        const lastUpdate = new Date(s.last_refreshed);
        const today = new Date();
        return lastUpdate.toDateString() === today.toDateString();
    }).length;

    document.getElementById('total-subscriptions').textContent = total;
    document.getElementById('active-subscriptions').textContent = active;
    document.getElementById('paused-subscriptions').textContent = paused;
    document.getElementById('updates-today').textContent = updatedToday;
}

// Folder management
function renderFolders() {
    const folderList = document.querySelector('.folder-list');

    // Remove existing dynamic folders first
    document.querySelectorAll('.folder-item[data-folder]:not([data-folder="all"]):not([data-folder="uncategorized"])').forEach(el => el.remove());

    const dynamicFolders = folders.map(folder => `
        <div class="folder-item" data-folder="${folder.id}">
            <i class="bi bi-folder"></i> ${folder.name}
            <span class="folder-count">${countSubscriptionsInFolder(folder.id)}</span>
        </div>
    `).join('');

    // Update counts for default folders
    document.querySelector('[data-folder="all"] .folder-count').textContent = subscriptions.length;
    document.querySelector('[data-folder="uncategorized"] .folder-count').textContent =
        subscriptions.filter(s => !s.folder_id).length;

    // Insert dynamic folders after uncategorized
    const uncategorizedFolder = document.querySelector('[data-folder="uncategorized"]');
    if (dynamicFolders) {
        uncategorizedFolder.insertAdjacentHTML('afterend', dynamicFolders);
    }

    // Folder dropdowns now handled by dedicated form pages
}

function countSubscriptionsInFolder(folderId) {
    return subscriptions.filter(s => s.folder_id === folderId).length;
}

// Jitter info loading removed - now handled by dedicated form pages

function selectFolder(folderId) {
    // Update active state
    document.querySelectorAll('.folder-item').forEach(item => {
        item.classList.toggle('active', item.dataset.folder === folderId);
    });

    currentFilter.folder = folderId;
    renderSubscriptions();
}

async function createNewFolder() {
    const name = prompt('Enter folder name:');
    if (!name || !name.trim()) return;

    try {
        const response = await fetch('/news/api/subscription/folders', {
            ...fetchOptions,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify({
                name: name.trim()
            })
        });

        if (response.ok) {
            const folder = await response.json();
            showAlert(`Folder "${folder.name}" created successfully!`, 'success');

            // Reload folders
            await loadFolders();

            // Re-render subscriptions to update folder badges
            renderSubscriptions();
        } else {
            const error = await response.json();
            if (response.status === 409) {
                showAlert('A folder with that name already exists', 'warning');
            } else {
                showAlert(error.error || 'Failed to create folder', 'error');
            }
        }
    } catch (error) {
        console.error('Error creating folder:', error);
        showAlert('Failed to create folder', 'error');
    }
}

// Utility functions
function getCurrentUserId() {
    return localStorage.getItem('userId') || 'anonymous';
}

function getCSRFToken() {
    return document.querySelector('meta[name="csrf-token"]')?.content || '';
}

function getFolderName(folderId) {
    const folder = folders.find(f => f.id === folderId);
    return folder ? folder.name : 'Unknown';
}

function showAlert(message, type) {
    // Simple alert implementation
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.page-header');
    container.insertAdjacentElement('afterend', alertDiv);

    // Auto-dismiss after 5 seconds
    setTimeout(() => alertDiv.remove(), 5000);
}

// Scheduler status functions
async function checkSchedulerStatus() {
    const statusIndicator = document.getElementById('status-indicator');
    const statusText = document.getElementById('status-text');
    const schedulerDetails = document.getElementById('scheduler-details');
    const toggleButton = document.getElementById('toggle-scheduler');

    // Set checking state
    statusIndicator.className = 'status-indicator checking';
    statusText.textContent = 'Checking...';

    try {
        const response = await fetch('/news/api/scheduler/status', fetchOptions);
        if (response.ok) {
            const data = await response.json();

            if (data.scheduler_available) {
                if (data.is_running) {
                    // Check if user is tracked
                    if (data.active_users && data.active_users > 0) {
                        statusIndicator.className = 'status-indicator active';
                        statusText.textContent = 'Active';
                        schedulerDetails.textContent = `${data.active_users} active users, ${data.scheduled_jobs || 0} scheduled jobs`;
                    } else {
                        statusIndicator.className = 'status-indicator inactive';
                        statusText.textContent = 'Not Tracking Your Session';
                        schedulerDetails.innerHTML = 'Log out and log back in to activate automatic scheduling. <a href="#" onclick="showSchedulerInfo(); return false;">Learn more</a>';
                    }

                    // Update button state
                    toggleButton.style.display = 'none';
                } else {
                    statusIndicator.className = 'status-indicator inactive';
                    statusText.textContent = 'Stopped';
                    schedulerDetails.textContent = 'Auto-refresh is disabled';

                    // Show start button
                    toggleButton.style.display = 'inline-flex';
                    toggleButton.innerHTML = '<i class="bi bi-play-fill"></i> Start Scheduler';
                    toggleButton.onclick = () => startScheduler();
                }
            } else {
                statusIndicator.className = 'status-indicator inactive';
                statusText.textContent = 'Not Available';
                schedulerDetails.textContent = 'Install APScheduler: pip install apscheduler';
                toggleButton.style.display = 'none';
            }
        } else {
            throw new Error('Failed to check scheduler status');
        }
    } catch (error) {
        console.error('Error checking scheduler status:', error);
        statusIndicator.className = 'status-indicator inactive';
        statusText.textContent = 'Error';
        schedulerDetails.textContent = 'Unable to check scheduler status';
        toggleButton.style.display = 'none';
    }

}

async function startScheduler() {
    const toggleButton = document.getElementById('toggle-scheduler');
    toggleButton.disabled = true;
    toggleButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Starting...';

    try {
        const response = await fetch('/news/api/scheduler/start', {
            ...fetchOptions,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCSRFToken()
            }
        });

        if (response.ok) {
            showAlert('Scheduler started successfully', 'success');
            // Recheck status after a delay
            setTimeout(() => checkSchedulerStatus(), 1000);
        } else {
            const error = await response.json();
            showAlert(`Failed to start scheduler: ${error.error || 'Unknown error'}`, 'error');
            toggleButton.disabled = false;
        }
    } catch (error) {
        console.error('Error starting scheduler:', error);
        showAlert('Failed to start scheduler', 'error');
        toggleButton.disabled = false;
    }
}

// Show scheduler information
function showSchedulerInfo() {
    showAlert(`
        <h6>About the Subscription Scheduler</h6>
        <p>The scheduler runs your subscriptions automatically in the background:</p>
        <ul>
            <li>Your subscriptions are stored in an encrypted database</li>
            <li>When you log in, the scheduler securely stores access for 48 hours</li>
            <li>Subscriptions run automatically based on their schedule</li>
            <li>You don't need to stay logged in - the scheduler works in the background</li>
            <li>Any overdue subscriptions will run shortly after login</li>
        </ul>
        <p>The scheduler will continue running subscriptions for 48 hours after your last login. Simply log in periodically to keep it active.</p>
    `, 'info');
}
