// News Page Design Enhancements
document.addEventListener('DOMContentLoaded', function() {

    // Add smooth hover effects to news items
    const newsItems = document.querySelectorAll('.news-item');
    newsItems.forEach((item, index) => {
        // Add staggered animation
        item.style.animationDelay = `${index * 0.1}s`;

        // Add interactive hover effects
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Enhance vote buttons with ripple effect
    const voteButtons = document.querySelectorAll('.vote-btn');
    voteButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            // Create ripple effect
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';

            setTimeout(() => ripple.remove(), 600);
        });
    });

    // Enhance topic pills
    const topicPills = document.querySelectorAll('.topic-pill');
    topicPills.forEach(pill => {
        pill.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });

    // Add loading skeleton when refreshing
    const refreshBtn = document.getElementById('refresh-feed-btn');
    if (refreshBtn) {
        const originalRefresh = refreshBtn.onclick;
        refreshBtn.onclick = function() {
            // Add loading state
            const newsContainer = document.querySelector('.news-cards-container');
            if (newsContainer) {
                newsContainer.style.opacity = '0.5';
                newsContainer.classList.add('skeleton-loader');
            }

            // Call original function
            if (originalRefresh) originalRefresh.call(this);

            // Remove loading state after delay
            setTimeout(() => {
                if (newsContainer) {
                    newsContainer.style.opacity = '1';
                    newsContainer.classList.remove('skeleton-loader');
                }
            }, 1000);
        };
    }

    // Enhance search input
    const searchInput = document.getElementById('news-search');
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    }

    // Add smooth scroll behavior
    document.documentElement.style.scrollBehavior = 'smooth';

    // Enhance impact bars with animation
    const impactBars = document.querySelectorAll('.impact-fill');
    impactBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0';
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });

    // Add parallax effect to header
    const header = document.querySelector('.feed-header-section');
    if (header) {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = -scrolled * 0.1;
            header.style.transform = `translateY(${parallax}px)`;
        });
    }
});

// CSS for ripple effect
const style = document.createElement('style');
style.textContent = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.5);
        transform: scale(0);
        animation: ripple-animation 0.6s ease-out;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .vote-btn {
        position: relative;
        overflow: hidden;
    }
`;
document.head.appendChild(style);
