/**
 * Research Details Page JavaScript
 * Handles displaying detailed metrics for a specific research session
 */
(function() {
    'use strict';

    let researchId = null;
    let metricsData = null;
    let timelineChart = null;
    let searchChart = null;

    // Format large numbers with commas
    function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }

    // Format currency
    function formatCurrency(amount) {
        if (amount < 0.01) {
            return `$${amount.toFixed(6)}`;
        } else if (amount < 1) {
            return `$${amount.toFixed(4)}`;
        } else {
            return `$${amount.toFixed(2)}`;
        }
    }

    // Helper function to generate colors for dynamic number of categories
    function generateChartColors(count) {
        const baseColors = [
            'rgba(107, 70, 193, 0.8)',   // Purple
            'rgba(245, 158, 11, 0.8)',   // Orange
            'rgba(59, 130, 246, 0.8)',   // Blue
            'rgba(16, 185, 129, 0.8)',   // <PERSON>
            'rgba(239, 68, 68, 0.8)',    // Red
            'rgba(139, 69, 19, 0.8)',    // <PERSON>
            'rgba(255, 192, 203, 0.8)',  // <PERSON>
            'rgba(128, 128, 128, 0.8)',  // Gray
            'rgba(255, 165, 0, 0.8)',    // Orange
            'rgba(75, 0, 130, 0.8)'      // Indigo
        ];

        const background = [];
        const border = [];

        for (let i = 0; i < count; i++) {
            const colorIndex = i % baseColors.length;
            background.push(baseColors[colorIndex]);
            border.push(baseColors[colorIndex].replace('0.8', '1'));
        }

        return { background, border };
    }

    // Get research ID from URL
    function getResearchIdFromUrl() {
        const id = URLBuilder.extractResearchIdFromPattern('details');
        console.log('getResearchIdFromUrl called, extracted ID:', id);
        console.log('Current URL:', window.location.href);
        console.log('Current pathname:', window.location.pathname);
        return id;
    }

    // Load link analytics for the research
    async function loadLinkAnalytics(researchId) {
        try {
            console.log('Loading link analytics for research:', researchId);

            const response = await fetch(`/metrics/api/metrics/research/${researchId}/links`);
            if (!response.ok) {
                console.error('Failed to load link analytics:', response.status);
                return;
            }

            const result = await response.json();
            if (result.status !== 'success') {
                console.error('Error loading link analytics:', result.message);
                return;
            }

            const data = result.data;

            // Show the link analytics sections
            document.getElementById('source-distribution-section').style.display = 'block';
            document.getElementById('link-analytics-section').style.display = 'block';

            // Update summary metrics
            document.getElementById('total-links').textContent = data.total_links || 0;
            document.getElementById('unique-domains').textContent = data.unique_domains || 0;

            // Update category metrics from LLM classification
            const domainCategories = data.domain_categories || {};
            const categoryEntries = Object.entries(domainCategories);

            // Update the first two category cards with actual data
            if (categoryEntries.length > 0) {
                document.getElementById('academic-sources').textContent = categoryEntries[0]?.[1] || 0;
                // Update label
                const academicLabel = document.querySelector('#academic-sources').previousElementSibling;
                if (academicLabel) academicLabel.textContent = categoryEntries[0]?.[0] || 'Category 1';
            } else {
                document.getElementById('academic-sources').textContent = 0;
            }

            if (categoryEntries.length > 1) {
                document.getElementById('news-sources').textContent = categoryEntries[1]?.[1] || 0;
                // Update label
                const newsLabel = document.querySelector('#news-sources').previousElementSibling;
                if (newsLabel) newsLabel.textContent = categoryEntries[1]?.[0] || 'Category 2';
            } else {
                document.getElementById('news-sources').textContent = 0;
            }

            // Display domain list
            const domainList = document.getElementById('domain-list');
            if (data.domains && data.domains.length > 0) {
                domainList.innerHTML = data.domains.map(domain => `
                    <div style="display: flex; justify-content: space-between; align-items: center; padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                        <span style="font-weight: 500;">${domain.domain}</span>
                        <div style="display: flex; gap: 1rem; align-items: center;">
                            <span style="background: var(--primary-color); color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.875rem;">
                                ${domain.count} links
                            </span>
                            <span style="color: var(--text-secondary); font-size: 0.875rem;">
                                ${domain.percentage}%
                            </span>
                        </div>
                    </div>
                `).join('');
            } else {
                domainList.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 1rem;">No domain data available</div>';
            }

            // Display resource samples
            const resourceSample = document.getElementById('resource-sample');
            if (data.resources && data.resources.length > 0) {
                resourceSample.innerHTML = data.resources.map(resource => `
                    <div style="padding: 0.75rem; border-bottom: 1px solid var(--border-color);">
                        <div style="font-weight: 500; margin-bottom: 0.25rem;">${resource.title}</div>
                        <a href="${resource.url}" target="_blank" style="color: var(--primary-color); text-decoration: none; font-size: 0.875rem; word-break: break-all;">
                            ${resource.url}
                        </a>
                        ${resource.preview ? `<div style="color: var(--text-secondary); font-size: 0.875rem; margin-top: 0.5rem;">${resource.preview}</div>` : ''}
                    </div>
                `).join('');
            } else {
                resourceSample.innerHTML = '<div style="text-align: center; color: var(--text-secondary); padding: 1rem;">No resource samples available</div>';
            }

            // Create generic source type pie chart
            if (domainCategories && Object.keys(domainCategories).length > 0) {
                const ctx = document.getElementById('source-type-chart');
                if (ctx) {
                    // Generate dynamic labels and data from whatever categories the LLM provides
                    const labels = Object.keys(domainCategories);
                    const chartData = Object.values(domainCategories);

                    // Generate colors dynamically based on number of categories
                    const colors = generateChartColors(labels.length);

                    new Chart(ctx, {
                        type: 'pie',
                        data: {
                            labels: labels,
                            datasets: [{
                                data: chartData,
                                backgroundColor: colors.background,
                                borderColor: colors.border,
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom'
                                }
                            }
                        }
                    });
                }
            } else if (data.total_links > 0) {
                // Show placeholder when there are links but no categories
                const chartContainer = document.getElementById('source-type-chart').parentElement;
                chartContainer.innerHTML = `
                    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 300px; text-align: center;">
                        <i class="fas fa-robot" style="font-size: 3rem; color: var(--text-secondary); margin-bottom: 1rem;"></i>
                        <h3 style="color: var(--text-primary); margin-bottom: 0.5rem;">AI Classification Not Available</h3>
                        <p style="color: var(--text-secondary); margin-bottom: 1rem; max-width: 400px;">
                            Domain categories haven't been classified yet. Use the button below to analyze your domains.
                        </p>
                    </div>
                `;
            } else {
                // Show no links message
                const chartContainer = document.getElementById('source-type-chart').parentElement;
                chartContainer.innerHTML = `
                    <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 300px; text-align: center;">
                        <i class="fas fa-link" style="font-size: 3rem; color: var(--text-secondary); margin-bottom: 1rem;"></i>
                        <h3 style="color: var(--text-primary); margin-bottom: 0.5rem;">No Links Available</h3>
                        <p style="color: var(--text-secondary);">This research session doesn't have any links to classify.</p>
                    </div>
                `;
            }

            // Always add the classify button if there are links (alongside the chart or placeholder)
            if (data.total_links > 0) {
                const sourceDistributionSection = document.getElementById('source-distribution-section');
                const cardContent = sourceDistributionSection.querySelector('.ldr-card-content');

                // Add classify button container after the chart
                const classifyContainer = document.createElement('div');
                classifyContainer.innerHTML = `
                    <div style="text-align: center; margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid var(--border-color);">
                        <button id="classify-domains-btn" class="btn btn-primary" style="display: flex; align-items: center; gap: 0.5rem; margin: 0 auto;">
                            <i class="fas fa-magic"></i>
                            Classify Domains with AI
                        </button>
                        <p style="color: var(--text-secondary); font-size: 0.875rem; margin-top: 0.5rem;">
                            Analyze and categorize all domains using AI classification
                        </p>
                    </div>
                `;
                cardContent.appendChild(classifyContainer);

                // Add click handler for link analytics button
                const classifyBtn = document.getElementById('classify-domains-btn');

                classifyBtn.addEventListener('click', () => {
                    window.location.href = '/metrics/links';
                });
            }

        } catch (error) {
            console.error('Error loading link analytics:', error);
        }
    }

    // Load research metrics data
    async function loadResearchMetrics() {
        try {
            console.log('Loading research metrics for ID:', researchId);

            // Show loading state
            document.getElementById('loading').style.display = 'block';
            document.getElementById('details-content').style.display = 'none';
            document.getElementById('error').style.display = 'none';

            // Load research details (includes strategy)
            console.log('Fetching research details...');
            console.log('Using research ID:', researchId);
            const detailsUrl = URLBuilder.historyDetails(researchId);
            console.log('Details URL:', detailsUrl);
            console.log('Full URL being fetched:', window.location.origin + detailsUrl);
            const detailsResponse = await fetch(detailsUrl);
            console.log('Details response status:', detailsResponse.status);
            console.log('Details response URL:', detailsResponse.url);

            let researchDetails = null;
            if (detailsResponse.ok) {
                researchDetails = await detailsResponse.json();
                console.log('Research details loaded:', researchDetails);
            } else {
                console.error('Failed to load research details:', detailsResponse.status);
                const errorText = await detailsResponse.text();
                console.error('Error response:', errorText);
                try {
                    const errorJson = JSON.parse(errorText);
                    console.error('Error JSON:', errorJson);
                    if (errorJson.message) {
                        throw new Error(errorJson.message);
                    }
                } catch (e) {
                    // Not JSON, use original text
                }
                throw new Error(`Failed to load research details: ${detailsResponse.status}`);
            }

            // Load research metrics
            console.log('Fetching research metrics...');
            const metricsResponse = await fetch(URLBuilder.build(URLS.METRICS_API.RESEARCH, researchId));
            console.log('Metrics response status:', metricsResponse.status);

            if (!metricsResponse.ok) {
                throw new Error(`Metrics API failed: ${metricsResponse.status}`);
            }

            const metricsResult = await metricsResponse.json();
            console.log('Metrics result:', metricsResult);

            if (metricsResult.status !== 'success') {
                throw new Error('Failed to load research metrics');
            }

            metricsData = metricsResult.metrics;
            console.log('Metrics data loaded:', metricsData);

            // Display research details first
            if (researchDetails) {
                displayResearchDetails(researchDetails);
            }

            // Load timeline metrics
            console.log('Fetching timeline metrics...');
            const timelineResponse = await fetch(URLBuilder.build(URLS.METRICS_API.RESEARCH_TIMELINE, researchId));
            console.log('Timeline response status:', timelineResponse.status);

            let timelineData = null;
            if (timelineResponse.ok) {
                const timelineResult = await timelineResponse.json();
                console.log('Timeline result:', timelineResult);
                if (timelineResult.status === 'success') {
                    timelineData = timelineResult.metrics;
                }
            }

            // Load search metrics
            console.log('Fetching search metrics...');
            const searchResponse = await fetch(URLBuilder.build(URLS.METRICS_API.RESEARCH_SEARCH, researchId));
            console.log('Search response status:', searchResponse.status);

            let searchData = null;
            if (searchResponse.ok) {
                const searchResult = await searchResponse.json();
                console.log('Search result:', searchResult);
                if (searchResult.status === 'success') {
                    searchData = searchResult.metrics;
                }
            }

            // Display all data
            console.log('Displaying research metrics...');
            displayResearchMetrics();

            if (timelineData) {
                console.log('Displaying timeline metrics...');
                displayTimelineMetrics(timelineData);

                console.log('Chart.js available:', typeof Chart !== 'undefined');
                console.log('Timeline data for chart:', timelineData);
                createTimelineChart(timelineData);
            }

            if (searchData) {
                console.log('Displaying search metrics...');
                displaySearchMetrics(searchData);
                createSearchChart(searchData);
            }

            // Load cost data
            console.log('Loading cost data...');
            loadCostData();

            console.log('Showing details content...');
            const loadingEl = document.getElementById('loading');
            const contentEl = document.getElementById('details-content');
            const errorEl = document.getElementById('error');

            loadingEl.style.display = 'none';
            errorEl.style.display = 'none';
            contentEl.style.display = 'block';

            // Show metrics sections
            console.log('Showing metrics sections...');
            const tokenMetricsSection = document.getElementById('token-metrics-section');
            const searchMetricsSection = document.getElementById('search-metrics-section');

            if (tokenMetricsSection) {
                tokenMetricsSection.style.display = 'block';
                console.log('Token metrics section shown');
            }

            if (searchMetricsSection) {
                searchMetricsSection.style.display = 'block';
                console.log('Search metrics section shown');
            }

            // Force visibility with CSS overrides
            contentEl.style.visibility = 'visible';
            contentEl.style.opacity = '1';
            contentEl.style.position = 'relative';
            contentEl.style.zIndex = '1000';

            console.log('Loading display:', loadingEl.style.display);
            console.log('Content display:', contentEl.style.display);
            console.log('Error display:', errorEl.style.display);

            // Verify content is actually populated
            const totalTokensEl = document.getElementById('total-tokens');
            const researchQueryEl = document.getElementById('research-query');
            console.log('Total tokens value:', totalTokensEl ? totalTokensEl.textContent : 'ELEMENT NOT FOUND');
            console.log('Research query value:', researchQueryEl ? researchQueryEl.textContent : 'ELEMENT NOT FOUND');
            console.log('Content element height:', contentEl.offsetHeight);
            console.log('Content element children:', contentEl.children.length);

        } catch (error) {
            console.error('Error loading research metrics:', error);
            console.error('Error details:', error.message, error.stack);
            showError();
        }
    }

    // Display research details from history endpoint
    function displayResearchDetails(details) {
        console.log('displayResearchDetails called with:', details);

        // Update basic research info
        if (details.query) {
            document.getElementById('research-query').textContent = details.query;
        }
        if (details.mode) {
            document.getElementById('research-mode').textContent = details.mode;
        }
        if (details.created_at) {
            const date = new Date(details.created_at);
            document.getElementById('research-date').textContent = date.toLocaleString();
        }

        // Update strategy information
        if (details.strategy) {
            document.getElementById('research-strategy').textContent = details.strategy;
        } else {
            document.getElementById('research-strategy').textContent = 'Not recorded';
        }

        // Update progress
        if (details.progress !== undefined) {
            const progressFill = document.getElementById('detail-progress-fill');
            const progressText = document.getElementById('detail-progress-percentage');
            if (progressFill && progressText) {
                progressFill.style.width = `${details.progress}%`;
                progressText.textContent = `${details.progress}%`;
            }
        }
    }

    // Display basic research metrics
    function displayResearchMetrics() {
        console.log('displayResearchMetrics called with:', metricsData);
        if (!metricsData) {
            console.error('No metrics data available');
            return;
        }

        // Update summary cards
        const totalTokensEl = document.getElementById('total-tokens');
        const totalTokens = formatNumber(metricsData.total_tokens || 0);
        console.log('Setting total tokens to:', totalTokens);
        totalTokensEl.textContent = totalTokens;

        // Calculate prompt/completion tokens from model usage
        let totalPromptTokens = 0;
        let totalCompletionTokens = 0;
        let totalCalls = 0;
        let model = 'Unknown';

        if (metricsData.model_usage && metricsData.model_usage.length > 0) {
            metricsData.model_usage.forEach(usage => {
                totalPromptTokens += usage.prompt_tokens || 0;
                totalCompletionTokens += usage.completion_tokens || 0;
                totalCalls += usage.calls || 0;
                if (model === 'Unknown') {
                    model = usage.model || 'Unknown';
                }
            });
        }

        document.getElementById('prompt-tokens').textContent = formatNumber(totalPromptTokens);
        document.getElementById('completion-tokens').textContent = formatNumber(totalCompletionTokens);
        document.getElementById('llm-calls').textContent = formatNumber(metricsData.total_calls || totalCalls);

        // Update model info
        document.getElementById('model-used').textContent = model;

        // Response time will be updated by timeline data
        document.getElementById('avg-response-time').textContent = '0s';
    }

    // Display timeline metrics
    function displayTimelineMetrics(timelineData) {
        if (!timelineData) return;

        // Update research info from timeline data
        if (timelineData.research_details) {
            const details = timelineData.research_details;
            document.getElementById('research-query').textContent = details.query || 'Unknown';
            document.getElementById('research-mode').textContent = details.mode || 'Unknown';
            if (details.created_at) {
                const date = new Date(details.created_at);
                document.getElementById('research-date').textContent = date.toLocaleString();
            }
        }

        // Update summary info
        if (timelineData.summary) {
            const summary = timelineData.summary;
            const avgResponseTime = (summary.avg_response_time || 0) / 1000;
            document.getElementById('avg-response-time').textContent = `${avgResponseTime.toFixed(1)}s`;
        }

        // Display phase breakdown
        if (timelineData.phase_stats) {
            const container = document.getElementById('phase-breakdown');
            container.innerHTML = '';

            Object.entries(timelineData.phase_stats).forEach(([phaseName, stats]) => {
                const item = document.createElement('div');
                item.className = 'phase-stat-item';
                item.innerHTML = `
                    <div class="phase-name">${phaseName}</div>
                    <div class="phase-tokens">${formatNumber(stats.tokens)} tokens</div>
                    <div class="phase-calls">${formatNumber(stats.count)} calls</div>
                `;
                container.appendChild(item);
            });
        }
    }

    // Display search metrics
    function displaySearchMetrics(searchData) {
        if (!searchData) return;

        // Update search summary metrics
        const totalSearches = searchData.total_searches || 0;
        const totalResults = searchData.total_results || 0;
        const avgResponseTime = searchData.avg_response_time || 0;
        const successRate = searchData.success_rate || 0;

        document.getElementById('total-searches').textContent = formatNumber(totalSearches);
        document.getElementById('total-search-results').textContent = formatNumber(totalResults);
        document.getElementById('avg-search-response-time').textContent = `${avgResponseTime.toFixed(0)}ms`;
        document.getElementById('search-success-rate').textContent = `${successRate.toFixed(1)}%`;

        // Display search engine breakdown
        if (!searchData.search_calls) return;

        const container = document.getElementById('search-engine-breakdown');
        container.innerHTML = '';

        searchData.search_calls.forEach(call => {
            const item = document.createElement('div');
            item.className = 'search-engine-item';
            item.innerHTML = `
                <div class="search-engine-info">
                    <div class="search-engine-name">${call.engine || 'Unknown'}</div>
                    <div class="search-engine-query">${call.query || 'No query'}</div>
                </div>
                <div class="search-engine-stats">
                    <div class="search-results">${formatNumber(call.results_count || 0)} results</div>
                    <div class="search-time">${((call.response_time_ms || 0) / 1000).toFixed(1)}s</div>
                </div>
            `;
            container.appendChild(item);
        });
    }

    // Create timeline chart
    function createTimelineChart(timelineData) {
        console.log('createTimelineChart called with:', timelineData);

        if (!timelineData || !timelineData.timeline) {
            console.log('No timeline data for chart, timelineData:', timelineData);
            return;
        }

        if (typeof Chart === 'undefined') {
            console.error('Chart.js not loaded!');
            return;
        }

        const chartElement = document.getElementById('timeline-chart');
        if (!chartElement) {
            console.error('Timeline chart element not found');
            return;
        }

        console.log('Creating timeline chart with', timelineData.timeline.length, 'data points');

        try {
            const ctx = chartElement.getContext('2d');
            console.log('Canvas context obtained');

            // Destroy existing chart
            if (timelineChart) {
                timelineChart.destroy();
            }

            // Prepare chart data with enhanced information
            const chartData = timelineData.timeline.map((item, index) => ({
                phase: item.research_phase || item.phase || `Step ${index + 1}`,
                tokens: item.tokens || 0,
                promptTokens: item.prompt_tokens || 0,
                completionTokens: item.completion_tokens || 0,
                timestamp: item.timestamp || item.created_at,
                responseTime: item.response_time_ms || 0
            }));

            const labels = chartData.map(item => item.phase);
            const totalTokens = chartData.map(item => item.tokens);
            const promptTokens = chartData.map(item => item.promptTokens);
            const completionTokens = chartData.map(item => item.completionTokens);

            console.log('Enhanced chart data:', chartData);

            timelineChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Input Tokens',
                            data: promptTokens,
                            backgroundColor: 'rgba(99, 102, 241, 0.8)',
                            borderColor: 'rgba(99, 102, 241, 1)',
                            borderWidth: 1,
                            borderRadius: 4,
                            borderSkipped: false,
                        },
                        {
                            label: 'Output Tokens',
                            data: completionTokens,
                            backgroundColor: 'rgba(34, 197, 94, 0.8)',
                            borderColor: 'rgba(34, 197, 94, 1)',
                            borderWidth: 1,
                            borderRadius: 4,
                            borderSkipped: false,
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    scales: {
                        x: {
                            stacked: true,
                            grid: {
                                display: false
                            },
                            ticks: {
                                font: {
                                    size: 11
                                },
                                maxRotation: 45,
                                minRotation: 0
                            }
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)',
                                drawBorder: false
                            },
                            ticks: {
                                font: {
                                    size: 11
                                },
                                callback: function(value) {
                                    return formatNumber(value);
                                }
                            },
                            title: {
                                display: true,
                                text: 'Tokens',
                                font: {
                                    size: 12,
                                    weight: 'bold'
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            align: 'end',
                            labels: {
                                usePointStyle: true,
                                pointStyle: 'rect',
                                font: {
                                    size: 11
                                },
                                padding: 15
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: 'white',
                            bodyColor: 'white',
                            borderColor: 'rgba(255, 255, 255, 0.1)',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: true,
                            callbacks: {
                                title: function(tooltipItems) {
                                    const index = tooltipItems[0].dataIndex;
                                    const phase = chartData[index].phase;
                                    return phase;
                                },
                                beforeBody: function(tooltipItems) {
                                    const index = tooltipItems[0].dataIndex;
                                    const data = chartData[index];
                                    const total = data.tokens;
                                    return [`Total: ${formatNumber(total)} tokens`];
                                },
                                afterBody: function(tooltipItems) {
                                    const index = tooltipItems[0].dataIndex;
                                    const data = chartData[index];
                                    const lines = [];

                                    if (data.responseTime > 0) {
                                        lines.push(`Response time: ${(data.responseTime / 1000).toFixed(1)}s`);
                                    }

                                    if (data.timestamp) {
                                        const time = new Date(data.timestamp).toLocaleTimeString();
                                        lines.push(`Time: ${time}`);
                                    }

                                    return lines;
                                }
                            }
                        }
                    }
                }
            });

        console.log('Timeline chart created successfully');
        } catch (error) {
            console.error('Error creating timeline chart:', error);
            console.error('Chart error details:', error.message, error.stack);
        }
    }

    // Create search chart
    function createSearchChart(searchData) {
        if (!searchData || !searchData.search_calls) {
            console.log('No search data for chart');
            return;
        }

        const chartElement = document.getElementById('search-chart');
        if (!chartElement) {
            console.error('Search chart element not found');
            return;
        }

        const ctx = chartElement.getContext('2d');

        // Destroy existing chart
        if (searchChart) {
            searchChart.destroy();
        }

        // Prepare enhanced search data
        const searchCalls = searchData.search_calls.map((call, index) => ({
            label: call.query ? call.query.substring(0, 20) + '...' : `Search ${index + 1}`,
            results: call.results_count || 0,
            engine: call.engine || 'Unknown',
            responseTime: call.response_time_ms || 0,
            timestamp: call.timestamp
        }));

        const labels = searchCalls.map(call => call.label);
        const results = searchCalls.map(call => call.results);

        searchChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Results Found',
                    data: results,
                    borderColor: 'rgba(168, 85, 247, 1)',
                    backgroundColor: 'rgba(168, 85, 247, 0.1)',
                    borderWidth: 2.5,
                    fill: true,
                    tension: 0.3,
                    pointBackgroundColor: 'rgba(168, 85, 247, 1)',
                    pointBorderColor: 'rgba(255, 255, 255, 1)',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 800,
                    easing: 'easeInOutQuart'
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            },
                            maxRotation: 45
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                size: 10
                            },
                            callback: function(value) {
                                return formatNumber(value);
                            }
                        },
                        title: {
                            display: true,
                            text: 'Results',
                            font: {
                                size: 11,
                                weight: 'bold'
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1,
                        cornerRadius: 6,
                        callbacks: {
                            title: function(tooltipItems) {
                                const index = tooltipItems[0].dataIndex;
                                return searchCalls[index].label;
                            },
                            beforeBody: function(tooltipItems) {
                                const index = tooltipItems[0].dataIndex;
                                const call = searchCalls[index];
                                return [`Engine: ${call.engine}`];
                            },
                            afterBody: function(tooltipItems) {
                                const index = tooltipItems[0].dataIndex;
                                const call = searchCalls[index];
                                const lines = [];

                                if (call.responseTime > 0) {
                                    lines.push(`Response time: ${(call.responseTime / 1000).toFixed(1)}s`);
                                }

                                return lines;
                            }
                        }
                    }
                }
            }
        });
    }

    // Load cost data
    async function loadCostData() {
        try {
            // Temporarily disable cost calculation until pricing logic is optimized
            document.getElementById('total-cost').textContent = '-';
            return;

            const response = await fetch(URLBuilder.build(URLS.METRICS_API.RESEARCH_COSTS, researchId));
            if (response.ok) {
                const data = await response.json();
                if (data.status === 'success') {
                    document.getElementById('total-cost').textContent = formatCurrency(data.total_cost || 0);
                }
            }
        } catch (error) {
            console.error('Error loading cost data:', error);
            document.getElementById('total-cost').textContent = '-';
        }
    }

    // Show error message
    function showError() {
        document.getElementById('loading').style.display = 'none';
        document.getElementById('error').style.display = 'block';
        document.getElementById('details-content').style.display = 'none';
    }

    // Check if all required DOM elements exist
    function checkRequiredElements() {
        const requiredIds = [
            'loading', 'error', 'details-content', 'total-tokens', 'prompt-tokens',
            'completion-tokens', 'llm-calls', 'avg-response-time', 'model-used',
            'research-query', 'research-mode', 'research-date', 'research-strategy', 'total-cost',
            'phase-breakdown', 'search-engine-breakdown', 'timeline-chart', 'search-chart'
        ];

        const missing = [];
        requiredIds.forEach(id => {
            if (!document.getElementById(id)) {
                missing.push(id);
            }
        });

        if (missing.length > 0) {
            console.error('Missing required DOM elements:', missing);
            return false;
        }
        return true;
    }

    // Load and display context overflow data
    async function loadContextOverflowData(researchId) {
        try {
            const response = await fetch(`/metrics/api/research/${researchId}/context-overflow`);
            if (!response.ok) {
                console.error('Failed to load context overflow data');
                return;
            }

            const result = await response.json();
            if (result.status === 'success' && result.data) {
                displayContextOverflow(result.data);
                document.getElementById('context-overflow-section').style.display = 'block';
            }
        } catch (error) {
            console.error('Error loading context overflow data:', error);
        }
    }

    // Display context overflow data
    function displayContextOverflow(data) {
        const { overview, phase_stats, requests, model, provider } = data;

        // Update overview cards
        document.getElementById('co-total-tokens').textContent = formatNumber(overview.total_tokens);
        document.getElementById('co-context-limit').textContent = overview.context_limit ? formatNumber(overview.context_limit) : 'N/A';
        document.getElementById('co-max-tokens').textContent = formatNumber(overview.max_tokens_used);

        // Update truncation status
        const truncationStatus = document.getElementById('co-truncation-status');
        if (overview.truncation_occurred) {
            truncationStatus.innerHTML = `<span style="color: #ff4444;">Yes (${overview.truncated_count} requests)</span>`;
        } else {
            truncationStatus.innerHTML = '<span style="color: #4CAF50;">No truncation</span>';
        }

        // Display phase breakdown
        displayPhaseBreakdown(phase_stats);

        // Display requests table
        displayRequestsTable(requests);

        // Create usage chart
        if (requests && requests.length > 0) {
            createUsageChart(requests, overview.context_limit);
        }
    }

    // Display phase breakdown
    function displayPhaseBreakdown(phaseStats) {
        const container = document.getElementById('co-phase-breakdown');
        if (!phaseStats || Object.keys(phaseStats).length === 0) {
            container.innerHTML = '<p style="text-align: center; padding: 2rem; color: var(--text-secondary);">No phase data available</p>';
            return;
        }

        let html = '<div style="overflow-x: auto; background: var(--card-bg); border-radius: 0.5rem; padding: 0.5rem;">';
        html += '<table class="data-table">';
        html += '<thead><tr>';
        html += '<th>Phase</th>';
        html += '<th style="text-align: right;">Requests</th>';
        html += '<th style="text-align: right;">Prompt Tokens</th>';
        html += '<th style="text-align: right;">Completion Tokens</th>';
        html += '<th style="text-align: right;">Total Tokens</th>';
        html += '<th style="text-align: center;">Truncated</th>';
        html += '</tr></thead>';
        html += '<tbody>';

        for (const [phase, stats] of Object.entries(phaseStats)) {
            const truncatedBadge = stats.truncated_count > 0
                ? `<span class="badge badge-danger">${stats.truncated_count}</span>`
                : '<span class="badge badge-success">0</span>';

            html += `
                <tr>
                    <td>${phase}</td>
                    <td style="text-align: right;">${stats.count}</td>
                    <td style="text-align: right;">${formatNumber(stats.prompt_tokens)}</td>
                    <td style="text-align: right;">${formatNumber(stats.completion_tokens)}</td>
                    <td style="text-align: right; font-weight: bold;">${formatNumber(stats.total_tokens)}</td>
                    <td style="text-align: center;">${truncatedBadge}</td>
                </tr>
            `;
        }

        html += '</tbody></table>';
        html += '</div>';
        container.innerHTML = html;
    }

    // Display requests table
    function displayRequestsTable(requests) {
        const tbody = document.getElementById('co-requests-table');
        if (!requests || requests.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 2rem; color: var(--text-secondary);">No request data available</td></tr>';
            return;
        }

        let html = '';
        requests.forEach(req => {
            const timestamp = new Date(req.timestamp).toLocaleTimeString();
            const truncatedBadge = req.context_truncated
                ? '<span class="badge badge-danger">Yes</span>'
                : '<span class="badge badge-success">No</span>';
            const responseTime = req.response_time_ms ? `${req.response_time_ms}ms` : 'N/A';

            html += `
                <tr>
                    <td style="white-space: nowrap;">${timestamp}</td>
                    <td>${req.phase || 'N/A'}</td>
                    <td style="font-size: 0.85rem; max-width: 200px; overflow: hidden; text-overflow: ellipsis;">${req.calling_function || 'N/A'}</td>
                    <td style="text-align: right;">${formatNumber(req.prompt_tokens)}</td>
                    <td style="text-align: right;">${formatNumber(req.completion_tokens)}</td>
                    <td style="text-align: right; font-weight: bold;">${formatNumber(req.total_tokens)}</td>
                    <td style="text-align: right;">${req.context_limit ? formatNumber(req.context_limit) : 'N/A'}</td>
                    <td style="text-align: center;">${truncatedBadge}</td>
                    <td style="white-space: nowrap;">${responseTime}</td>
                </tr>
            `;
        });

        tbody.innerHTML = html;
    }

    // Create usage chart
    function createUsageChart(requests, contextLimit) {
        const canvas = document.getElementById('co-usage-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');

        // Prepare data
        const labels = requests.map((req, idx) => `Request ${idx + 1}`);
        const promptData = requests.map(req => req.prompt_tokens || 0);
        const completionData = requests.map(req => req.completion_tokens || 0);

        // Create chart
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: 'Prompt Tokens',
                        data: promptData,
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.1
                    },
                    {
                        label: 'Completion Tokens',
                        data: completionData,
                        borderColor: 'rgb(34, 197, 94)',
                        backgroundColor: 'rgba(34, 197, 94, 0.1)',
                        tension: 0.1
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: false
                    },
                    annotation: contextLimit ? {
                        annotations: {
                            line1: {
                                type: 'line',
                                yMin: contextLimit,
                                yMax: contextLimit,
                                borderColor: 'rgb(255, 99, 132)',
                                borderWidth: 2,
                                borderDash: [5, 5],
                                label: {
                                    content: `Context Limit (${contextLimit})`,
                                    enabled: true,
                                    position: 'end'
                                }
                            }
                        }
                    } : undefined
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Tokens'
                        }
                    }
                }
            }
        });
    }

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, initializing details page');

        researchId = getResearchIdFromUrl();
        console.log('Research ID from URL:', researchId);

        if (!researchId) {
            console.error('No research ID found in URL');
            showError();
            return;
        }

        // Check if all required elements exist
        if (!checkRequiredElements()) {
            console.error('Required DOM elements missing');
            showError();
            return;
        }

        // Update page title
        document.title = `Research Details #${researchId} - Deep Research System`;

        // Load research metrics
        loadResearchMetrics();

        // Load link analytics for this research
        loadLinkAnalytics(researchId);

        // Load context overflow data
        loadContextOverflowData(researchId);

        // View Results button
        const viewResultsBtn = document.getElementById('view-results-btn');
        if (viewResultsBtn) {
            viewResultsBtn.addEventListener('click', () => {
                window.location.href = URLBuilder.resultsPage(researchId);
            });
        }

        // Back button
        const backBtn = document.getElementById('back-to-history');
        if (backBtn) {
            backBtn.addEventListener('click', () => {
                window.location.href = URLS.PAGES.HISTORY;
            });
        }
    });

})();
