/**
 * Formatting Fallback Utilities
 * Basic implementations of formatting utilities that can be used if the main formatting module is not available
 */
(function() {
    // Only initialize if window.formatting is not already defined
    if (window.formatting) {
        console.log('Main formatting utilities already available, skipping fallback');
        return;
    }

    console.log('Initializing fallback formatting utilities');

    /**
     * Format a date
     * @param {string} dateStr - ISO date string
     * @returns {string} Formatted date
     */
    function formatDate(dateStr) {
        if (!dateStr) return 'N/A';

        try {
            const date = new Date(dateStr);
            return date.toLocaleString(undefined, {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (e) {
            console.error('Error formatting date:', e);
            return dateStr;
        }
    }

    /**
     * Format a status string
     * @param {string} status - Status code
     * @returns {string} Formatted status
     */
    function formatStatus(status) {
        if (!status) return 'Unknown';

        const statusMap = {
            'in_progress': 'In Progress',
            'completed': 'Completed',
            'failed': 'Failed',
            'suspended': 'Suspended',
            'pending': 'Pending',
            'error': 'Error'
        };

        return statusMap[status] || status;
    }

    /**
     * Format a mode string
     * @param {string} mode - Mode code
     * @returns {string} Formatted mode
     */
    function formatMode(mode) {
        if (!mode) return 'Unknown';

        const modeMap = {
            'quick': 'Quick Summary',
            'detailed': 'Detailed Report',
            'standard': 'Standard Research',
            'advanced': 'Advanced Research'
        };

        return modeMap[mode] || mode;
    }

    /**
     * Format duration in seconds to readable text
     * @param {number} seconds - Duration in seconds
     * @returns {string} Formatted duration
     */
    function formatDuration(seconds) {
        if (!seconds && seconds !== 0) return 'N/A';

        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;

        if (minutes === 0) {
            return `${remainingSeconds}s`;
        } else {
            return `${minutes}m ${remainingSeconds}s`;
        }
    }

    /**
     * Format file size in bytes to human readable format
     * @param {number} bytes - Size in bytes
     * @returns {string} Formatted size
     */
    function formatFileSize(bytes) {
        if (!bytes && bytes !== 0) return 'N/A';

        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let size = bytes;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(1)} ${units[unitIndex]}`;
    }

    // Export utilities to window.formatting
    window.formatting = {
        formatDate,
        formatStatus,
        formatMode,
        formatDuration,
        formatFileSize
    };
})();
