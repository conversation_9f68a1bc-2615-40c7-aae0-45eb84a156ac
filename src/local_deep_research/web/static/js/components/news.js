// News Component JavaScript
// Handles reusable news-related UI components

// Empty alert container
let alertTimeout = null;

// Component initialization
document.addEventListener('DOMContentLoaded', function() {
    // Initialize any news-specific components
    initializeSliders();
});

// Initialize sliders with value display
function initializeSliders() {
    const sliders = document.querySelectorAll('input[type="range"]');
    sliders.forEach(slider => {
        const valueDisplay = slider.nextElementSibling;
        if (valueDisplay && valueDisplay.classList.contains('slider-value')) {
            slider.addEventListener('input', function() {
                valueDisplay.textContent = this.value;
            });
        }
    });
}

// Modal utilities
window.showModal = function(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
    }
};

window.hideModal = function(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
};

// Close modal on outside click
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
});

// Empty state helper
window.showEmptyState = function(container, message, icon = 'fas fa-newspaper') {
    container.innerHTML = `
        <div class="empty-state">
            <i class="${icon}"></i>
            <h3>No items found</h3>
            <p>${message}</p>
        </div>
    `;
};

// Loading state helper
window.showLoadingState = function(container, message = 'Loading...') {
    container.innerHTML = `
        <div class="loading-placeholder">
            <div class="loading-spinner"></div>
            <p>${message}</p>
        </div>
    `;
};

// Error state helper
window.showErrorState = function(container, message) {
    container.innerHTML = `
        <div class="error-state">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>Error</h3>
            <p>${message}</p>
        </div>
    `;
};

// Format timestamp
window.formatTimeAgo = function(timestamp) {
    const now = new Date();
    const time = new Date(timestamp);
    const diff = Math.floor((now - time) / 1000); // seconds

    if (diff < 60) return 'just now';
    if (diff < 3600) return `${Math.floor(diff / 60)} minutes ago`;
    if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`;
    if (diff < 604800) return `${Math.floor(diff / 86400)} days ago`;

    return time.toLocaleDateString();
};

// Create tag element
window.createTag = function(text, className = 'tag') {
    const tag = document.createElement('span');
    tag.className = className;
    tag.textContent = text;
    return tag;
};

// Debounce helper
window.debounce = function(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

// Export utilities for use in other scripts
window.NewsUtils = {
    showModal,
    hideModal,
    showEmptyState,
    showLoadingState,
    showErrorState,
    formatTimeAgo,
    createTag,
    debounce
};
