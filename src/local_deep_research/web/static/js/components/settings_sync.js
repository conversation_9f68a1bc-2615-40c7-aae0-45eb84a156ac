// Function to save settings using the individual settings manager API
function saveMenuSettings(settingKey, settingValue) {
    console.log('Saving setting:', settingKey, '=', settingValue);

    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Use the individual settings API endpoint that uses the settings manager
    fetch(`/settings/api/${settingKey}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify({ value: settingValue })
    })
    .then(response => response.json())
    .then(data => {
        console.log(`Setting ${settingKey} saved via settings manager:`, data);

        // If the response includes warnings, display them directly
        if (data.warnings && typeof window.displayWarnings === 'function') {
            window.displayWarnings(data.warnings);
        }

        // Also trigger client-side warning recalculation for search settings
        if (settingKey.startsWith('search.') || settingKey === 'llm.provider') {
            if (typeof window.refetchSettingsAndUpdateWarnings === 'function') {
                window.refetchSettingsAndUpdateWarnings();
            }
        }

        // Show success notification if UI module is available
        if (window.ui && window.ui.showMessage) {
            window.ui.showMessage(`${settingKey.split('.').pop()} updated successfully`, 'success');
        } else {
            console.log('Setting saved successfully:', data);
        }
    })
    .catch(error => {
        console.error(`Error saving setting ${settingKey}:`, error);
        if (window.ui && window.ui.showMessage) {
            window.ui.showMessage(`Error updating ${settingKey}: ${error.message}`, 'error');
        }
    });
}

/**
 * Connects the menu settings to use the same save method as the settings page.
 */
function connectMenuSettings() {
    console.log('Initializing menu settings handler');

    // Handle model dropdown changes
    const modelInput = document.getElementById('model');
    const modelHidden = document.getElementById('model_hidden');

    if (modelHidden) {
        modelHidden.addEventListener('change', function(e) {
            console.log('Model changed to:', this.value);
            saveMenuSettings('llm.model', this.value);
        });
    }

    // Handle provider dropdown changes
    const providerSelect = document.getElementById('model_provider');
    if (providerSelect) {
        providerSelect.addEventListener('change', function(e) {
            console.log('Provider changed to:', this.value);
            saveMenuSettings('llm.provider', this.value);
        });
    }

    // Handle search engine dropdown changes
    const searchEngineHidden = document.getElementById('search_engine_hidden');
    if (searchEngineHidden) {
        searchEngineHidden.addEventListener('change', function(e) {
            console.log('Search engine changed to:', this.value);
            saveMenuSettings('search.tool', this.value);
        });
    }

    // Handle iterations and questions per iteration
    const iterationsInput = document.getElementById('iterations');
    if (iterationsInput) {
        iterationsInput.addEventListener('change', function(e) {
            console.log('Iterations changed to:', this.value);
            saveMenuSettings('search.iterations', this.value);
        });
    }

    const questionsInput = document.getElementById('questions_per_iteration');
    if (questionsInput) {
        questionsInput.addEventListener('change', function(e) {
            console.log('Questions per iteration changed to:', this.value);
            saveMenuSettings('search.questions_per_iteration', this.value);
        });
    }

    // Handle search strategy dropdown changes
    const strategySelect = document.getElementById('strategy');
    if (strategySelect) {
        strategySelect.addEventListener('change', function(e) {
            console.log('Search strategy changed to:', this.value);
            saveMenuSettings('search.search_strategy', this.value);
        });
    }

    console.log('Menu settings handlers initialized');
}

// Call this function after the page and other scripts are loaded
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit to ensure other scripts have loaded
    setTimeout(connectMenuSettings, 1000);
});
