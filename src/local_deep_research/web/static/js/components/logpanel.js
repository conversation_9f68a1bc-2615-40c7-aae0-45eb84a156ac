/**
 * LogPanel Component
 * Handles the display and interaction with the research log panel
 * Used by both progress.js and results.js
 */
(function() {
    // Shared state for log panel
    window._logPanelState = window._logPanelState || {
        expanded: false,
        queuedLogs: [],
        logCount: 0,
        initialized: false, // Track initialization state
        connectedResearchId: null, // Track which research we're connected to
        currentFilter: 'all', // Track current filter type
        autoscroll: true, // Track whether autoscroll is enabled.
    };

    /**
     * Initialize the log panel
     * @param {string} researchId - Optional research ID to load logs for
     */
    function initializeLogPanel(researchId = null) {
        // Check if already initialized
        if (window._logPanelState.initialized) {
            console.log('Log panel already initialized, checking if research ID has changed');

            // If we're already connected to this research, do nothing
            if (window._logPanelState.connectedResearchId === researchId) {
                console.log('Already connected to research ID:', researchId);
                return;
            }

            // If the research ID has changed, we'll update our connection
            console.log('Research ID changed from', window._logPanelState.connectedResearchId, 'to', researchId);
            window._logPanelState.connectedResearchId = researchId;
        } else {
             // Add callback for log download button.
            const downloadButton = document.getElementById('log-download-button');
            if (downloadButton) {
                downloadButton.addEventListener('click', downloadLogs);
            }
        }

        console.log('Initializing shared log panel, research ID:', researchId);

        // Check if we're on a research-specific page (progress, results)
        const isResearchPage = window.location.pathname.includes('/progress/') ||
                              window.location.pathname.includes('/results/') ||
                              document.getElementById('research-progress') ||
                              document.getElementById('research-results');

        // Get all log panels on the page (there might be duplicates)
        const logPanels = document.querySelectorAll('.collapsible-log-panel');

        if (logPanels.length > 1) {
            console.warn(`Found ${logPanels.length} log panels, removing duplicates`);

            // Keep only the first one and remove others
            for (let i = 1; i < logPanels.length; i++) {
                console.log(`Removing duplicate log panel #${i}`);
                logPanels[i].remove();
            }
        } else if (logPanels.length === 0) {
            console.error('No log panel found in the DOM!');
            return;
        }

        // Get log panel elements with both old and new names for compatibility
        let logPanelToggle = document.getElementById('log-panel-toggle');
        let logPanelContent = document.getElementById('log-panel-content');

        // Fallback to the old element IDs if needed
        if (!logPanelToggle) logPanelToggle = document.getElementById('logToggle');
        if (!logPanelContent) logPanelContent = document.getElementById('logPanel');

        if (!logPanelToggle || !logPanelContent) {
            console.warn('Log panel elements not found, skipping initialization');
            return;
        }

        const autoscrollButton = document.querySelector('#log-autoscroll-button');

        // Handle visibility based on page type
        if (!isResearchPage) {
            console.log('Not on a research-specific page, hiding log panel');

            // Hide the log panel on non-research pages
            const panel = logPanelContent.closest('.collapsible-log-panel');
            if (panel) {
                panel.style.display = 'none';
            } else if (logPanelContent.parentElement) {
                logPanelContent.parentElement.style.display = 'none';
            } else {
                logPanelContent.style.display = 'none';
            }
            return;
        } else {
            // Ensure log panel is visible on research pages
            console.log('On a research page, ensuring log panel is shown');
            const panel = logPanelContent.closest('.collapsible-log-panel');
            if (panel) {
                panel.style.display = 'flex';
            }
        }

        console.log('Log panel elements found, setting up handlers');

        // Mark as initialized to prevent double initialization
        window._logPanelState.initialized = true;

        // Check for CSS issue - if the panel's computed style has display:none, the panel won't be visible
        const computedStyle = window.getComputedStyle(logPanelContent);
        console.log('Log panel CSS visibility:', {
            display: computedStyle.display,
            visibility: computedStyle.visibility,
            height: computedStyle.height,
            overflow: computedStyle.overflow
        });

        // Ensure the panel is visible in the DOM
        if (computedStyle.display === 'none') {
            console.warn('Log panel has display:none - forcing display:flex');
            logPanelContent.style.display = 'flex';
        }

        // Ensure we have a console log container
        const consoleLogContainer = document.getElementById('console-log-container');
        if (!consoleLogContainer) {
            console.error('Console log container not found, logs will not be displayed');
        } else {
            // Add placeholder message
            consoleLogContainer.innerHTML = '<div class="empty-log-message">No logs available. Expand panel to load logs.</div>';
        }

        // Set up toggle click handler
        logPanelToggle.addEventListener('click', function() {
            console.log('Log panel toggle clicked');

            // Toggle collapsed state
            logPanelContent.classList.toggle('collapsed');
            logPanelToggle.classList.toggle('collapsed');

            const collapsed = logPanelContent.classList.contains('collapsed');

            const toggleIcon = logPanelToggle.querySelector('.toggle-icon');
            if (toggleIcon && !collapsed) {
                // Load logs if not already loaded
                if (!logPanelContent.dataset.loaded && researchId) {
                    console.log('First expansion of log panel, loading logs');
                    loadLogsForResearch(researchId);
                    logPanelContent.dataset.loaded = 'true';
                }

                // Process any queued logs
                if (window._logPanelState.queuedLogs.length > 0) {
                    console.log(`Processing ${window._logPanelState.queuedLogs.length} queued logs`);
                    window._logPanelState.queuedLogs.forEach(logEntry => {
                        addLogEntryToPanel(logEntry);
                    });
                    window._logPanelState.queuedLogs = [];
                }
            }

            // Default to showing the autoscroll button.
            if (autoscrollButton !== null) {
                autoscrollButton.style.display = 'inline';
            }

            const logPanel = document.querySelector('.collapsible-log-panel');
            const progressHeader = document.querySelector('#research-progres-header');
            const progressCard = document.querySelector('#research-progress-card');
            if (!collapsed && logPanel !== null && progressCard !== null && progressHeader !== null) {
                // Set the height of the log panel to occupy all the screen
                // real-estate that's not currently being used by the `research-progress`
                // component.
                // We add a little extra space to account for margins.
                logPanel.style.height = `calc(100vh - 96px - ${getComputedStyle(progressCard).height} - ${getComputedStyle(progressHeader).height})`;

                // Start with autoscroll on when expanding.
                window._logPanelState.autoscroll = false;
                toggleAutoscroll();
            } else if (logPanel !== null) {
                // Use the default height.
                logPanel.style.height = 'auto';
                // Hide the autoscroll button since it doesn't make
                // sense in this context.
                if (autoscrollButton !== null) {
                    autoscrollButton.style.display = 'none';
                }
            }

            // Track expanded state
            window._logPanelState.expanded = !collapsed;
        });

        if (autoscrollButton) {
            // Set up autoscroll handler for the log panel. When autoscroll is
            // enabled, it will automatically scroll as new logs are added.
            autoscrollButton.addEventListener('click', toggleAutoscroll);
        }

        // Set up filter button click handlers
        const filterButtons = document.querySelectorAll('.log-filter .filter-buttons button');
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                const type = this.textContent.toLowerCase();
                console.log(`Filtering logs by type: ${type}`);

                // Update active state
                filterButtons.forEach(btn => btn.classList.remove('selected'));
                this.classList.add('selected');

                // Apply filtering
                filterLogsByType(type);
            });
        });

        // Start with panel collapsed and fix initial chevron direction
        logPanelContent.classList.add('collapsed');
        const initialToggleIcon = logPanelToggle.querySelector('.toggle-icon');
        if (initialToggleIcon) {
            initialToggleIcon.className = 'fas fa-chevron-right toggle-icon';
        }

        // Initialize the log count
        const logIndicators = document.querySelectorAll('.log-indicator');
        if (logIndicators.length > 0) {
            // Set count on all indicators
            logIndicators.forEach(indicator => {
                indicator.textContent = '0';
            });

            // Fetch the log count from the API and update the indicators
            fetch(`/history/log_count/${researchId}`)
                .then(response => response.json())
                .then(data => {
                    console.log('Log count data:', data);
                    if (data && typeof data.total_logs === 'number') {
                        logIndicators.forEach(indicator => {
                            indicator.textContent = data.total_logs;
                        });
                    } else {
                        console.error('Invalid log count data received from API');
                    }
                })
                .catch(error => {
                    console.error('Error fetching log count:', error);
                });
        } else {
            console.warn('No log indicators found for initialization');
        }

        // Check CSS display property of the log panel
        const logPanel = document.querySelector('.collapsible-log-panel');
        if (logPanel) {
            const panelStyle = window.getComputedStyle(logPanel);
            console.log('Log panel CSS display:', panelStyle.display);

            if (panelStyle.display === 'none') {
                console.warn('Log panel has CSS display:none - forcing display:flex');
                logPanel.style.display = 'flex';
            }
        }

        // Pre-load logs if hash includes #logs
        if (window.location.hash === '#logs' && researchId) {
            console.log('Auto-loading logs due to #logs in URL');
            setTimeout(() => {
                logPanelToggle.click();
            }, 500);
        }

        // DEBUG: Force expand the log panel if URL has debug parameter
        if (window.location.search.includes('debug=logs') || window.location.hash.includes('debug')) {
            console.log('DEBUG: Force-expanding log panel');
            setTimeout(() => {
                if (logPanelContent.classList.contains('collapsed')) {
                    logPanelToggle.click();
                }
            }, 800);
        }

        // Register global functions to ensure they work across modules
        window.addConsoleLog = addConsoleLog;
        window.filterLogsByType = filterLogsByType;

        // Add a connector to socket.js
        // Track when we last received this exact message to avoid re-adding within 10 seconds
        const processedMessages = new Map();
        window._socketAddLogEntry = function(logEntry) {
            // Simple message deduplication for socket events
            const message = logEntry.message || logEntry.content || '';
            const messageKey = `${message}-${logEntry.type || 'info'}`;
            const now = Date.now();

            // Check if we've seen this message recently (within 10 seconds)
            if (processedMessages.has(messageKey)) {
                const lastProcessed = processedMessages.get(messageKey);
                const timeDiff = now - lastProcessed;

                if (timeDiff < 10000) { // 10 seconds
                    console.log(`Skipping duplicate socket message received within ${timeDiff}ms:`, message);
                    return;
                }
            }

            // Update our tracking
            processedMessages.set(messageKey, now);

            // Clean up old entries (keep map from growing indefinitely)
            if (processedMessages.size > 100) {
                // Remove entries older than 60 seconds
                for (const [key, timestamp] of processedMessages.entries()) {
                    if (now - timestamp > 60000) {
                        processedMessages.delete(key);
                    }
                }
            }

            // Process the log entry
            addLogEntryToPanel(logEntry);
        };

        console.log('Log panel initialized');
    }

    /**
     * @brief Toggles autoscroll on or off.
     */
    function toggleAutoscroll() {
        window._logPanelState.autoscroll = !window._logPanelState.autoscroll;

        const autoscrollButton = document.querySelector('#log-autoscroll-button');
        const consoleLogContainer = document.getElementById('console-log-container');
        if (!autoscrollButton || !consoleLogContainer) {
            console.error("Autoscroll button or console log container not found.");
            return;
        }

        // Highlight the autoscroll button in purple when it's
        // enabled to make that clear.
        if (window._logPanelState.autoscroll) {
            autoscrollButton.classList.add('selected');
            // Immediately scroll to the bottom of the panel.
            consoleLogContainer.scrollTop = consoleLogContainer.scrollHeight;
        } else {
            autoscrollButton.classList.remove('selected');
        }
    }

    /**
     * @brief Fetches all the logs for a research instance from the API.
     * @param researchId The ID of the research instance.
     * @returns {Promise<any>} The logs.
     */
    async function fetchLogsForResearch(researchId) {
        // Fetch logs from API
        const response = await fetch(URLBuilder.researchLogs(researchId));
        return await response.json();
    }

    /**
     * Load logs for a specific research
     * @param {string} researchId - The research ID to load logs for
     */
    async function loadLogsForResearch(researchId) {
        try {
            // Show loading state
            const logContent = document.getElementById('console-log-container');
            if (logContent) {
                logContent.innerHTML = '<div class="loading-spinner centered"><div class="spinner"></div><div style="margin-left: 10px;">Loading logs...</div></div>';
            }

            console.log('Loading logs for research ID:', researchId);

            const data = await fetchLogsForResearch(researchId);
            console.log('Logs API response:', data);

            // Initialize array to hold all logs from different sources
            const allLogs = [];

            // Track seen messages to avoid duplicate content with different timestamps
            const seenMessages = new Map();

            // Process progress_log if available
            if (data.progress_log && typeof data.progress_log === 'string') {
                try {
                    const progressLogs = JSON.parse(data.progress_log);
                    if (Array.isArray(progressLogs) && progressLogs.length > 0) {
                        console.log(`Found ${progressLogs.length} logs in progress_log`);

                        // Process progress logs
                        progressLogs.forEach(logItem => {
                            if (!logItem.time || !logItem.message) return; // Skip invalid logs

                            // Skip if we've seen this exact message before
                            const messageKey = normalizeMessage(logItem.message);
                            if (seenMessages.has(messageKey)) {
                                // Only consider logs within 1 minute of each other as duplicates
                                const previousLog = seenMessages.get(messageKey);
                                const previousTime = new Date(previousLog.time);
                                const currentTime = new Date(logItem.time);
                                const timeDiff = Math.abs(currentTime - previousTime) / 1000; // in seconds

                                if (timeDiff < 60) { // Within 1 minute
                                    // Use the newer timestamp if available
                                    if (currentTime > previousTime) {
                                        previousLog.time = logItem.time;
                                    }
                                    return; // Skip this duplicate
                                }

                                // If we get here, it's the same message but far apart in time (e.g., a repeated step)
                                // We'll include it as a separate entry
                            }

                            // Determine log type based on metadata
                            let logType = 'info';
                            if (logItem.metadata) {
                                if (logItem.metadata.phase === 'iteration_complete' ||
                                    logItem.metadata.phase === 'report_complete' ||
                                    logItem.metadata.phase === 'complete' ||
                                    logItem.metadata.is_milestone === true) {
                                    logType = 'milestone';
                                } else if (logItem.metadata.phase === 'error') {
                                    logType = 'error';
                                }
                            }

                            // Add message keywords for better type detection
                            if (logType !== 'milestone') {
                                const msg = logItem.message.toLowerCase();
                                if (msg.includes('complete') ||
                                    msg.includes('finished') ||
                                    msg.includes('starting phase') ||
                                    msg.includes('generated report')) {
                                    logType = 'milestone';
                                } else if (msg.includes('error') || msg.includes('failed')) {
                                    logType = 'error';
                                }
                            }

                            // Create a log entry object with a unique ID for deduplication
                            const logEntry = {
                                id: `${logItem.time}-${hashString(logItem.message)}`,
                                time: logItem.time,
                                message: logItem.message,
                                type: logType,
                                metadata: logItem.metadata || {},
                                source: 'progress_log'
                            };

                            // Track this message to avoid showing exact duplicates with different timestamps
                            seenMessages.set(messageKey, logEntry);

                            // Add to all logs array
                            allLogs.push(logEntry);
                        });
                    }
                } catch (e) {
                    console.error('Error parsing progress_log:', e);
                }
            }

            // Standard logs array processing
            // Check if data is directly an array (new format) or has a logs property (old format)
            const logsArray = Array.isArray(data) ? data : (data && data.logs);

            if (logsArray && Array.isArray(logsArray)) {
                console.log(`Processing ${logsArray.length} standard logs`);

                // Process each standard log
                logsArray.forEach(log => {
                    if (!log.timestamp && !log.time) return; // Skip invalid logs

                    // Skip duplicates based on message content
                    const messageKey = normalizeMessage(log.message || log.content || '');
                    if (seenMessages.has(messageKey)) {
                        // Only consider logs within 1 minute of each other as duplicates
                        const previousLog = seenMessages.get(messageKey);
                        const previousTime = new Date(previousLog.time);
                        const currentTime = new Date(log.timestamp || log.time);
                        const timeDiff = Math.abs(currentTime - previousTime) / 1000; // in seconds

                        if (timeDiff < 60) { // Within 1 minute
                            // Use the newer timestamp if available
                            if (currentTime > previousTime) {
                                previousLog.time = log.timestamp || log.time;
                            }
                            return; // Skip this duplicate
                        }
                    }

                    // Create standardized log entry
                    const logEntry = {
                        id: `${log.timestamp || log.time}-${hashString(log.message || log.content || '')}`,
                        time: log.timestamp || log.time,
                        message: log.message || log.content || 'No message',
                        type: log.log_type || log.type || log.level || 'info',
                        metadata: log.metadata || {},
                        source: 'standard_logs'
                    };

                    // Track this message
                    seenMessages.set(messageKey, logEntry);

                    // Add to all logs array
                    allLogs.push(logEntry);
                });
            }

            // Clear container
            if (logContent) {
                if (allLogs.length === 0) {
                    logContent.innerHTML = '<div class="empty-log-message">No logs available for this research.</div>';
                    return;
                }

                logContent.innerHTML = '';

                // Normalize timestamps - in case there are logs with mismatched AM/PM time zones
                // This attempts to ensure logs are in a proper chronological order
                normalizeTimestamps(allLogs);

                // Deduplicate logs by ID and sort by timestamp (oldest first)
                const uniqueLogsMap = new Map();
                allLogs.forEach(log => {
                    // Use the ID as the key for deduplication
                    uniqueLogsMap.set(log.id, log);
                });

                // Convert map back to array
                const uniqueLogs = Array.from(uniqueLogsMap.values());

                // Sort logs by timestamp (oldest first)
                const sortedLogs = uniqueLogs.sort((a, b) => {
                    return new Date(a.time) - new Date(b.time);
                });

                console.log(`Displaying ${sortedLogs.length} logs after deduplication (from original ${allLogs.length})`);

                // Add each log entry to panel
                sortedLogs.forEach(log => {
                    addLogEntryToPanel(log, false); // False means don't increment counter
                });

                // Update log count indicator
                const logIndicators = document.querySelectorAll('.log-indicator');
                if (logIndicators.length > 0) {
                    // Set all indicators to the same count
                    logIndicators.forEach(indicator => {
                        indicator.textContent = sortedLogs.length;
                    });
                }
            }

        } catch (error) {
            console.error('Error loading logs:', error);

            // Show error in log panel
            const logContent = document.getElementById('console-log-container');
            if (logContent) {
                logContent.innerHTML = `<div class="error-message">Error loading logs: ${error.message}</div>`;
            }
        }
    }

    /**
     * Normalize a message for deduplication comparison
     * @param {string} message - The message to normalize
     * @returns {string} - Normalized message for comparison
     */
    function normalizeMessage(message) {
        if (!message) return '';
        // Remove extra whitespace and lowercase
        return message.trim().toLowerCase();
    }

    /**
     * Normalize timestamps across logs to ensure consistent ordering
     * @param {Array} logs - The logs to normalize
     */
    function normalizeTimestamps(logs) {
        // Find the most common date in the logs (ignoring the time)
        const dateFrequency = new Map();

        logs.forEach(log => {
            try {
                const date = new Date(log.time);
                // Extract just the date part (YYYY-MM-DD)
                const dateStr = date.toISOString().split('T')[0];
                dateFrequency.set(dateStr, (dateFrequency.get(dateStr) || 0) + 1);
            } catch (e) {
                console.error('Error parsing date:', log.time);
            }
        });

        // Find the most frequent date
        let mostCommonDate = null;
        let highestFrequency = 0;

        dateFrequency.forEach((count, date) => {
            if (count > highestFrequency) {
                highestFrequency = count;
                mostCommonDate = date;
            }
        });

        console.log(`Most common date: ${mostCommonDate} with ${highestFrequency} occurrences`);

        if (!mostCommonDate) return; // Can't normalize without a common date

        // Normalize all logs to the most common date
        logs.forEach(log => {
            try {
                const date = new Date(log.time);
                const dateStr = date.toISOString().split('T')[0];

                // If this log is from a different date, adjust it to the most common date
                // while preserving the time portion
                if (dateStr !== mostCommonDate) {
                    const [year, month, day] = mostCommonDate.split('-');
                    date.setFullYear(parseInt(year));
                    date.setMonth(parseInt(month) - 1); // Months are 0-indexed
                    date.setDate(parseInt(day));

                    // Update the log time
                    log.time = date.toISOString();
                    log.id = `${log.time}-${hashString(log.message)}`;
                    console.log(`Normalized timestamp for "${log.message.substring(0, 30)}..." from ${dateStr} to ${mostCommonDate}`);
                }
            } catch (e) {
                console.error('Error normalizing date:', log.time);
            }
        });
    }

    /**
     * Simple hash function for strings
     * @param {string} str - String to hash
     * @returns {string} - Hashed string for use as ID
     */
    function hashString(str) {
        if (!str) return '0';
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return hash.toString();
    }

    /**
     * Add a log entry to the console - public API
     * @param {string} message - Log message
     * @param {string} level - Log level (info, milestone, error)
     * @param {Object} metadata - Optional metadata
     */
    function addConsoleLog(message, level = 'info', metadata = null) {
        console.log(`[${level.toUpperCase()}] ${message}`);

        const timestamp = new Date().toISOString();
        const logEntry = {
            id: `${timestamp}-${hashString(message)}`,
            time: timestamp,
            message: message,
            type: level,
            metadata: metadata || { type: level }
        };

        // Queue log entries if panel is not expanded yet
        if (!window._logPanelState.expanded) {
            window._logPanelState.queuedLogs.push(logEntry);
            console.log('Queued log entry for later display');

            // Update log count even if not displaying yet
            updateLogCounter(1);

            // Auto-expand log panel on first log
            const logPanelToggle = document.getElementById('log-panel-toggle');
            if (logPanelToggle) {
                console.log('Auto-expanding log panel because logs are available');
                logPanelToggle.click();
            }

            return;
        }

        // Add directly to panel if it's expanded
        addLogEntryToPanel(logEntry, true);
    }

    /**
     * Add a log entry directly to the panel
     * @param {Object} logEntry - The log entry to add
     * @param {boolean} incrementCounter - Whether to increment the log counter
     */
    function addLogEntryToPanel(logEntry, incrementCounter = true) {
        console.log('Adding log entry to panel:', logEntry);

        const consoleLogContainer = document.getElementById('console-log-container');
        if (!consoleLogContainer) {
            console.warn('Console log container not found');
            return;
        }

        // Clear empty message if present
        const emptyMessage = consoleLogContainer.querySelector('.empty-log-message');
        if (emptyMessage) {
            emptyMessage.remove();
        }

        // Ensure the log entry has an ID
        if (!logEntry.id) {
            const timestamp = logEntry.time || logEntry.timestamp || new Date().toISOString();
            const message = logEntry.message || logEntry.content || 'No message';
            logEntry.id = `${timestamp}-${hashString(message)}`;
        }

        // More robust deduplication: First check by ID if available
        if (logEntry.id) {
            const existingEntryById = consoleLogContainer.querySelector(`.console-log-entry[data-log-id="${logEntry.id}"]`);
            if (existingEntryById) {
                console.log('Skipping duplicate log entry by ID:', logEntry.id);

                // Increment counter on existing entry
                let counter = parseInt(existingEntryById.dataset.counter || '1');
                counter++;
                existingEntryById.dataset.counter = counter;

                // Update visual counter badge
                if (counter > 1) {
                    let counterBadge = existingEntryById.querySelector('.duplicate-counter');
                    if (!counterBadge) {
                        counterBadge = document.createElement('span');
                        counterBadge.className = 'duplicate-counter';
                        existingEntryById.appendChild(counterBadge);
                    }
                    counterBadge.textContent = `(${counter}×)`;
                }

                // Still update the global counter if needed
                if (incrementCounter) {
                    updateLogCounter(1);
                }

                return;
            }
        }

        // Secondary check for duplicate by message content (for backward compatibility)
        const existingEntries = consoleLogContainer.querySelectorAll('.console-log-entry');
        if (existingEntries.length > 0) {
            const message = logEntry.message || logEntry.content || '';
            const logType = (logEntry.type || 'info').toLowerCase();

            // Start from the end since newest logs are now at the bottom
            for (let i = existingEntries.length - 1; i >= Math.max(0, existingEntries.length - 10); i--) {
                // Only check the 10 most recent entries for efficiency
                const entry = existingEntries[i];
                const entryMessage = entry.querySelector('.log-message')?.textContent;
                const entryType = entry.dataset.logType;

                // If message and type match, consider it a duplicate (unless it's a milestone)
                if (entryMessage === message &&
                    entryType === logType &&
                    logType !== 'milestone') {

                    console.log('Skipping duplicate log entry by content:', message);

                    // Increment counter on existing entry
                    let counter = parseInt(entry.dataset.counter || '1');
                    counter++;
                    entry.dataset.counter = counter;

                    // Update visual counter badge
                    if (counter > 1) {
                        let counterBadge = entry.querySelector('.duplicate-counter');
                        if (!counterBadge) {
                            counterBadge = document.createElement('span');
                            counterBadge.className = 'duplicate-counter';
                            entry.appendChild(counterBadge);
                        }
                        counterBadge.textContent = `(${counter}×)`;
                    }

                    // Still update the global counter if needed
                    if (incrementCounter) {
                        updateLogCounter(1);
                    }

                    return;
                }
            }
        }

        // Get the log template
        const template = document.getElementById('console-log-entry-template');

        // Determine log level - CHECK FOR DIRECT TYPE FIELD FIRST
        let logLevel = 'info';
        if (logEntry.type) {
            logLevel = logEntry.type;
        } else if (logEntry.metadata && logEntry.metadata.type) {
            logLevel = logEntry.metadata.type;
        } else if (logEntry.level) {
            logLevel = logEntry.level;
        }

        // Format timestamp
        const timestamp = new Date(logEntry.time || logEntry.timestamp || new Date());
        const timeStr = timestamp.toLocaleTimeString();

        // Get message
        const message = logEntry.message || logEntry.content || 'No message';

        if (template) {
            // Create a new log entry from the template
            const entry = document.importNode(template.content, true);
            const logEntryElement = entry.querySelector('.console-log-entry');

            // Add the log type as data attribute for filtering
            if (logEntryElement) {
                logEntryElement.dataset.logType = logLevel.toLowerCase();
                logEntryElement.classList.add(`log-${logLevel.toLowerCase()}`);
                // Initialize counter for duplicate tracking
                logEntryElement.dataset.counter = '1';
                // Store log ID for deduplication
                if (logEntry.id) {
                    logEntryElement.dataset.logId = logEntry.id;
                }

                // Add special attribute for engine selection events
                if (logEntry.metadata && logEntry.metadata.phase === 'engine_selected') {
                    logEntryElement.dataset.engineSelected = 'true';
                    // Store engine name as a data attribute
                    if (logEntry.metadata.engine) {
                        logEntryElement.dataset.engine = logEntry.metadata.engine;
                    }
                }
            }

            // Set content
            entry.querySelector('.log-timestamp').textContent = timeStr;
            entry.querySelector('.log-badge').textContent = logLevel.charAt(0).toUpperCase() + logLevel.slice(1);
            entry.querySelector('.log-badge').className = `log-badge ${logLevel.toLowerCase()}`;
            entry.querySelector('.log-message').textContent = message;

            // Add to container (at the end for oldest first)
            consoleLogContainer.appendChild(entry);
        } else {
            // Create a simple log entry without template
            const entry = document.createElement('div');
            entry.className = 'console-log-entry';
            entry.dataset.logType = logLevel.toLowerCase();
            entry.classList.add(`log-${logLevel.toLowerCase()}`);
            entry.dataset.counter = '1';
            if (logEntry.id) {
                entry.dataset.logId = logEntry.id;
            }

            // Create log content
            entry.innerHTML = `
                <span class="log-timestamp">${timeStr}</span>
                <span class="log-badge ${logLevel.toLowerCase()}">${logLevel.charAt(0).toUpperCase() + logLevel.slice(1)}</span>
                <span class="log-message">${message}</span>
            `;

            // Add to container (at the end for oldest first)
            consoleLogContainer.appendChild(entry);
        }

        // Check if the entry should be visible based on current filter
        const currentFilter = window._logPanelState.currentFilter || 'all';
        const shouldShow = checkLogVisibility(logLevel.toLowerCase(), currentFilter);

        // Apply visibility based on the current filter
        const newEntry = consoleLogContainer.lastElementChild;
        if (newEntry) {
            newEntry.style.display = shouldShow ? '' : 'none';
        }

        // Update log count using helper function if needed
        if (incrementCounter) {
            updateLogCounter(1);
        }

        // No need to scroll when loading all logs
        // Scroll will be handled after all logs are loaded
        if (incrementCounter && window._logPanelState.autoscroll) {
            // Auto-scroll to newest log (at the bottom)
            setTimeout(() => {
                consoleLogContainer.scrollTop = consoleLogContainer.scrollHeight;
            }, 0);
        }
    }

    /**
     * Helper function to update the log counter
     * @param {number} increment - Amount to increment the counter by
     */
    function updateLogCounter(increment) {
        const logIndicators = document.querySelectorAll('.log-indicator');
        if (logIndicators.length > 0) {
            const currentCount = parseInt(logIndicators[0].textContent) || 0;
            const newCount = currentCount + increment;

            // Update all indicators
            logIndicators.forEach(indicator => {
                indicator.textContent = newCount;
            });
        }
    }

    /**
     * Check if a log entry should be visible based on filter type
     * @param {string} logType - The type of log (info, milestone, error)
     * @param {string} filterType - The selected filter (all, info, milestone, error)
     * @returns {boolean} - Whether the log should be visible
     */
    function checkLogVisibility(logType, filterType) {
        switch (filterType) {
            case 'all':
                return true;
            case 'info':
                return logType === 'info' || logType === 'warning' || logType === 'milestone' || logType === 'error';
            case 'milestone':
            case 'milestones': // Handle plural form too
                return logType === 'milestone';
            case 'warning':
            case 'warnings':
                return logType === 'warning' || logType === 'error';
            case 'error':
            case 'errors': // Handle plural form too
                return logType === 'error';
            default:
                return true; // Default to showing everything
        }
    }

    /**
     * Filter logs by type
     * @param {string} filterType - The type to filter by (all, info, milestone, error)
     */
    function filterLogsByType(filterType = 'all') {
        console.log('Filtering logs by type:', filterType);

        filterType = filterType.toLowerCase();

        // Store current filter in shared state
        window._logPanelState.currentFilter = filterType;

        // Get all log entries from the DOM
        const logEntries = document.querySelectorAll('.console-log-entry');
        console.log(`Found ${logEntries.length} log entries to filter`);

        let visibleCount = 0;

        // Apply filters
        logEntries.forEach(entry => {
            // Use data attribute for log type
            const logType = entry.dataset.logType || 'info';

            // Determine visibility based on filter type
            const shouldShow = checkLogVisibility(logType, filterType);

            // Set display style based on filter result
            entry.style.display = shouldShow ? '' : 'none';

            if (shouldShow) {
                visibleCount++;
            }
        });

        console.log(`Filtering complete. Showing ${visibleCount} of ${logEntries.length} logs`);

        // Show 'no logs' message if all logs are filtered out
        const consoleContainer = document.getElementById('console-log-container');
        if (consoleContainer && logEntries.length > 0) {
            // Remove any existing empty message
            const existingEmptyMessage = consoleContainer.querySelector('.empty-log-message');
            if (existingEmptyMessage) {
                existingEmptyMessage.remove();
            }

            // Add empty message if needed
            if (visibleCount === 0) {
                console.log(`Adding 'no logs' message for filter: ${filterType}`);
                const newEmptyMessage = document.createElement('div');
                newEmptyMessage.className = 'empty-log-message';
                newEmptyMessage.textContent = `No ${filterType} logs to display.`;
                consoleContainer.appendChild(newEmptyMessage);
            }
        }
    }

    /**
     * @brief Handler for the log download button which downloads all the
     * saved logs to the user's computer.
     */
    function downloadLogs() {
        const researchId = window._logPanelState.connectedResearchId;
        fetchLogsForResearch(researchId).then((logData) => {
            // Create a blob with the logs data
            const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });

            // Create a link element and trigger download
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `research_logs_${researchId}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });
    }

    // Expose public API
    window.logPanel = {
        initialize: initializeLogPanel,
        addLog: addConsoleLog,
        filterLogs: filterLogsByType,
        loadLogs: loadLogsForResearch
    };

    // Self-invoke to initialize when DOM content is loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM ready - checking if log panel should be initialized');

        // Find research ID from URL if available (supports both integer and UUID)
        let researchId = null;
        const urlMatch = window.location.pathname.match(/\/(progress|results)\/([a-zA-Z0-9-]+)/);
        if (urlMatch && urlMatch[2]) {
            researchId = urlMatch[2];
            console.log('Found research ID in URL:', researchId);

            // Store the current research ID in the state
            window._logPanelState.connectedResearchId = researchId;
        }

        // Check for research page elements
        const isResearchPage = window.location.pathname.includes('/progress/') ||
                              window.location.pathname.includes('/results/') ||
                              document.getElementById('research-progress') ||
                              document.getElementById('research-results');

        // Initialize log panel if on a research page
        if (isResearchPage) {
            console.log('On a research page, initializing log panel for research ID:', researchId);
            initializeLogPanel(researchId);

            // Extra check: If we have a research ID but panel not initialized properly
            setTimeout(() => {
                if (researchId && !window._logPanelState.initialized) {
                    console.log('Log panel not initialized properly, retrying...');
                    initializeLogPanel(researchId);
                }
            }, 1000);
        } else {
            console.log('Not on a research page, skipping log panel initialization');
        }
    });
})();
