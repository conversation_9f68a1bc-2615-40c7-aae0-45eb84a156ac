/**
 * Centralized URL configuration for the Local Deep Research application
 * This prevents hardcoded URLs scattered throughout the codebase
 */

const URLS = {
    // API endpoints
    API: {
        START_RESEARCH: '/api/start_research',
        RESEARCH_STATUS: '/api/research/{id}/status',
        RESEARCH_DETAILS: '/api/research/{id}',
        RESEARCH_LOGS: '/api/research/{id}/logs',
        RESEARCH_REPORT: '/api/report/{id}',
        TERMINATE_RESEARCH: '/api/terminate/{id}',
        DELETE_RESEARCH: '/api/delete/{id}',
        CLEAR_HISTORY: '/api/clear_history',
        SAVE_RAW_CONFIG: '/api/save_raw_config',
        SAVE_MAIN_CONFIG: '/api/save_main_config',
        SAVE_SEARCH_ENGINES_CONFIG: '/api/save_search_engines_config',
        SAVE_COLLECTIONS_CONFIG: '/api/save_collections_config',
        SAVE_API_KEYS_CONFIG: '/api/save_api_keys_config',
        SAVE_LLM_CONFIG: '/api/save_llm_config',
        MARKDOWN_EXPORT: '/api/markdown/{id}',
        HISTORY: '/history/api',  // Changed to match backend route
        HEALTH: '/api/v1/health'  // Added health check endpoint
    },

    // Page routes
    PAGES: {
        HOME: '/',
        PROGRESS: '/progress/{id}',
        RESULTS: '/results/{id}',
        DETAILS: '/details/{id}',
        HISTORY: '/history/',
        SETTINGS: '/settings/',
        METRICS: '/metrics/',
        METRICS_COSTS: '/metrics/costs',  // Added metrics subpage
        METRICS_STAR_REVIEWS: '/metrics/star-reviews'  // Added metrics subpage
    },

    // History API routes
    HISTORY_API: {
        STATUS: '/history/status/{id}',
        DETAILS: '/history/details/{id}',
        LOGS: '/history/logs/{id}',
        REPORT: '/history/report/{id}',
        MARKDOWN: '/history/markdown/{id}',
        LOG_COUNT: '/history/log_count/{id}'
    },

    // Settings API routes
    SETTINGS_API: {
        BASE: '/settings/api',
        GET_SETTING: '/settings/api/{key}',
        UPDATE_SETTING: '/settings/api/{key}',
        DELETE_SETTING: '/settings/api/{key}',
        IMPORT_SETTINGS: '/settings/api/import',
        CATEGORIES: '/settings/api/categories',
        TYPES: '/settings/api/types',
        UI_ELEMENTS: '/settings/api/ui_elements',
        AVAILABLE_MODELS: '/settings/api/available-models',
        AVAILABLE_SEARCH_ENGINES: '/settings/api/available-search-engines',
        WARNINGS: '/settings/api/warnings',
        OLLAMA_STATUS: '/settings/api/ollama-status',
        LLM_MODEL: '/settings/api/llm.model',
        LLM_PROVIDER: '/settings/api/llm.provider',
        LLM_CONFIG: '/settings/api/llm',
        SEARCH_TOOL: '/settings/api/search.tool',
        SEARCH_STRATEGY: '/settings/api/search.search_strategy',
        SAVE_ALL_SETTINGS: '/settings/save_all_settings',
        RESET_TO_DEFAULTS: '/settings/reset_to_defaults',
        FIX_CORRUPTED_SETTINGS: '/settings/fix_corrupted_settings'
    },

    // Metrics API routes
    METRICS_API: {
        BASE: '/metrics/api/metrics',
        RESEARCH: '/metrics/api/metrics/research/{id}',
        RESEARCH_TIMELINE: '/metrics/api/metrics/research/{id}/timeline',
        RESEARCH_SEARCH: '/metrics/api/metrics/research/{id}/search',
        COST_ANALYTICS: '/metrics/api/cost-analytics',
        PRICING: '/metrics/api/pricing',
        RATINGS_GET: '/metrics/api/ratings/{id}',
        RATINGS_SAVE: '/metrics/api/ratings/{id}',
        RESEARCH_COSTS: '/metrics/api/research-costs/{id}'
    }
};

/**
 * URL builder utility functions
 */
const URLBuilder = {
    /**
     * Build a URL by replacing {id} placeholders with actual values
     * @param {string} urlTemplate - URL template with {id} placeholders
     * @param {string|number} id - The ID to substitute
     * @returns {string} - The built URL
     */
    build(urlTemplate, id) {
        return urlTemplate.replace('{id}', id);
    },

    /**
     * Build a URL with custom replacements
     * @param {string} urlTemplate - URL template with placeholders
     * @param {Object} replacements - Object with key-value pairs for replacement
     * @returns {string} - The built URL
     */
    buildWithReplacements(urlTemplate, replacements) {
        let url = urlTemplate;
        for (const [key, value] of Object.entries(replacements)) {
            url = url.replace(`{${key}}`, value);
        }
        return url;
    },

    // Convenience methods for common URL patterns
    progressPage(researchId) {
        return this.build(URLS.PAGES.PROGRESS, researchId);
    },

    resultsPage(researchId) {
        return this.build(URLS.PAGES.RESULTS, researchId);
    },

    detailsPage(researchId) {
        return this.build(URLS.PAGES.DETAILS, researchId);
    },

    researchStatus(researchId) {
        return this.build(URLS.API.RESEARCH_STATUS, researchId);
    },

    researchDetails(researchId) {
        return this.build(URLS.API.RESEARCH_DETAILS, researchId);
    },

    researchLogs(researchId) {
        return this.build(URLS.API.RESEARCH_LOGS, researchId);
    },

    researchReport(researchId) {
        return this.build(URLS.API.RESEARCH_REPORT, researchId);
    },

    terminateResearch(researchId) {
        return this.build(URLS.API.TERMINATE_RESEARCH, researchId);
    },

    deleteResearch(researchId) {
        return this.build(URLS.API.DELETE_RESEARCH, researchId);
    },

    // History API convenience methods
    historyStatus(researchId) {
        return this.build(URLS.HISTORY_API.STATUS, researchId);
    },

    historyDetails(researchId) {
        return this.build(URLS.HISTORY_API.DETAILS, researchId);
    },

    historyLogs(researchId) {
        return this.build(URLS.HISTORY_API.LOGS, researchId);
    },

    markdownExport(researchId) {
        return this.build(URLS.API.MARKDOWN_EXPORT, researchId);
    },

    historyReport(researchId) {
        return this.build(URLS.HISTORY_API.REPORT, researchId);
    },

    // URL Pattern Extraction Methods
    /**
     * Extract research ID from current URL path
     * @returns {string|null} Research ID or null if not found
     */
    extractResearchId() {
        const path = window.location.pathname;

        // Try different URL patterns
        const patterns = [
            /\/results\/(\d+)/,      // /results/123
            /\/details\/(\d+)/,      // /details/123
            /\/progress\/(\d+)/      // /progress/123
        ];

        for (const pattern of patterns) {
            const match = path.match(pattern);
            if (match) {
                return match[1];
            }
        }

        return null;
    },

    /**
     * Extract research ID from a specific URL pattern
     * @param {string} pattern - The pattern to match ('results', 'details', 'progress')
     * @returns {string|null} Research ID or null if not found
     */
    extractResearchIdFromPattern(pattern) {
        const path = window.location.pathname;
        // Match both integer IDs and UUIDs
        const regex = new RegExp(`\\/${pattern}\\/([a-zA-Z0-9-]+)`);
        const match = path.match(regex);
        return match ? match[1] : null;
    },

    /**
     * Get current page type based on URL
     * @returns {string} Page type ('home', 'results', 'details', 'progress', 'history', 'settings', 'metrics', 'unknown')
     */
    getCurrentPageType() {
        const path = window.location.pathname;

        if (path === '/' || path === '/index' || path === '/home') return 'home';
        if (path.startsWith('/results/')) return 'results';
        if (path.startsWith('/details/')) return 'details';
        if (path.startsWith('/progress/')) return 'progress';
        if (path.startsWith('/history')) return 'history';
        if (path.startsWith('/settings')) return 'settings';
        if (path.startsWith('/metrics')) return 'metrics';

        return 'unknown';
    },

    historyMarkdown(researchId) {
        return this.build(URLS.HISTORY_API.MARKDOWN, researchId);
    },

    historyLogCount(researchId) {
        return this.build(URLS.HISTORY_API.LOG_COUNT, researchId);
    },

    // Settings API convenience methods
    getSetting(key) {
        return this.buildWithReplacements(URLS.SETTINGS_API.GET_SETTING, { key });
    },

    updateSetting(key) {
        return this.buildWithReplacements(URLS.SETTINGS_API.UPDATE_SETTING, { key });
    },

    deleteSetting(key) {
        return this.buildWithReplacements(URLS.SETTINGS_API.DELETE_SETTING, { key });
    },

    // Metrics API convenience methods
    researchMetrics(researchId) {
        return this.build(URLS.METRICS_API.RESEARCH, researchId);
    },

    researchTimelineMetrics(researchId) {
        return this.build(URLS.METRICS_API.RESEARCH_TIMELINE, researchId);
    },

    researchSearchMetrics(researchId) {
        return this.build(URLS.METRICS_API.RESEARCH_SEARCH, researchId);
    },

    getRating(researchId) {
        return this.build(URLS.METRICS_API.RATINGS_GET, researchId);
    },

    saveRating(researchId) {
        return this.build(URLS.METRICS_API.RATINGS_SAVE, researchId);
    },

    researchCosts(researchId) {
        return this.build(URLS.METRICS_API.RESEARCH_COSTS, researchId);
    }
};

// Export for module systems first (before window assignment)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { URLS, URLBuilder };
}

// Make URLs and URLBuilder available globally
if (typeof window !== 'undefined') {
    window.URLS = URLS;
    window.URLBuilder = URLBuilder;
}
