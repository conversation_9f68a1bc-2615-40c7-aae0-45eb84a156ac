/**
 * Main JavaScript Entry Point
 * Loads appropriate components based on the current page
 */
(function() {
    // Map of page identifiers to component scripts
    const pageComponents = {
        'page-home': ['research.js'],
        'page-progress': ['progress.js'],
        'page-results': ['results.js'],
        'page-detail': ['detail.js'],
        'page-history': ['history.js'],
        'page-settings': ['settings.js']
    };

    // Core services to always load
    const coreServices = [
        'formatting.js',
        'ui.js',
        'api.js',
        'socket.js'
        // 'audio.js' - Removed from here, loaded separately
    ];

    // Optional services to load when needed
    const optionalServices = {
        'page-results': ['pdf.js'],
        'page-detail': ['pdf.js']
    };

    /**
     * Initialize the application
     */
    function initializeApp() {
        // Detect current page
        const currentPage = detectCurrentPage();

        if (!currentPage) {
            console.error('Cannot detect current page type');
            return;
        }

        console.log('Current page detected:', currentPage);

        // IMPORTANT: Load audio.js first, before ANY other scripts
        loadAudioServiceFirst(() => {
            // Continue loading other scripts after audio service is loaded
            console.log('Audio service script loaded, continuing with other scripts');

            // Load UI and formatting utils
            loadScripts('utils', coreServices.filter(s => s.includes('formatting') || s.includes('ui')));

            // Then load the rest
            loadScripts('services', coreServices.filter(s => s.includes('api') || s.includes('socket')));

            // Load optional services for this page
            if (optionalServices[currentPage]) {
                loadScripts('services', optionalServices[currentPage]);
            }

            // Load components for this page AFTER all services
            if (pageComponents[currentPage]) {
                loadScripts('components', pageComponents[currentPage]);
            }

            // Initialize tooltips and other global UI elements
            initializeGlobalUI();
        });
    }

    /**
     * Load audio service first and separately to ensure it's fully loaded before other scripts
     * @param {Function} callback - Function to call after audio service is loaded
     */
    function loadAudioServiceFirst(callback) {
        console.log('Loading audio service script first...');
        const audioScript = document.createElement('script');
        audioScript.src = `/static/js/services/audio.js?t=${new Date().getTime()}`; // Add timestamp to avoid cache
        audioScript.async = false;

        // Set up callback for when script loads
        audioScript.onload = function() {
            console.log('Audio service script loaded successfully');

            // Check if audio service is available in window object
            setTimeout(() => {
                if (window.audio) {
                    console.log('Audio service initialized in window object');
                } else {
                    console.warn('Audio service not available in window object after script load');
                }

                // Continue regardless
                callback();
            }, 100); // Small delay to ensure script executes
        };

        // Error handling
        audioScript.onerror = function() {
            console.error('Failed to load audio service script');
            // Continue with other scripts even if audio fails
            callback();
        };

        // Add to document
        document.body.appendChild(audioScript);
    }

    /**
     * Detect the current page based on body class
     * @returns {string|null} The page identifier or null if not found
     */
    function detectCurrentPage() {
        const bodyClasses = document.body.classList;

        for (const pageId in pageComponents) {
            if (bodyClasses.contains(pageId)) {
                return pageId;
            }
        }

        // Check URL patterns as fallback
        const path = window.location.pathname;

        if (path === '/' || path === '/index' || path === '/home') {
            return 'page-home';
        } else if (path.includes('/progress')) {
            return 'page-progress';
        } else if (path.includes('/results')) {
            return 'page-results';
        } else if (path.includes('/details')) {
            return 'page-detail';
        } else if (path.includes('/history')) {
            return 'page-history';
        } else if (path.includes('/settings')) {
            return 'page-settings';
        }

        return null;
    }

    /**
     * Load scripts dynamically
     * @param {string} folder - The folder containing the scripts
     * @param {Array} scripts - Array of script filenames to load
     */
    function loadScripts(folder, scripts) {
        if (!scripts || !scripts.length) return;

        scripts.forEach(script => {
            const scriptElement = document.createElement('script');
            scriptElement.src = `/static/js/${folder}/${script}`;
            scriptElement.async = false; // Load in sequence
            document.body.appendChild(scriptElement);
        });
    }

    /**
     * Initialize global UI elements
     */
    function initializeGlobalUI() {
        // Initialize notifications if browser supports it
        if ("Notification" in window && Notification.permission === "default") {
            // Only ask for permission when user interacts with the page
            document.addEventListener('click', requestNotificationPermission, { once: true });
        }

        // Initialize theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            // Check for saved theme preference or use system preference
            const savedTheme = localStorage.getItem('theme');
            const systemDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const isDarkMode = savedTheme === 'dark' || (savedTheme === null && systemDarkMode);

            // Set initial theme
            document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
            themeToggle.innerHTML = isDarkMode ?
                '<i class="fas fa-sun"></i>' :
                '<i class="fas fa-moon"></i>';

            // Listen for theme toggle click
            themeToggle.addEventListener('click', function() {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);

                themeToggle.innerHTML = newTheme === 'dark' ?
                    '<i class="fas fa-sun"></i>' :
                    '<i class="fas fa-moon"></i>';
            });
        }

        // Initialize mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navMenu = document.getElementById('main-nav');

        if (mobileMenuToggle && navMenu) {
            mobileMenuToggle.addEventListener('click', function() {
                navMenu.classList.toggle('open');
                mobileMenuToggle.setAttribute(
                    'aria-expanded',
                    navMenu.classList.contains('open') ? 'true' : 'false'
                );
            });
        }
    }

    /**
     * Request notification permission
     */
    function requestNotificationPermission() {
        if ("Notification" in window && Notification.permission === "default") {
            Notification.requestPermission();
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeApp);
    } else {
        initializeApp();
    }
})();
