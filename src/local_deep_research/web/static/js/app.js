/**
 * Main application entry point for Vite
 * Imports all vendor dependencies and makes them available globally
 */

// Import CSS first
import '@fortawesome/fontawesome-free/css/all.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap-icons/font/bootstrap-icons.css';
import 'highlight.js/styles/github-dark.css';
import '../css/styles.css';

// Import and setup core libraries
import { marked } from 'marked';
import io from 'socket.io-client';
import hljs from 'highlight.js';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

// Import Bootstrap JavaScript
import * as bootstrap from 'bootstrap';

// Chart.js and plugins (imported but not attached to window by default)
import Chart from 'chart.js/auto';
import 'chartjs-adapter-date-fns';
import annotationPlugin from 'chartjs-plugin-annotation';

// Register Chart.js plugin
Chart.register(annotationPlugin);

// Make libraries available globally for existing code compatibility
window.marked = marked;
window.io = io;
window.hljs = hljs;
window.jsPDF = jsPDF;
window.html2canvas = html2canvas;
window.bootstrap = bootstrap;
window.Chart = Chart;

// Configure marked
marked.setOptions({
  headerIds: false,
  mangle: false,
  smartypants: false,
  highlight: function(code, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(code, { language: lang }).value;
      } catch (err) {
        console.error('Highlight error:', err);
      }
    }
    return code;
  }
});

// Initialize highlight.js when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Highlight all code blocks
  document.querySelectorAll('pre code').forEach((block) => {
    hljs.highlightElement(block);
  });

  // Initialize Bootstrap tooltips and popovers if present
  const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
  [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

  const popoverTriggerList = document.querySelectorAll('[data-bs-toggle="popover"]');
  [...popoverTriggerList].map(popoverTriggerEl => new bootstrap.Popover(popoverTriggerEl));
});

// Log successful initialization
console.log('✨ LDR App initialized with Vite');

// Export for potential module usage
export {
  marked,
  io,
  hljs,
  jsPDF,
  html2canvas,
  bootstrap,
  Chart
};
