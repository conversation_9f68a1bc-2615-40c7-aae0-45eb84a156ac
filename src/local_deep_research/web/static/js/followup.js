/**
 * Follow-up Research JavaScript Module
 *
 * Handles UI interactions for asking follow-up questions on existing research.
 */

class FollowUpResearch {
    constructor() {
        this.parentResearchId = null;
        this.modalElement = null;
    }

    /**
     * Initialize follow-up button on research results
     */
    init() {
        // Get research ID from page
        this.parentResearchId = this.getResearchIdFromPage();

        console.log('Follow-up Research: Parent research ID:', this.parentResearchId);

        // Set up the follow-up button if it exists
        const followUpBtn = document.getElementById('ask-followup-btn');
        if (followUpBtn) {
            if (this.parentResearchId) {
                followUpBtn.onclick = () => this.showFollowUpModal();
            } else {
                console.warn('Follow-up Research: No research ID found, disabling button');
                followUpBtn.disabled = true;
                followUpBtn.title = 'No research ID available';
            }
        }

        // Create modal for follow-up questions
        this.createModal();
    }

    /**
     * Get research ID from current page
     */
    getResearchIdFromPage() {
        // Try multiple ways to get research ID

        // 1. From URL path (e.g., /results/abc-123-def)
        const pathMatch = window.location.pathname.match(/\/results\/([a-zA-Z0-9-]+)/);
        if (pathMatch) {
            return pathMatch[1];
        }

        // 2. From URL params
        const urlParams = new URLSearchParams(window.location.search);
        const paramId = urlParams.get('research_id');
        if (paramId) {
            return paramId;
        }

        // 3. From data attribute
        const dataId = document.querySelector('[data-research-id]')?.dataset.researchId;
        if (dataId) {
            return dataId;
        }

        // 4. From window variable (set by results.js)
        if (window.currentResearchId) {
            return window.currentResearchId;
        }

        return null;
    }

    /**
     * Create modal for follow-up questions
     */
    async createModal() {
        // Check if modal already exists
        if (document.getElementById('followUpModal')) {
            return;
        }

        // Load modal template from server
        try {
            const response = await fetch('/static/templates/followup_modal.html');
            if (!response.ok) {
                throw new Error(`Failed to load modal template: ${response.status}`);
            }

            const modalHtml = await response.text();
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            this.modalElement = document.getElementById('followUpModal');
            this.attachModalEventHandlers();
            this.addModalStyles();
        } catch (e) {
            console.error('Error loading follow-up modal template:', e);
            // Show error message to user
            this.showModalLoadError();
        }
    }

    /**
     * Attach event handlers to modal elements
     */
    attachModalEventHandlers() {
        const startBtn = document.getElementById('startFollowUpBtn');
        if (startBtn) {
            startBtn.addEventListener('click', () => this.submitFollowUp());
        }
    }

    /**
     * Add modal styles if needed
     */
    addModalStyles() {
        // Add custom CSS if needed
        if (!document.getElementById('followup-modal-styles')) {
            const styles = `
                <style id="followup-modal-styles">
                    .modal-backdrop {
                        position: fixed;
                        top: 0;
                        left: 0;
                        z-index: 1040;
                        width: 100vw;
                        height: 100vh;
                        background-color: #000;
                        opacity: 0.5;
                    }
                    #followUpModal {
                        position: fixed;
                        top: 0;
                        left: 0;
                        z-index: 1050;
                        width: 100%;
                        height: 100%;
                        overflow-x: hidden;
                        overflow-y: auto;
                        outline: 0;
                        display: none;
                    }
                    #followUpModal.show {
                        display: flex !important;
                        align-items: center;
                        justify-content: center;
                    }
                    #followUpModal .modal-dialog {
                        position: relative;
                        margin: 1.75rem auto;
                        max-width: 800px;
                    }
                    #followUpModal .modal-content {
                        background: #1a1a1a;
                        color: #e0e0e0;
                        border-radius: 0.5rem;
                        box-shadow: 0 10px 25px rgba(0,0,0,0.5);
                        border: 1px solid #333;
                    }
                    #followUpModal .modal-header {
                        border-bottom: 1px solid #333;
                    }
                    #followUpModal .modal-footer {
                        border-top: 1px solid #333;
                    }
                    #followUpModal .btn-close {
                        filter: invert(1);
                    }
                    #followUpModal .ldr-form-control,
                    #followUpModal .form-select {
                        background-color: #2a2a2a;
                        color: #e0e0e0;
                        border: 1px solid #444;
                    }
                    #followUpModal .ldr-form-control:focus,
                    #followUpModal .form-select:focus {
                        background-color: #2a2a2a;
                        color: #e0e0e0;
                        border-color: #007bff;
                        box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
                    }
                    #followUpModal .bg-light {
                        background-color: #2a2a2a !important;
                    }
                    #followUpModal .text-muted {
                        color: #999 !important;
                    }
                    #followUpModal .alert-info {
                        background-color: #1e3a5f;
                        border-color: #2d5a8f;
                        color: #9ec5fe;
                    }
                </style>
            `;
            document.head.insertAdjacentHTML('beforeend', styles);
        }
    }

    /**
     * Show follow-up modal and load parent context
     */
    async showFollowUpModal() {
        if (!this.modalElement) {
            await this.createModal();
        }

        // Only proceed if modal was successfully created
        if (!this.modalElement) {
            return;
        }

        // Load parent context
        await this.loadParentContext();

        // Show modal
        const modal = new bootstrap.Modal(this.modalElement);
        modal.show();
    }

    /**
     * Show error message when modal template fails to load
     */
    showModalLoadError() {
        const followUpBtn = document.getElementById('ask-followup-btn');
        if (followUpBtn) {
            followUpBtn.disabled = true;
            followUpBtn.title = 'Failed to load follow-up modal template';
        }

        // Show user-friendly error
        alert('Unable to load follow-up interface. Please refresh the page and try again.');
    }

    /**
     * Load parent research context
     */
    async loadParentContext() {
        try {
            // Get CSRF token from meta tag or cookie
            let csrfToken = '';
            const csrfMeta = document.querySelector('meta[name="csrf-token"]');
            if (csrfMeta) {
                csrfToken = csrfMeta.content;
            } else {
                // Try to get from cookie
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 'csrf_token') {
                        csrfToken = decodeURIComponent(value);
                        break;
                    }
                }
            }

            const response = await fetch('/api/followup/prepare', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    parent_research_id: this.parentResearchId,
                    question: 'test' // Needs a non-empty question
                })
            });

            const data = await response.json();

            if (data.success) {
                // Display parent context
                document.getElementById('parentContext').style.display = 'block';
                document.getElementById('parentSummary').textContent = data.parent_summary;
                document.getElementById('parentSources').textContent = data.available_sources;
            }
        } catch (error) {
            console.error('Error loading parent context:', error);
        }
    }

    /**
     * Submit follow-up research
     */
    async submitFollowUp() {
        const question = document.getElementById('followUpQuestion').value.trim();
        if (!question) {
            alert('Please enter a follow-up question');
            return;
        }

        if (!this.parentResearchId) {
            alert('No parent research ID available');
            return;
        }

        console.log('Submitting follow-up research:', {
            parent_research_id: this.parentResearchId,
            question: question
        });

        try {
            // Get CSRF token from meta tag or cookie
            let csrfToken = '';
            const csrfMeta = document.querySelector('meta[name="csrf-token"]');
            if (csrfMeta) {
                csrfToken = csrfMeta.content;
            } else {
                // Try to get from cookie
                const cookies = document.cookie.split(';');
                for (let cookie of cookies) {
                    const [name, value] = cookie.trim().split('=');
                    if (name === 'csrf_token') {
                        csrfToken = decodeURIComponent(value);
                        break;
                    }
                }
            }

            // Start follow-up research
            const response = await fetch('/api/followup/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    parent_research_id: this.parentResearchId,
                    question: question
                    // Strategy now comes from database settings
                })
            });

            if (!response.ok) {
                console.error('Follow-up API error:', response.status, response.statusText);
                const text = await response.text();
                console.error('Response body:', text);
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('Follow-up API response:', data);

            if (data.success) {
                // Close modal
                bootstrap.Modal.getInstance(this.modalElement).hide();

                // Redirect to progress page to show the research is running
                window.location.href = `/progress/${data.research_id}`;
            } else {
                alert('Error starting follow-up research: ' + (data.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error submitting follow-up:', error);
            alert('Failed to start follow-up research: ' + error.message);
        }
    }
}

// Initialize on page load
const followUpResearch = new FollowUpResearch();
document.addEventListener('DOMContentLoaded', () => {
    followUpResearch.init();
});

// Helper function for toggling advanced options
function toggleAdvancedOptions(event) {
    event.preventDefault();
    const panel = document.getElementById('advancedOptionsPanel');
    panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
}
