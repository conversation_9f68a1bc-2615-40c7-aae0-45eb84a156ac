/**
 * Socket Service
 * Manages WebSocket communication using Socket.IO
 */

window.socket = (function() {
    let socket = null;
    let researchEventHandlers = {};
    let reconnectCallback = null;
    let connectionAttempts = 0;
    const MAX_CONNECTION_ATTEMPTS = 3;

    // Keep track of the research we're currently subscribed to
    let currentResearchId = null;

    // Track if we're using polling fallback
    let usingPolling = false;

    /**
     * Initialize the Socket.IO connection
     */
    function initializeSocket() {
        if (socket) {
            // Already initialized
            return socket;
        }

        // Only initialize socket.io on research pages
        const currentPath = window.location.pathname;
        const isResearchPage = currentPath.includes('/research') ||
                              currentPath.includes('/progress') ||
                              currentPath.includes('/benchmark');

        if (!isResearchPage) {
            console.log('Socket.IO not needed on this page:', currentPath);
            return null;
        }

        // Get the base URL from the current page
        const baseUrl = window.location.protocol + '//' + window.location.host;

        // Create a new socket instance
        try {
            // Use polling only to avoid WebSocket issues
            socket = io(baseUrl, {
                path: '/socket.io',
                reconnection: true,
                reconnectionDelay: 1000,
                reconnectionAttempts: 5,
                transports: ['polling']  // Use only polling to avoid WebSocket issues
            });

            setupSocketEvents();
            console.log('Socket.IO initialized with polling only strategy');
        } catch (error) {
            console.error('Error initializing Socket.IO:', error);
            // Set a flag that we're not connected - will use polling for updates
            usingPolling = true;
        }

        return socket;
    }

    /**
     * Set up the socket event handlers
     */
    function setupSocketEvents() {
        socket.on('connect', () => {
            console.log('Socket connected');
            connectionAttempts = 0;
            usingPolling = false;

            // Re-subscribe to current research if any
            if (currentResearchId) {
                subscribeToResearch(currentResearchId);
            }

            // Call reconnect callback if exists
            if (reconnectCallback) {
                reconnectCallback();
            }
        });

        socket.on('connect_error', (error) => {
            console.warn('Socket connection error:', error);
            connectionAttempts++;

            if (connectionAttempts >= MAX_CONNECTION_ATTEMPTS) {
                console.warn(`Failed to connect after ${MAX_CONNECTION_ATTEMPTS} attempts, falling back to polling`);
                usingPolling = true;

                // If we can't establish a socket connection, use polling for any active research
                if (currentResearchId && typeof window.pollResearchStatus === 'function') {
                    window.pollResearchStatus(currentResearchId);
                }
            }
        });

        // Add handler for search engine selection events
        socket.on('search_engine_selected', (data) => {
            console.log('Received search_engine_selected event:', data);
            if (data && data.engine) {
                const engineName = data.engine;
                const resultCount = data.result_count || 0;

                // Add to log panel
                if (typeof window.addConsoleLog === 'function') {
                    // Format engine name - capitalize first letter
                    const displayEngineName = engineName.charAt(0).toUpperCase() + engineName.slice(1);
                    const message = `Search engine selected: ${displayEngineName} (found ${resultCount} results)`;
                    window.addConsoleLog(message, 'info', {
                        type: 'info',
                        phase: 'engine_selected',
                        engine: engineName,
                        result_count: resultCount,
                        is_engine_selection: true
                    });
                }
            }
        });

        socket.on('disconnect', (reason) => {
            console.log('Socket disconnected:', reason);

            // Fall back to polling on disconnect
            if (currentResearchId) {
                fallbackToPolling(currentResearchId);
            }
        });

        socket.on('reconnect', (attemptNumber) => {
            console.log('Socket reconnected after', attemptNumber, 'attempts');
            connectionAttempts = 0;
        });

        socket.on('reconnect_attempt', (attemptNumber) => {
            console.log('Socket reconnection attempt:', attemptNumber);
        });

        socket.on('error', (error) => {
            console.error('Socket error:', error);

            // Fall back to polling on any error
            if (currentResearchId) {
                fallbackToPolling(currentResearchId);
            }
        });
    }

    /**
     * Subscribe to research events
     * @param {string} researchId - The research ID to subscribe to
     * @param {function} callback - Optional callback for progress updates
     */
    function subscribeToResearch(researchId, callback) {
        if (!socket && !usingPolling) {
            console.warn('Socket not initialized, initializing now');
            initializeSocket();
        }

        if (!researchId) {
            console.error('No research ID provided');
            return;
        }

        console.log('Subscribing to research:', researchId);

        // Remember the current research ID
        currentResearchId = researchId;

        // Add the callback if provided
        if (callback && typeof callback === 'function') {
            addResearchEventHandler(researchId, callback);
        }

        // If we have a socket connection, join the research room
        if (socket && socket.connected) {
            try {
                socket.emit('join', { research_id: researchId });

                // Setup direct event handler for progress updates
                socket.on(`progress_${researchId}`, (data) => {
                    handleProgressUpdate(researchId, data);
                });
            } catch (error) {
                console.error('Error subscribing to research:', error);
                fallbackToPolling(researchId);
            }
        } else {
            // If no socket connection, use polling
            fallbackToPolling(researchId);
        }
    }

    /**
     * Handle progress updates from research
     * @param {string} researchId - The research ID this update is for
     * @param {Object} data - The progress data
     */
    function handleProgressUpdate(researchId, data) {
        console.log('Progress update for research', researchId, ':', data);

        // Special handling for synthesis errors to make them more visible to users
        if (data.metadata && (data.metadata.phase === 'synthesis_error' || data.metadata.error_type)) {
            const errorType = data.metadata.error_type || 'unknown';
            let errorMessage = 'Error during research synthesis';
            let detailedMessage = '';

            // Format user-friendly error messages based on error type
            switch(errorType) {
                case 'timeout':
                    errorMessage = 'LLM Timeout Error';
                    detailedMessage = 'The AI model took too long to respond. This may be due to server load or the complexity of your query.';
                    break;
                case 'token_limit':
                    errorMessage = 'Token Limit Exceeded';
                    detailedMessage = 'Your research query generated too much data for the AI model to process. Try a more specific query.';
                    break;
                case 'connection':
                    errorMessage = 'LLM Connection Error';
                    detailedMessage = 'Could not connect to the AI service. Please check that your LLM service is running.';
                    break;
                case 'rate_limit':
                    errorMessage = 'API Rate Limit Reached';
                    detailedMessage = 'The AI service API rate limit was reached. Please wait a few minutes and try again.';
                    break;
                case 'llm_error':
                default:
                    errorMessage = 'LLM Synthesis Error';
                    detailedMessage = 'The AI model encountered an error during final answer synthesis. A fallback response will be provided.';
            }

            // Add prominent error notification
            try {
                // If we have a UI notification function available
                if (typeof window.showNotification === 'function') {
                    window.showNotification(errorMessage, detailedMessage, 'error', 10000); // Show for 10 seconds
                }

                // Log to console
                console.error(`Research error (${errorType}): ${errorMessage} - ${detailedMessage}`);

                // Add to log panel with the error status
                if (typeof window.addConsoleLog === 'function') {
                    window.addConsoleLog(`${errorMessage}: ${detailedMessage}`, 'error', {
                        phase: 'synthesis_error',
                        error_type: errorType
                    });

                    // Add explanation about fallback mode as a separate log entry
                    window.addConsoleLog(
                        'Switching to fallback mode. Research will continue with available data.',
                        'milestone',
                        {phase: 'synthesis_fallback'}
                    );
                }
            } catch (notificationError) {
                console.error('Error showing notification:', notificationError);
            }
        }

        // Continue with normal progress update handling
        // NOTE: We defer calling handlers until after we've processed log_entry data
        // so that handlers can see the complete data including any log entries

        // Handle special engine selection events
        if (data.event === 'search_engine_selected' || (data.engine && data.result_count !== undefined)) {
            // Extract engine information
            const engineName = data.engine || 'unknown';
            const resultCount = data.result_count || 0;

            // Log the event
            console.log(`Search engine selected: ${engineName} (found ${resultCount} results)`);

            // Add to log panel as an info message with special metadata
            if (typeof window.addConsoleLog === 'function') {
                // Format engine name - capitalize first letter
                const displayEngineName = engineName.charAt(0).toUpperCase() + engineName.slice(1);
                const message = `Search engine selected: ${displayEngineName} (found ${resultCount} results)`;
                window.addConsoleLog(message, 'info', {
                    type: 'info',
                    phase: 'engine_selected',
                    engine: engineName,
                    result_count: resultCount,
                    is_engine_selection: true
                });
            }
        }

        // Initialize message tracking if not exists
        window._processedSocketMessages = window._processedSocketMessages || new Map();

        // Process logs from progress_log if available
        if (data.progress_log && typeof data.progress_log === 'string') {
            try {
                const progressLogs = JSON.parse(data.progress_log);
                if (Array.isArray(progressLogs) && progressLogs.length > 0) {
                    console.log(`Socket received ${progressLogs.length} logs in progress_log`);

                    // Process each log entry
                    progressLogs.forEach(logItem => {
                        // Skip if no message or time
                        if (!logItem.message || !logItem.time) return;

                        // Generate a unique key for this message
                        const messageKey = `${logItem.time}-${logItem.message}`;

                        // Skip if we've seen this exact message before
                        if (window._processedSocketMessages.has(messageKey)) {
                            console.log('Skipping duplicate socket message:', logItem.message);
                            return;
                        }

                        // Record that we've processed this message
                        window._processedSocketMessages.set(messageKey, Date.now());

                        // Determine log type based on metadata
                        let logType = 'info';
                        if (logItem.metadata) {
                            if (logItem.metadata.phase === 'iteration_complete' ||
                                logItem.metadata.phase === 'report_complete' ||
                                logItem.metadata.phase === 'complete' ||
                                logItem.metadata.phase === 'search_complete' ||
                                logItem.metadata.is_milestone === true ||
                                logItem.metadata.type === 'milestone' ||
                                logItem.metadata.type === 'MILESTONE') {
                                logType = 'MILESTONE';
                            } else if (logItem.metadata.phase === 'error' ||
                                       logItem.metadata.type === 'error') {
                                logType = 'error';
                            }
                        }

                        // Also check for keywords in the message for better milestone detection
                        if (logType !== 'milestone' && logItem.message) {
                            const msg = logItem.message.toLowerCase();
                            if (msg.includes('complete') ||
                                msg.includes('finished') ||
                                msg.includes('starting phase') ||
                                msg.includes('generated report')) {
                                logType = 'milestone';
                            } else if (msg.includes('error') || msg.includes('failed')) {
                                logType = 'error';
                            }
                        }

                        // Send to log panel
                        if (typeof window.addConsoleLog === 'function') {
                            // Use the main console log function if available
                            window.addConsoleLog(logItem.message, logType, logItem.metadata);
                        } else if (typeof window._socketAddLogEntry === 'function') {
                            // Fallback to the direct connector if needed
                            const logEntry = {
                                time: logItem.time,
                                message: logItem.message,
                                type: logType,
                                metadata: logItem.metadata || {}
                            };
                            window._socketAddLogEntry(logEntry);
                        } else {
                            console.warn('No log handler function available for log:', logItem);
                        }
                    });

                    // Clean up old entries from message tracking (keep only last 5 minutes)
                    const now = Date.now();
                    for (const [key, timestamp] of window._processedSocketMessages.entries()) {
                        if (now - timestamp > 5 * 60 * 1000) { // 5 minutes
                            window._processedSocketMessages.delete(key);
                        }
                    }
                }
            } catch (error) {
                console.error('Error processing progress_log:', error);
            }
        }

        // If the event contains log data, add it to the console
        if (data.log_entry) {
            console.log('Adding log entry from socket event:', data.log_entry);

            // Debug: Check if this is a milestone
            if (data.log_entry.type === 'milestone' || data.log_entry.type === 'MILESTONE') {
                console.log('MILESTONE LOG received:', data.log_entry.message);
            }

            // Make sure global tracking is initialized
            window._processedSocketMessages = window._processedSocketMessages || new Map();

            // Generate a message key
            const messageKey = `${data.log_entry.time || new Date().toISOString()}-${data.log_entry.message}`;

            // Skip if we've seen this message before
            if (window._processedSocketMessages.has(messageKey)) {
                console.log('Skipping duplicate individual log entry:', data.log_entry.message);
                // Don't return here - we still need to call handlers in case this is a milestone
                // that should update the current task
            } else {
                // Record that we've processed this message
                window._processedSocketMessages.set(messageKey, Date.now());

                if (typeof window.addConsoleLog === 'function') {
                    window.addConsoleLog(
                        data.log_entry.message,
                        data.log_entry.type ||
                        (data.log_entry.metadata && data.log_entry.metadata.type) ||
                        'info',
                        data.log_entry.metadata
                    );
                } else if (typeof window._socketAddLogEntry === 'function') {
                    window._socketAddLogEntry(data.log_entry);
                } else {
                    console.warn('No log handler function available for direct log entry');
                }
            }
        } else if (data.message && typeof window.addConsoleLog === 'function') {
            // Use the message field if no specific log entry
            console.log('Adding message from socket event:', data.message);

            // Skip duplicate general messages too
            const messageKey = `${new Date().toISOString()}-${data.message}`;
            if (window._processedSocketMessages.has(messageKey)) {
                console.log('Skipping duplicate message:', data.message);
                // Don't return - still call handlers
            } else {
                // Record this message
                window._processedSocketMessages.set(messageKey, Date.now());

                window.addConsoleLog(data.message, determineLogLevel(data.status));
            }
        }

        // Call all registered event handlers for this research AFTER processing all data
        // This ensures handlers see the complete data including any log entries
        if (researchEventHandlers[researchId]) {
            console.log(`Calling ${researchEventHandlers[researchId].length} handlers for research ${researchId} with data:`, {
                hasLogEntry: !!data.log_entry,
                logType: data.log_entry?.type,
                message: data.log_entry?.message?.substring(0, 50) + '...'
            });
            researchEventHandlers[researchId].forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error('Error in progress update handler:', error);
                }
            });
        } else {
            console.log(`No handlers registered for research ${researchId}`);
        }
    }

    /**
     * Determine log level based on status
     * @param {string} status - The research status
     * @returns {string} Log level (info, milestone, error, etc)
     */
    function determineLogLevel(status) {
        if (!status) return 'info';

        if (status === 'completed' || status === 'failed' || status === 'cancelled' || status === 'error') {
            return 'milestone';
        }

        if (status === 'error' || status.includes('error')) {
            return 'error';
        }

        return 'info';
    }

    /**
     * Add a log entry to the console log container
     * @param {Object} logEntry - The log entry data
     */
    function addLogEntry(logEntry) {
        // If the logpanel's log function is available, use it
        if (typeof window._socketAddLogEntry === 'function') {
            console.log('Using logpanel\'s _socketAddLogEntry for log:', logEntry.message);
            window._socketAddLogEntry(logEntry);
            return;
        }

        // If window.addConsoleLog is available, use it
        if (typeof window.addConsoleLog === 'function') {
            console.log('Using window.addConsoleLog for log:', logEntry.message);
            let logLevel = 'info';
            if (logEntry.type) {
                logLevel = logEntry.type;
            } else if (logEntry.metadata && logEntry.metadata.type) {
                logLevel = logEntry.metadata.type;
            }
            window.addConsoleLog(logEntry.message, logLevel, logEntry.metadata);
            return;
        }

        // Fallback implementation if none of the above is available
        console.log('Using socket.js fallback log implementation for:', logEntry.message);
        const consoleLogContainer = document.getElementById('console-log-container');
        if (!consoleLogContainer) return;

        // Clear empty message if present
        const emptyMessage = consoleLogContainer.querySelector('.empty-log-message');
        if (emptyMessage) {
            emptyMessage.remove();
        }

        // Get the log template
        const template = document.getElementById('console-log-entry-template');
        if (!template) {
            console.error('Console log entry template not found');
            return;
        }

        // Create a new log entry from the template
        const entry = document.importNode(template.content, true);

        // Determine the log level
        let logLevel = 'info';
        if (logEntry.metadata && logEntry.metadata.type) {
            logLevel = logEntry.metadata.type;
        } else if (logEntry.metadata && logEntry.metadata.phase) {
            if (logEntry.metadata.phase === 'complete' ||
                logEntry.metadata.phase === 'iteration_complete' ||
                logEntry.metadata.phase === 'report_complete') {
                logLevel = 'milestone';
            }
        }

        // Format the timestamp
        const timestamp = new Date(logEntry.time);
        const timeStr = timestamp.toLocaleTimeString();

        // Set content
        entry.querySelector('.log-timestamp').textContent = timeStr;
        entry.querySelector('.log-badge').textContent = logLevel.charAt(0).toUpperCase() + logLevel.slice(1);
        entry.querySelector('.log-badge').className = `log-badge ${logLevel}`;
        entry.querySelector('.log-message').textContent = logEntry.message;

        // Add to container (at the beginning for newest first)
        consoleLogContainer.insertBefore(entry, consoleLogContainer.firstChild);

        // Update log count
        const logIndicator = document.getElementById('log-indicator');
        if (logIndicator) {
            const currentCount = parseInt(logIndicator.textContent) || 0;
            logIndicator.textContent = currentCount + 1;
        }
    }

    /**
     * Fall back to polling for research updates
     * @param {string} researchId - The research ID
     */
    function fallbackToPolling(researchId) {
        console.log('Falling back to polling for research', researchId);
        usingPolling = true;

        // Start polling if the global polling function exists
        if (typeof window.pollResearchStatus === 'function') {
            window.pollResearchStatus(researchId);
        } else {
            // Define a simple polling function if it doesn't exist
            window.pollResearchStatus = function(id) {
                if (!window.api || !window.api.getResearchStatus) {
                    console.error('API service not available for polling');
                    return;
                }

                const pollInterval = setInterval(async () => {
                    try {
                        const data = await window.api.getResearchStatus(id);
                        if (data) {
                            handleProgressUpdate(id, data);

                            // Stop polling if the research is complete
                            if (data.status === 'completed' || data.status === 'failed' || data.status === 'cancelled') {
                                clearInterval(pollInterval);
                            }
                        }
                    } catch (error) {
                        console.error('Error polling research status:', error);
                    }
                }, 3000);

                // Store the interval ID for later cleanup
                window.pollIntervals = window.pollIntervals || {};
                window.pollIntervals[id] = pollInterval;
            };

            // Start polling for this research
            window.pollResearchStatus(researchId);
        }
    }

    /**
     * Unsubscribe from research events
     * @param {string} researchId - The research ID to unsubscribe from
     */
    function unsubscribeFromResearch(researchId) {
        if (!researchId) return;

        console.log('Unsubscribing from research:', researchId);

        // Clear any polling intervals
        if (window.pollIntervals && window.pollIntervals[researchId]) {
            clearInterval(window.pollIntervals[researchId]);
            delete window.pollIntervals[researchId];
        }

        // If we have a socket connection, leave the research room
        if (socket && socket.connected) {
            try {
                // Leave the research room
                socket.emit('leave', { research_id: researchId });

                // Remove the event handler
                socket.off(`progress_${researchId}`);
            } catch (error) {
                console.error('Error unsubscribing from research:', error);
            }
        }

        // Clear handlers
        if (researchId === currentResearchId) {
            currentResearchId = null;
        }

        // Clear event handlers
        if (researchEventHandlers[researchId]) {
            delete researchEventHandlers[researchId];
        }
    }

    /**
     * Add a research event handler
     * @param {string} researchId - The research ID to handle events for
     * @param {function} callback - The function to call when an event occurs
     */
    function addResearchEventHandler(researchId, callback) {
        if (!researchId || typeof callback !== 'function') {
            console.error('Invalid research event handler');
            return;
        }

        // Initialize the handlers array if needed
        if (!researchEventHandlers[researchId]) {
            researchEventHandlers[researchId] = [];
        }

        // Add the handler if it's not already in the array
        if (!researchEventHandlers[researchId].includes(callback)) {
            researchEventHandlers[researchId].push(callback);
        }
    }

    /**
     * Remove a research event handler
     * @param {string} researchId - The research ID to remove handler for
     * @param {function} callback - The function to remove
     */
    function removeResearchEventHandler(researchId, callback) {
        if (!researchId || !researchEventHandlers[researchId]) return;

        if (callback) {
            // Find the handler index
            const index = researchEventHandlers[researchId].indexOf(callback);

            // Remove if found
            if (index !== -1) {
                researchEventHandlers[researchId].splice(index, 1);
            }
        } else {
            // Remove all handlers for this research
            delete researchEventHandlers[researchId];
        }
    }

    /**
     * Set a callback for socket reconnection
     * @param {function} callback - The function to call on reconnection
     */
    function setReconnectCallback(callback) {
        reconnectCallback = callback;
    }

    /**
     * Disconnect the socket
     */
    function disconnectSocket() {
        // Clear any polling intervals
        if (window.pollIntervals) {
            Object.keys(window.pollIntervals).forEach(id => {
                clearInterval(window.pollIntervals[id]);
            });
            window.pollIntervals = {};
        }

        if (socket) {
            try {
                socket.disconnect();
            } catch (error) {
                console.error('Error disconnecting socket:', error);
            }
            socket = null;
        }

        researchEventHandlers = {};
        reconnectCallback = null;
        currentResearchId = null;
        connectionAttempts = 0;
        usingPolling = false;
    }

    /**
     * Filter logs by type
     * @param {string} type - The log type to filter by ('all', 'info', 'error', 'milestone')
     */
    function filterLogsByType(type) {
        // If the logpanel's filter function is available, use it
        if (typeof window.filterLogsByType === 'function') {
            console.log('Using logpanel\'s filterLogsByType for filter:', type);
            window.filterLogsByType(type);
            return;
        }

        console.log('Using socket.js filtering implementation for:', type);

        // Update button UI
        const buttons = document.querySelectorAll('.filter-buttons .small-btn');
        buttons.forEach(button => {
            button.classList.remove('selected');
            if (button.textContent.toLowerCase() === type ||
                (type === 'all' && button.textContent.toLowerCase() === 'all')) {
                button.classList.add('selected');
            }
        });

        // Get all log entries
        const logEntries = document.querySelectorAll('.console-log-entry');

        logEntries.forEach(entry => {
            // Use dataset for type if available (new way)
            if (entry.dataset && entry.dataset.logType) {
                if (type === 'all' || entry.dataset.logType === type) {
                    entry.style.display = '';
                } else {
                    entry.style.display = 'none';
                }
                return;
            }

            // Fallback to badge content (old way)
            const badge = entry.querySelector('.log-badge');
            const logType = badge ? badge.textContent.toLowerCase() : 'info';

            if (type === 'all' || logType === type) {
                entry.style.display = '';
            } else {
                entry.style.display = 'none';
            }
        });

        // Update empty state message if needed
        const logContainer = document.getElementById('console-log-container');
        if (logContainer) {
            const visibleEntries = logContainer.querySelectorAll('.console-log-entry[style="display: ;"], .console-log-entry:not([style])');
            const emptyMessage = logContainer.querySelector('.empty-log-message');

            if (visibleEntries.length === 0 && !emptyMessage) {
                logContainer.innerHTML = `<div class="empty-log-message">No ${type === 'all' ? '' : type + ' '}logs available.</div>` + logContainer.innerHTML;
            } else if (visibleEntries.length > 0 && emptyMessage) {
                emptyMessage.remove();
            }
        }
    }

    /**
     * Check if socket is connected
     * @returns {boolean} True if connected
     */
    function isConnected() {
        return socket && socket.connected;
    }

    /**
     * Check if we're using polling fallback
     * @returns {boolean} True if using polling
     */
    function isUsingPolling() {
        return usingPolling;
    }

    // Initialize socket on load with a small delay to ensure document is ready
    setTimeout(initializeSocket, 100);

    // Expose functions globally
    window.filterLogsByType = filterLogsByType;
    window._socketAddLogEntry = addLogEntry; // Expose the addLogEntry function

    // Public API
    return {
        init: initializeSocket,
        subscribeToResearch,
        unsubscribeFromResearch,
        onReconnect: setReconnectCallback,
        disconnect: disconnectSocket,
        getSocketInstance: () => socket,
        isConnected,
        isUsingPolling
    };
})();

/**
 * Register global functions for filtering logs - ONLY if they don't already exist
 */
if (!window.filterLogsByType) {
    window.filterLogsByType = function(type) {
        console.log('Filter logs by type (socket.js fallback):', type);
        // If the socket object exists and has the function
        if (window.socket && typeof window.socket.filterLogsByType === 'function') {
            window.socket.filterLogsByType(type);
            return;
        }

        // Otherwise do basic filtering
        const logEntries = document.querySelectorAll('.console-log-entry');
        logEntries.forEach(entry => {
            // Try using dataset first (new way)
            if (entry.dataset && entry.dataset.logType) {
                if (type === 'all' || entry.dataset.logType === type.toLowerCase()) {
                    entry.style.display = '';
                } else {
                    entry.style.display = 'none';
                }
                return;
            }

            // Fallback to badge (old way)
            const badge = entry.querySelector('.log-badge');
            const logType = badge ? badge.textContent.toLowerCase() : 'info';

            if (type === 'all' || logType === type.toLowerCase()) {
                entry.style.display = '';
            } else {
                entry.style.display = 'none';
            }
        });
    };
}

/**
 * Function to add a log entry to the console - Only create if it doesn't exist
 * @param {string} message - Log message
 * @param {string} level - Log level (info, milestone, error)
 * @param {Object} metadata - Optional metadata
 */
if (!window.addConsoleLog) {
    window.addConsoleLog = function(message, level = 'info', metadata = null) {
        console.log(`Adding console log (socket.js fallback): ${message} (${level})`);

        // Create a log entry object
        const logEntry = {
            time: new Date().toISOString(),
            message: message,
            type: level,
            metadata: metadata || { type: level }
        };

        // Try to use the log panel's direct function first
        if (window.logPanel && typeof window.logPanel.addLog === 'function') {
            console.log('Using logPanel.addLog to add log entry');
            window.logPanel.addLog(message, level, metadata);
            return;
        }

        // Then try the socket's connector function
        if (window._socketAddLogEntry) {
            console.log('Using _socketAddLogEntry to add log entry');
            window._socketAddLogEntry(logEntry);
            return;
        }

        console.warn('LogPanel functions not available, using fallback implementation');

        // FALLBACK IMPLEMENTATION
        const consoleLogContainer = document.getElementById('console-log-container');
        if (!consoleLogContainer) {
            console.warn('Console log container not found, log will be lost');
            return;
        }

        // Clear empty message if present
        const emptyMessage = consoleLogContainer.querySelector('.empty-log-message');
        if (emptyMessage) {
            emptyMessage.remove();
        }

        // Get or create a new log entry
        const template = document.getElementById('console-log-entry-template');
        let entry;

        if (template) {
            entry = document.importNode(template.content, true);

            // Set content
            entry.querySelector('.log-timestamp').textContent = new Date().toLocaleTimeString();
            entry.querySelector('.log-badge').textContent = level.charAt(0).toUpperCase() + level.slice(1);
            entry.querySelector('.log-badge').className = `log-badge ${level}`;
            entry.querySelector('.log-message').textContent = message;

            // Add data attribute for filtering
            const logEntry = entry.querySelector('.console-log-entry');
            if (logEntry) {
                logEntry.dataset.logType = level.toLowerCase();
                logEntry.classList.add(`log-${level.toLowerCase()}`);
            }
        } else {
            // Create a simple log entry without template
            entry = document.createElement('div');
            entry.className = 'console-log-entry';
            entry.dataset.logType = level.toLowerCase();
            entry.classList.add(`log-${level.toLowerCase()}`);

            // Create log content
            entry.innerHTML = `
                <span class="log-timestamp">${new Date().toLocaleTimeString()}</span>
                <span class="log-badge ${level}">${level.charAt(0).toUpperCase() + level.slice(1)}</span>
                <span class="log-message">${message}</span>
            `;
        }

        // Add to container (at the beginning for newest first)
        consoleLogContainer.insertBefore(entry, consoleLogContainer.firstChild);

        // Update log count
        const logIndicator = document.getElementById('log-indicator');
        if (logIndicator) {
            const currentCount = parseInt(logIndicator.textContent) || 0;
            logIndicator.textContent = currentCount + 1;
        }

        // Show log panel if hidden
        const logPanelToggle = document.getElementById('log-panel-toggle');
        const logPanelContent = document.getElementById('log-panel-content');

        if (logPanelContent && logPanelContent.classList.contains('collapsed') && logPanelToggle) {
            // Auto-expand after a few logs
            if (!window._logAutoExpandTimer) {
                window._logAutoExpandTimer = setTimeout(() => {
                    console.log('Auto-expanding log panel due to accumulated logs');
                    logPanelToggle.click();
                    window._logAutoExpandTimer = null;
                }, 500);
            }
        }
    };
}
