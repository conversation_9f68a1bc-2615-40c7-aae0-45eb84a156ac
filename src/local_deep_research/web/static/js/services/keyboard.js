/**
 * Global Keyboard Shortcuts Service
 * Provides consistent keyboard shortcuts across the application
 */
(function() {
    'use strict';

    // Keyboard shortcut registry - simplified to just the essential ones
    const shortcuts = {
        'newSearch': {
            keys: ['escape'],
            description: 'Return to main search',
            handler: () => {
                // Always navigate to main research page
                window.location.href = URLS.PAGES.HOME;
            }
        },
        'navNewResearch': {
            keys: ['ctrl+shift+1'],
            description: 'Go to New Research',
            handler: () => {
                window.location.href = URLS.PAGES.HOME;
            }
        },
        'navHistory': {
            keys: ['ctrl+shift+2'],
            description: 'Go to History',
            handler: () => {
                window.location.href = URLS.PAGES.HISTORY;
            }
        },
        'navMetrics': {
            keys: ['ctrl+shift+3'],
            description: 'Go to Metrics',
            handler: () => {
                window.location.href = URLS.PAGES.METRICS;
            }
        },
        'navSettings': {
            keys: ['ctrl+shift+4'],
            description: 'Go to Settings',
            handler: () => {
                window.location.href = URLS.PAGES.SETTINGS;
            }
        }
    };

    /**
     * Check if a keyboard event matches a shortcut pattern
     */
    function matchesShortcut(event, pattern) {
        const parts = pattern.toLowerCase().split('+');
        const key = parts[parts.length - 1];

        // Check modifiers
        const requiresCtrl = parts.includes('ctrl');
        const requiresCmd = parts.includes('cmd');
        const requiresShift = parts.includes('shift');
        const requiresAlt = parts.includes('alt');

        // For single key shortcuts, ensure NO modifiers are pressed
        if (parts.length === 1) {
            if (event.ctrlKey || event.metaKey || event.shiftKey || event.altKey) {
                return false;
            }
        }

        // Match key
        const eventKey = event.key.toLowerCase();
        if (eventKey !== key && event.code.toLowerCase() !== `key${key}` && event.code.toLowerCase() !== `digit${key}`) {
            // Special handling for special keys
            if (key === '/' && eventKey !== '/') return false;
            else if (key === ',' && eventKey !== ',') return false;
            else if (key !== '/' && key !== ',' && eventKey !== key) return false;
        }

        // Match modifiers (only if required)
        if (requiresCtrl && !event.ctrlKey) return false;
        if (requiresCmd && !event.metaKey) return false;
        if (requiresShift && !event.shiftKey) return false;
        if (requiresAlt && !event.altKey) return false;

        return true;
    }

    /**
     * Initialize keyboard shortcuts
     */
    function initializeKeyboardShortcuts() {
        console.log('Keyboard shortcuts initialized');

        document.addEventListener('keydown', function(event) {
            // Skip if user is typing in an input field
            const activeElement = document.activeElement;
            const isTyping = activeElement && (
                activeElement.tagName === 'INPUT' ||
                activeElement.tagName === 'TEXTAREA' ||
                activeElement.contentEditable === 'true'
            );

            // Skip shortcuts when typing, except for navigation shortcuts and Esc (unless on settings page)
            if (isTyping) {
                const isNavShortcut = event.ctrlKey && event.shiftKey && (
                    ['1', '2', '3', '4'].includes(event.key) ||
                    ['Digit1', 'Digit2', 'Digit3', 'Digit4'].includes(event.code)
                );
                const isEscOnSettingsPage = event.key === 'Escape' && window.location.pathname.includes('/settings');

                // Debug navigation shortcuts
                if (event.ctrlKey && event.shiftKey) {
                    console.log('Nav shortcut attempt:', event.key, event.code, 'isNavShortcut:', isNavShortcut);
                }

                if (!isNavShortcut && (event.key !== 'Escape' || isEscOnSettingsPage)) {
                    return;
                }
            }

            // Debug log
            if (event.key.length === 1 && !event.ctrlKey && !event.metaKey && !event.altKey) {
                console.log('Key pressed:', event.key, 'Code:', event.code);
            }

            // Check each shortcut
            for (const [name, shortcut] of Object.entries(shortcuts)) {
                for (const pattern of shortcut.keys) {
                    if (matchesShortcut(event, pattern)) {
                        console.log('Shortcut matched:', name, pattern);
                        event.preventDefault();
                        shortcut.handler(event);
                        return;
                    }
                }
            }
        });

        // Add help text to footer if on main pages
        addKeyboardHints();
    }

    /**
     * Add subtle keyboard hints to the UI
     */
    function addKeyboardHints() {
        // Keyboard hints disabled
    }

    /**
     * Get list of available shortcuts for current page
     */
    function getAvailableShortcuts() {
        const currentPath = window.location.pathname;
        const allShortcuts = { ...shortcuts };

        // Add page-specific shortcuts
        if (currentPath.includes('/progress/')) {
            allShortcuts.viewResults = {
                keys: ['enter'],
                description: 'View results (when complete)',
                handler: () => {
                    const viewBtn = document.getElementById('view-results-btn');
                    if (viewBtn && viewBtn.style.display !== 'none') {
                        window.location.href = viewBtn.href;
                    }
                }
            };
        }

        if (currentPath.includes('/results/')) {
            allShortcuts.escape = {
                keys: ['escape'],
                description: 'Back to new search',
                handler: () => window.location.href = URLS.PAGES.HOME
            };
        }

        return allShortcuts;
    }

    // Initialize keyboard shortcuts on all pages
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeKeyboardShortcuts);
    } else {
        initializeKeyboardShortcuts();
    }

    // Expose API for other components
    window.KeyboardService = {
        shortcuts: getAvailableShortcuts,
        addShortcut: (name, config) => {
            shortcuts[name] = config;
        },
        removeShortcut: (name) => {
            delete shortcuts[name];
        }
    };

})();
