"""Server configuration management for web app startup.

This module handles server configuration that needs to be available before
Flask app context is established. It provides a bridge between environment
variables and database settings.
"""

import json
import os
from pathlib import Path
from typing import Dict, Any

from loguru import logger

from ..config.paths import get_data_dir


def get_server_config_path() -> Path:
    """Get the path to the server configuration file."""
    return Path(get_data_dir()) / "server_config.json"


def load_server_config() -> Dict[str, Any]:
    """Load server configuration from file or environment variables.

    Returns:
        dict: Server configuration with keys: host, port, debug, use_https
    """
    config_path = get_server_config_path()

    # Default configuration
    config = {"host": "0.0.0.0", "port": 5000, "debug": True, "use_https": True}

    # Try to load from config file first
    if config_path.exists():
        try:
            with open(config_path, "r") as f:
                saved_config = json.load(f)
                config.update(saved_config)
                logger.debug(f"Loaded server config from {config_path}")
        except Exception as e:
            logger.warning(f"Failed to load server config: {e}")

    # Environment variables override file settings
    if "LDR_HOST" in os.environ:
        config["host"] = os.environ["LDR_HOST"]
    if "LDR_PORT" in os.environ:
        config["port"] = int(os.environ["LDR_PORT"])
    if "LDR_DEBUG" in os.environ:
        config["debug"] = os.environ["LDR_DEBUG"].lower() == "true"
    if "LDR_USE_HTTPS" in os.environ:
        config["use_https"] = os.environ["LDR_USE_HTTPS"].lower() == "true"

    return config


def save_server_config(config: Dict[str, Any]) -> None:
    """Save server configuration to file.

    This should be called when web.host or web.port settings are updated
    through the UI.

    Args:
        config: Server configuration dict
    """
    config_path = get_server_config_path()

    try:
        # Ensure directory exists
        config_path.parent.mkdir(parents=True, exist_ok=True)

        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)

        logger.info(f"Saved server config to {config_path}")
    except Exception:
        logger.exception("Failed to save server config")


def sync_from_settings(settings_snapshot: Dict[str, Any]) -> None:
    """Sync server config from settings snapshot.

    This should be called when settings are updated through the UI.

    Args:
        settings_snapshot: Settings snapshot containing web.host and web.port
    """
    config = load_server_config()

    # Update from settings if available
    if "web.host" in settings_snapshot:
        config["host"] = settings_snapshot["web.host"]
    if "web.port" in settings_snapshot:
        config["port"] = settings_snapshot["web.port"]

    save_server_config(config)
