"""
Stores global state.
"""

# Active research processes and socket subscriptions
active_research = {}
socket_subscriptions = {}
# Add termination flags dictionary
termination_flags = {}


def get_globals():
    """
    Returns:
        Global state for other modules to access.

    """
    return {
        "active_research": active_research,
        "socket_subscriptions": socket_subscriptions,
        "termination_flags": termination_flags,
    }
