"""
Route Registry - Central documentation of all application routes
This file provides a single place to see all available routes across blueprints

NOTE: This is primarily for documentation and reference purposes.
The actual routes are defined in their respective blueprint files.
To keep this in sync:
1. Update this file when adding/removing routes
2. Run tests to ensure URLs in tests match actual routes
3. Consider using get_all_routes() in tests to validate against actual Flask routes
"""

# Route patterns by blueprint
ROUTE_REGISTRY = {
    "research": {
        "blueprint": "research_bp",
        "url_prefix": None,  # Routes at root level
        "routes": [
            ("GET", "/", "index", "Home/Research page"),
            (
                "POST",
                "/api/start_research",
                "start_research",
                "Start new research",
            ),
            (
                "GET",
                "/api/research/<string:research_id>",
                "get_research_details",
                "Get research details",
            ),
            (
                "GET",
                "/api/research/<string:research_id>/logs",
                "get_research_logs",
                "Get research logs",
            ),
            (
                "GET",
                "/api/research/<research_id>/status",
                "get_research_status",
                "Get research status",
            ),
            (
                "GET",
                "/api/report/<string:research_id>",
                "get_research_report",
                "Get research report",
            ),
            (
                "POST",
                "/api/terminate/<string:research_id>",
                "terminate_research",
                "Stop research",
            ),
            (
                "DELETE",
                "/api/delete/<string:research_id>",
                "delete_research",
                "Delete research",
            ),
            ("GET", "/api/history", "get_history", "Get research history"),
            (
                "POST",
                "/api/clear_history",
                "clear_history",
                "Clear all history",
            ),
            (
                "GET",
                "/progress/<string:research_id>",
                "progress_page",
                "Research progress page",
            ),
            (
                "GET",
                "/results/<string:research_id>",
                "results_page",
                "Research results page",
            ),
            (
                "GET",
                "/details/<string:research_id>",
                "research_details_page",
                "Research details page",
            ),
        ],
    },
    "api_v1": {
        "blueprint": "api_blueprint",
        "url_prefix": "/api/v1",
        "routes": [
            ("GET", "/", "api_documentation", "API documentation"),
            ("GET", "/health", "health_check", "Health check"),
            (
                "POST",
                "/quick_summary",
                "api_quick_summary",
                "Quick LLM summary",
            ),
            (
                "POST",
                "/quick_summary_test",
                "api_quick_summary_test",
                "Test quick summary",
            ),
            (
                "POST",
                "/generate_report",
                "api_generate_report",
                "Generate research report",
            ),
            (
                "POST",
                "/analyze_documents",
                "api_analyze_documents",
                "Analyze documents",
            ),
        ],
    },
    "history": {
        "blueprint": "history_bp",
        "url_prefix": "/history",
        "routes": [
            ("GET", "/", "history_page", "History page"),
            ("GET", "/api", "get_history", "Get history data"),
            (
                "GET",
                "/status/<string:research_id>",
                "get_research_status",
                "Get research status",
            ),
            (
                "GET",
                "/details/<string:research_id>",
                "get_research_details",
                "Get research details",
            ),
            (
                "GET",
                "/logs/<string:research_id>",
                "get_research_logs",
                "Get research logs",
            ),
            (
                "GET",
                "/log_count/<string:research_id>",
                "get_log_count",
                "Get log count",
            ),
            (
                "GET",
                "/history/report/<string:research_id>",
                "get_report",
                "Get research report",
            ),
            (
                "GET",
                "/markdown/<string:research_id>",
                "get_markdown",
                "Get markdown report",
            ),
        ],
    },
    "settings": {
        "blueprint": "settings_bp",
        "url_prefix": "/settings",
        "routes": [
            ("GET", "/", "settings_page", "Settings page"),
            (
                "POST",
                "/save_all_settings",
                "save_all_settings",
                "Save all settings",
            ),
            (
                "POST",
                "/reset_to_defaults",
                "reset_to_defaults",
                "Reset to defaults",
            ),
            ("GET", "/api", "api_get_all_settings", "Get all settings"),
            (
                "GET",
                "/api/<path:key>",
                "api_get_setting",
                "Get specific setting",
            ),
            ("POST", "/api/<path:key>", "api_update_setting", "Update setting"),
            (
                "DELETE",
                "/api/<path:key>",
                "api_delete_setting",
                "Delete setting",
            ),
            ("POST", "/api/import", "api_import_settings", "Import settings"),
            (
                "GET",
                "/api/categories",
                "api_get_categories",
                "Get setting categories",
            ),
            ("GET", "/api/types", "api_get_types", "Get setting types"),
            (
                "GET",
                "/api/ui_elements",
                "api_get_ui_elements",
                "Get UI elements",
            ),
            (
                "GET",
                "/api/available-models",
                "api_get_available_models",
                "Get available models",
            ),
            (
                "GET",
                "/api/available-search-engines",
                "api_get_available_search_engines",
                "Get search engines",
            ),
            (
                "GET",
                "/api/warnings",
                "api_get_warnings",
                "Get settings warnings",
            ),
            (
                "GET",
                "/api/ollama-status",
                "check_ollama_status",
                "Check Ollama status",
            ),
        ],
    },
    "metrics": {
        "blueprint": "metrics_bp",
        "url_prefix": "/metrics",
        "routes": [
            ("GET", "/", "metrics_dashboard", "Metrics dashboard"),
            ("GET", "/costs", "costs_page", "Costs page"),
            ("GET", "/star-reviews", "star_reviews_page", "Star reviews page"),
            ("GET", "/api/metrics", "api_metrics", "Get metrics data"),
            (
                "GET",
                "/api/cost-analytics",
                "api_cost_analytics",
                "Get cost analytics",
            ),
            ("GET", "/api/pricing", "api_pricing", "Get pricing data"),
            (
                "GET",
                "/api/metrics/research/<string:research_id>",
                "api_research_metrics",
                "Research metrics",
            ),
            (
                "GET",
                "/api/metrics/research/<string:research_id>/timeline",
                "api_research_timeline_metrics",
                "Timeline metrics",
            ),
            (
                "GET",
                "/api/metrics/research/<string:research_id>/search",
                "api_research_search_metrics",
                "Search metrics",
            ),
            (
                "GET",
                "/api/ratings/<string:research_id>",
                "api_get_research_rating",
                "Get research rating",
            ),
            (
                "POST",
                "/api/ratings/<string:research_id>",
                "api_save_research_rating",
                "Save research rating",
            ),
            (
                "GET",
                "/api/research-costs/<string:research_id>",
                "api_research_costs",
                "Get research costs",
            ),
        ],
    },
}


def get_all_routes():
    """Get a flat list of all routes across blueprints"""
    all_routes = []
    for blueprint_name, blueprint_info in ROUTE_REGISTRY.items():
        prefix = blueprint_info["url_prefix"] or ""
        for method, path, endpoint, description in blueprint_info["routes"]:
            full_path = f"{prefix}{path}" if prefix else path
            all_routes.append(
                {
                    "method": method,
                    "path": full_path,
                    "endpoint": f"{blueprint_name}.{endpoint}",
                    "description": description,
                    "blueprint": blueprint_name,
                }
            )
    return all_routes


def get_routes_by_blueprint(blueprint_name):
    """Get routes for a specific blueprint"""
    if blueprint_name not in ROUTE_REGISTRY:
        return []

    blueprint_info = ROUTE_REGISTRY[blueprint_name]
    prefix = blueprint_info["url_prefix"] or ""
    routes = []

    for method, path, endpoint, description in blueprint_info["routes"]:
        full_path = f"{prefix}{path}" if prefix else path
        routes.append(
            {
                "method": method,
                "path": full_path,
                "endpoint": endpoint,
                "description": description,
            }
        )
    return routes


def find_route(path_pattern):
    """Find routes matching a path pattern"""
    all_routes = get_all_routes()
    matching_routes = []

    for route in all_routes:
        if path_pattern.lower() in route["path"].lower():
            matching_routes.append(route)

    return matching_routes


if __name__ == "__main__":
    # Example usage
    print("All API routes:")
    for route in get_all_routes():
        if "/api" in route["path"]:
            print(
                f"{route['method']:6} {route['path']:40} - {route['description']}"
            )
