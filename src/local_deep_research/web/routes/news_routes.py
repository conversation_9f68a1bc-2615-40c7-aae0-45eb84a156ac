"""
Flask routes for news API endpoints.
"""

from flask import Blueprint, jsonify, request, session
from loguru import logger

from ...news import api as news_api
from ...news.exceptions import NewsAPIException

# Create blueprint
bp = Blueprint("news_api", __name__, url_prefix="/api/news")


@bp.errorhandler(NewsAPIException)
def handle_news_api_exception(error: NewsAPIException):
    """Handle NewsAPIException and convert to JSON response."""
    logger.error(f"News API error: {error.message} (code: {error.error_code})")
    return jsonify(error.to_dict()), error.status_code


@bp.route("/feed", methods=["GET"])
def get_news_feed():
    """Get personalized news feed."""
    try:
        user_id = session.get("username", "anonymous")
        limit = request.args.get("limit", 20, type=int)
        use_cache = request.args.get("use_cache", "true").lower() == "true"
        focus = request.args.get("focus")
        search_strategy = request.args.get("search_strategy")
        subscription_id = request.args.get("subscription_id")

        result = news_api.get_news_feed(
            user_id=user_id,
            limit=limit,
            use_cache=use_cache,
            focus=focus,
            search_strategy=search_strategy,
            subscription_id=subscription_id,
        )

        return jsonify(result)
    except NewsAPIException:
        # Let the error handler deal with it
        raise
    except Exception:
        logger.exception("Unexpected error in get_news_feed")
        return jsonify({"error": "Internal server error"}), 500


@bp.route("/debug/research", methods=["GET"])
def debug_research_items():
    """Debug endpoint to check research items in database."""
    try:
        user_id = session.get("username", "anonymous")
        result = news_api.debug_research_items(user_id)
        return jsonify(result)
    except NewsAPIException:
        raise
    except Exception:
        logger.exception("Unexpected error in debug_research_items")
        return jsonify({"error": "Internal server error"}), 500


@bp.route("/subscriptions", methods=["GET"])
def get_subscriptions():
    """Get all subscriptions for the current user."""
    try:
        user_id = session.get("username", "anonymous")
        result = news_api.get_subscriptions(user_id)
        return jsonify(result)
    except NewsAPIException:
        raise
    except Exception:
        logger.exception("Unexpected error in get_subscriptions")
        return jsonify({"error": "Internal server error"}), 500


@bp.route("/subscriptions", methods=["POST"])
def create_subscription():
    """Create a new subscription."""
    try:
        user_id = session.get("username", "anonymous")
        data = request.get_json()

        result = news_api.create_subscription(
            user_id=user_id,
            query=data.get("query"),
            subscription_type=data.get("type", "search"),
            refresh_minutes=data.get("refresh_minutes"),
            source_research_id=data.get("source_research_id"),
            model_provider=data.get("model_provider"),
            model=data.get("model"),
            search_strategy=data.get("search_strategy"),
            custom_endpoint=data.get("custom_endpoint"),
            name=data.get("name"),
            folder_id=data.get("folder_id"),
            is_active=data.get("is_active", True),
            search_engine=data.get("search_engine"),
            search_iterations=data.get("search_iterations"),
            questions_per_iteration=data.get("questions_per_iteration"),
        )

        return jsonify(result), 201
    except NewsAPIException:
        raise
    except Exception:
        logger.exception("Unexpected error in create_subscription")
        return jsonify({"error": "Internal server error"}), 500


@bp.route("/subscriptions/<subscription_id>", methods=["GET"])
def get_subscription(subscription_id):
    """Get a single subscription by ID."""
    try:
        result = news_api.get_subscription(subscription_id)
        return jsonify(result)
    except NewsAPIException:
        raise
    except Exception:
        logger.exception(
            f"Unexpected error getting subscription {subscription_id}"
        )
        return jsonify({"error": "Internal server error"}), 500


@bp.route("/subscriptions/<subscription_id>", methods=["PUT", "PATCH"])
def update_subscription(subscription_id):
    """Update an existing subscription."""
    try:
        data = request.get_json()
        result = news_api.update_subscription(subscription_id, data)
        return jsonify(result)
    except NewsAPIException:
        raise
    except Exception:
        logger.exception(
            f"Unexpected error updating subscription {subscription_id}"
        )
        return jsonify({"error": "Internal server error"}), 500


@bp.route("/subscriptions/<subscription_id>", methods=["DELETE"])
def delete_subscription(subscription_id):
    """Delete a subscription."""
    try:
        result = news_api.delete_subscription(subscription_id)
        return jsonify(result)
    except NewsAPIException:
        raise
    except Exception:
        logger.exception(
            f"Unexpected error deleting subscription {subscription_id}"
        )
        return jsonify({"error": "Internal server error"}), 500


@bp.route("/subscriptions/<subscription_id>/history", methods=["GET"])
def get_subscription_history(subscription_id):
    """Get research history for a specific subscription."""
    try:
        limit = request.args.get("limit", 20, type=int)
        result = news_api.get_subscription_history(subscription_id, limit)
        return jsonify(result)
    except NewsAPIException:
        raise
    except Exception:
        logger.exception(
            f"Unexpected error getting history for {subscription_id}"
        )
        return jsonify({"error": "Internal server error"}), 500


@bp.route("/feedback", methods=["POST"])
def submit_feedback():
    """Submit feedback (vote) for a news card."""
    try:
        user_id = session.get("username", "anonymous")
        data = request.get_json()

        card_id = data.get("card_id")
        vote = data.get("vote")

        if not card_id or vote not in ["up", "down"]:
            return jsonify({"error": "Invalid request"}), 400

        result = news_api.submit_feedback(card_id, user_id, vote)
        return jsonify(result)
    except NewsAPIException:
        raise
    except Exception:
        logger.exception("Unexpected error in submit_feedback")
        return jsonify({"error": "Internal server error"}), 500


@bp.route("/research", methods=["POST"])
def research_news_item():
    """Perform deeper research on a news item."""
    try:
        data = request.get_json()
        card_id = data.get("card_id")
        depth = data.get("depth", "quick")

        if not card_id:
            return jsonify({"error": "card_id is required"}), 400

        result = news_api.research_news_item(card_id, depth)
        return jsonify(result)
    except NewsAPIException:
        raise
    except Exception:
        logger.exception("Unexpected error in research_news_item")
        return jsonify({"error": "Internal server error"}), 500


@bp.route("/preferences", methods=["POST"])
def save_preferences():
    """Save user preferences for news."""
    try:
        user_id = session.get("username", "anonymous")
        preferences = request.get_json()

        result = news_api.save_news_preferences(user_id, preferences)
        return jsonify(result)
    except NewsAPIException:
        raise
    except Exception:
        logger.exception("Unexpected error in save_preferences")
        return jsonify({"error": "Internal server error"}), 500


@bp.route("/categories", methods=["GET"])
def get_categories():
    """Get available news categories with counts."""
    try:
        result = news_api.get_news_categories()
        return jsonify(result)
    except NewsAPIException:
        raise
    except Exception:
        logger.exception("Unexpected error in get_categories")
        return jsonify({"error": "Internal server error"}), 500
