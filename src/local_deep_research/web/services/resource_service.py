from datetime import datetime, UTC

from loguru import logger

from ...database.models import ResearchResource
from ...database.session_context import get_user_db_session


def get_resources_for_research(research_id):
    """
    Retrieve resources associated with a specific research project

    Args:
        research_id (str): The UUID of the research

    Returns:
        list: List of resource objects for the research
    """
    try:
        with get_user_db_session() as db_session:
            # Query to get resources for the research
            resources_list = (
                db_session.query(ResearchResource)
                .filter_by(research_id=research_id)
                .order_by(ResearchResource.id.asc())
                .all()
            )

            resources = []
            for resource in resources_list:
                resources.append(
                    {
                        "id": resource.id,
                        "research_id": resource.research_id,
                        "title": resource.title,
                        "url": resource.url,
                        "content_preview": resource.content_preview,
                        "source_type": resource.source_type,
                        "metadata": resource.resource_metadata or {},
                    }
                )

            return resources

    except Exception:
        logger.exception("Error retrieving resources for research")
        raise


def add_resource(
    research_id,
    title,
    url,
    content_preview=None,
    source_type="web",
    metadata=None,
):
    """
    Add a new resource to the research_resources table

    Args:
        research_id (str): The UUID of the research
        title (str): The title of the resource
        url (str): The URL of the resource
        content_preview (str, optional): A preview of the content
        source_type (str, optional): The type of source
        metadata (dict, optional): Additional metadata

    Returns:
        ResearchResource: The created resource object
    """
    try:
        with get_user_db_session() as db_session:
            resource = ResearchResource(
                research_id=research_id,
                title=title,
                url=url,
                content_preview=content_preview,
                source_type=source_type,
                resource_metadata=metadata,
                accessed_at=datetime.now(UTC),
            )

            db_session.add(resource)
            db_session.commit()

            return resource

    except Exception:
        logger.exception("Error adding resource")
        raise


def delete_resource(resource_id):
    """
    Delete a resource from the database

    Args:
        resource_id (int): The ID of the resource to delete

    Returns:
        bool: True if deletion was successful

    Raises:
        ValueError: If resource not found
    """
    try:
        with get_user_db_session() as db_session:
            # Find the resource
            resource = (
                db_session.query(ResearchResource)
                .filter_by(id=resource_id)
                .first()
            )

            if not resource:
                raise ValueError(f"Resource with ID {resource_id} not found")

            db_session.delete(resource)
            db_session.commit()

            logger.info(f"Deleted resource {resource_id}")
            return True

    except ValueError:
        raise
    except Exception:
        logger.exception("Error deleting resource")
        raise


def update_resource_content(resource_id, content):
    """Update resource content if needed"""
    try:
        with get_user_db_session() as db_session:
            resource = (
                db_session.query(ResearchResource)
                .filter_by(id=resource_id)
                .first()
            )
            if resource:
                resource.content = content
                resource.last_fetched = datetime.now(UTC)
                db_session.commit()
                return resource
    except Exception:
        logger.exception("Error updating resource content")
        return None
