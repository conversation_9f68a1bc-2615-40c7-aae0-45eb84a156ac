from loguru import logger


def convert_debug_to_markdown(raw_text, query):
    """
    Convert the debug-formatted text to clean markdown.

    Args:
        raw_text: The raw formatted findings with debug symbols
        query: Original research query

    Returns:
        Clean markdown formatted text
    """
    try:
        logger.info(f"Starting markdown conversion for query: {query}")
        logger.info(f"Raw text type: {type(raw_text)}")

        # Handle None or empty input
        if not raw_text:
            logger.warning("WARNING: raw_text is empty or None")
            return f"No detailed findings available for '{query}'."

        # If there's a "DETAILED FINDINGS:" section, extract everything after it
        if "DETAILED FINDINGS:" in raw_text:
            logger.info("Found DETAILED FINDINGS section")
            detailed_index = raw_text.index("DETAILED FINDINGS:")
            content = raw_text[
                detailed_index + len("DETAILED FINDINGS:") :
            ].strip()
        else:
            logger.info("No DETAILED FINDINGS section found, using full text")
            content = raw_text

        # Remove divider lines with === symbols
        lines_before = len(content.split("\n"))
        content = "\n".join(
            [
                line
                for line in content.split("\n")
                if not line.strip().startswith("===")
                and not line.strip() == "=" * 80
            ]
        )
        lines_after = len(content.split("\n"))
        logger.info(f"Removed {lines_before - lines_after} divider lines")

        logger.info(f"Final markdown length: {len(content.strip())}")
        return content.strip()
    except Exception as e:
        logger.exception(f"Error in convert_debug_to_markdown: {e!s}")
        # Return a basic message with the original query as fallback
        return f"# Research on {query}\n\nThere was an error formatting the research results."
