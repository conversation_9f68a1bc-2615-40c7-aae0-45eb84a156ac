# local_deep_research/config.py
from loguru import logger

from ..web_search_engines.search_engine_factory import (
    get_search as factory_get_search,
)
from .llm_config import get_llm
from .thread_settings import get_setting_from_snapshot

# Whether to check the quality search results using the LLM.
QUALITY_CHECK_DDG_URLS = True


# get_setting_from_snapshot is now imported from thread_settings


def get_search_snippets_only_setting(username=None, settings_snapshot=None):
    """
    Lazily retrieve the 'search.snippets_only' setting.

    Args:
        username: Optional username for thread context
        settings_snapshot: Optional settings snapshot for thread safety
    """
    return get_setting_from_snapshot(
        "search.snippets_only",
        True,
        username=username,
        settings_snapshot=settings_snapshot,
    )


# Expose get_search function
def get_search(
    search_tool=None,
    llm_instance=None,
    username=None,
    settings_snapshot=None,
    programmatic_mode=False,
):
    """
    Helper function to get search engine

    Args:
        search_tool: Override the search tool setting (e.g. searxng, wikipedia)
        llm_instance: Override the LLM instance
        username: Optional username for thread context (e.g., background research threads)
        settings_snapshot: Settings snapshot from thread context
        programmatic_mode: If True, disables database operations and metrics tracking
    """

    # Use specified tool or default from settings
    tool = search_tool or get_setting_from_snapshot(
        "search.tool",
        "searxng",
        username=username,
        settings_snapshot=settings_snapshot,
    )

    # Debug: Check if we got a dict instead of a string
    if isinstance(tool, dict):
        logger.warning(
            f"Got dict for search.tool, extracting value: {tool.get('value')}"
        )
        if "value" in tool:
            tool = tool["value"]

    logger.info(
        f"Creating search engine with tool: {tool} (type: {type(tool)})"
    )

    # Get LLM instance (use provided or get fresh one)
    llm = llm_instance or get_llm(settings_snapshot=settings_snapshot)

    # Get search parameters
    params = {
        "search_tool": tool,
        "llm_instance": llm,
        "max_results": get_setting_from_snapshot(
            "search.max_results",
            10,
            username=username,
            settings_snapshot=settings_snapshot,
        ),
        "region": get_setting_from_snapshot(
            "search.region",
            "wt-wt",
            username=username,
            settings_snapshot=settings_snapshot,
        ),
        "time_period": get_setting_from_snapshot(
            "search.time_period",
            "all",
            username=username,
            settings_snapshot=settings_snapshot,
        ),
        "safe_search": get_setting_from_snapshot(
            "search.safe_search",
            True,
            username=username,
            settings_snapshot=settings_snapshot,
        ),
        "search_snippets_only": get_setting_from_snapshot(
            "search.snippets_only",
            True,
            username=username,
            settings_snapshot=settings_snapshot,
        ),
        "search_language": get_setting_from_snapshot(
            "search.search_language",
            "English",
            username=username,
            settings_snapshot=settings_snapshot,
        ),
        "max_filtered_results": get_setting_from_snapshot(
            "search.max_filtered_results",
            5,
            username=username,
            settings_snapshot=settings_snapshot,
        ),
    }

    # Log NULL parameters for debugging
    logger.info(
        f"Search config: tool={tool}, max_results={params['max_results']}, time_period={params['time_period']}"
    )
    logger.info(f"Full params dict: {params}")

    # Create search engine
    search_engine = factory_get_search(
        settings_snapshot=settings_snapshot,
        programmatic_mode=programmatic_mode,
        **params,
    )

    # Log the created engine type
    if search_engine:
        logger.info(
            f"Successfully created search engine of type: {type(search_engine).__name__}"
        )
    else:
        logger.error(f"Failed to create search engine for tool: {tool}")

    return search_engine
