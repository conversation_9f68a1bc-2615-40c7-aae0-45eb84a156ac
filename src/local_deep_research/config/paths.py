"""
Centralized path configuration for Local Deep Research.
Handles database location using platformdirs for proper user data storage.
"""

import hashlib
import os
from pathlib import Path

import platformdirs
from loguru import logger


def get_data_directory() -> Path:
    """
    Get the appropriate data directory for storing application data.
    Uses platformdirs to get platform-specific user data directory.

    Environment variable:
        LDR_DATA_DIR: Override the default data directory location.
                     All subdirectories (research_outputs, cache, logs, database)
                     will be created under this directory.

    Returns:
        Path to data directory
    """
    # Check for explicit override via environment variable
    custom_path = os.getenv("LDR_DATA_DIR")
    if custom_path:
        data_dir = Path(custom_path)
        logger.debug(
            f"Using custom data directory from LDR_DATA_DIR: {data_dir}"
        )
        return data_dir

    # Use platformdirs for platform-specific user data directory
    # Windows: C:\Users\<USER>\AppData\Local\local-deep-research
    # macOS: ~/Library/Application Support/local-deep-research
    # Linux: ~/.local/share/local-deep-research
    data_dir = Path(platformdirs.user_data_dir("local-deep-research"))
    # Log only the directory pattern, not the full path which may contain username
    logger.debug(
        f"Using platformdirs data directory pattern: .../{data_dir.name}"
    )

    return data_dir


def get_research_outputs_directory() -> Path:
    """
    Get the directory for storing research outputs (reports, etc.).

    Returns:
        Path to research outputs directory
    """
    # Use subdirectory of main data directory
    data_dir = get_data_directory()
    outputs_dir = data_dir / "research_outputs"
    outputs_dir.mkdir(parents=True, exist_ok=True)

    logger.debug(f"Using research outputs directory: {outputs_dir}")
    return outputs_dir


def get_cache_directory() -> Path:
    """
    Get the directory for storing cache files (search cache, etc.).

    Returns:
        Path to cache directory
    """
    # Use subdirectory of main data directory
    data_dir = get_data_directory()
    cache_dir = data_dir / "cache"
    cache_dir.mkdir(parents=True, exist_ok=True)

    logger.debug(f"Using cache directory: {cache_dir}")
    return cache_dir


def get_logs_directory() -> Path:
    """
    Get the directory for storing log files.

    Returns:
        Path to logs directory
    """
    # Use subdirectory of main data directory
    data_dir = get_data_directory()
    logs_dir = data_dir / "logs"
    logs_dir.mkdir(parents=True, exist_ok=True)

    logger.debug(f"Using logs directory: {logs_dir}")
    return logs_dir


def get_database_path() -> Path:
    """
    Get the path to the application database.
    DEPRECATED: This returns the shared database path which should not be used.
    Use per-user databases instead via encrypted_db.py

    Returns:
        Path to database file (deprecated)
    """
    # Get data directory and ensure it exists
    data_dir = get_data_directory()
    data_dir.mkdir(parents=True, exist_ok=True)

    # Raise exception instead of returning path
    db_path = data_dir / "deprecated_shared.db"
    raise DeprecationWarning(
        f"DEPRECATED: Shared database path requested: {db_path}. "
        "This function should not be used - use per-user encrypted databases instead"
    )


def get_encrypted_database_path() -> Path:
    """Get the path to the encrypted databases directory.

    Returns:
        Path to the encrypted databases directory
    """
    data_dir = get_data_directory()
    encrypted_db_path = data_dir / "encrypted_databases"
    encrypted_db_path.mkdir(parents=True, exist_ok=True)
    return encrypted_db_path


def get_user_database_filename(username: str) -> str:
    """Get the database filename for a specific user.

    Args:
        username: The username to generate a filename for

    Returns:
        The database filename (not full path) for the user
    """
    # Use username hash to avoid filesystem issues with special characters
    username_hash = hashlib.sha256(username.encode()).hexdigest()[:16]
    return f"ldr_user_{username_hash}.db"


# Convenience functions for backward compatibility
def get_data_dir() -> str:
    """Get data directory as string for backward compatibility."""
    return str(get_data_directory())


def get_db_path() -> str:
    """Get database path as string for backward compatibility."""
    return str(get_database_path())
