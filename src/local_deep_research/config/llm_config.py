import os
from functools import cache

from langchain_anthropic import <PERSON>t<PERSON>nt<PERSON>
from langchain_community.llms import VLLM
from langchain_core.language_models import BaseChatModel, FakeListChatModel
from langchain_ollama import ChatOllama
from langchain_openai import Chat<PERSON>penAI
from loguru import logger

from ..llm import get_llm_from_registry, is_llm_registered
from ..utilities.search_utilities import remove_think_tags
from ..utilities.url_utils import normalize_url
from .thread_settings import (
    get_setting_from_snapshot as _get_setting_from_snapshot,
    NoSettingsContextError,
)

# Valid provider options
VALID_PROVIDERS = [
    "ollama",
    "openai",
    "anthropic",
    "vllm",
    "openai_endpoint",
    "lmstudio",
    "llamacpp",
    "none",
]


def get_setting_from_snapshot(
    key, default=None, username=None, settings_snapshot=None
):
    """Get setting from context only - no database access from threads.

    This is a wrapper around the shared function that enables fallback LLM check.
    """
    return _get_setting_from_snapshot(
        key, default, username, settings_snapshot, check_fallback_llm=True
    )


def is_openai_available(settings_snapshot=None):
    """Check if OpenAI is available"""
    try:
        api_key = get_setting_from_snapshot(
            "llm.openai.api_key",
            default=None,
            settings_snapshot=settings_snapshot,
        )
        return bool(api_key)
    except Exception:
        return False


def is_anthropic_available(settings_snapshot=None):
    """Check if Anthropic is available"""
    try:
        api_key = get_setting_from_snapshot(
            "llm.anthropic.api_key",
            default=None,
            settings_snapshot=settings_snapshot,
        )
        return bool(api_key)
    except Exception:
        return False


def is_openai_endpoint_available(settings_snapshot=None):
    """Check if OpenAI endpoint is available"""
    try:
        api_key = get_setting_from_snapshot(
            "llm.openai_endpoint.api_key",
            default=None,
            settings_snapshot=settings_snapshot,
        )
        return bool(api_key)
    except Exception:
        return False


def is_ollama_available(settings_snapshot=None):
    """Check if Ollama is running"""
    try:
        import requests

        raw_base_url = get_setting_from_snapshot(
            "llm.ollama.url",
            "http://localhost:11434",
            settings_snapshot=settings_snapshot,
        )
        base_url = (
            normalize_url(raw_base_url)
            if raw_base_url
            else "http://localhost:11434"
        )
        logger.info(f"Checking Ollama availability at {base_url}/api/tags")

        try:
            response = requests.get(f"{base_url}/api/tags", timeout=3.0)
            if response.status_code == 200:
                logger.info(
                    f"Ollama is available. Status code: {response.status_code}"
                )
                # Log first 100 chars of response to debug
                logger.info(f"Response preview: {str(response.text)[:100]}")
                return True
            else:
                logger.warning(
                    f"Ollama API returned status code: {response.status_code}"
                )
                return False
        except requests.exceptions.RequestException as req_error:
            logger.exception(
                f"Request error when checking Ollama: {req_error!s}"
            )
            return False
        except Exception:
            logger.exception("Unexpected error when checking Ollama")
            return False
    except Exception:
        logger.exception("Error in is_ollama_available")
        return False


def is_vllm_available():
    """Check if VLLM capability is available"""
    try:
        import torch  # noqa: F401
        import transformers  # noqa: F401

        return True
    except ImportError:
        return False


def is_lmstudio_available(settings_snapshot=None):
    """Check if LM Studio is available"""
    try:
        import requests

        lmstudio_url = get_setting_from_snapshot(
            "llm.lmstudio.url",
            "http://localhost:1234",
            settings_snapshot=settings_snapshot,
        )
        # LM Studio typically uses OpenAI-compatible endpoints
        response = requests.get(f"{lmstudio_url}/v1/models", timeout=1.0)
        return response.status_code == 200
    except Exception:
        return False


def is_llamacpp_available(settings_snapshot=None):
    """Check if LlamaCpp is available and configured"""
    try:
        # Import check
        from langchain_community.llms import LlamaCpp  # noqa: F401

        # Get the configured model path
        model_path_str = get_setting_from_snapshot(
            "llm.llamacpp_model_path",
            default=None,
            settings_snapshot=settings_snapshot,
        )

        # If no path configured, LlamaCpp is not available
        if not model_path_str:
            return False

        # Security Note: Path validation is critical here
        # CodeQL may flag filesystem operations with user input
        # We validate paths are within allowed directories before any filesystem access

        # For security, we simply check if a path is configured
        # The actual path validation will happen when the model is loaded
        # This avoids CodeQL alerts about filesystem access with user input
        # The LlamaCpp library itself will validate the path when loading
        return True

    except ImportError:
        # LlamaCpp library not installed
        return False

    except Exception:
        return False


@cache
def get_available_providers(settings_snapshot=None):
    """Return available model providers"""
    providers = {}

    if is_ollama_available(settings_snapshot):
        providers["ollama"] = "Ollama (local models)"

    if is_openai_available(settings_snapshot):
        providers["openai"] = "OpenAI API"

    if is_anthropic_available(settings_snapshot):
        providers["anthropic"] = "Anthropic API"

    if is_openai_endpoint_available(settings_snapshot):
        providers["openai_endpoint"] = "OpenAI-compatible Endpoint"

    if is_lmstudio_available(settings_snapshot):
        providers["lmstudio"] = "LM Studio (local models)"

    if is_llamacpp_available(settings_snapshot):
        providers["llamacpp"] = "LlamaCpp (local models)"

    # Check for VLLM capability
    try:
        import torch  # noqa: F401
        import transformers  # noqa: F401

        providers["vllm"] = "VLLM (local models)"
    except ImportError:
        pass

    # Default fallback
    if not providers:
        providers["none"] = "No model providers available"

    return providers


def get_selected_llm_provider(settings_snapshot=None):
    return get_setting_from_snapshot(
        "llm.provider", "ollama", settings_snapshot=settings_snapshot
    ).lower()


def get_llm(
    model_name=None,
    temperature=None,
    provider=None,
    openai_endpoint_url=None,
    research_id=None,
    research_context=None,
    settings_snapshot=None,
):
    """
    Get LLM instance based on model name and provider.

    Args:
        model_name: Name of the model to use (if None, uses database setting)
        temperature: Model temperature (if None, uses database setting)
        provider: Provider to use (if None, uses database setting)
        openai_endpoint_url: Custom endpoint URL to use (if None, uses database
            setting)
        research_id: Optional research ID for token tracking
        research_context: Optional research context for enhanced token tracking

    Returns:
        A LangChain LLM instance with automatic think-tag removal
    """

    # Use database values for parameters if not provided
    if model_name is None:
        model_name = get_setting_from_snapshot(
            "llm.model", "gemma:latest", settings_snapshot=settings_snapshot
        )
    if temperature is None:
        temperature = get_setting_from_snapshot(
            "llm.temperature", 0.7, settings_snapshot=settings_snapshot
        )
    if provider is None:
        provider = get_setting_from_snapshot(
            "llm.provider", "ollama", settings_snapshot=settings_snapshot
        )

    # Clean model name: remove quotes and extra whitespace
    if model_name:
        model_name = model_name.strip().strip("\"'").strip()

    # Clean provider: remove quotes and extra whitespace
    if provider:
        provider = provider.strip().strip("\"'").strip()

    # Normalize provider: convert to lowercase
    provider = provider.lower() if provider else None

    # Check if this is a registered custom LLM first
    if provider and is_llm_registered(provider):
        logger.info(f"Using registered custom LLM: {provider}")
        custom_llm = get_llm_from_registry(provider)

        # Check if it's a callable (factory function) or a BaseChatModel instance
        if callable(custom_llm) and not isinstance(custom_llm, BaseChatModel):
            # It's a callable (factory function), call it with parameters
            try:
                llm_instance = custom_llm(
                    model_name=model_name,
                    temperature=temperature,
                )
            except TypeError as e:
                # Re-raise TypeError with better message
                raise TypeError(
                    f"Registered LLM factory '{provider}' has invalid signature. "
                    f"Factory functions must accept 'model_name' and 'temperature' parameters. "
                    f"Error: {e}"
                )

            # Validate the result is a BaseChatModel
            if not isinstance(llm_instance, BaseChatModel):
                raise ValueError(
                    f"Factory function for {provider} must return a BaseChatModel instance, "
                    f"got {type(llm_instance).__name__}"
                )
        elif isinstance(custom_llm, BaseChatModel):
            # It's already a proper LLM instance, use it directly
            llm_instance = custom_llm
        else:
            raise ValueError(
                f"Registered LLM {provider} must be either a BaseChatModel instance "
                f"or a callable factory function. Got: {type(custom_llm).__name__}"
            )

        return wrap_llm_without_think_tags(
            llm_instance,
            research_id=research_id,
            provider=provider,
            research_context=research_context,
            settings_snapshot=settings_snapshot,
        )

    # Check if we're in testing mode and should use fallback (but only when no API keys are configured)
    # Skip fallback check if we're in test mode with mocks
    if os.environ.get("LDR_USE_FALLBACK_LLM", "") and not os.environ.get(
        "LDR_TESTING_WITH_MOCKS", ""
    ):
        # Only use fallback if the provider has no valid configuration
        provider_has_config = False

        if (
            (
                provider == "openai"
                and get_setting_from_snapshot(
                    "llm.openai.api_key",
                    default=None,
                    settings_snapshot=settings_snapshot,
                )
            )
            or (
                provider == "anthropic"
                and get_setting_from_snapshot(
                    "llm.anthropic.api_key",
                    default=None,
                    settings_snapshot=settings_snapshot,
                )
            )
            or (
                provider == "openai_endpoint"
                and get_setting_from_snapshot(
                    "llm.openai_endpoint.api_key",
                    settings_snapshot=settings_snapshot,
                )
            )
            or (
                provider == "ollama"
                and is_ollama_available(settings_snapshot=settings_snapshot)
            )
        ):
            provider_has_config = True
        elif provider in ["vllm", "lmstudio", "llamacpp"]:
            # These are local providers, check their availability
            if (
                (provider == "vllm" and is_vllm_available())
                or (
                    provider == "lmstudio"
                    and is_lmstudio_available(
                        settings_snapshot=settings_snapshot
                    )
                )
                or (
                    provider == "llamacpp"
                    and is_llamacpp_available(
                        settings_snapshot=settings_snapshot
                    )
                )
            ):
                provider_has_config = True

        if not provider_has_config:
            logger.info(
                "LDR_USE_FALLBACK_LLM is set and no valid provider config found, using fallback model"
            )
            return wrap_llm_without_think_tags(
                get_fallback_model(temperature),
                research_id=research_id,
                provider="fallback",
                research_context=research_context,
                settings_snapshot=settings_snapshot,
            )

    # Validate provider
    if provider not in VALID_PROVIDERS:
        logger.error(f"Invalid provider in settings: {provider}")
        raise ValueError(
            f"Invalid provider: {provider}. Must be one of: {VALID_PROVIDERS}"
        )
    logger.info(
        f"Getting LLM with model: {model_name}, temperature: {temperature}, provider: {provider}"
    )

    # Common parameters for all models
    common_params = {
        "temperature": temperature,
    }

    # Get context window size from settings (use different defaults for local vs cloud providers)
    def get_context_window_size(provider_type):
        if provider_type in ["ollama", "llamacpp", "lmstudio"]:
            # Local providers: use smaller default to prevent memory issues
            window_size = get_setting_from_snapshot(
                "llm.local_context_window_size",
                4096,
                settings_snapshot=settings_snapshot,
            )
            # Ensure it's an integer
            return int(window_size) if window_size is not None else 4096
        else:
            # Cloud providers: check if unrestricted mode is enabled
            use_unrestricted = get_setting_from_snapshot(
                "llm.context_window_unrestricted",
                True,
                settings_snapshot=settings_snapshot,
            )
            if use_unrestricted:
                # Let cloud providers auto-handle context (return None or very large value)
                return None  # Will be handled per provider
            else:
                # Use user-specified limit
                window_size = get_setting_from_snapshot(
                    "llm.context_window_size",
                    128000,
                    settings_snapshot=settings_snapshot,
                )
                return int(window_size) if window_size is not None else 128000

    context_window_size = get_context_window_size(provider)

    # Add context limit to research context for overflow detection
    if research_context and context_window_size:
        research_context["context_limit"] = context_window_size
        logger.info(
            f"Set context_limit={context_window_size} in research_context"
        )
    else:
        logger.debug(
            f"Context limit not set: research_context={bool(research_context)}, context_window_size={context_window_size}"
        )

    if get_setting_from_snapshot(
        "llm.supports_max_tokens", True, settings_snapshot=settings_snapshot
    ):
        # Use 80% of context window to leave room for prompts
        if context_window_size is not None:
            max_tokens = min(
                int(
                    get_setting_from_snapshot(
                        "llm.max_tokens",
                        100000,
                        settings_snapshot=settings_snapshot,
                    )
                ),
                int(context_window_size * 0.8),
            )
            common_params["max_tokens"] = max_tokens
        else:
            # Unrestricted context: use provider's default max_tokens
            max_tokens = int(
                get_setting_from_snapshot(
                    "llm.max_tokens",
                    100000,
                    settings_snapshot=settings_snapshot,
                )
            )
            common_params["max_tokens"] = max_tokens

    # Handle different providers
    if provider == "anthropic":
        api_key = get_setting_from_snapshot(
            "llm.anthropic.api_key", settings_snapshot=settings_snapshot
        )

        if not api_key:
            logger.warning(
                "Anthropic API key not found in settings. Falling back to default model."
            )
            return get_fallback_model(temperature)

        llm = ChatAnthropic(
            model=model_name, anthropic_api_key=api_key, **common_params
        )
        return wrap_llm_without_think_tags(
            llm,
            research_id=research_id,
            provider=provider,
            research_context=research_context,
            settings_snapshot=settings_snapshot,
        )

    elif provider == "openai":
        api_key = get_setting_from_snapshot(
            "llm.openai.api_key", settings_snapshot=settings_snapshot
        )

        if not api_key:
            logger.warning(
                "OpenAI API key not found in settings. Falling back to default model."
            )
            return get_fallback_model(temperature)

        # Build OpenAI-specific parameters
        openai_params = {
            "model": model_name,
            "api_key": api_key,
            **common_params,
        }

        # Add optional parameters if they exist in settings
        try:
            api_base = get_setting_from_snapshot(
                "llm.openai.api_base",
                default=None,
                settings_snapshot=settings_snapshot,
            )
            if api_base:
                openai_params["openai_api_base"] = api_base
        except NoSettingsContextError:
            pass  # Optional parameter

        try:
            organization = get_setting_from_snapshot(
                "llm.openai.organization",
                default=None,
                settings_snapshot=settings_snapshot,
            )
            if organization:
                openai_params["openai_organization"] = organization
        except NoSettingsContextError:
            pass  # Optional parameter

        try:
            streaming = get_setting_from_snapshot(
                "llm.streaming",
                default=None,
                settings_snapshot=settings_snapshot,
            )
        except NoSettingsContextError:
            streaming = None  # Optional parameter
        if streaming is not None:
            openai_params["streaming"] = streaming

        try:
            max_retries = get_setting_from_snapshot(
                "llm.max_retries",
                default=None,
                settings_snapshot=settings_snapshot,
            )
            if max_retries is not None:
                openai_params["max_retries"] = max_retries
        except NoSettingsContextError:
            pass  # Optional parameter

        try:
            request_timeout = get_setting_from_snapshot(
                "llm.request_timeout",
                default=None,
                settings_snapshot=settings_snapshot,
            )
            if request_timeout is not None:
                openai_params["request_timeout"] = request_timeout
        except NoSettingsContextError:
            pass  # Optional parameter

        llm = ChatOpenAI(**openai_params)
        return wrap_llm_without_think_tags(
            llm,
            research_id=research_id,
            provider=provider,
            research_context=research_context,
            settings_snapshot=settings_snapshot,
        )

    elif provider == "openai_endpoint":
        api_key = get_setting_from_snapshot(
            "llm.openai_endpoint.api_key", settings_snapshot=settings_snapshot
        )

        if not api_key:
            logger.warning(
                "OpenAI endpoint API key not found in settings. Falling back to default model."
            )
            return get_fallback_model(temperature)

        # Get endpoint URL from settings
        if openai_endpoint_url is None:
            openai_endpoint_url = get_setting_from_snapshot(
                "llm.openai_endpoint.url",
                "https://openrouter.ai/api/v1",
                settings_snapshot=settings_snapshot,
            )
        openai_endpoint_url = normalize_url(openai_endpoint_url)

        llm = ChatOpenAI(
            model=model_name,
            api_key=api_key,
            openai_api_base=openai_endpoint_url,
            **common_params,
        )
        return wrap_llm_without_think_tags(
            llm,
            research_id=research_id,
            provider=provider,
            research_context=research_context,
            settings_snapshot=settings_snapshot,
        )

    elif provider == "vllm":
        try:
            llm = VLLM(
                model=model_name,
                trust_remote_code=True,
                max_new_tokens=128,
                top_k=10,
                top_p=0.95,
                temperature=temperature,
            )
            return wrap_llm_without_think_tags(
                llm,
                research_id=research_id,
                provider=provider,
                research_context=research_context,
            )
        except Exception:
            logger.exception("Error loading VLLM model")
            return get_fallback_model(temperature)

    elif provider == "ollama":
        try:
            # Use the configurable Ollama base URL
            raw_base_url = get_setting_from_snapshot(
                "llm.ollama.url",
                "http://localhost:11434",
                settings_snapshot=settings_snapshot,
            )
            base_url = (
                normalize_url(raw_base_url)
                if raw_base_url
                else "http://localhost:11434"
            )

            # Check if Ollama is available before trying to use it
            if not is_ollama_available(settings_snapshot=settings_snapshot):
                logger.error(
                    f"Ollama not available at {base_url}. Falling back to dummy model."
                )
                return get_fallback_model(temperature)

            # Check if the requested model exists
            import requests

            try:
                logger.info(
                    f"Checking if model '{model_name}' exists in Ollama"
                )
                response = requests.get(f"{base_url}/api/tags", timeout=3.0)
                if response.status_code == 200:
                    # Handle both newer and older Ollama API formats
                    data = response.json()
                    models = []
                    if "models" in data:
                        # Newer Ollama API
                        models = data.get("models", [])
                    else:
                        # Older Ollama API format
                        models = data

                    # Get list of model names
                    model_names = [m.get("name", "").lower() for m in models]
                    logger.info(
                        f"Available Ollama models: {', '.join(model_names[:5])}{' and more' if len(model_names) > 5 else ''}"
                    )

                    if model_name.lower() not in model_names:
                        logger.error(
                            f"Model '{model_name}' not found in Ollama. Available models: {', '.join(model_names[:5])}"
                        )
                        return get_fallback_model(temperature)
            except Exception:
                logger.exception(
                    f"Error checking for model '{model_name}' in Ollama"
                )
                # Continue anyway, let ChatOllama handle potential errors

            logger.info(
                f"Creating ChatOllama with model={model_name}, base_url={base_url}"
            )
            try:
                # Add num_ctx parameter for Ollama context window size
                ollama_params = {**common_params}
                if context_window_size is not None:
                    ollama_params["num_ctx"] = context_window_size
                llm = ChatOllama(
                    model=model_name, base_url=base_url, **ollama_params
                )

                # Log the actual client configuration after creation
                logger.debug(
                    f"ChatOllama created - base_url attribute: {getattr(llm, 'base_url', 'not found')}"
                )
                if hasattr(llm, "_client"):
                    client = llm._client
                    logger.debug(f"ChatOllama _client type: {type(client)}")
                    if hasattr(client, "_client"):
                        inner_client = client._client
                        logger.debug(
                            f"ChatOllama inner client type: {type(inner_client)}"
                        )
                        if hasattr(inner_client, "base_url"):
                            logger.debug(
                                f"ChatOllama inner client base_url: {inner_client.base_url}"
                            )

                return wrap_llm_without_think_tags(
                    llm,
                    research_id=research_id,
                    provider=provider,
                    research_context=research_context,
                    settings_snapshot=settings_snapshot,
                )
            except Exception:
                logger.exception("Error creating or testing ChatOllama")
                return get_fallback_model(temperature)
        except Exception:
            logger.exception("Error in Ollama provider section")
            return get_fallback_model(temperature)

    elif provider == "lmstudio":
        # LM Studio supports OpenAI API format, so we can use ChatOpenAI directly
        lmstudio_url = get_setting_from_snapshot(
            "llm.lmstudio.url",
            "http://localhost:1234",
            settings_snapshot=settings_snapshot,
        )

        llm = ChatOpenAI(
            model=model_name,
            api_key="lm-studio",  # LM Studio doesn't require a real API key  # pragma: allowlist secret
            base_url=f"{lmstudio_url}/v1",  # Use the configured URL with /v1 endpoint
            temperature=temperature,
            max_tokens=max_tokens,  # Use calculated max_tokens based on context size
        )
        return wrap_llm_without_think_tags(
            llm,
            research_id=research_id,
            provider=provider,
            research_context=research_context,
            settings_snapshot=settings_snapshot,
        )

    # Update the llamacpp section in get_llm function
    elif provider == "llamacpp":
        # Import LlamaCpp
        from langchain_community.llms import LlamaCpp

        # Get LlamaCpp connection mode from settings
        connection_mode = get_setting_from_snapshot(
            "llm.llamacpp_connection_mode",
            "local",
            settings_snapshot=settings_snapshot,
        )

        if connection_mode == "http":
            # Use HTTP client mode
            from langchain_community.llms.llamacpp_client import LlamaCppClient

            server_url = get_setting_from_snapshot(
                "llm.llamacpp_server_url",
                "http://localhost:8000",
                settings_snapshot=settings_snapshot,
            )

            llm = LlamaCppClient(
                server_url=server_url,
                temperature=temperature,
                max_tokens=get_setting_from_snapshot(
                    "llm.max_tokens", 8192, settings_snapshot=settings_snapshot
                ),
            )
        else:
            # Use direct model loading (existing code)
            # Get LlamaCpp model path from settings
            model_path = get_setting_from_snapshot(
                "llm.llamacpp_model_path", settings_snapshot=settings_snapshot
            )
            if not model_path:
                logger.error("llamacpp_model_path not set in settings")
                raise ValueError("llamacpp_model_path not set in settings")

            # Validate model path for security using centralized validator
            from ..security.path_validator import PathValidator

            try:
                validated_path = PathValidator.validate_model_path(model_path)
                model_path = str(validated_path)
            except ValueError:
                logger.exception("Model path validation failed")
                raise

            # Get additional LlamaCpp parameters
            n_gpu_layers = get_setting_from_snapshot(
                "llm.llamacpp_n_gpu_layers",
                1,
                settings_snapshot=settings_snapshot,
            )
            n_batch = get_setting_from_snapshot(
                "llm.llamacpp_n_batch", 512, settings_snapshot=settings_snapshot
            )
            f16_kv = get_setting_from_snapshot(
                "llm.llamacpp_f16_kv", True, settings_snapshot=settings_snapshot
            )

            # Create LlamaCpp instance
            llm = LlamaCpp(
                model_path=model_path,
                temperature=temperature,
                max_tokens=max_tokens,  # Use calculated max_tokens
                n_gpu_layers=n_gpu_layers,
                n_batch=n_batch,
                f16_kv=f16_kv,
                n_ctx=context_window_size,  # Set context window size directly (None = use default)
                verbose=True,
            )

        return wrap_llm_without_think_tags(
            llm,
            research_id=research_id,
            provider=provider,
            research_context=research_context,
            settings_snapshot=settings_snapshot,
        )

    else:
        return wrap_llm_without_think_tags(
            get_fallback_model(temperature),
            research_id=research_id,
            provider=provider,
            research_context=research_context,
            settings_snapshot=settings_snapshot,
        )


def get_fallback_model(temperature=None):
    """Create a dummy model for when no providers are available"""
    return FakeListChatModel(
        responses=[
            "No language models are available. Please install Ollama or set up API keys."
        ]
    )


def wrap_llm_without_think_tags(
    llm,
    research_id=None,
    provider=None,
    research_context=None,
    settings_snapshot=None,
):
    """Create a wrapper class that processes LLM outputs with remove_think_tags and token counting"""

    # First apply rate limiting if enabled
    from ..web_search_engines.rate_limiting.llm import (
        create_rate_limited_llm_wrapper,
    )

    # Check if LLM rate limiting is enabled (independent of search rate limiting)
    # Use the thread-safe get_db_setting defined in this module
    if get_setting_from_snapshot(
        "rate_limiting.llm_enabled", False, settings_snapshot=settings_snapshot
    ):
        llm = create_rate_limited_llm_wrapper(llm, provider)

    # Import token counting functionality if research_id is provided
    callbacks = []
    if research_id is not None:
        from ..metrics import TokenCounter

        token_counter = TokenCounter()
        token_callback = token_counter.create_callback(
            research_id, research_context
        )
        # Set provider and model info on the callback
        if provider:
            token_callback.preset_provider = provider
        # Try to extract model name from the LLM instance
        if hasattr(llm, "model_name"):
            token_callback.preset_model = llm.model_name
        elif hasattr(llm, "model"):
            token_callback.preset_model = llm.model
        callbacks.append(token_callback)

    # Add callbacks to the LLM if it supports them
    if callbacks and hasattr(llm, "callbacks"):
        if llm.callbacks is None:
            llm.callbacks = callbacks
        else:
            llm.callbacks.extend(callbacks)

    class ProcessingLLMWrapper:
        def __init__(self, base_llm):
            self.base_llm = base_llm

        def invoke(self, *args, **kwargs):
            # Log detailed request information for Ollama models
            if hasattr(self.base_llm, "base_url"):
                logger.debug(
                    f"LLM Request - Base URL: {self.base_llm.base_url}"
                )
                logger.debug(
                    f"LLM Request - Model: {getattr(self.base_llm, 'model', 'unknown')}"
                )
                logger.debug(
                    f"LLM Request - Args count: {len(args)}, Kwargs: {list(kwargs.keys())}"
                )

                # Log the prompt if it's in args
                if args and len(args) > 0:
                    prompt_text = (
                        str(args[0])[:200] + "..."
                        if len(str(args[0])) > 200
                        else str(args[0])
                    )
                    logger.debug(f"LLM Request - Prompt preview: {prompt_text}")

                # Check if there's any client configuration
                if hasattr(self.base_llm, "_client"):
                    client = self.base_llm._client
                    if hasattr(client, "_client") and hasattr(
                        client._client, "base_url"
                    ):
                        logger.debug(
                            f"LLM Request - Client base URL: {client._client.base_url}"
                        )

            try:
                response = self.base_llm.invoke(*args, **kwargs)
                logger.debug(f"LLM Response - Success, type: {type(response)}")
            except Exception as e:
                logger.exception("LLM Request - Failed with error")
                # Log any URL information from the error
                error_str = str(e)
                if "http://" in error_str or "https://" in error_str:
                    logger.exception(
                        f"LLM Request - Error contains URL info: {error_str}"
                    )
                raise

            # Process the response content if it has a content attribute
            if hasattr(response, "content"):
                response.content = remove_think_tags(response.content)
            elif isinstance(response, str):
                response = remove_think_tags(response)

            return response

        # Pass through any other attributes to the base LLM
        def __getattr__(self, name):
            return getattr(self.base_llm, name)

    return ProcessingLLMWrapper(llm)
