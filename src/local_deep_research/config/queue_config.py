"""
Queue configuration settings.
"""

import os

# Queue mode configuration
QUEUE_MODE = os.environ.get(
    "LDR_QUEUE_MODE", "direct"
).lower()  # "direct" or "queue"

# Maximum concurrent researches per user
MAX_CONCURRENT_PER_USER = int(os.environ.get("LDR_MAX_CONCURRENT", "3"))

# Whether to use queue processor at all
USE_QUEUE_PROCESSOR = QUEUE_MODE == "queue"

# Queue check interval (seconds) - only used if queuing is enabled
QUEUE_CHECK_INTERVAL = int(os.environ.get("LDR_QUEUE_INTERVAL", "10"))
