"""
Database encryption and performance settings.

NOTE: Database settings have been moved to environment variables only,
as they cannot be changed after database creation.

Environment variables:
- LDR_DB_KDF_ITERATIONS: Number of key derivation iterations (default: 4000)
- LDR_DB_PAGE_SIZE: Database page size in bytes (default: 4096)
- <PERSON>DR_DB_CACHE_SIZE_MB: Cache size in megabytes (default: 64)
- LDR_DB_JOURNAL_MODE: Journal mode (default: WAL)
- LDR_DB_SYNCHRONOUS: Synchronous mode (default: NORMAL)
- LDR_DB_HMAC_ALGORITHM: HMAC algorithm (default: HMAC_SHA512)
- <PERSON>DR_DB_KDF_ALGORITHM: KDF algorithm (default: PBKDF2_HMAC_SHA512)
"""

# Empty list since all database settings are now environment-only
database_settings = []
