"""
Optimization submodule for parameter tuning in Local Deep Research.

This module provides tools for finding optimal parameter configurations
for the research system using Optuna and other optimization methods.
"""

from local_deep_research.benchmarks.optimization.api import (
    optimize_for_efficiency,
    optimize_for_quality,
    optimize_for_speed,
    optimize_parameters,
)
from local_deep_research.benchmarks.optimization.metrics import (
    calculate_combined_score,
    calculate_quality_metrics,
    calculate_resource_metrics,
    calculate_speed_metrics,
)
from local_deep_research.benchmarks.optimization.optuna_optimizer import (
    OptunaOptimizer,
)

__all__ = [
    "OptunaOptimizer",
    "calculate_combined_score",
    "calculate_quality_metrics",
    "calculate_resource_metrics",
    "calculate_speed_metrics",
    "optimize_for_efficiency",
    "optimize_for_quality",
    "optimize_for_speed",
    "optimize_parameters",
]
