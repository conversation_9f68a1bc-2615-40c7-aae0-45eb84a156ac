"""
Benchmarking module for Local Deep Research.

This module provides tools for evaluating LDR's performance on standard benchmarks
and for optimizing performance through parameter tuning.
"""

__version__ = "0.2.0"

# Core benchmark functionality
from .datasets import get_available_datasets, load_dataset
from .metrics import (
    calculate_combined_score,
    calculate_metrics,
    calculate_quality_metrics,
    calculate_resource_metrics,
    calculate_speed_metrics,
    generate_report,
)

# Optimization functionality
from .optimization import (
    optimize_for_efficiency,
    optimize_for_quality,
    optimize_for_speed,
    optimize_parameters,
)
from .runners import (
    run_benchmark,
    run_browsecomp_benchmark,
    run_simpleqa_benchmark,
)

__all__ = [
    # Core benchmark functionality
    "run_benchmark",
    "run_simpleqa_benchmark",
    "run_browsecomp_benchmark",
    "load_dataset",
    "get_available_datasets",
    "calculate_metrics",
    "generate_report",
    # Metrics for optimization
    "calculate_quality_metrics",
    "calculate_speed_metrics",
    "calculate_resource_metrics",
    "calculate_combined_score",
    # Optimization functionality
    "optimize_parameters",
    "optimize_for_quality",
    "optimize_for_speed",
    "optimize_for_efficiency",
]
