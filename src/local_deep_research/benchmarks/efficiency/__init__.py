"""
Efficiency submodule for speed and resource monitoring in Local Deep Research.

This module provides tools for measuring and optimizing execution speed
and resource usage of the research system.
"""

from local_deep_research.benchmarks.efficiency.resource_monitor import (
    ResourceMonitor,
)
from local_deep_research.benchmarks.efficiency.speed_profiler import (
    SpeedProfiler,
)

__all__ = [
    "ResourceMonitor",
    "SpeedProfiler",
]
