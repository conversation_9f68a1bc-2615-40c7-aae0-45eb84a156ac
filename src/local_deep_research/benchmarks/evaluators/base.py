"""
Base class for benchmark evaluators.

This module defines the abstract base class that all benchmark evaluators
must implement, establishing a common interface for different benchmark types.
"""

from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict


class BaseBenchmarkEvaluator(ABC):
    """
    Abstract base class for benchmark evaluators.

    All benchmark evaluator implementations must inherit from this class and
    implement the evaluate method to run their specific benchmark type.
    """

    def __init__(self, name: str):
        """
        Initialize benchmark evaluator with a name.

        Args:
            name: Unique identifier for this benchmark type
        """
        self.name = name

    def get_name(self) -> str:
        """
        Get the benchmark name.

        Returns:
            The benchmark identifier
        """
        return self.name

    @abstractmethod
    def evaluate(
        self,
        system_config: Dict[str, Any],
        num_examples: int,
        output_dir: str,
    ) -> Dict[str, Any]:
        """
        Run benchmark evaluation with given system configuration.

        Args:
            system_config: Configuration parameters for the system under test
            num_examples: Number of benchmark examples to evaluate
            output_dir: Directory to save evaluation results

        Returns:
            Dictionary with evaluation metrics including quality_score (0-1)
        """
        pass

    def _create_subdirectory(self, output_dir: str) -> str:
        """
        Create a benchmark-specific subdirectory for output.

        Args:
            output_dir: Parent directory for output

        Returns:
            Path to the benchmark-specific directory
        """
        benchmark_dir = Path(output_dir) / self.name
        benchmark_dir.mkdir(parents=True, exist_ok=True)
        return str(benchmark_dir)
