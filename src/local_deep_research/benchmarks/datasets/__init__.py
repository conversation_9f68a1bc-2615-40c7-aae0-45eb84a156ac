"""
Dataset handling for benchmarks.

This module provides functions and classes for loading and processing
benchmark datasets in a maintainable, extensible way.
"""

from .base import DatasetRegistry
from .browsecomp import BrowseCompDataset
from .simpleqa import SimpleQADataset

# Register datasets
DatasetRegistry.register(SimpleQADataset)
DatasetRegistry.register(BrowseCompDataset)

# Add convenience functions mirroring the old interface
DEFAULT_DATASET_URLS = {
    "simpleqa": SimpleQADataset.get_default_dataset_path(),
    "browsecomp": BrowseCompDataset.get_default_dataset_path(),
}


def get_available_datasets():
    """Get information about all registered datasets."""
    return DatasetRegistry.get_available_datasets()


def load_dataset(
    dataset_type,
    dataset_path=None,
    num_examples=None,
    seed=42,
):
    """Load a dataset by name.

    This is a backwards-compatible function that uses the new dataset classes
    under the hood. It exists to maintain compatibility with existing code.

    Args:
        dataset_type: Type of dataset ('simpleqa' or 'browsecomp')
        dataset_path: Path or URL to dataset (if None, uses default URL)
        num_examples: Optional number of examples to sample
        seed: Random seed for sampling

    Returns:
        List of processed examples
    """
    return DatasetRegistry.load_dataset(
        dataset_id=dataset_type.lower(),
        dataset_path=dataset_path,
        num_examples=num_examples,
        seed=seed,
    )
