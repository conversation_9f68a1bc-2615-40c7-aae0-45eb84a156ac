# Unified Metrics Module

This module provides a unified approach to metrics calculation, reporting, and visualization for both standard benchmarks and parameter optimization.

## Overview

The metrics module consists of three primary components:

1. **Calculation**: Core functions for calculating metrics from benchmark results and system configurations
2. **Reporting**: Functions for generating detailed reports from benchmark results
3. **Visualization**: Utilities for creating visualizations of optimization results

## Usage

### Basic Metrics Calculation

```python
from local_deep_research.benchmarks.metrics import calculate_metrics

# Calculate metrics from a results file
metrics = calculate_metrics("path/to/results.jsonl")
```

### Generating Reports

```python
from local_deep_research.benchmarks.metrics import generate_report

# Generate a detailed report
report_path = generate_report(
    metrics=metrics,
    results_file="path/to/results.jsonl",
    output_file="report.md",
    dataset_name="SimpleQA",
    config_info={"Dataset": "SimpleQA", "Examples": 100}
)
```

### Optimization Metrics

```python
from local_deep_research.benchmarks.metrics import (
    calculate_quality_metrics,
    calculate_speed_metrics,
    calculate_resource_metrics,
    calculate_combined_score
)

# Calculate quality metrics for a configuration
quality_metrics = calculate_quality_metrics(
    system_config={"iterations": 3, "questions_per_iteration": 3}
)

# Calculate a combined score using multiple metrics
combined_score = calculate_combined_score(
    metrics={
        "quality": quality_metrics,
        "speed": speed_metrics,
        "resource": resource_metrics
    },
    weights={"quality": 0.6, "speed": 0.3, "resource": 0.1}
)
```

### Visualization

```python
from local_deep_research.benchmarks.metrics.visualization import (
    plot_optimization_history,
    plot_parameter_importance,
    plot_quality_vs_speed
)

# Plot optimization history
fig = plot_optimization_history(
    trial_values=[0.5, 0.6, 0.7, 0.65, 0.8],
    best_values=[0.5, 0.6, 0.7, 0.7, 0.8],
    output_file="optimization_history.png"
)
```
