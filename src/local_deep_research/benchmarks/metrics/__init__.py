"""
Unified metrics module for benchmark evaluation and optimization.

This package provides metrics calculation, reporting, and visualization
functionality for both regular benchmarks and parameter optimization.
"""

from .calculation import (
    calculate_combined_score,
    calculate_metrics,
    calculate_quality_metrics,
    calculate_resource_metrics,
    calculate_speed_metrics,
)
from .reporting import generate_report

__all__ = [
    "calculate_combined_score",
    "calculate_metrics",
    "calculate_quality_metrics",
    "calculate_resource_metrics",
    "calculate_speed_metrics",
    "generate_report",
]
