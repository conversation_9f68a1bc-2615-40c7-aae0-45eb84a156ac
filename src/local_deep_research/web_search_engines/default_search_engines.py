"""
Default search engine configurations.
This file can be used to initialize the search engine configurations.
"""


def get_default_elasticsearch_config():
    """
    Returns the default Elasticsearch search engine configuration.

    Returns:
        dict: Default configuration for Elasticsearch search engine
    """
    return {
        "module_path": "local_deep_research.web_search_engines.engines.search_engine_elasticsearch",
        "class_name": "ElasticsearchSearchEngine",
        "requires_llm": True,
        "default_params": {
            "hosts": ["http://************:9200"],
            "index_name": "sample_documents",
            "highlight_fields": ["content", "title"],
            "search_fields": ["content", "title"],
        },
        "description": "Search engine for Elasticsearch databases",
        "strengths": "Efficient for searching document collections and structured data",
        "weaknesses": "Requires an Elasticsearch instance and properly indexed data",
        "reliability": "High, depending on your Elasticsearch setup",
    }


def get_default_search_engine_configs():
    """
    Returns a dictionary of default search engine configurations.

    Returns:
        dict: Dictionary of default search engine configurations
    """
    return {
        "elasticsearch": get_default_elasticsearch_config(),
    }
