import importlib
import inspect
from typing import Any, Dict, Optional

from loguru import logger

from .retriever_registry import retriever_registry
from .search_engine_base import BaseSearchEngine
from .search_engines_config import search_config


def create_search_engine(
    engine_name: str,
    llm=None,
    username: str = None,
    settings_snapshot: Dict[str, Any] = None,
    programmatic_mode: bool = False,
    **kwargs,
) -> Optional[BaseSearchEngine]:
    """
    Create a search engine instance based on the engine name.

    Args:
        engine_name: Name of the search engine to create
        llm: Language model instance (required for some engines like meta)
        programmatic_mode: If True, disables database operations and metrics tracking
        **kwargs: Additional parameters to override defaults

    Returns:
        Initialized search engine instance or None if creation failed
    """
    # Debug logging
    logger.info(
        f"create_search_engine called with engine_name={engine_name} (type: {type(engine_name)})"
    )

    # Check if this is a registered retriever first
    retriever = retriever_registry.get(engine_name)
    if retriever:
        logger.info(f"Using registered LangChain retriever: {engine_name}")
        from .engines.search_engine_retriever import RetrieverSearchEngine

        return RetrieverSearchEngine(
            retriever=retriever,
            name=engine_name,
            max_results=kwargs.get("max_results", 10),
        )

    # Extract search engine configs from settings snapshot
    if settings_snapshot:
        config = search_config(
            username=username, settings_snapshot=settings_snapshot
        )

        logger.debug(
            f"Extracted search engines from snapshot: {list(config.keys())}"
        )
    else:
        raise RuntimeError(
            "settings_snapshot is required for search engine creation in threads"
        )

    if engine_name not in config:
        logger.warning(
            f"Search engine '{engine_name}' not found in config, using default"
        )
        # Try to use 'auto' as default if available
        if "auto" in config:
            engine_name = "auto"
        else:
            logger.error(
                f"No default search engine available. Available engines: {list(config.keys())}"
            )
            return None

    # Get engine configuration
    engine_config = config[engine_name]

    # Set default max_results from config if not provided in kwargs
    if "max_results" not in kwargs:
        if settings_snapshot and "search.max_results" in settings_snapshot:
            max_results = (
                settings_snapshot["search.max_results"].get("value", 20)
                if isinstance(settings_snapshot["search.max_results"], dict)
                else settings_snapshot["search.max_results"]
            )
        else:
            max_results = 20
        kwargs["max_results"] = max_results

    # Check for API key requirements
    if engine_config.get("requires_api_key", False):
        # Check the settings snapshot for the API key
        api_key = None
        if settings_snapshot:
            api_key_setting = settings_snapshot.get(
                f"search.engine.web.{engine_name}.api_key"
            )
            if api_key_setting:
                api_key = (
                    api_key_setting.get("value")
                    if isinstance(api_key_setting, dict)
                    else api_key_setting
                )

        # Still try to get from engine config if not found
        if not api_key:
            api_key = engine_config.get("api_key")

        if not api_key:
            logger.info(
                f"Required API key for {engine_name} not found in settings."
            )
            return None

        # Pass the API key in kwargs for engines that need it
        if api_key:
            kwargs["api_key"] = api_key

    # Check for LLM requirements
    if engine_config.get("requires_llm", False) and not llm:
        logger.info(
            f"Engine {engine_name} requires an LLM instance but none was provided"
        )
        return None

    try:
        # Load the engine class
        module_path = engine_config["module_path"]
        class_name = engine_config["class_name"]

        package = None
        if module_path.startswith("."):
            # This is a relative import. Assume it's relative to
            # `web_search_engines`.
            package = "local_deep_research.web_search_engines"
        module = importlib.import_module(module_path, package=package)
        engine_class = getattr(module, class_name)

        # Get the engine class's __init__ parameters to filter out unsupported ones
        engine_init_signature = inspect.signature(engine_class.__init__)
        engine_init_params = list(engine_init_signature.parameters.keys())

        # Combine default parameters with provided ones
        all_params = {**engine_config.get("default_params", {}), **kwargs}

        # Filter out parameters that aren't accepted by the engine class
        # Note: 'self' is always the first parameter of instance methods, so we skip it
        filtered_params = {
            k: v for k, v in all_params.items() if k in engine_init_params[1:]
        }

        # Always pass settings_snapshot if the engine accepts it
        if "settings_snapshot" in engine_init_params[1:] and settings_snapshot:
            filtered_params["settings_snapshot"] = settings_snapshot

        # Pass programmatic_mode if the engine accepts it
        if "programmatic_mode" in engine_init_params[1:]:
            filtered_params["programmatic_mode"] = programmatic_mode

        # Add LLM if required
        if engine_config.get("requires_llm", False):
            filtered_params["llm"] = llm

        # Add API key if required and not already in filtered_params
        if (
            engine_config.get("requires_api_key", False)
            and "api_key" not in filtered_params
        ):
            # Use the api_key we got earlier from settings
            if api_key:
                filtered_params["api_key"] = api_key

        logger.info(
            f"Creating {engine_name} with filtered parameters: {filtered_params.keys()}"
        )

        # Create the engine instance with filtered parameters
        engine = engine_class(**filtered_params)

        # Check if we need to wrap with full search capabilities
        if kwargs.get("use_full_search", False) and engine_config.get(
            "supports_full_search", False
        ):
            return _create_full_search_wrapper(
                engine_name, engine, llm, kwargs, username, settings_snapshot
            )

        return engine

    except Exception:
        logger.exception(f"Failed to create search engine '{engine_name}'")
        return None


def _create_full_search_wrapper(
    engine_name: str,
    base_engine: BaseSearchEngine,
    llm,
    params: Dict[str, Any],
    username: str = None,
    settings_snapshot: Dict[str, Any] = None,
) -> Optional[BaseSearchEngine]:
    """Create a full search wrapper for the base engine if supported"""
    try:
        # Extract search engine config from settings snapshot
        if settings_snapshot:
            config = {}

            # Extract web search engines
            web_engines = {}
            for key, value in settings_snapshot.items():
                if key.startswith("search.engine.web."):
                    # Extract engine name from key like "search.engine.web.searxng.class_name"
                    parts = key.split(".")
                    if len(parts) >= 4:
                        engine_name_from_key = parts[3]
                        if engine_name_from_key not in web_engines:
                            web_engines[engine_name_from_key] = {}
                        # Store the config value
                        remaining_key = (
                            ".".join(parts[4:]) if len(parts) > 4 else ""
                        )
                        if remaining_key:
                            web_engines[engine_name_from_key][remaining_key] = (
                                value.get("value")
                                if isinstance(value, dict)
                                else value
                            )

            config.update(web_engines)
        else:
            # Fallback to search_config if no snapshot (not recommended for threads)
            config = search_config(
                username=username, settings_snapshot=settings_snapshot
            )

        if engine_name not in config:
            logger.warning(f"Engine config for {engine_name} not found")
            return base_engine

        engine_config = config[engine_name]

        # Get full search class details
        module_path = engine_config.get("full_search_module")
        class_name = engine_config.get("full_search_class")

        if not module_path or not class_name:
            logger.warning(
                f"Full search configuration missing for {engine_name}"
            )
            return base_engine

        # Import the full search class
        module = importlib.import_module(module_path)
        full_search_class = getattr(module, class_name)

        # Get the wrapper's __init__ parameters to filter out unsupported ones
        wrapper_init_signature = inspect.signature(full_search_class.__init__)
        wrapper_init_params = list(wrapper_init_signature.parameters.keys())[
            1:
        ]  # Skip 'self'

        # Extract relevant parameters for the full search wrapper
        wrapper_params = {
            k: v for k, v in params.items() if k in wrapper_init_params
        }

        # Special case for SerpAPI which needs the API key directly
        if (
            engine_name == "serpapi"
            and "serpapi_api_key" in wrapper_init_params
        ):
            # Check settings snapshot for API key
            serpapi_api_key = None
            if settings_snapshot:
                serpapi_setting = settings_snapshot.get(
                    "search.engine.web.serpapi.api_key"
                )
                if serpapi_setting:
                    serpapi_api_key = (
                        serpapi_setting.get("value")
                        if isinstance(serpapi_setting, dict)
                        else serpapi_setting
                    )
            if serpapi_api_key:
                wrapper_params["serpapi_api_key"] = serpapi_api_key

            # Map some parameter names to what the wrapper expects
            if (
                "language" in params
                and "search_language" not in params
                and "language" in wrapper_init_params
            ):
                wrapper_params["language"] = params["language"]

            if (
                "safesearch" not in wrapper_params
                and "safe_search" in params
                and "safesearch" in wrapper_init_params
            ):
                wrapper_params["safesearch"] = (
                    "active" if params["safe_search"] else "off"
                )

        # Special case for Brave which needs the API key directly
        if engine_name == "brave" and "api_key" in wrapper_init_params:
            # Check settings snapshot for API key
            brave_api_key = None
            if settings_snapshot:
                brave_setting = settings_snapshot.get(
                    "search.engine.web.brave.api_key"
                )
                if brave_setting:
                    brave_api_key = (
                        brave_setting.get("value")
                        if isinstance(brave_setting, dict)
                        else brave_setting
                    )

            if brave_api_key:
                wrapper_params["api_key"] = brave_api_key

            # Map some parameter names to what the wrapper expects
            if (
                "language" in params
                and "search_language" not in params
                and "language" in wrapper_init_params
            ):
                wrapper_params["language"] = params["language"]

            if (
                "safesearch" not in wrapper_params
                and "safe_search" in params
                and "safesearch" in wrapper_init_params
            ):
                wrapper_params["safesearch"] = (
                    "moderate" if params["safe_search"] else "off"
                )

        # Always include llm if it's a parameter
        if "llm" in wrapper_init_params:
            wrapper_params["llm"] = llm

        # If the wrapper needs the base engine and has a parameter for it
        if "web_search" in wrapper_init_params:
            wrapper_params["web_search"] = base_engine

        logger.debug(
            f"Creating full search wrapper for {engine_name} with filtered parameters: {wrapper_params.keys()}"
        )

        # Create the full search wrapper with filtered parameters
        full_search = full_search_class(**wrapper_params)

        return full_search

    except Exception:
        logger.exception(
            f"Failed to create full search wrapper for {engine_name}"
        )
        return base_engine


def get_search(
    search_tool: str,
    llm_instance,
    max_results: int = 10,
    region: str = "us",
    time_period: str = "y",
    safe_search: bool = True,
    search_snippets_only: bool = False,
    search_language: str = "English",
    max_filtered_results: Optional[int] = None,
    settings_snapshot: Dict[str, Any] = None,
    programmatic_mode: bool = False,
):
    """
    Get search tool instance based on the provided parameters.

    Args:
        search_tool: Name of the search engine to use
        llm_instance: Language model instance
        max_results: Maximum number of search results
        region: Search region/locale
        time_period: Time period for search results
        safe_search: Whether to enable safe search
        search_snippets_only: Whether to return just snippets (vs. full content)
        search_language: Language for search results
        max_filtered_results: Maximum number of results to keep after filtering
        programmatic_mode: If True, disables database operations and metrics tracking

    Returns:
        Initialized search engine instance
    """
    # Common parameters
    params = {
        "max_results": max_results,
        "llm": llm_instance,  # Only used by engines that need it
    }

    # Add max_filtered_results if provided
    if max_filtered_results is not None:
        params["max_filtered_results"] = max_filtered_results

    # Add engine-specific parameters
    if search_tool in ["duckduckgo", "serpapi", "google_pse", "brave"]:
        params.update(
            {
                "region": region,
                "safe_search": safe_search,
                "use_full_search": not search_snippets_only,
            }
        )

    if search_tool in ["serpapi", "brave", "google_pse"]:
        params["search_language"] = search_language

    if search_tool == "serpapi":
        params["time_period"] = time_period

    # Create and return the search engine
    logger.info(
        f"Creating search engine for tool: {search_tool} (type: {type(search_tool)}) with params: {params.keys()}"
    )
    logger.info(
        f"About to call create_search_engine with search_tool={search_tool}, settings_snapshot type={type(settings_snapshot)}"
    )
    logger.info(f"Params being passed to create_search_engine: {params}")

    engine = create_search_engine(
        search_tool,
        settings_snapshot=settings_snapshot,
        programmatic_mode=programmatic_mode,
        **params,
    )

    # Add debugging to check if engine is None
    if engine is None:
        logger.error(
            f"Failed to create search engine for {search_tool} - returned None"
        )
    else:
        engine_type = type(engine).__name__
        logger.info(
            f"Successfully created search engine of type: {engine_type}"
        )
        # Check if the engine has run method
        if hasattr(engine, "run"):
            logger.info(f"Engine has 'run' method: {engine.run}")
        else:
            logger.error("Engine does NOT have 'run' method!")

        # For SearxNG, check availability flag
        if hasattr(engine, "is_available"):
            logger.info(f"Engine availability flag: {engine.is_available}")

    return engine
