"""Factory for creating report storage instances."""

from typing import Optional
from sqlalchemy.orm import Session
from loguru import logger

from .base import ReportStorage
from .database_with_file_backup import DatabaseWithFileBackupStorage
from ..config.thread_settings import (
    get_setting_from_snapshot,
    NoSettingsContextError,
)


def get_report_storage(
    session: Optional[Session] = None,
    settings_snapshot: Optional[dict] = None,
    enable_file_backup: Optional[bool] = None,
) -> ReportStorage:
    """Get a report storage instance that always uses database with optional file backup.

    Args:
        session: Database session (required)
        settings_snapshot: Settings snapshot for thread-safe access
        enable_file_backup: Whether to enable file backup. If None, uses setting.

    Returns:
        ReportStorage instance (DatabaseWithFileBackupStorage)

    Raises:
        ValueError: If database session is not provided
    """
    if session is None:
        raise ValueError("Database session is required for report storage")

    # Determine if file backup should be enabled
    if enable_file_backup is None:
        try:
            enable_file_backup = get_setting_from_snapshot(
                "report.enable_file_backup",
                settings_snapshot=settings_snapshot,
            )
        except NoSettingsContextError:
            # Fall back to default if no settings context
            enable_file_backup = False

    logger.info(
        f"Report storage: Database (primary) with file backup {'enabled' if enable_file_backup else 'disabled'}"
    )

    return DatabaseWithFileBackupStorage(
        session=session, enable_file_storage=enable_file_backup
    )


# Global singleton for request context
_request_storage: Optional[ReportStorage] = None


def get_request_report_storage() -> Optional[ReportStorage]:
    """Get the report storage instance for the current request context."""
    return _request_storage


def set_request_report_storage(storage: ReportStorage) -> None:
    """Set the report storage instance for the current request context."""
    global _request_storage
    _request_storage = storage


def clear_request_report_storage() -> None:
    """Clear the report storage instance for the current request context."""
    global _request_storage
    _request_storage = None
