"""Base interface for report storage backends."""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from pathlib import Path


class ReportStorage(ABC):
    """Abstract base class for report storage backends."""

    @abstractmethod
    def save_report(
        self,
        research_id: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        username: Optional[str] = None,
    ) -> bool:
        """Save a research report.

        Args:
            research_id: Unique identifier for the research
            content: Report content (markdown)
            metadata: Optional metadata to store with the report
            username: Optional username for user-specific storage

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    def get_report(
        self, research_id: str, username: Optional[str] = None
    ) -> Optional[str]:
        """Retrieve a research report.

        Args:
            research_id: Unique identifier for the research
            username: Optional username for user-specific storage

        Returns:
            Report content if found, None otherwise
        """
        pass

    @abstractmethod
    def get_report_with_metadata(
        self, research_id: str, username: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Retrieve a research report with its metadata.

        Args:
            research_id: Unique identifier for the research
            username: Optional username for user-specific storage

        Returns:
            Dictionary with 'content' and 'metadata' keys if found, None otherwise
        """
        pass

    @abstractmethod
    def delete_report(
        self, research_id: str, username: Optional[str] = None
    ) -> bool:
        """Delete a research report.

        Args:
            research_id: Unique identifier for the research
            username: Optional username for user-specific storage

        Returns:
            True if successful, False otherwise
        """
        pass

    @abstractmethod
    def report_exists(
        self, research_id: str, username: Optional[str] = None
    ) -> bool:
        """Check if a report exists.

        Args:
            research_id: Unique identifier for the research
            username: Optional username for user-specific storage

        Returns:
            True if report exists, False otherwise
        """
        pass

    def get_report_as_temp_file(
        self, research_id: str, username: Optional[str] = None
    ) -> Optional[Path]:
        """Get report content as a temporary file.

        This is useful for export operations that need a file path.

        Args:
            research_id: Unique identifier for the research
            username: Optional username for user-specific storage

        Returns:
            Path to temporary file if successful, None otherwise
        """
        import tempfile

        content = self.get_report(research_id, username)
        if not content:
            return None

        try:
            with tempfile.NamedTemporaryFile(
                mode="w", suffix=".md", delete=False, encoding="utf-8"
            ) as tmp_file:
                tmp_file.write(content)
                return Path(tmp_file.name)
        except Exception:
            return None
