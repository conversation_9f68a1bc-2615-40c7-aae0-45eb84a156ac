"""Storage that always uses database as primary storage with optional file backup."""

from typing import Dict, Any, Optional
from pathlib import Path
from loguru import logger
from sqlalchemy.orm import Session

from .base import ReportStorage
from .database import DatabaseReportStorage
from .file import FileReportStorage


class DatabaseWithFileBackupStorage(ReportStorage):
    """
    Storage that always saves to database and optionally backs up to file system.

    Database is the primary storage and is always used.
    File storage is optional for external access/backup purposes.
    """

    def __init__(self, session: Session, enable_file_storage: bool = False):
        """
        Initialize combined storage.

        Args:
            session: SQLAlchemy database session
            enable_file_storage: Whether to also save reports to file system
        """
        self.db_storage = DatabaseReportStorage(session)
        self.file_storage = FileReportStorage() if enable_file_storage else None
        self.enable_file_storage = enable_file_storage

    def save_report(
        self,
        research_id: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        username: Optional[str] = None,
    ) -> bool:
        """Save report to database and optionally to file."""
        # Always save to database first (primary storage)
        success = self.db_storage.save_report(
            research_id, content, metadata, username
        )

        if not success:
            logger.error(f"Failed to save report {research_id} to database")
            return False

        # Optionally save to file system
        if self.file_storage:
            try:
                file_success = self.file_storage.save_report(
                    research_id, content, metadata, username
                )
                if not file_success:
                    logger.warning(
                        f"Failed to save report {research_id} to file, "
                        "but database save was successful"
                    )
            except Exception as e:
                logger.warning(
                    f"Error saving report {research_id} to file: {e}, "
                    "but database save was successful"
                )

        return success

    def get_report(
        self, research_id: str, username: Optional[str] = None
    ) -> Optional[str]:
        """Get report from database (never from file)."""
        # Always read from database for consistency
        return self.db_storage.get_report(research_id, username)

    def delete_report(
        self, research_id: str, username: Optional[str] = None
    ) -> bool:
        """Delete report from database and file if it exists."""
        # Delete from database
        db_success = self.db_storage.delete_report(research_id, username)

        # Also delete from file if enabled
        if self.file_storage and db_success:
            try:
                self.file_storage.delete_report(research_id, username)
            except Exception as e:
                logger.warning(f"Error deleting report file {research_id}: {e}")

        return db_success

    def list_reports(self, username: Optional[str] = None) -> list:
        """List reports from database only."""
        # Always list from database for consistency
        return self.db_storage.list_reports(username)

    def get_report_as_temp_file(
        self, research_id: str, username: Optional[str] = None
    ) -> Optional[Path]:
        """Get report as temporary file from database."""
        # Always use database implementation
        return self.db_storage.get_report_as_temp_file(research_id, username)

    def get_report_with_metadata(
        self, research_id: str, username: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Get report with metadata from database."""
        # Always read from database for consistency
        return self.db_storage.get_report_with_metadata(research_id, username)

    def report_exists(
        self, research_id: str, username: Optional[str] = None
    ) -> bool:
        """Check if report exists in database."""
        # Always check database for consistency
        return self.db_storage.report_exists(research_id, username)
