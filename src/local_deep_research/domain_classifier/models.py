"""Database models for domain classification."""

from sqlalchemy import Column, String, Text, Float, Integer, Index
from sqlalchemy_utc import UtcDateTime, utcnow
from ..database.models import Base


class DomainClassification(Base):
    """Store domain classifications generated by LLM."""

    __tablename__ = "domain_classifications"

    id = Column(Integer, primary_key=True, autoincrement=True)
    domain = Column(String(255), unique=True, nullable=False, index=True)
    category = Column(String(100), nullable=False)
    subcategory = Column(String(100))
    confidence = Column(Float, default=0.0)
    reasoning = Column(Text)  # Store LLM's reasoning for the classification
    sample_titles = Column(Text)  # JSON array of sample titles from this domain
    sample_count = Column(
        Integer, default=0
    )  # Number of resources used for classification
    created_at = Column(UtcDateTime, default=utcnow())
    updated_at = Column(UtcDateTime, default=utcnow(), onupdate=utcnow())

    # Create index for faster lookups
    __table_args__ = (Index("idx_domain_category", "domain", "category"),)

    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "domain": self.domain,
            "category": self.category,
            "subcategory": self.subcategory,
            "confidence": self.confidence,
            "reasoning": self.reasoning,
            "sample_titles": self.sample_titles,
            "sample_count": self.sample_count,
            "created_at": self.created_at.isoformat()
            if self.created_at
            else None,
            "updated_at": self.updated_at.isoformat()
            if self.updated_at
            else None,
        }
