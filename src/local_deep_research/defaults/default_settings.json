{"app.debug": {"category": "app_interface", "description": "Enable debug mode for the web application", "editable": true, "max_value": null, "min_value": null, "name": "Debug Mode", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": true, "visible": true}, "app.lock_settings": {"category": "app_interface", "description": "If true, disables editing for all settings", "editable": false, "max_value": null, "min_value": null, "name": "Lock Settings", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": false, "visible": false}, "app.enable_notifications": {"category": "app_interface", "description": "Enable browser notifications for research events", "editable": true, "max_value": null, "min_value": null, "name": "Enable Notifications", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": true, "visible": true}, "app.enable_web": {"category": "app_interface", "description": "Enable the web server", "editable": true, "max_value": null, "min_value": null, "name": "Enable Web Server", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": true, "visible": true}, "app.enable_file_logging": {"category": "app_interface", "description": "Enable logging to files (WARNING: Log files are unencrypted and may contain sensitive data)", "editable": true, "max_value": null, "min_value": null, "name": "Enable File Logging", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": false, "visible": true}, "app.max_concurrent_researches": {"category": "app_interface", "description": "Maximum number of concurrent research processes allowed per user", "editable": true, "env_var": "LDR_MAX_CONCURRENT", "max_value": 10, "min_value": 1, "name": "<PERSON>", "options": null, "step": 1, "type": "APP", "ui_element": "number", "value": 3, "visible": true}, "app.queue_mode": {"category": "app_interface", "description": "Queue processing mode: 'direct' for immediate execution, 'queue' for background processing", "editable": true, "env_var": "LDR_QUEUE_MODE", "max_value": null, "min_value": null, "name": "Queue Mode", "options": [{"label": "Direct (Immediate)", "value": "direct"}, {"label": "<PERSON><PERSON> (Background)", "value": "queue"}], "step": null, "type": "APP", "ui_element": "select", "value": "direct", "visible": true}, "app.host": {"category": "app_interface", "description": "Host address to bind the web server", "editable": true, "max_value": null, "min_value": null, "name": "Web Host", "options": null, "step": null, "type": "APP", "ui_element": "text", "value": "0.0.0.0", "visible": true}, "app.port": {"category": "app_interface", "description": "Port for the web server", "editable": true, "max_value": 65535.0, "min_value": 1.0, "name": "Web Port", "options": null, "step": null, "type": "APP", "ui_element": "number", "value": 5000, "visible": true}, "app.theme": {"category": "app_interface", "description": "User interface theme", "editable": true, "max_value": null, "min_value": null, "name": "UI Theme", "options": [{"label": "Dark", "value": "dark"}, {"label": "Light", "value": "light"}, {"label": "System Default", "value": "system"}], "step": null, "type": "APP", "ui_element": "select", "value": "dark", "visible": true}, "app.web_interface": {"category": "app_interface", "description": "Enable the web interface", "editable": true, "max_value": null, "min_value": null, "name": "Web Interface", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": true, "visible": true}, "llm.llamacpp_f16_kv": {"category": null, "description": "Setting for llm.llamacpp_f16_kv", "editable": true, "max_value": null, "min_value": null, "name": "Llamacpp F16 Kv", "options": null, "step": null, "type": "LLM", "ui_element": "checkbox", "value": true, "visible": true}, "llm.llamacpp_model_path": {"category": null, "description": "Setting for llm.llamacpp_model_path", "editable": true, "max_value": null, "min_value": null, "name": "Llamacpp Model Path", "options": null, "step": null, "type": "LLM", "ui_element": "text", "value": "", "visible": true}, "llm.llamacpp_n_batch": {"category": null, "description": "Setting for llm.llamacpp_n_batch", "editable": true, "max_value": null, "min_value": 1, "name": "Llamacpp N Batch", "options": null, "step": null, "type": "LLM", "ui_element": "number", "value": 512, "visible": true}, "llm.llamacpp_n_gpu_layers": {"category": null, "description": "Setting for llm.llamacpp_n_gpu_layers", "editable": true, "max_value": null, "min_value": 0, "name": "Llamacpp N Gpu Layers", "options": null, "step": null, "type": "LLM", "ui_element": "number", "value": 1, "visible": true}, "llm.lmstudio.url": {"category": null, "description": "URL of the LMStudio endpoint.", "editable": true, "max_value": null, "min_value": null, "name": "Lmstudio Url", "options": null, "step": null, "type": "LLM", "ui_element": "text", "value": "http://localhost:1234", "visible": true}, "llm.max_tokens": {"category": "llm_parameters", "description": "Maximum number of tokens in model responses", "editable": true, "max_value": 100000, "min_value": 100.0, "name": "<PERSON>", "options": null, "step": null, "type": "LLM", "ui_element": "number", "value": 30000, "visible": true}, "llm.context_window_unrestricted": {"category": "llm_parameters", "description": "Let cloud providers automatically handle context sizing (recommended). Uncheck to set a specific limit.", "editable": true, "max_value": null, "min_value": null, "name": "Use Unrestricted Cloud Context Window", "options": null, "step": null, "type": "LLM", "ui_element": "checkbox", "value": true, "visible": true}, "llm.context_window_size": {"category": "llm_parameters", "description": "Maximum context window size in tokens for cloud LLMs. Only used when unrestricted context is disabled.", "editable": true, "max_value": 20000000.0, "min_value": 512.0, "name": "Cloud Provider Context Window Limit", "options": null, "step": null, "type": "LLM", "ui_element": "number", "value": 128000, "visible": true}, "llm.local_context_window_size": {"category": "llm_parameters", "description": "Context window size in tokens for local LLMs (Ollama, LlamaCpp). Smaller values prevent memory issues.", "editable": true, "max_value": 131072.0, "min_value": 512.0, "name": "Local Provider Context Window Size", "options": null, "step": 512.0, "type": "LLM", "ui_element": "number", "value": 4096, "visible": true}, "llm.supports_max_tokens": {"category": "llm_parameters", "description": "Whether the LLM API supports the 'max_tokens' option.", "editable": true, "max_value": null, "min_value": null, "name": "Supports '<PERSON>'", "options": null, "step": null, "type": "LLM", "ui_element": "checkbox", "value": true, "visible": true}, "llm.model": {"category": "llm_general", "description": "Language model to use for research and analysis", "editable": true, "max_value": null, "min_value": null, "name": "LLM Model", "options": [{"label": "GPT-4o (OpenAI)", "value": "gpt-4o"}, {"label": "GPT-3.5 Turbo (OpenAI)", "value": "gpt-3.5-turbo"}, {"label": "<PERSON> 3.5 <PERSON><PERSON> (Anthropic)", "value": "claude-3-5-sonnet-latest"}, {"label": "Claude 3 Opus (Anthropic)", "value": "claude-3-opus-20240229"}, {"label": "Llama 3 (Meta)", "value": "llama3"}, {"label": "<PERSON><PERSON><PERSON> (Mistral AI)", "value": "mistral"}, {"label": "<PERSON><PERSON> (Mistral AI)", "value": "mixtral"}], "step": null, "type": "LLM", "ui_element": "select", "value": "gemma3:12b", "visible": true}, "llm.openai_endpoint.url": {"category": null, "description": "URL of the OpenAI-compatible endpoint.", "editable": true, "max_value": null, "min_value": null, "name": "Openai Endpoint Url", "options": null, "step": null, "type": "LLM", "ui_element": "text", "value": "https://openrouter.ai/api/v1", "visible": true}, "llm.ollama.url": {"category": null, "description": "URL of the Ollama endpoint.", "editable": true, "max_value": null, "min_value": null, "name": "Ollama Endpoint Url", "options": null, "step": null, "type": "LLM", "ui_element": "text", "value": "http://localhost:11434", "visible": true}, "llm.provider": {"category": "llm_general", "description": "Service provider for the language model", "editable": true, "max_value": null, "min_value": null, "name": "LLM Provider", "options": [{"label": "OpenAI API", "value": "openai"}, {"label": "Anthropic API", "value": "anthropic"}, {"label": "<PERSON><PERSON><PERSON> (Local)", "value": "ollama"}, {"label": "LM Studio (Local)", "value": "lmstudio"}, {"label": "vLLM (Local)", "value": "vllm"}, {"label": "Custom OpenAI-compatible API", "value": "openai_endpoint"}], "step": null, "type": "LLM", "ui_element": "select", "value": "OLLAMA", "visible": true}, "llm.temperature": {"category": "llm_parameters", "description": "Controls randomness in model outputs (0.0 - 1.0)", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Temperature", "options": null, "step": 0.05, "type": "LLM", "ui_element": "range", "value": 0.7, "visible": true}, "llm.anthropic.api_key": {"category": "llm_general", "description": "API key to use for the Anthropic provider.", "editable": true, "max_value": null, "min_value": null, "name": "Anthropic API Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "ANTHROPIC_API_KEY", "visible": true}, "llm.openai.api_key": {"category": "llm_general", "description": "API key to use for the OpenAI provider.", "editable": true, "max_value": null, "min_value": null, "name": "OpenAI API Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "OPENAI_API_KEY", "visible": true}, "llm.openai_endpoint.api_key": {"category": "llm_general", "description": "API key to use for the OpenAI-compatible endpoint provider.", "editable": true, "max_value": null, "min_value": null, "name": "OpenAI Endpoint API Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "OPENAI_ENDPOINT_API_KEY", "visible": true}, "report.detailed_citations": {"category": "report_parameters", "description": "Include detailed citations in reports", "editable": true, "max_value": null, "min_value": null, "name": "Detailed Citations", "options": null, "step": null, "type": "REPORT", "ui_element": "checkbox", "value": true, "visible": true}, "report.enable_fact_checking": {"category": "report_parameters", "description": "Enable fact checking for report contents", "editable": true, "max_value": null, "min_value": null, "name": "Enable Fact Checking", "options": null, "step": null, "type": "REPORT", "ui_element": "checkbox", "value": true, "visible": true}, "report.searches_per_section": {"category": "report_parameters", "description": "Number of searches to run per report section", "editable": true, "max_value": 5.0, "min_value": 1.0, "name": "Searches Per Section", "options": null, "step": null, "type": "REPORT", "ui_element": "number", "value": 2, "visible": true}, "report.citation_format": {"category": "report_parameters", "description": "Citation format style for reports", "editable": true, "max_value": null, "min_value": null, "name": "Citation Format", "options": [{"value": "number_hyperlinks", "label": "Numbers with hyperlinks [1]"}, {"value": "domain_hyperlinks", "label": "Domain names with hyperlinks [arxiv.org]"}, {"value": "domain_id_hyperlinks", "label": "Domain with smart numbering [arxiv.org] or [arxiv.org-1]"}, {"value": "domain_id_always_hyperlinks", "label": "Domain with consistent numbering [arxiv.org-1]"}, {"value": "no_hyperlinks", "label": "Numbers without hyperlinks [1]"}], "step": null, "type": "REPORT", "ui_element": "select", "value": "number_hyperlinks", "visible": true}, "report.export_formats": {"category": "report_parameters", "description": "Available export formats for reports", "editable": true, "max_value": null, "min_value": null, "name": "Export Formats", "options": [{"value": "markdown", "label": "Markdown (.md)"}, {"value": "latex", "label": "LaTeX (.tex)"}, {"value": "quarto", "label": "Quarto (.qmd)"}], "step": null, "type": "REPORT", "ui_element": "multiselect", "value": ["markdown", "latex", "quarto", "ris"], "visible": true}, "report.enable_file_backup": {"category": "report_parameters", "description": "Also save reports to file system for external access (reports are always stored in database)", "editable": true, "max_value": null, "min_value": null, "name": "Enable File Backup", "options": null, "step": null, "type": "REPORT", "ui_element": "checkbox", "value": false, "visible": true}, "search.iterations": {"category": null, "description": "Setting for search.iterations", "editable": true, "max_value": null, "min_value": 1, "name": "Search Iterations", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 1, "visible": true}, "search.max_filtered_results": {"category": "search_parameters", "description": "Maximum number of search results to display after relevance filtering", "editable": true, "max_value": 100, "min_value": 1, "name": "<PERSON> Filtered Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 20, "visible": true}, "search.max_results": {"category": "search_parameters", "description": "Maximum number of search results to retrieve", "editable": true, "max_value": 50.0, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 50, "visible": true}, "search.final_max_results": {"category": "search_parameters", "description": "Maximum number of search results to include in the final report.", "editable": true, "max_value": 200.0, "min_value": 1, "name": "Final Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 100, "visible": true}, "search.quality_check_urls": {"category": null, "description": "Setting for search.quality_check_urls", "editable": true, "max_value": null, "min_value": null, "name": "Quality Check Urls", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.questions_per_iteration": {"category": "search_parameters", "description": "Number of questions to generate per research cycle", "editable": true, "max_value": 10.0, "min_value": 1.0, "name": "Questions Per Iteration", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 3, "visible": true}, "search.search_strategy": {"category": "search_parameters", "description": "Search strategy to use for research", "editable": true, "max_value": null, "min_value": null, "name": "Search Strategy", "options": ["source-based", "focused-iteration"], "step": null, "type": "SEARCH", "ui_element": "select", "value": "source-based", "visible": true}, "search.region": {"category": "search_parameters", "description": "Geographic region for search results", "editable": true, "max_value": null, "min_value": null, "name": "Search Region", "options": [{"label": "United States", "value": "us"}, {"label": "United Kingdom", "value": "uk"}, {"label": "France", "value": "fr"}, {"label": "Germany", "value": "de"}, {"label": "Japan", "value": "jp"}, {"label": "No Region (Worldwide)", "value": "wt-wt"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "us", "visible": true}, "search.safe_search": {"category": "search_parameters", "description": "Enable safe search filtering", "editable": true, "max_value": null, "min_value": null, "name": "Safe Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.search_language": {"category": "search_parameters", "description": "Language for search results", "editable": true, "max_value": null, "min_value": null, "name": "Search Language", "options": [{"label": "English", "value": "English"}, {"label": "French", "value": "French"}, {"label": "German", "value": "German"}, {"label": "Spanish", "value": "Spanish"}, {"label": "Italian", "value": "Italian"}, {"label": "Japanese", "value": "Japanese"}, {"label": "Chinese", "value": "Chinese"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "English", "visible": true}, "search.searches_per_section": {"category": "search_parameters", "description": "Number of searches to run per report section", "editable": true, "max_value": 5.0, "min_value": 1.0, "name": "Searches Per Section", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 2, "visible": true}, "search.skip_relevance_filter": {"category": "search_parameters", "description": "Skip filtering search results for relevance", "editable": true, "max_value": null, "min_value": null, "name": "Skip Relevance Filter", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.journal_reputation.threshold": {"category": "journal_quality_filter_parameters", "description": "If enabled, journals with quality scores (scale from 1-10) below this threshold will be filtered out.", "editable": true, "max_value": 10, "min_value": 1, "name": "Journal Quality Threshold", "options": null, "step": 1, "type": "SEARCH", "ui_element": "range", "value": 4, "visible": true}, "search.journal_reputation.max_context": {"category": "journal_quality_filter_parameters", "description": "Maximum number of characters to include in the prompt for journal quality checking.", "editable": true, "max_value": 1000000, "min_value": 500, "name": "Journal Quality Context Size", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 3000, "visible": true}, "search.journal_reputation.exclude_non_published": {"category": "journal_quality_filter_parameters", "description": "If true, quality filtering will exclude results that do not have a published journal reference.", "editable": true, "max_value": null, "min_value": null, "name": "Exclude Non-Published Results", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.journal_reputation.reanalysis_period": {"category": "journal_quality_filter_parameters", "description": "Period at which to re-check the quality of journals.", "editable": true, "max_value": null, "min_value": null, "name": "Quality Reanalysis Period", "options": [{"label": "Yearly", "value": "365"}, {"label": "Every 6 Months", "value": "182"}, {"label": "Every Month", "value": "30"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "265", "visible": true}, "search.snippets_only": {"category": "search_parameters", "description": "Only retrieve snippets instead of full search results", "editable": true, "max_value": null, "min_value": null, "name": "Snippets Only", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.time_period": {"category": "search_parameters", "description": "Time period for search results", "editable": true, "max_value": null, "min_value": null, "name": "Time Period", "options": [{"label": "Past 24 hours", "value": "d"}, {"label": "Past week", "value": "w"}, {"label": "Past month", "value": "m"}, {"label": "Past year", "value": "y"}, {"label": "All time", "value": "all"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "y", "visible": true}, "search.tool": {"category": "search_general", "description": "Web search engine to use for research", "editable": true, "max_value": null, "min_value": null, "name": "Search Engine", "options": [{"label": "Auto (Default)", "value": "auto"}, {"label": "Arxiv", "value": "arxiv"}, {"label": "Wikipedia", "value": "wikipedia"}, {"label": "Pubmed", "value": "pubmed"}, {"label": "<PERSON><PERSON><PERSON>", "value": "github"}, {"label": "SerpAPI (Google)", "value": "serp<PERSON>i"}, {"label": "SearXNG (Self-hosted)", "value": "searxng"}, {"label": "Google Programmable Search Engine", "value": "google_pse"}, {"label": "DuckDuckGo", "value": "duckduck<PERSON>"}, {"label": "Brave", "value": "brave"}, {"label": "Wayback", "value": "wayback"}, {"label": "Local All", "value": "local_all"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "searxng", "visible": true}, "search.engine.web.arxiv.display_name": {"category": "arxiv", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "ArXiv", "visible": false}, "search.engine.web.arxiv.description": {"category": "arxiv", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search papers uploaded to ArXiv.", "visible": false}, "search.engine.web.arxiv.class_name": {"category": "arxiv", "description": "Setting for arxiv.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "ArXivSearchEngine", "visible": true}, "search.engine.web.arxiv.journal_reputation.enabled": {"category": "arxiv", "description": "Enable journal quality filtering for this search engine.", "editable": true, "max_value": null, "min_value": null, "name": "Filter Low-Quality Journals", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.arxiv.default_params.max_results": {"category": "arxiv", "description": "Setting for arxiv.default_params.max_results", "editable": true, "max_value": null, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 20, "visible": true}, "search.engine.web.arxiv.default_params.sort_by": {"category": "arxiv", "description": "Setting for arxiv.default_params.sort_by", "editable": true, "max_value": null, "min_value": null, "name": "Sort By", "options": [{"label": "Relevance", "value": "relevance"}, {"label": "Last Updated Date", "value": "lastUpdatedDate"}, {"label": "Submitted Date", "value": "submittedDate"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "relevance", "visible": true}, "search.engine.web.arxiv.default_params.sort_order": {"category": "arxiv", "description": "Setting for arxiv.default_params.sort_order", "editable": true, "max_value": null, "min_value": null, "name": "Sort Order", "options": [{"label": "Ascending", "value": "ascending"}, {"label": "Descending", "value": "descending"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "descending", "visible": true}, "search.engine.web.arxiv.module_path": {"category": "arxiv", "description": "Setting for arxiv.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_arxiv", "visible": true}, "search.engine.web.arxiv.reliability": {"category": "arxiv", "description": "Setting for arxiv.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.9, "visible": true}, "search.engine.web.arxiv.requires_api_key": {"category": "arxiv", "description": "Setting for arxiv.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.arxiv.strengths": {"category": "arxiv", "description": "Setting for arxiv.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["scientific papers", "academic research", "physics", "computer science", "mathematics", "statistics", "machine learning", "preprints"], "visible": true}, "search.engine.web.arxiv.weaknesses": {"category": "arxiv", "description": "Setting for arxiv.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["non-academic topics", "consumer products", "news", "general information"], "visible": true}, "search.engine.auto.display_name": {"category": "auto", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Auto", "visible": false}, "search.engine.auto.description": {"category": "auto", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Attempt to choose the best combination of search engines automatically.", "visible": false}, "search.engine.auto.class_name": {"category": "auto", "description": "Setting for auto.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "MetaSearchEngine", "visible": true}, "search.engine.auto.default_params.max_engines_to_try": {"category": "auto", "description": "Setting for auto.default_params.max_engines_to_try", "editable": true, "max_value": null, "min_value": 1, "name": "Max Engines To Try", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 3, "visible": true}, "search.engine.auto.default_params.use_api_key_services": {"category": "auto", "description": "Setting for auto.default_params.use_api_key_services", "editable": true, "max_value": null, "min_value": null, "name": "Use Api Key Services", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.auto.module_path": {"category": "auto", "description": "Setting for auto.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.meta_search_engine", "visible": true}, "search.engine.auto.reliability": {"category": "auto", "description": "Setting for auto.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.85, "visible": true}, "search.engine.auto.requires_api_key": {"category": "auto", "description": "Setting for auto.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.auto.requires_llm": {"category": "auto", "description": "Setting for auto.requires_llm", "editable": true, "max_value": null, "min_value": null, "name": "Requires Llm", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.auto.strengths": {"category": "auto", "description": "Setting for auto.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["intelligent engine selection", "adaptable to query type", "fallback capabilities"], "visible": true}, "search.engine.auto.weaknesses": {"category": "auto", "description": "Setting for auto.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["slightly slower due to LLM analysis"], "visible": true}, "search.engine.web.brave.display_name": {"category": "brave", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Brave", "visible": false}, "search.engine.web.brave.description": {"category": "brave", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search the web using the Brave search engine.", "visible": false}, "search.engine.web.brave.api_key": {"category": "brave", "description": "The Brave API key to use.", "editable": true, "max_value": null, "min_value": null, "name": "Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "BRAVE_API_KEY", "visible": true}, "search.engine.web.brave.class_name": {"category": "brave", "description": "Setting for brave.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "BraveSearchEngine", "visible": true}, "search.engine.web.brave.default_params.region": {"category": "brave", "description": "Setting for brave.default_params.region", "editable": true, "max_value": null, "min_value": null, "name": "Region", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "US", "visible": true}, "search.engine.web.brave.default_params.safe_search": {"category": "brave", "description": "Setting for brave.default_params.safe_search", "editable": true, "max_value": null, "min_value": null, "name": "Safe Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.brave.default_params.search_language": {"category": "brave", "description": "Setting for brave.default_params.search_language", "editable": true, "max_value": null, "min_value": null, "name": "Search Language", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "English", "visible": true}, "search.engine.web.brave.default_params.time_period": {"category": "brave", "description": "Setting for brave.default_params.time_period", "editable": true, "max_value": null, "min_value": null, "name": "Time Period", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "y", "visible": true}, "search.engine.web.brave.full_search_class": {"category": "brave", "description": "Setting for brave.full_search_class", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Class", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "FullSearchResults", "visible": true}, "search.engine.web.brave.full_search_module": {"category": "brave", "description": "Setting for brave.full_search_module", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Module", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "local_deep_research.web_search_engines.engines.full_search", "visible": true}, "search.engine.web.brave.module_path": {"category": "brave", "description": "Setting for brave.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_brave", "visible": true}, "search.engine.web.brave.reliability": {"category": "brave", "description": "Setting for brave.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.7, "visible": true}, "search.engine.web.brave.requires_api_key": {"category": "brave", "description": "Setting for brave.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.brave.strengths": {"category": "brave", "description": "Setting for brave.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["privacy-focused web search", "product information", "reviews", "recent content", "news", "broad coverage"], "visible": true}, "search.engine.web.brave.supports_full_search": {"category": "brave", "description": "Setting for brave.supports_full_search", "editable": true, "max_value": null, "min_value": null, "name": "Supports Full Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.brave.weaknesses": {"category": "brave", "description": "Setting for brave.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["requires API key with usage limits", "smaller index than Google"], "visible": true}, "general.enable_fact_checking": {"category": null, "description": "Setting for general.enable_fact_checking", "editable": true, "max_value": null, "min_value": null, "name": "Enable Fact Checking", "options": null, "step": null, "type": "APP", "ui_element": "text", "value": false, "visible": true}, "general.knowledge_accumulation": {"category": null, "description": "Setting for general.knowledge_accumulation", "editable": true, "max_value": null, "min_value": null, "name": "Knowledge Accumulation", "options": null, "step": null, "type": "APP", "ui_element": "text", "value": "ITERATION", "visible": true}, "general.knowledge_accumulation_context_limit": {"category": null, "description": "Setting for general.knowledge_accumulation_context_limit", "editable": true, "max_value": null, "min_value": null, "name": "Knowledge Accumulation Context Limit", "options": null, "step": null, "type": "APP", "ui_element": "text", "value": 2000000, "visible": true}, "general.output_dir": {"category": null, "description": "Setting for general.output_dir", "editable": true, "max_value": null, "min_value": null, "name": "Output Dir", "options": null, "step": null, "type": "APP", "ui_element": "text", "value": "research_outputs", "visible": true}, "search.engine.web.github.display_name": {"category": "github", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "<PERSON><PERSON><PERSON>", "visible": false}, "search.engine.web.github.description": {"category": "github", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search projects on Github.", "visible": false}, "search.engine.web.github.class_name": {"category": "github", "description": "Setting for github.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "GitHubSearchEngine", "visible": true}, "search.engine.web.github.default_params.include_issues": {"category": "github", "description": "Setting for github.default_params.include_issues", "editable": true, "max_value": null, "min_value": null, "name": "Include Issues", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.github.default_params.include_readme": {"category": "github", "description": "Setting for github.default_params.include_readme", "editable": true, "max_value": null, "min_value": null, "name": "Include Readme", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.github.default_params.max_results": {"category": "github", "description": "Setting for github.default_params.max_results", "editable": true, "max_value": null, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 15, "visible": true}, "search.engine.web.github.default_params.search_type": {"category": "github", "description": "Setting for github.default_params.search_type", "editable": true, "max_value": null, "min_value": null, "name": "Search Type", "options": [{"label": "Repositories", "value": "repositories"}, {"label": "Code", "value": "code"}, {"label": "Issues", "value": "issues"}, {"label": "Users", "value": "users"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "repositories", "visible": true}, "search.engine.web.github.module_path": {"category": "github", "description": "Setting for github.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_github", "visible": true}, "search.engine.web.github.reliability": {"category": "github", "description": "Setting for github.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.99, "visible": true}, "search.engine.web.github.requires_api_key": {"category": "github", "description": "Setting for github.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.github.strengths": {"category": "github", "description": "Setting for github.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["code repositories", "software documentation", "open source projects", "programming issues", "developer information", "technical documentation"], "visible": true}, "search.engine.web.github.supports_full_search": {"category": "github", "description": "Setting for github.supports_full_search", "editable": true, "max_value": null, "min_value": null, "name": "Supports Full Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.github.weaknesses": {"category": "github", "description": "Setting for github.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["non-technical content", "content outside GitHub", "rate limits without API key"], "visible": true}, "search.engine.web.google_pse.display_name": {"category": "google_pse", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Google PSE", "visible": false}, "search.engine.web.google_pse.description": {"category": "google_pse", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search the web using Google's Programmable Search Engine API.", "visible": false}, "search.engine.web.google_pse.api_key": {"category": "google_pse", "description": "The Google PSE API key to use.", "editable": true, "max_value": null, "min_value": null, "name": "Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "GOOGLE_PSE_API_KEY", "visible": true}, "search.engine.web.google_pse.class_name": {"category": "google_pse", "description": "Setting for google_pse.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "GooglePSESearchEngine", "visible": true}, "search.engine.web.google_pse.default_params.region": {"category": "google_pse", "description": "Setting for google_pse.default_params.region", "editable": true, "max_value": null, "min_value": null, "name": "Region", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "us", "visible": true}, "search.engine.web.google_pse.default_params.safe_search": {"category": "google_pse", "description": "Setting for google_pse.default_params.safe_search", "editable": true, "max_value": null, "min_value": null, "name": "Safe Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.google_pse.default_params.search_language": {"category": "google_pse", "description": "Setting for google_pse.default_params.search_language", "editable": true, "max_value": null, "min_value": null, "name": "Search Language", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "English", "visible": true}, "search.engine.web.google_pse.full_search_class": {"category": "google_pse", "description": "Setting for google_pse.full_search_class", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Class", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "FullSearchResults", "visible": true}, "search.engine.web.google_pse.full_search_module": {"category": "google_pse", "description": "Setting for google_pse.full_search_module", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Module", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.full_search", "visible": true}, "search.engine.web.google_pse.module_path": {"category": "google_pse", "description": "Setting for google_pse.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_google_pse", "visible": true}, "search.engine.web.google_pse.reliability": {"category": "google_pse", "description": "Setting for google_pse.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.9, "visible": true}, "search.engine.web.google_pse.requires_api_key": {"category": "google_pse", "description": "Setting for google_pse.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.google_pse.strengths": {"category": "google_pse", "description": "Setting for google_pse.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["custom search scope", "high-quality results", "domain-specific search", "configurable search experience", "control over search index"], "visible": true}, "search.engine.web.google_pse.supports_full_search": {"category": "google_pse", "description": "Setting for google_pse.supports_full_search", "editable": true, "max_value": null, "min_value": null, "name": "Supports Full Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.google_pse.weaknesses": {"category": "google_pse", "description": "Setting for google_pse.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["requires API key with usage limits", "limited to 10,000 queries/day on free tier", "requires search engine configuration in Google Control Panel"], "visible": true}, "search.engine.local.local_all.display_name": {"category": "local_all", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Local Documents", "visible": false}, "search.engine.local.local_all.description": {"category": "local_all", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search only local documents using RAG.", "visible": false}, "search.engine.DEFAULT_SEARCH_ENGINE": {"category": "local_all", "description": "Setting for local_all.DEFAULT_SEARCH_ENGINE", "editable": true, "max_value": null, "min_value": null, "name": "Default Search Engine", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "wikipedia", "visible": true}, "search.engine.local.local_all.class_name": {"category": "local_all", "description": "Setting for local_all.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "LocalAllSearchEngine", "visible": true}, "search.engine.local.local_all.module_path": {"category": "local_all", "description": "Setting for local_all.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_local_all", "visible": true}, "search.engine.local.local_all.reliability": {"category": "local_all", "description": "Setting for local_all.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.85, "visible": true}, "search.engine.local.local_all.requires_api_key": {"category": "local_all", "description": "Setting for local_all.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.local.local_all.requires_llm": {"category": "local_all", "description": "Setting for local_all.requires_llm", "editable": true, "max_value": null, "min_value": null, "name": "Requires Llm", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.local.local_all.strengths": {"category": "local_all", "description": "Setting for local_all.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["searches all local collections", "personal documents", "offline access"], "visible": true}, "search.engine.local.local_all.weaknesses": {"category": "local_all", "description": "Setting for local_all.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["may return too many results", "requires indexing"], "visible": true}, "search.engine.local.personal_notes.cache_dir": {"category": "personal_notes", "description": "Setting for personal_notes.cache_dir", "editable": true, "max_value": null, "min_value": null, "name": "<PERSON><PERSON>", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "__CACHE_DIR__/local_search/personal_notes", "visible": true}, "search.engine.local.personal_notes.chunk_overlap": {"category": "personal_notes", "description": "Setting for personal_notes.chunk_overlap", "editable": true, "max_value": null, "min_value": 0, "name": "<PERSON><PERSON>", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 100, "visible": true}, "search.engine.local.personal_notes.chunk_size": {"category": "personal_notes", "description": "Setting for personal_notes.chunk_size", "editable": true, "max_value": null, "min_value": 1, "name": "Chunk Size", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 500, "visible": true}, "search.engine.local.personal_notes.description": {"category": "personal_notes", "description": "Setting for personal_notes.description", "editable": true, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Personal notes and documents", "visible": true}, "search.engine.local.personal_notes.embedding_device": {"category": "personal_notes", "description": "Setting for personal_notes.embedding_device", "editable": true, "max_value": null, "min_value": null, "name": "Embedding Device", "options": [{"label": "CPU", "value": "cpu"}, {"label": "CUDA", "value": "cuda"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "cpu", "visible": true}, "search.engine.local.personal_notes.embedding_model": {"category": "personal_notes", "description": "Setting for personal_notes.embedding_model", "editable": true, "max_value": null, "min_value": null, "name": "Embedding Model", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "all-MiniLM-L6-v2", "visible": true}, "search.engine.local.personal_notes.embedding_model_type": {"category": "personal_notes", "description": "Model provider to use for generating document embeddings.", "editable": true, "max_value": null, "min_value": null, "name": "Embedding Model Type", "options": [{"label": "SentenceTransformers", "value": "sentence_transformers"}, {"label": "Ollama", "value": "ollama"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "sentence_transformers", "visible": true}, "search.engine.local.personal_notes.enabled": {"category": "personal_notes", "description": "Setting for personal_notes.enabled", "editable": true, "max_value": null, "min_value": null, "name": "Enabled", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.local.personal_notes.max_filtered_results": {"category": "personal_notes", "description": "Setting for personal_notes.max_filtered_results", "editable": true, "max_value": null, "min_value": null, "name": "<PERSON> Filtered Results", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": 10, "visible": true}, "search.engine.local.personal_notes.max_results": {"category": "personal_notes", "description": "Setting for personal_notes.max_results", "editable": true, "max_value": null, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 30, "visible": true}, "search.engine.local.personal_notes.name": {"category": "personal_notes", "description": "Setting for personal_notes.name", "editable": true, "max_value": null, "min_value": null, "name": "Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Personal Notes", "visible": true}, "search.engine.local.personal_notes.paths": {"category": "personal_notes", "description": "Setting for personal_notes.paths", "editable": true, "max_value": null, "min_value": null, "name": "Paths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["/local_collections/personal_notes"], "visible": true}, "search.engine.local.personal_notes.reliability": {"category": "personal_notes", "description": "Setting for personal_notes.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.75, "visible": true}, "search.engine.local.personal_notes.strengths": {"category": "personal_notes", "description": "Setting for personal_notes.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["personal knowledge", "notes", "private documents"], "visible": true}, "search.engine.local.personal_notes.weaknesses": {"category": "personal_notes", "description": "Setting for personal_notes.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["subjective content", "informal information"], "visible": true}, "search.engine.local.project_docs.cache_dir": {"category": "project_docs", "description": "Setting for project_docs.cache_dir", "editable": true, "max_value": null, "min_value": null, "name": "<PERSON><PERSON>", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "__CACHE_DIR__/local_search/project_docs", "visible": true}, "search.engine.local.project_docs.chunk_overlap": {"category": "project_docs", "description": "Setting for project_docs.chunk_overlap", "editable": true, "max_value": null, "min_value": 0, "name": "<PERSON><PERSON>", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 200, "visible": true}, "search.engine.local.project_docs.chunk_size": {"category": "project_docs", "description": "Setting for project_docs.chunk_size", "editable": true, "max_value": null, "min_value": 1, "name": "Chunk Size", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 1000, "visible": true}, "search.engine.local.project_docs.description": {"category": "project_docs", "description": "Setting for project_docs.description", "editable": true, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Project documentation and specifications", "visible": true}, "search.engine.local.project_docs.embedding_device": {"category": "project_docs", "description": "Setting for project_docs.embedding_device", "editable": true, "max_value": null, "min_value": null, "name": "Embedding Device", "options": [{"label": "CPU", "value": "cpu"}, {"label": "CUDA", "value": "cuda"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "cpu", "visible": true}, "search.engine.local.project_docs.embedding_model": {"category": "project_docs", "description": "Setting for project_docs.embedding_model", "editable": true, "max_value": null, "min_value": null, "name": "Embedding Model", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "all-MiniLM-L6-v2", "visible": true}, "search.engine.local.project_docs.embedding_model_type": {"category": "project_docs", "description": "Model provider to use for generating document embeddings.", "editable": true, "max_value": null, "min_value": null, "name": "Embedding Model Type", "options": [{"label": "SentenceTransformers", "value": "sentence_transformers"}, {"label": "Ollama", "value": "ollama"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "sentence_transformers", "visible": true}, "search.engine.local.project_docs.enabled": {"category": "project_docs", "description": "Setting for project_docs.enabled", "editable": true, "max_value": null, "min_value": null, "name": "Enabled", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.local.project_docs.max_filtered_results": {"category": "project_docs", "description": "Setting for project_docs.max_filtered_results", "editable": true, "max_value": null, "min_value": null, "name": "<PERSON> Filtered Results", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": 5, "visible": true}, "search.engine.local.project_docs.max_results": {"category": "project_docs", "description": "Setting for project_docs.max_results", "editable": true, "max_value": null, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 20, "visible": true}, "search.engine.local.project_docs.name": {"category": "project_docs", "description": "Setting for project_docs.name", "editable": true, "max_value": null, "min_value": null, "name": "Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Project Documents", "visible": true}, "search.engine.local.project_docs.paths": {"category": "project_docs", "description": "Setting for project_docs.paths", "editable": true, "max_value": null, "min_value": null, "name": "Paths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["/local_collections/project_docs/"], "visible": true}, "search.engine.local.project_docs.reliability": {"category": "project_docs", "description": "Setting for project_docs.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.9, "visible": true}, "search.engine.local.project_docs.strengths": {"category": "project_docs", "description": "Setting for project_docs.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["project documentation", "specifications", "internal documents"], "visible": true}, "search.engine.local.project_docs.weaknesses": {"category": "project_docs", "description": "Setting for project_docs.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["no external information", "limited to organizational knowledge"], "visible": true}, "search.engine.web.pubmed.display_name": {"category": "pubmed", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "PubMed", "visible": false}, "search.engine.web.pubmed.description": {"category": "pubmed", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search papers indexed by PubMed.", "visible": false}, "search.engine.web.pubmed.api_key": {"category": "pubmed", "description": "The PubMed API key to use.", "editable": true, "max_value": null, "min_value": null, "name": "Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "PUBMED_API_KEY", "visible": true}, "search.engine.web.pubmed.class_name": {"category": "pubmed", "description": "Setting for pubmed.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "PubMedSearchEngine", "visible": true}, "search.engine.web.pubmed.default_params.days_limit": {"category": "pubmed", "description": "Setting for pubmed.default_params.days_limit", "editable": true, "max_value": null, "min_value": 0, "name": "Days Limit", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 0, "visible": true}, "search.engine.web.pubmed.default_params.full_text_limit": {"category": "pubmed", "description": "Setting for pubmed.default_params.full_text_limit", "editable": true, "max_value": null, "min_value": 0, "name": "Full Text Limit", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 3, "visible": true}, "search.engine.web.pubmed.default_params.get_abstracts": {"category": "pubmed", "description": "Setting for pubmed.default_params.get_abstracts", "editable": true, "max_value": null, "min_value": null, "name": "Get Abstracts", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.pubmed.default_params.get_full_text": {"category": "pubmed", "description": "Setting for pubmed.default_params.get_full_text", "editable": true, "max_value": null, "min_value": null, "name": "Get Full Text", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.pubmed.default_params.max_results": {"category": "pubmed", "description": "Setting for pubmed.default_params.max_results", "editable": true, "max_value": null, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 20, "visible": true}, "search.engine.web.pubmed.default_params.optimize_queries": {"category": "pubmed", "description": "Setting for pubmed.default_params.optimize_queries", "editable": true, "max_value": null, "min_value": null, "name": "Optimize Queries", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.pubmed.module_path": {"category": "pubmed", "description": "Setting for pubmed.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_pubmed", "visible": true}, "search.engine.web.pubmed.reliability": {"category": "pubmed", "description": "Setting for pubmed.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.98, "visible": true}, "search.engine.web.pubmed.requires_api_key": {"category": "pubmed", "description": "Setting for pubmed.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.pubmed.requires_llm": {"category": "pubmed", "description": "Setting for pubmed.requires_llm", "editable": true, "max_value": null, "min_value": null, "name": "Requires Llm", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.pubmed.strengths": {"category": "pubmed", "description": "Setting for pubmed.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["biomedical literature", "medical research", "clinical studies", "life sciences", "health information", "scientific papers"], "visible": true}, "search.engine.web.pubmed.weaknesses": {"category": "pubmed", "description": "Setting for pubmed.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["non-medical topics", "very recent papers may be missing", "limited to published research"], "visible": true}, "search.engine.local.research_papers.cache_dir": {"category": "research_papers", "description": "Setting for research_papers.cache_dir", "editable": true, "max_value": null, "min_value": null, "name": "<PERSON><PERSON>", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "__CACHE_DIR__/local_search/research_papers", "visible": true}, "search.engine.local.research_papers.chunk_overlap": {"category": "research_papers", "description": "Setting for research_papers.chunk_overlap", "editable": true, "max_value": null, "min_value": 0, "name": "<PERSON><PERSON>", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 150, "visible": true}, "search.engine.local.research_papers.chunk_size": {"category": "research_papers", "description": "Setting for research_papers.chunk_size", "editable": true, "max_value": null, "min_value": 1, "name": "Chunk Size", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 800, "visible": true}, "search.engine.local.research_papers.description": {"category": "research_papers", "description": "Setting for research_papers.description", "editable": true, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Academic research papers and articles", "visible": true}, "search.engine.local.research_papers.embedding_device": {"category": "research_papers", "description": "Setting for research_papers.embedding_device", "editable": true, "max_value": null, "min_value": null, "name": "Embedding Device", "options": [{"label": "CPU", "value": "cpu"}, {"label": "CUDA", "value": "cuda"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "cpu", "visible": true}, "search.engine.local.research_papers.embedding_model": {"category": "research_papers", "description": "Setting for research_papers.embedding_model", "editable": true, "max_value": null, "min_value": null, "name": "Embedding Model", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "all-MiniLM-L6-v2", "visible": true}, "search.engine.local.research_papers.embedding_model_type": {"category": "research_papers", "description": "Model provider to use for generating document embeddings.", "editable": true, "max_value": null, "min_value": null, "name": "Embedding Model Type", "options": [{"label": "SentenceTransformers", "value": "sentence_transformers"}, {"label": "Ollama", "value": "ollama"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "sentence_transformers", "visible": true}, "search.engine.local.research_papers.enabled": {"category": "research_papers", "description": "Setting for research_papers.enabled", "editable": true, "max_value": null, "min_value": null, "name": "Enabled", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.local.research_papers.max_filtered_results": {"category": "research_papers", "description": "Setting for research_papers.max_filtered_results", "editable": true, "max_value": null, "min_value": null, "name": "<PERSON> Filtered Results", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": 5, "visible": true}, "search.engine.local.research_papers.max_results": {"category": "research_papers", "description": "Setting for research_papers.max_results", "editable": true, "max_value": null, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 20, "visible": true}, "search.engine.local.research_papers.name": {"category": "research_papers", "description": "Setting for research_papers.name", "editable": true, "max_value": null, "min_value": null, "name": "Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Research Papers", "visible": true}, "search.engine.local.research_papers.paths": {"category": "research_papers", "description": "Setting for research_papers.paths", "editable": true, "max_value": null, "min_value": null, "name": "Paths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["/local_collections/research_papers/"], "visible": true}, "search.engine.local.research_papers.reliability": {"category": "research_papers", "description": "Setting for research_papers.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.85, "visible": true}, "search.engine.local.research_papers.strengths": {"category": "research_papers", "description": "Setting for research_papers.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["academic research", "scientific papers", "scholarly content"], "visible": true}, "search.engine.local.research_papers.weaknesses": {"category": "research_papers", "description": "Setting for research_papers.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["potentially outdated", "limited to collected papers"], "visible": true}, "search.engine.web.searxng.display_name": {"category": "searxng", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "SearXNG (Locally-hosted)", "visible": false}, "search.engine.web.searxng.description": {"category": "searxng", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "A locally-hosted meta-search engine.", "visible": false}, "search.engine.web.searxng.class_name": {"category": "searxng", "description": "Setting for searxng.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "SearXNGSearchEngine", "visible": true}, "search.engine.web.searxng.default_params.instance_url": {"category": "searxng", "description": "The SearXNG API endpoint URL.", "editable": true, "max_value": null, "min_value": null, "name": "Endpoint URL", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "http://localhost:8080", "visible": true}, "search.engine.web.searxng.default_params.categories": {"category": "searxng", "description": "Setting for searxng.default_params.categories", "editable": true, "max_value": null, "min_value": null, "name": "Categories", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["general"], "visible": true}, "search.engine.web.searxng.default_params.delay_between_requests": {"category": "searxng", "description": "Setting for searxng.default_params.delay_between_requests", "editable": true, "env_var": "SEARXNG_DELAY", "max_value": null, "min_value": 0, "name": "Delay Between Requests", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 0, "visible": true}, "search.engine.web.searxng.default_params.include_full_content": {"category": "searxng", "description": "Setting for searxng.default_params.include_full_content", "editable": true, "max_value": null, "min_value": null, "name": "Include Full Content", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.searxng.default_params.language": {"category": "searxng", "description": "Setting for searxng.default_params.language", "editable": true, "max_value": null, "min_value": null, "name": "Language", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "en", "visible": true}, "search.engine.web.searxng.default_params.max_results": {"category": "searxng", "description": "Setting for searxng.default_params.max_results", "editable": true, "max_value": null, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 15, "visible": true}, "search.engine.web.searxng.default_params.safe_search": {"category": "searxng", "description": "Configure the safe search level", "editable": true, "max_value": null, "min_value": null, "name": "Safe Search", "options": [{"label": "Off", "value": "OFF"}, {"label": "Moderate", "value": "MODERATE"}, {"label": "Strict", "value": "STRICT"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "OFF", "visible": true}, "search.engine.web.searxng.full_search_class": {"category": "searxng", "description": "Setting for searxng.full_search_class", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Class", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "FullSearchResults", "visible": true}, "search.engine.web.searxng.full_search_module": {"category": "searxng", "description": "Setting for searxng.full_search_module", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Module", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.full_search", "visible": true}, "search.engine.web.searxng.module_path": {"category": "searxng", "description": "Setting for searxng.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_searxng", "visible": true}, "search.engine.web.searxng.reliability": {"category": "searxng", "description": "Setting for searxng.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 1.0, "visible": true}, "search.engine.web.searxng.requires_api_key": {"category": "searxng", "description": "Setting for searxng.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.searxng.strengths": {"category": "searxng", "description": "Setting for searxng.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["comprehensive general information", "current events and news", "technical documentation", "factual queries", "historical information", "consumer products", "educational content", "multi-source aggregation", "real-time results", "combined results from major search engines"], "visible": true}, "search.engine.web.searxng.supports_full_search": {"category": "searxng", "description": "Setting for searxng.supports_full_search", "editable": true, "max_value": null, "min_value": null, "name": "Supports Full Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.searxng.weaknesses": {"category": "searxng", "description": "Setting for searxng.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["requires self-hosting", "depends on other search engines", "may be rate limited by underlying engines"], "visible": true}, "search.engine.web.serpapi.display_name": {"category": "serp<PERSON>i", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "SerpApi", "visible": false}, "search.engine.web.serpapi.description": {"category": "serp<PERSON>i", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search the web with Google's search API.", "visible": false}, "search.engine.web.serpapi.api_key": {"category": "serp<PERSON>i", "description": "The Serp API key to use.", "editable": true, "max_value": null, "min_value": null, "name": "API Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "SERP_API_KEY", "visible": true}, "search.engine.web.serpapi.class_name": {"category": "serp<PERSON>i", "description": "Setting for serpapi.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "SerpAPISearchEngine", "visible": true}, "search.engine.web.serpapi.default_params.region": {"category": "serp<PERSON>i", "description": "Setting for serpapi.default_params.region", "editable": true, "max_value": null, "min_value": null, "name": "Region", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "us", "visible": true}, "search.engine.web.serpapi.default_params.safe_search": {"category": "serp<PERSON>i", "description": "Setting for serpapi.default_params.safe_search", "editable": true, "max_value": null, "min_value": null, "name": "Safe Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.serpapi.default_params.search_language": {"category": "serp<PERSON>i", "description": "Setting for serpapi.default_params.search_language", "editable": true, "max_value": null, "min_value": null, "name": "Search Language", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "English", "visible": true}, "search.engine.web.serpapi.default_params.time_period": {"category": "serp<PERSON>i", "description": "Setting for serpapi.default_params.time_period", "editable": true, "max_value": null, "min_value": null, "name": "Time Period", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "y", "visible": true}, "search.engine.web.serpapi.full_search_class": {"category": "serp<PERSON>i", "description": "Setting for serpapi.full_search_class", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Class", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "FullSerpAPISearchResults", "visible": true}, "search.engine.web.serpapi.full_search_module": {"category": "serp<PERSON>i", "description": "Setting for serpapi.full_search_module", "editable": true, "max_value": null, "min_value": null, "name": "Full Search Module", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "local_deep_research.web_search_engines.engines.full_serp_search_results_old", "visible": true}, "search.engine.web.serpapi.module_path": {"category": "serp<PERSON>i", "description": "Setting for serpapi.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_serpapi", "visible": true}, "search.engine.web.serpapi.reliability": {"category": "serp<PERSON>i", "description": "Setting for serpapi.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.6, "visible": true}, "search.engine.web.serpapi.requires_api_key": {"category": "serp<PERSON>i", "description": "Setting for serpapi.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.serpapi.strengths": {"category": "serp<PERSON>i", "description": "Setting for serpapi.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["comprehensive web search", "product information", "reviews", "recent content", "news", "broad coverage"], "visible": true}, "search.engine.web.serpapi.supports_full_search": {"category": "serp<PERSON>i", "description": "Setting for serpapi.supports_full_search", "editable": true, "max_value": null, "min_value": null, "name": "Supports Full Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.serpapi.weaknesses": {"category": "serp<PERSON>i", "description": "Setting for serpapi.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["requires API key with usage limits", "not specialized for academic content"], "visible": true}, "search.engine.web.wayback.display_name": {"category": "wayback", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Wayback", "visible": false}, "search.engine.web.wayback.description": {"category": "wayback", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search the Internet Archive's Wayback Machine.", "visible": false}, "search.engine.web.wayback.class_name": {"category": "wayback", "description": "Setting for wayback.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "WaybackSearchEngine", "visible": true}, "search.engine.web.wayback.default_params.closest_only": {"category": "wayback", "description": "Setting for wayback.default_params.closest_only", "editable": true, "max_value": null, "min_value": null, "name": "Closest Only", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.wayback.default_params.language": {"category": "wayback", "description": "Setting for wayback.default_params.language", "editable": true, "max_value": null, "min_value": null, "name": "Language", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "English", "visible": true}, "search.engine.web.wayback.default_params.max_results": {"category": "wayback", "description": "Setting for wayback.default_params.max_results", "editable": true, "max_value": null, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 15, "visible": true}, "search.engine.web.wayback.default_params.max_snapshots_per_url": {"category": "wayback", "description": "Setting for wayback.default_params.max_snapshots_per_url", "editable": true, "max_value": null, "min_value": 1, "name": "Max Snapshots Per Url", "options": null, "step": null, "type": "SEARCH", "ui_element": "number", "value": 3, "visible": true}, "search.engine.web.wayback.module_path": {"category": "wayback", "description": "Setting for wayback.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_wayback", "visible": true}, "search.engine.web.wayback.reliability": {"category": "wayback", "description": "Setting for wayback.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.5, "visible": true}, "search.engine.web.wayback.requires_api_key": {"category": "wayback", "description": "Setting for wayback.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.wayback.strengths": {"category": "wayback", "description": "Setting for wayback.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["historical web content", "archived websites", "content verification", "deleted or changed web pages", "website evolution tracking"], "visible": true}, "search.engine.web.wayback.supports_full_search": {"category": "wayback", "description": "Setting for wayback.supports_full_search", "editable": true, "max_value": null, "min_value": null, "name": "Supports Full Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.wayback.weaknesses": {"category": "wayback", "description": "Setting for wayback.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["limited to previously archived content", "may miss recent changes", "archiving quality varies"], "visible": true}, "web.debug": {"category": null, "description": "Setting for web.debug", "editable": true, "max_value": null, "min_value": null, "name": "Debug", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": true, "visible": true}, "web.host": {"category": null, "description": "Setting for web.host", "editable": true, "max_value": null, "min_value": null, "name": "Host", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "0.0.0.0", "visible": true}, "web.port": {"category": null, "description": "Setting for web.port", "editable": true, "max_value": null, "min_value": null, "name": "Port", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": 5000, "visible": true}, "search.engine.web.wikipedia.display_name": {"category": "wikipedia", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Wikipedia", "visible": false}, "search.engine.web.wikipedia.description": {"category": "wikipedia", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search Wikipedia articles", "visible": false}, "search.engine.web.wikipedia.class_name": {"category": "wikipedia", "description": "Setting for wikipedia.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "WikipediaSearchEngine", "visible": true}, "search.engine.web.wikipedia.default_params.include_content": {"category": "wikipedia", "description": "Setting for wikipedia.default_params.include_content", "editable": true, "max_value": null, "min_value": null, "name": "Include Content", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.wikipedia.default_params.max_results": {"category": "wikipedia", "description": "Setting for wikipedia.default_params.max_results", "editable": true, "max_value": null, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 20, "visible": true}, "search.engine.web.wikipedia.module_path": {"category": "wikipedia", "description": "Setting for wikipedia.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_wikipedia", "visible": true}, "search.engine.web.wikipedia.reliability": {"category": "wikipedia", "description": "Setting for wikipedia.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.95, "visible": true}, "search.engine.web.wikipedia.requires_api_key": {"category": "wikipedia", "description": "Setting for wikipedia.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.wikipedia.strengths": {"category": "wikipedia", "description": "Setting for wikipedia.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["factual information", "general knowledge", "definitions", "historical facts", "biographies", "overview information"], "visible": true}, "search.engine.web.wikipedia.weaknesses": {"category": "wikipedia", "description": "Setting for wikipedia.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["recent events", "specialized academic topics", "product comparisons"], "visible": true}, "search.cross_engine_max_results": {"category": "search_parameters", "description": "Maximum number of search results to keep after cross-engine filtering. When results from multiple search engines are combined, this limits how many total results are displayed. Higher values show more comprehensive results.", "editable": true, "max_value": 1000, "min_value": 1, "name": "Cross-Engine Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 100, "visible": true}, "search.engine.web.pubmed.use_in_auto_search": {"category": "pubmed", "description": "Include PubMed in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.arxiv.use_in_auto_search": {"category": "arxiv", "description": "Include ArXiv in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.searxng.use_in_auto_search": {"category": "searxng", "description": "Include SearXNG in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.github.use_in_auto_search": {"category": "github", "description": "Include GitHub in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.wikipedia.use_in_auto_search": {"category": "wikipedia", "description": "Include Wikipedia in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.brave.use_in_auto_search": {"category": "brave", "description": "Include Brave search in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.google_pse.use_in_auto_search": {"category": "google_pse", "description": "Include Google PSE in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.serpapi.use_in_auto_search": {"category": "serp<PERSON>i", "description": "Include SerpAPI in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.wayback.use_in_auto_search": {"category": "wayback", "description": "Include Wayback in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.tavily.display_name": {"category": "tavily", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "<PERSON><PERSON>", "visible": false}, "search.engine.web.tavily.description": {"category": "tavily", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "AI-powered search engine optimized for research with built-in answer extraction.", "visible": false}, "search.engine.web.tavily.api_key": {"category": "tavily", "description": "The Tavily API key to use.", "editable": true, "max_value": null, "min_value": null, "name": "Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "TAVILY_API_KEY", "visible": true}, "search.engine.web.tavily.class_name": {"category": "tavily", "description": "Setting for tavily.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "TavilySearchEngine", "visible": true}, "search.engine.web.tavily.default_params.search_depth": {"category": "tavily", "description": "Search depth - basic for speed, advanced for quality", "editable": true, "max_value": null, "min_value": null, "name": "Search Depth", "options": ["basic", "advanced"], "step": null, "type": "SEARCH", "ui_element": "select", "value": "basic", "visible": true}, "search.engine.web.tavily.default_params.include_full_content": {"category": "tavily", "description": "Include full webpage content in results", "editable": true, "max_value": null, "min_value": null, "name": "Include Full Content", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.tavily.module_path": {"category": "tavily", "description": "Setting for tavily.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": ".engines.search_engine_tavily", "visible": true}, "search.engine.web.tavily.requires_api_key": {"category": "tavily", "description": "Setting for tavily.requires_api_key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.tavily.reliability": {"category": "tavily", "description": "Setting for tavily.reliability", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.8, "visible": true}, "search.engine.web.tavily.strengths": {"category": "tavily", "description": "Setting for tavily.strengths", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["AI-powered search optimization", "built-in answer extraction", "research-focused results", "high-quality content filtering", "fast response times"], "visible": true}, "search.engine.web.tavily.supports_full_search": {"category": "tavily", "description": "Setting for tavily.supports_full_search", "editable": true, "max_value": null, "min_value": null, "name": "Supports Full Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.tavily.weaknesses": {"category": "tavily", "description": "Setting for tavily.weaknesses", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": ["requires API key with usage limits", "newer service with smaller historical data"], "visible": true}, "search.engine.web.tavily.use_in_auto_search": {"category": "tavily", "description": "Include Tavi<PERSON> in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.local.local_all.use_in_auto_search": {"category": "local_all", "description": "Include local documents in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.local.personal_notes.use_in_auto_search": {"category": "personal_notes", "description": "Include personal notes in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.local.project_docs.use_in_auto_search": {"category": "project_docs", "description": "Include project documents in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.local.research_papers.use_in_auto_search": {"category": "research_papers", "description": "Include research papers in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "llm.llamacpp_connection_mode": {"category": "llm_parameters", "description": "Connection mode for LlamaCpp: 'local' for direct model loading or 'http' for using a remote server", "editable": true, "max_value": null, "min_value": null, "name": "LlamaCpp Connection Mode", "options": [{"label": "Local Model", "value": "local"}, {"label": "HTTP Server", "value": "http"}], "step": null, "type": "LLM", "ui_element": "select", "value": "local", "visible": true}, "llm.llamacpp_server_url": {"category": "llm_parameters", "description": "URL of the LlamaCpp HTTP server (only used when connection mode is 'http')", "editable": true, "max_value": null, "min_value": null, "name": "LlamaCpp Server URL", "options": null, "step": null, "type": "LLM", "ui_element": "text", "value": "http://localhost:8000", "visible": true}, "app.warnings.dismiss_high_context": {"category": "warnings", "description": "Dismiss warnings about high context window sizes that may cause memory issues", "editable": true, "max_value": null, "min_value": null, "name": "Dismiss High Context Warnings", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": false, "visible": true}, "app.warnings.dismiss_low_context_focused": {"category": "warnings", "description": "Dismiss warnings about using focused iteration with low context window sizes", "editable": true, "max_value": null, "min_value": null, "name": "Dismiss Low Context + Focused Strategy Warnings", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": false, "visible": true}, "app.warnings.dismiss_model_mismatch": {"category": "warnings", "description": "Dismiss warnings about context size vs model size mismatches", "editable": true, "max_value": null, "min_value": null, "name": "Dismiss Model Mismatch Warnings", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": false, "visible": true}, "app.warnings.dismiss_searxng_recommendation": {"category": "warnings", "description": "Dismiss recommendations about using more questions instead of iterations with SearXNG", "editable": true, "max_value": null, "min_value": null, "name": "Dismiss SearXNG Optimization Tips", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": false, "visible": true}, "rate_limiting.enabled": {"category": "rate_limiting", "description": "Enable adaptive rate limiting system that learns optimal wait times for each search engine", "editable": true, "max_value": null, "min_value": null, "name": "Enable Rate Limiting", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": true, "visible": true}, "rate_limiting.exploration_rate": {"category": "rate_limiting", "description": "Percentage of attempts that will explore faster rates to discover improvements (0.0-1.0)", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Exploration Rate", "options": null, "step": 0.05, "type": "APP", "ui_element": "range", "value": 0.1, "visible": true}, "rate_limiting.learning_rate": {"category": "rate_limiting", "description": "How quickly to adapt to new rate limit information (higher = faster adaptation)", "editable": true, "max_value": 1.0, "min_value": 0.05, "name": "Learning Rate", "options": null, "step": 0.05, "type": "APP", "ui_element": "range", "value": 0.3, "visible": true}, "rate_limiting.memory_window": {"category": "rate_limiting", "description": "Number of recent attempts to keep in memory for learning", "editable": true, "max_value": 1000, "min_value": 10, "name": "Memory Window", "options": null, "step": 10, "type": "APP", "ui_element": "number", "value": 100, "visible": true}, "rate_limiting.profile": {"category": "rate_limiting", "description": "Rate limiting aggressiveness profile", "editable": true, "max_value": null, "min_value": null, "name": "Rate Limiting Profile", "options": [{"label": "Conservative (slower, more reliable)", "value": "conservative"}, {"label": "Balanced (default)", "value": "balanced"}, {"label": "Aggressive (faster, higher risk)", "value": "aggressive"}], "step": null, "type": "APP", "ui_element": "select", "value": "balanced", "visible": true}, "rate_limiting.decay_per_day": {"category": "rate_limiting", "description": "Confidence decay factor per day for old rate limit estimates (0.5-0.99, lower = faster decay)", "editable": true, "max_value": 0.99, "min_value": 0.5, "name": "Decay Factor Per Day", "options": null, "step": 0.01, "type": "APP", "ui_element": "range", "value": 0.95, "visible": true}, "rate_limiting.llm_enabled": {"category": "rate_limiting", "description": "Enable adaptive rate limiting for LLM API calls (e.g., for free tier APIs with request limits)", "editable": true, "max_value": null, "min_value": null, "name": "Enable LLM Rate Limiting", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": false, "visible": true}, "benchmark.evaluation.provider": {"category": "benchmark", "description": "Provider for benchmark evaluation model", "editable": true, "max_value": null, "min_value": null, "name": "Evaluation Provider", "options": [{"label": "OpenRouter (Recommended)", "value": "openai_endpoint"}, {"label": "OpenAI API", "value": "openai"}, {"label": "Anthropic API", "value": "anthropic"}, {"label": "<PERSON><PERSON><PERSON> (Local)", "value": "ollama"}], "step": null, "type": "SEARCH", "ui_element": "select", "value": "openai_endpoint", "visible": true}, "benchmark.evaluation.model": {"category": "benchmark", "description": "Model for evaluating benchmark results", "editable": true, "max_value": null, "min_value": null, "name": "Evaluation Model", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "anthropic/claude-3.7-sonnet", "visible": true}, "benchmark.evaluation.endpoint_url": {"category": "benchmark", "description": "Endpoint URL for evaluation model (when using OpenAI-compatible APIs)", "editable": true, "max_value": null, "min_value": null, "name": "Evaluation Endpoint URL", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "https://openrouter.ai/api/v1", "visible": true}, "benchmark.evaluation.temperature": {"category": "benchmark", "description": "Temperature for evaluation (0 recommended for consistency)", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Evaluation Temperature", "options": null, "step": 0.1, "type": "SEARCH", "ui_element": "range", "value": 0, "visible": true}, "news.feed.default_limit": {"category": "news", "description": "Default number of news items to show in feed", "editable": true, "max_value": 100, "min_value": 5, "name": "De<PERSON><PERSON> Feed Limit", "options": null, "step": 5, "type": "APP", "ui_element": "number", "value": 20, "visible": true}, "news.storage.default_limit": {"category": "news", "description": "Default limit for storing news items", "editable": true, "max_value": 500, "min_value": 10, "name": "Default Storage Limit", "options": null, "step": 10, "type": "APP", "ui_element": "number", "value": 100, "visible": true}, "news.preferences.max_stored": {"category": "news", "description": "Maximum number of stored preferences per user", "editable": true, "max_value": 500, "min_value": 10, "name": "Max Stored Preferences", "options": null, "step": 10, "type": "APP", "ui_element": "number", "value": 100, "visible": true}, "news.refresh.default_hours": {"category": "news", "description": "Default refresh interval in hours", "editable": true, "max_value": 24, "min_value": 1, "name": "Default Refresh Hours", "options": null, "step": 1, "type": "APP", "ui_element": "number", "value": 4, "visible": true}, "news.subscription.refresh_minutes": {"category": "news", "description": "Default subscription refresh interval in minutes", "editable": true, "max_value": 2880, "min_value": 60, "name": "Subscription Refresh Minutes", "options": null, "step": 60, "type": "APP", "ui_element": "number", "value": 360, "visible": true}, "news.trending.lookback_hours": {"category": "news", "description": "Hours to look back for trending analysis", "editable": true, "max_value": 168, "min_value": 1, "name": "Trending Lookback Hours", "options": null, "step": 1, "type": "APP", "ui_element": "number", "value": 24, "visible": true}, "news.progress.generating_searches": {"category": "news", "description": "Progress percentage when generating searches", "editable": false, "max_value": 100, "min_value": 0, "name": "Progress Generating Searches", "options": null, "step": 1, "type": "APP", "ui_element": "number", "value": 50, "visible": false}, "news.progress.complete": {"category": "news", "description": "Progress percentage when complete", "editable": false, "max_value": 100, "min_value": 0, "name": "Progress Complete", "options": null, "step": 1, "type": "APP", "ui_element": "number", "value": 100, "visible": false}, "news.display.max_query_length": {"category": "news", "description": "Maximum query display length in UI", "editable": true, "max_value": 200, "min_value": 20, "name": "Max Query Display Length", "options": null, "step": 10, "type": "APP", "ui_element": "number", "value": 50, "visible": true}, "news.display.default_headline_max_length": {"category": "news", "description": "Default maximum headline length", "editable": true, "max_value": 300, "min_value": 50, "name": "Default Headline Max Length", "options": null, "step": 10, "type": "APP", "ui_element": "number", "value": 100, "visible": true}, "news.subscription.default_type": {"category": "news", "description": "Default subscription type", "editable": true, "max_value": null, "min_value": null, "name": "Default Subscription Type", "options": [{"label": "Search", "value": "search"}, {"label": "Topic", "value": "topic"}], "step": null, "type": "APP", "ui_element": "select", "value": "search", "visible": true}, "news.scheduler.enabled": {"category": "news_scheduler", "description": "Enable automatic news subscription updates for active users", "editable": true, "max_value": null, "min_value": null, "name": "Enable News Scheduler", "options": null, "step": null, "type": "APP", "ui_element": "checkbox", "value": true, "visible": true}, "news.scheduler.retention_hours": {"category": "news_scheduler", "description": "Hours to keep user credentials in memory after last activity", "editable": true, "max_value": 168, "min_value": 1, "name": "User Retention Period (hours)", "options": null, "step": 1, "type": "APP", "ui_element": "number", "value": 48, "visible": true}, "news.scheduler.cleanup_interval_hours": {"category": "news_scheduler", "description": "How often to run cleanup job for inactive users", "editable": true, "max_value": 24, "min_value": 0.25, "name": "Cleanup Interval (hours)", "options": null, "step": 0.25, "type": "APP", "ui_element": "number", "value": 1, "visible": true}, "news.scheduler.max_jitter_seconds": {"category": "news_scheduler", "description": "Maximum random delay added to subscription checks to spread load", "editable": true, "max_value": 3600, "min_value": 0, "name": "Maximum Jitter (seconds)", "options": null, "step": 1, "type": "APP", "ui_element": "number", "value": 300, "visible": true}, "news.scheduler.max_concurrent_jobs": {"category": "news_scheduler", "description": "Maximum concurrent subscription checks across all users", "editable": true, "max_value": 50, "min_value": 1, "name": "<PERSON> Concurrent Jobs", "options": null, "step": 1, "type": "APP", "ui_element": "number", "value": 10, "visible": true}, "news.scheduler.batch_size": {"category": "news_scheduler", "description": "Number of subscriptions to process in each batch", "editable": true, "max_value": 20, "min_value": 1, "name": "Subscription Batch Size", "options": null, "step": 1, "type": "APP", "ui_element": "number", "value": 5, "visible": true}, "news.scheduler.activity_check_interval": {"category": "news_scheduler", "description": "How often to check for due subscriptions (minutes)", "editable": true, "max_value": 60, "min_value": 1, "name": "Activity Check Interval (minutes)", "options": null, "step": 1, "type": "APP", "ui_element": "number", "value": 5, "visible": true}}