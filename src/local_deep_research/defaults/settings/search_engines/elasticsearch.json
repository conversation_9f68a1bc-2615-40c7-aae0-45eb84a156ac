{"search.engine.web.elasticsearch.display_name": {"category": "elasticsearch", "description": "Display name to use in the U.I. for this search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Display Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Elasticsearch", "visible": true}, "search.engine.web.elasticsearch.description": {"category": "elasticsearch", "description": "Human-readable description of the search engine.", "editable": false, "max_value": null, "min_value": null, "name": "Description", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "Search engine for Elasticsearch databases. Efficient for searching document collections and structured data.", "visible": true}, "search.engine.web.elasticsearch.class_name": {"category": "elasticsearch", "description": "Setting for elasticsearch.class_name", "editable": true, "max_value": null, "min_value": null, "name": "Class Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "ElasticsearchSearchEngine", "visible": true}, "search.engine.web.elasticsearch.module_path": {"category": "elasticsearch", "description": "Setting for elasticsearch.module_path", "editable": true, "max_value": null, "min_value": null, "name": "Module Path", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "local_deep_research.web_search_engines.engines.search_engine_elasticsearch", "visible": true}, "search.engine.web.elasticsearch.default_params.hosts": {"category": "elasticsearch", "description": "Elasticsearch server URLs (JSON array format)", "editable": true, "max_value": null, "min_value": null, "name": "Elasticsearch Host URLs", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": "[\"http://localhost:9200\"]", "visible": true}, "search.engine.web.elasticsearch.default_params.index_name": {"category": "elasticsearch", "description": "Name of the Elasticsearch index to search", "editable": true, "max_value": null, "min_value": null, "name": "Index Name", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "documents", "visible": true}, "search.engine.web.elasticsearch.default_params.username": {"category": "elasticsearch", "description": "Username for Elasticsearch authentication (optional)", "editable": true, "max_value": null, "min_value": null, "name": "Username", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "", "visible": true}, "search.engine.web.elasticsearch.default_params.password": {"category": "elasticsearch", "description": "Password for Elasticsearch authentication (optional)", "editable": true, "max_value": null, "min_value": null, "name": "Password", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "", "visible": true}, "search.engine.web.elasticsearch.default_params.api_key": {"category": "elasticsearch", "description": "API key for Elasticsearch authentication (optional)", "editable": true, "max_value": null, "min_value": null, "name": "API Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "password", "value": "", "visible": true}, "search.engine.web.elasticsearch.default_params.cloud_id": {"category": "elasticsearch", "description": "Elastic Cloud ID for cloud-hosted Elasticsearch (optional)", "editable": true, "max_value": null, "min_value": null, "name": "Cloud ID", "options": null, "step": null, "type": "SEARCH", "ui_element": "text", "value": "", "visible": true}, "search.engine.web.elasticsearch.default_params.max_results": {"category": "elasticsearch", "description": "Maximum number of search results to return", "editable": true, "max_value": 100, "min_value": 1, "name": "Max Results", "options": null, "step": 1, "type": "SEARCH", "ui_element": "number", "value": 10, "visible": true}, "search.engine.web.elasticsearch.default_params.search_fields": {"category": "elasticsearch", "description": "Fields to search in (JSON array)", "editable": true, "max_value": null, "min_value": null, "name": "Search Fields", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": "[\"content\", \"title\", \"description\", \"text\"]", "visible": true}, "search.engine.web.elasticsearch.default_params.highlight_fields": {"category": "elasticsearch", "description": "Fields to highlight in search results (JSON array)", "editable": true, "max_value": null, "min_value": null, "name": "Highlight Fields", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": "[\"content\", \"title\"]", "visible": true}, "search.engine.web.elasticsearch.reliability": {"category": "elasticsearch", "description": "Reliability of the Elasticsearch search engine", "editable": true, "max_value": 1.0, "min_value": 0.0, "name": "Reliability", "options": null, "step": 0.05, "type": "SEARCH", "ui_element": "range", "value": 0.95, "visible": true}, "search.engine.web.elasticsearch.requires_api_key": {"category": "elasticsearch", "description": "Whether this search engine requires an API key", "editable": true, "max_value": null, "min_value": null, "name": "Requires Api Key", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}, "search.engine.web.elasticsearch.requires_llm": {"category": "elasticsearch", "description": "Whether this search engine requires an LLM for relevance filtering", "editable": true, "max_value": null, "min_value": null, "name": "Requires LLM", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.elasticsearch.supports_full_search": {"category": "elasticsearch", "description": "Whether this search engine supports full document search", "editable": true, "max_value": null, "min_value": null, "name": "Supports Full Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": true, "visible": true}, "search.engine.web.elasticsearch.strengths": {"category": "elasticsearch", "description": "Strengths of this search engine", "editable": true, "max_value": null, "min_value": null, "name": "Strengths", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": "[\"Fast full-text search\", \"Scalable and distributed\", \"Rich query DSL\", \"Supports aggregations\", \"Real-time search\"]", "visible": true}, "search.engine.web.elasticsearch.weaknesses": {"category": "elasticsearch", "description": "Weaknesses of this search engine", "editable": true, "max_value": null, "min_value": null, "name": "Weaknesses", "options": null, "step": null, "type": "SEARCH", "ui_element": "json", "value": "[\"Requires running Elasticsearch instance\", \"Needs proper index configuration\", \"Data must be pre-indexed\"]", "visible": true}, "search.engine.web.elasticsearch.use_in_auto_search": {"category": "elasticsearch", "description": "Include Elasticsearch in auto search mode", "editable": true, "max_value": null, "min_value": null, "name": "Include in Auto Search", "options": null, "step": null, "type": "SEARCH", "ui_element": "checkbox", "value": false, "visible": true}}