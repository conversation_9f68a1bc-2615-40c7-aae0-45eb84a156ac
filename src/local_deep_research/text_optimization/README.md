# Text Optimization Module

This module provides post-processing optimizations for research reports, with a focus on citation formatting and export capabilities.

## Features

### Citation Formatting

The `CitationFormatter` class provides three modes for formatting citations:

1. **Number Hyperlinks** (default): Converts `[1]` to `[[1]](url)` - clickable citation numbers
2. **Domain Hyperlinks**: Converts `[1]` to `[[arxiv.org]](url)` - shows the source domain
3. **No Hyperlinks**: Leaves citations as plain `[1]` without links

Supports multiple citation formats:
- Individual citations: `[1]`
- Consecutive citations: `[1][2][3]`
- Comma-separated citations: `[1, 2, 3]`

### Export Formats

#### LaTeX Export

The `LaTeXExporter` class converts markdown reports to LaTeX format, including:
- Proper heading conversion (`#` → `\section{}`)
- Citation formatting (`[1]` → `\cite{ref1}`)
- Bibliography generation
- Basic formatting (bold, italic, code)

#### Quarto Export

The `QuartoExporter` class converts markdown reports to Quarto (.qmd) format:
- YAML front matter with metadata
- Citation conversion to Quarto format (`[1]` → `[@ref1]`)
- BibTeX bibliography generation
- Support for both HTML and PDF output formats
- Automatic title extraction from markdown

## Usage

### In Research Service

The module is integrated into the research service and automatically formats citations based on user settings:

```python
from local_deep_research.text_optimization import CitationFormatter, CitationMode

# Get formatter with user settings
formatter = get_citation_formatter()
formatted_content = formatter.format_document(markdown_content)
```

### Settings

Citation format can be configured via the settings UI or environment variable:
- Setting key: `report.citation_format`
- Environment variable: `LDR_REPORT_CITATION_FORMAT`
- Options: `number_hyperlinks`, `domain_hyperlinks`, `no_hyperlinks`

Export formats can be configured to automatically export reports in multiple formats:
- Setting key: `report.export_formats`
- Environment variable: `LDR_REPORT_EXPORT_FORMATS`
- Options: `markdown`, `latex`, `quarto` (can select multiple)

### Export Examples

```python
from local_deep_research.web.services.research_service import export_report_to_format, export_report_to_latex

# Export to LaTeX
latex_path = export_report_to_latex(report_path)

# Export to Quarto
quarto_path = export_report_to_format(report_path, "quarto")

# Export to Quarto with custom title
quarto_path = export_report_to_format(report_path, "quarto", "My Research Title")
```

## Testing

Run the test script to see examples of all formatting modes:

```bash
python test_citation_formatter.py
```
