"""
Data models for follow-up research functionality.
"""

from dataclasses import dataclass
from typing import List, Dict, Any


@dataclass
class FollowUpRequest:
    """Request model for follow-up research."""

    parent_research_id: str
    question: str
    strategy: str = "source-based"  # Default delegate strategy
    max_iterations: int = 1  # Quick summary by default
    questions_per_iteration: int = 3

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API/service use."""
        return {
            "parent_research_id": self.parent_research_id,
            "question": self.question,
            "strategy": self.strategy,
            "max_iterations": self.max_iterations,
            "questions_per_iteration": self.questions_per_iteration,
        }


@dataclass
class FollowUpResponse:
    """Response model for follow-up research."""

    research_id: str
    question: str
    answer: str
    sources_used: List[Dict[str, str]]
    parent_context_used: bool
    reused_links_count: int
    new_links_count: int

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API response."""
        return {
            "research_id": self.research_id,
            "question": self.question,
            "answer": self.answer,
            "sources_used": self.sources_used,
            "parent_context_used": self.parent_context_used,
            "reused_links_count": self.reused_links_count,
            "new_links_count": self.new_links_count,
        }
