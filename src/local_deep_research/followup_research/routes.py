"""
Flask routes for follow-up research functionality.
"""

from flask import Blueprint, request, jsonify, session, g
from loguru import logger

from .service import FollowUpResearchService
from .models import FollowUpRequest
from ..web.auth.decorators import login_required

# Create blueprint
followup_bp = Blueprint("followup", __name__, url_prefix="/api/followup")


@followup_bp.route("/prepare", methods=["POST"])
@login_required
def prepare_followup():
    """
    Prepare a follow-up research by loading parent context.

    Request body:
    {
        "parent_research_id": "uuid",
        "question": "follow-up question"
    }

    Returns:
    {
        "success": true,
        "parent_summary": "...",
        "available_sources": 10,
        "suggested_strategy": "source-based"
    }
    """
    try:
        data = request.get_json()
        parent_id = data.get("parent_research_id")
        question = data.get("question")

        if not parent_id or not question:
            return jsonify(
                {
                    "success": False,
                    "error": "Missing parent_research_id or question",
                }
            ), 400

        # Get username from session
        username = session.get("username")

        # Get settings snapshot to use for suggested strategy
        from ..web.services.settings_manager import SettingsManager
        from ..database.session_context import get_user_db_session

        with get_user_db_session(username) as db_session:
            settings_manager = SettingsManager(db_session=db_session)
            settings_snapshot = settings_manager.get_all_settings()

        # Get strategy from settings
        strategy_from_settings = settings_snapshot.get(
            "search.search_strategy", {}
        ).get("value", "source-based")

        # Initialize service
        service = FollowUpResearchService(username=username)

        # Load parent context
        parent_data = service.load_parent_research(parent_id)

        if not parent_data:
            # For now, return success with empty data to allow testing
            logger.warning(
                f"Parent research {parent_id} not found, returning empty context"
            )
            return jsonify(
                {
                    "success": True,
                    "parent_summary": "Previous research context",
                    "available_sources": 0,
                    "suggested_strategy": strategy_from_settings,  # Use strategy from settings
                    "parent_research": {
                        "id": parent_id,
                        "query": "Previous query",
                        "sources_count": 0,
                    },
                }
            )

        # Prepare response with parent context summary
        response = {
            "success": True,
            "parent_summary": parent_data.get("query", ""),
            "available_sources": len(parent_data.get("resources", [])),
            "suggested_strategy": strategy_from_settings,  # Use strategy from settings
            "parent_research": {
                "id": parent_id,
                "query": parent_data.get("query", ""),
                "sources_count": len(parent_data.get("resources", [])),
            },
        }

        return jsonify(response)

    except Exception:
        logger.exception("Error preparing follow-up")
        return jsonify(
            {"success": False, "error": "An internal error has occurred."}
        ), 500


@followup_bp.route("/start", methods=["POST"])
@login_required
def start_followup():
    """
    Start a follow-up research.

    Request body:
    {
        "parent_research_id": "uuid",
        "question": "follow-up question",
        "strategy": "source-based",  # optional
        "max_iterations": 1,  # optional
        "questions_per_iteration": 3  # optional
    }

    Returns:
    {
        "success": true,
        "research_id": "new-uuid",
        "message": "Follow-up research started"
    }
    """
    try:
        from ..web.services.research_service import (
            start_research_process,
            run_research_process,
        )
        from ..web.routes.globals import active_research, termination_flags
        import uuid

        data = request.get_json()

        # Get username from session
        username = session.get("username")

        # Get settings snapshot first to use database values
        from ..web.services.settings_manager import SettingsManager
        from ..database.session_context import get_user_db_session

        with get_user_db_session(username) as db_session:
            settings_manager = SettingsManager(db_session=db_session)
            settings_snapshot = settings_manager.get_all_settings()

        # Get strategy from settings snapshot, fallback to source-based if not set
        strategy_from_settings = settings_snapshot.get(
            "search.search_strategy", {}
        ).get("value", "source-based")

        # Get iterations and questions from settings snapshot
        iterations_from_settings = settings_snapshot.get(
            "search.iterations", {}
        ).get("value", 1)
        questions_from_settings = settings_snapshot.get(
            "search.questions_per_iteration", {}
        ).get("value", 3)

        # Create follow-up request using settings values
        followup_request = FollowUpRequest(
            parent_research_id=data.get("parent_research_id"),
            question=data.get("question"),
            strategy=strategy_from_settings,  # Use strategy from settings
            max_iterations=iterations_from_settings,  # Use iterations from settings
            questions_per_iteration=questions_from_settings,  # Use questions from settings
        )

        # Initialize service
        service = FollowUpResearchService(username=username)

        # Prepare research parameters
        research_params = service.perform_followup(followup_request)

        logger.info(f"Research params type: {type(research_params)}")
        logger.info(
            f"Research params keys: {research_params.keys() if isinstance(research_params, dict) else 'Not a dict'}"
        )
        logger.info(
            f"Query value: {research_params.get('query') if isinstance(research_params, dict) else 'N/A'}"
        )
        logger.info(
            f"Query type: {type(research_params.get('query')) if isinstance(research_params, dict) else 'N/A'}"
        )

        # Generate new research ID
        research_id = str(uuid.uuid4())

        # Create database entry (settings_snapshot already captured above)
        from ..database.models import ResearchHistory
        from datetime import datetime, UTC

        created_at = datetime.now(UTC).isoformat()

        with get_user_db_session(username) as db_session:
            # Create the database entry (required for tracking)
            research_meta = {
                "submission": {
                    "parent_research_id": data.get("parent_research_id"),
                    "question": data.get("question"),
                    "strategy": "contextual-followup",
                },
            }

            research = ResearchHistory(
                id=research_id,
                query=research_params["query"],
                mode="quick",  # Use 'quick' not 'quick_summary'
                status="in_progress",
                created_at=created_at,
                progress_log=[{"time": created_at, "progress": 0}],
                research_meta=research_meta,
            )
            db_session.add(research)
            db_session.commit()
            logger.info(
                f"Created follow-up research entry with ID: {research_id}"
            )

        # Start the research process using the existing infrastructure
        # Use quick_summary mode for follow-ups by default
        logger.info(
            f"Starting follow-up research for query of type: {type(research_params.get('query'))}"
        )

        # Get user password for metrics database access
        user_password = None
        session_id = session.get("session_id")
        if session_id:
            from ..database.session_passwords import session_password_store

            user_password = session_password_store.retrieve(
                username, session_id
            )

        # Fallback to g.user_password (set by middleware if temp_auth was used)
        if not user_password:
            user_password = getattr(g, "user_password", None)

        # Last resort: try temp_auth_store
        if not user_password:
            from ..database.temp_auth import temp_auth_store

            auth_token = session.get("temp_auth_token")
            if auth_token:
                # Use peek_auth to avoid consuming the token
                auth_data = temp_auth_store.peek_auth(auth_token)
                if auth_data and auth_data[0] == username:
                    user_password = auth_data[1]

        if not user_password:
            logger.warning(
                f"No password available for metrics access for user {username}"
            )

        # Get model and search settings from user's settings
        model_provider = settings_snapshot.get("llm.provider", {}).get(
            "value", "OLLAMA"
        )
        model = settings_snapshot.get("llm.model", {}).get(
            "value", "gemma3:12b"
        )
        search_engine = settings_snapshot.get("search.tool", {}).get(
            "value", "searxng"
        )
        custom_endpoint = settings_snapshot.get(
            "llm.openai_endpoint.url", {}
        ).get("value")

        start_research_process(
            research_id,
            research_params["query"],
            "quick",  # Use 'quick' for quick summary mode
            active_research,
            termination_flags,
            run_research_process,
            username=username,
            user_password=user_password,  # Pass password for metrics database access
            model_provider=model_provider,  # Pass model provider
            model=model,  # Pass model name
            search_engine=search_engine,  # Pass search engine
            custom_endpoint=custom_endpoint,  # Pass custom endpoint if any
            strategy="enhanced-contextual-followup",  # Use enhanced contextual follow-up strategy
            iterations=research_params["max_iterations"],
            questions_per_iteration=research_params["questions_per_iteration"],
            delegate_strategy=research_params.get(
                "delegate_strategy", "source-based"
            ),
            research_context=research_params["research_context"],
            parent_research_id=research_params[
                "parent_research_id"
            ],  # Pass parent research ID
            settings_snapshot=settings_snapshot,
        )

        return jsonify(
            {
                "success": True,
                "research_id": research_id,
                "message": "Follow-up research started",
            }
        )

    except Exception:
        logger.exception("Error starting follow-up")
        return jsonify(
            {"success": False, "error": "An internal error has occurred."}
        ), 500
