"""Common query utilities for metrics module."""

from datetime import datetime, timedelta, UTC
from typing import Any

from sqlalchemy import Column


def get_time_filter_condition(period: str, timestamp_column: Column) -> Any:
    """Get SQLAlchemy condition for time filtering.

    Args:
        period: Time period ('7d', '30d', '3m', '1y', 'all')
        timestamp_column: SQLAlchemy timestamp column to filter on

    Returns:
        SQLAlchemy condition object or None for 'all'
    """
    if period == "all":
        return None
    elif period == "7d":
        cutoff = datetime.now(UTC) - timedelta(days=7)
    elif period == "30d":
        cutoff = datetime.now(UTC) - timedelta(days=30)
    elif period == "3m":
        cutoff = datetime.now(UTC) - timedelta(days=90)
    elif period == "1y":
        cutoff = datetime.now(UTC) - timedelta(days=365)
    else:
        # Default to 30 days for unknown periods
        cutoff = datetime.now(UTC) - timedelta(days=30)

    return timestamp_column >= cutoff


def get_research_mode_condition(research_mode: str, mode_column: Column) -> Any:
    """Get SQLAlchemy condition for research mode filtering.

    Args:
        research_mode: Research mode ('quick', 'detailed', 'all')
        mode_column: SQLAlchemy column to filter on

    Returns:
        SQLAlchemy condition object or None for 'all'
    """
    if research_mode == "all":
        return None
    elif research_mode in ["quick", "detailed"]:
        return mode_column == research_mode
    else:
        return None
