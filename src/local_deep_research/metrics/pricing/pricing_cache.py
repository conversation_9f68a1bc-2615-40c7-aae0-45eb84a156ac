"""
Pricing Cache System

Caches pricing data to avoid repeated API calls and improve performance.
Includes cache expiration and refresh mechanisms.
"""

import time
from typing import Any, Dict, Optional

from loguru import logger


class PricingCache:
    """Cache for LLM pricing data."""

    def __init__(self, cache_dir: Optional[str] = None, cache_ttl: int = 3600):
        """
        Initialize pricing cache.

        Args:
            cache_dir: Directory to store cache files (DEPRECATED - no longer used)
            cache_ttl: Cache time-to-live in seconds (default: 1 hour)
        """
        self.cache_ttl = cache_ttl
        # In-memory cache only - no file operations
        self._cache = {}
        logger.info("PricingCache initialized with in-memory storage only")

    def _load_cache(self):
        """DEPRECATED: No longer loads from disk."""
        pass

    def _save_cache(self):
        """DEPRECATED: No longer saves to disk."""
        pass

    def _is_expired(self, timestamp: float) -> bool:
        """Check if cache entry is expired."""
        return (time.time() - timestamp) > self.cache_ttl

    def get(self, key: str) -> Optional[Any]:
        """Get cached pricing data."""
        if key not in self._cache:
            return None

        entry = self._cache[key]
        if self._is_expired(entry["timestamp"]):
            # Remove expired entry
            del self._cache[key]
            return None

        return entry["data"]

    def set(self, key: str, data: Any):
        """Set cached pricing data."""
        self._cache[key] = {"data": data, "timestamp": time.time()}

    def get_model_pricing(self, model_name: str) -> Optional[Dict[str, float]]:
        """Get cached pricing for a specific model."""
        return self.get(f"model:{model_name}")

    def set_model_pricing(self, model_name: str, pricing: Dict[str, float]):
        """Cache pricing for a specific model."""
        self.set(f"model:{model_name}", pricing)

    def get_all_pricing(self) -> Optional[Dict[str, Dict[str, float]]]:
        """Get cached pricing for all models."""
        return self.get("all_models")

    def set_all_pricing(self, pricing: Dict[str, Dict[str, float]]):
        """Cache pricing for all models."""
        self.set("all_models", pricing)

    def clear(self):
        """Clear all cached data."""
        self._cache = {}
        logger.info("Pricing cache cleared")

    def clear_expired(self):
        """Remove expired cache entries."""
        expired_keys = []
        for key, entry in self._cache.items():
            if self._is_expired(entry["timestamp"]):
                expired_keys.append(key)

        for key in expired_keys:
            del self._cache[key]

        if expired_keys:
            logger.info(f"Removed {len(expired_keys)} expired cache entries")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        total_entries = len(self._cache)
        expired_count = 0

        for entry in self._cache.values():
            if self._is_expired(entry["timestamp"]):
                expired_count += 1

        return {
            "total_entries": total_entries,
            "expired_entries": expired_count,
            "valid_entries": total_entries - expired_count,
            "cache_type": "in-memory",
            "cache_ttl": self.cache_ttl,
        }
