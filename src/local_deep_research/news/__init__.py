"""
LDR News - AI-powered news aggregation and analysis module.
"""

# Import core classes
from ..database.models import (
    NewsSubscription as BaseSubscription,
    SubscriptionFolder,
    UserPreference,
    UserRating as Rating,
)
from .subscription_manager.scheduler import NewsScheduler, get_news_scheduler
from .folder_manager import FolderManager
from .subscription_manager import SearchSubscription, TopicSubscription

# Import API functions
from .api import (
    get_news_feed,
    research_news_item,
    save_news_preferences,
    get_news_categories,
)

__all__ = [
    # Core classes
    "BaseSubscription",
    "SubscriptionFolder",
    "UserPreference",
    "Rating",
    "NewsScheduler",
    "get_news_scheduler",
    "FolderManager",
    "SearchSubscription",
    "TopicSubscription",
    # API functions
    "get_news_feed",
    "research_news_item",
    "save_news_preferences",
    "get_news_categories",
]
