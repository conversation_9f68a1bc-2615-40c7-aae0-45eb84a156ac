"""
Flask blueprint for news system web routes.
"""

from flask import Blueprint, jsonify, render_template
from loguru import logger
from . import api

# get_db_setting not available in merged codebase - will use defaults


def create_news_blueprint():
    """
    Create Flask blueprint for news routes.

    Returns:
        Flask Blueprint instance with both page routes and API routes
    """
    bp = Blueprint("news", __name__)

    # Import the Flask API blueprint
    from .flask_api import news_api_bp

    # Register the API blueprint as sub-blueprint
    bp.register_blueprint(news_api_bp)

    # Page routes
    @bp.route("/")
    def news_page():
        """Render the main news page."""
        # Get available strategies for dropdown
        strategies = [
            "topic_based",
            "news_aggregation",
            "source_based",
            "focused_iteration",
        ]

        default_strategy = "topic_based"  # Default strategy

        return render_template(
            "pages/news.html",
            strategies=strategies,
            default_strategy=default_strategy,
        )

    @bp.route("/insights")
    def insights_page():
        """Render the news insights/transparency page."""
        return render_template("pages/news_insights.html")

    @bp.route("/preferences")
    def preferences_page():
        """Render the user preferences page."""
        return render_template("pages/news_preferences.html")

    @bp.route("/subscriptions")
    def subscriptions_page():
        """Render the subscriptions management page."""
        return render_template("pages/subscriptions.html")

    @bp.route("/subscriptions/new")
    def new_subscription_page():
        """Render the create subscription page."""
        from flask import session

        # Get username from session
        username = session.get("username", "anonymous")

        # Try to get settings from database, fall back to defaults
        default_settings = {
            "iterations": 3,
            "questions_per_iteration": 5,
            "search_engine": "auto",
            "model_provider": "OLLAMA",
            "model": "",
            "search_strategy": "source-based",
        }

        # Only try to get settings if user is logged in
        if username != "anonymous":
            # Load user settings using the extracted function
            from local_deep_research.database.session_context import (
                get_user_db_session,
            )

            with get_user_db_session(username) as db_session:
                load_user_settings(default_settings, db_session, username)

        return render_template(
            "pages/news-subscription-form.html",
            subscription=None,
            default_settings=default_settings,
        )

    @bp.route("/subscriptions/<subscription_id>/edit")
    def edit_subscription_page(subscription_id):
        """Render the edit subscription page."""
        from flask import session

        # Get username from session
        username = session.get("username", "anonymous")

        # Load subscription data
        subscription = None
        default_settings = {
            "iterations": 3,
            "questions_per_iteration": 5,
            "search_engine": "auto",
            "model_provider": "OLLAMA",
            "model": "",
            "search_strategy": "source-based",
        }

        try:
            # Load the subscription using the API
            subscription = api.get_subscription(subscription_id)
            logger.info(
                f"Loaded subscription {subscription_id}: {subscription}"
            )

            if not subscription:
                logger.warning(f"Subscription {subscription_id} not found")
                # Could redirect to 404 or subscriptions page
                return render_template(
                    "pages/news-subscription-form.html",
                    subscription=None,
                    error="Subscription not found",
                    default_settings=default_settings,
                )

            # Load user's default settings if logged in
            if username != "anonymous":
                # Load user settings using the extracted function
                from local_deep_research.database.session_context import (
                    get_user_db_session,
                )

                with get_user_db_session(username) as db_session:
                    load_user_settings(default_settings, db_session, username)

        except Exception as e:
            logger.exception(
                f"Error loading subscription {subscription_id}: {e}"
            )
            return render_template(
                "pages/news-subscription-form.html",
                subscription=None,
                error="Error loading subscription",
                default_settings=default_settings,
            )

        return render_template(
            "pages/news-subscription-form.html",
            subscription=subscription,
            default_settings=default_settings,
        )

    # Health check
    @bp.route("/health")
    def health_check():
        """Check if news system is healthy."""
        try:
            # Check if database is accessible
            from .core.storage_manager import StorageManager

            storage = StorageManager()

            # Try a simple query
            storage.get_user_feed("health_check", limit=1)

            return jsonify(
                {
                    "status": "healthy",
                    "enabled": True,  # Default: get_db_setting("news.enabled", True)
                    "database": "connected",
                }
            )
        except Exception:
            logger.exception("Health check failed")
            return jsonify(
                {
                    "status": "unhealthy",
                    "error": "An internal error has occurred.",
                }
            ), 500

    return bp


def load_user_settings(default_settings, db_session=None, username=None):
    """
    Load user settings and update default_settings dictionary.
    Extracted to avoid code duplication as suggested by djpetti.

    Args:
        default_settings: Dictionary to update with user settings
        db_session: Database session for accessing settings
        username: Username for settings context
    """
    if not db_session:
        logger.warning("No database session provided, using defaults")
        return

    try:
        from ..utilities.db_utils import get_settings_manager

        settings_manager = get_settings_manager(db_session, username)

        default_settings.update(
            {
                "iterations": settings_manager.get_setting(
                    "search.iterations", 3
                ),
                "questions_per_iteration": settings_manager.get_setting(
                    "search.questions_per_iteration", 5
                ),
                "search_engine": settings_manager.get_setting(
                    "search.tool", "auto"
                ),
                "model_provider": settings_manager.get_setting(
                    "llm.provider", "OLLAMA"
                ),
                "model": settings_manager.get_setting("llm.model", ""),
                "search_strategy": settings_manager.get_setting(
                    "search.search_strategy", "source-based"
                ),
                "custom_endpoint": settings_manager.get_setting(
                    "llm.openai_endpoint.url", ""
                ),
            }
        )
    except Exception as e:
        logger.warning(f"Could not load user settings: {e}")
        # Use defaults
