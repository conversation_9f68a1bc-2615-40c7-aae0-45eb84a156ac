"""
Simple folder manager for subscription organization.
Handles folder CRUD operations for the API routes.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from ..database.models import (
    SubscriptionFolder,
    NewsSubscription as BaseSubscription,
)


class FolderManager:
    """Manages subscription folders and organization."""

    def __init__(self, session: Session):
        self.session = session

    def get_user_folders(self, user_id: str) -> List[SubscriptionFolder]:
        """Get all folders for a user (user_id parameter kept for API compatibility)."""
        # In per-user databases, all folders belong to the current user
        return (
            self.session.query(SubscriptionFolder)
            .order_by(SubscriptionFolder.name)
            .all()
        )

    def create_folder(
        self, name: str, description: Optional[str] = None
    ) -> SubscriptionFolder:
        """Create a new folder."""
        import uuid

        folder = SubscriptionFolder(
            id=str(uuid.uuid4()),
            name=name,
            description=description,
        )
        self.session.add(folder)
        self.session.commit()
        return folder

    def update_folder(
        self, folder_id: int, **kwargs
    ) -> Optional[SubscriptionFolder]:
        """Update a folder."""
        folder = self.session.query(SubscriptionFolder).get(folder_id)
        if not folder:
            return None

        for key, value in kwargs.items():
            if hasattr(folder, key) and key not in [
                "id",
                "created_at",
            ]:
                setattr(folder, key, value)

        folder.updated_at = datetime.now(timezone.utc)
        self.session.commit()
        return folder

    def delete_folder(
        self, folder_id: int, move_to: Optional[str] = None
    ) -> bool:
        """Delete a folder, optionally moving subscriptions to another folder."""
        folder = self.session.query(SubscriptionFolder).get(folder_id)
        if not folder:
            return False

        # Move subscriptions if specified
        if move_to:
            self.session.query(BaseSubscription).filter_by(
                folder=folder.name
            ).update({"folder": move_to})
        else:
            # Set to None if no target folder
            self.session.query(BaseSubscription).filter_by(
                folder=folder.name
            ).update({"folder": None})

        self.session.delete(folder)
        self.session.commit()
        return True

    def get_subscriptions_by_folder(self, user_id: str) -> Dict[str, Any]:
        """Get subscriptions organized by folder."""
        folders = self.get_user_folders(user_id)

        result = {"folders": [], "uncategorized": []}

        # Get subscriptions for each folder
        for folder in folders:
            subs = (
                self.session.query(BaseSubscription)
                .filter_by(folder=folder.name, status="active")
                .all()
            )

            result["folders"].append(
                {
                    "folder": folder.to_dict(),
                    "subscriptions": [self._sub_to_dict(s) for s in subs],
                }
            )

        # Get uncategorized subscriptions
        uncategorized = (
            self.session.query(BaseSubscription)
            .filter_by(folder=None, status="active")
            .all()
        )

        result["uncategorized"] = [self._sub_to_dict(s) for s in uncategorized]

        return result

    def update_subscription(
        self, subscription_id: str, **kwargs
    ) -> Optional[BaseSubscription]:
        """Update a subscription."""
        sub = (
            self.session.query(BaseSubscription)
            .filter_by(id=subscription_id)
            .first()
        )
        if not sub:
            return None

        for key, value in kwargs.items():
            if hasattr(sub, key) and key not in ["id", "created_at"]:
                setattr(sub, key, value)

        # Recalculate next_refresh if refresh_interval_minutes changed
        if "refresh_interval_minutes" in kwargs:
            from datetime import timedelta
            from loguru import logger

            old_next = sub.next_refresh
            new_minutes = kwargs["refresh_interval_minutes"]

            if sub.last_refresh:
                sub.next_refresh = sub.last_refresh + timedelta(
                    minutes=new_minutes
                )
                logger.info(
                    f"Updated subscription {sub.id} next_refresh based on last_refresh: {sub.last_refresh} + {new_minutes}min = {sub.next_refresh}"
                )
            else:
                # If no last_refresh, calculate from now
                sub.next_refresh = datetime.now(timezone.utc) + timedelta(
                    minutes=new_minutes
                )
                logger.info(
                    f"Updated subscription {sub.id} next_refresh from now: {sub.next_refresh}"
                )

            logger.info(
                f"Subscription {sub.id} next_refresh changed from {old_next} to {sub.next_refresh}"
            )

        sub.updated_at = datetime.now(timezone.utc)
        self.session.commit()
        return sub

    def delete_subscription(self, subscription_id: str) -> bool:
        """Delete a subscription."""
        sub = (
            self.session.query(BaseSubscription)
            .filter_by(id=subscription_id)
            .first()
        )
        if not sub:
            return False

        self.session.delete(sub)
        self.session.commit()
        return True

    def get_subscription_stats(self, user_id: str) -> Dict[str, Any]:
        """Get subscription statistics for a user (user_id kept for API compatibility)."""
        # In per-user databases, all data belongs to the current user
        total = self.session.query(BaseSubscription).count()

        active = (
            self.session.query(BaseSubscription)
            .filter_by(status="active")
            .count()
        )

        by_type = {}
        for sub_type in ["search", "topic"]:
            count = (
                self.session.query(BaseSubscription)
                .filter_by(subscription_type=sub_type, status="active")
                .count()
            )
            by_type[sub_type] = count

        return {
            "total": total,
            "active": active,
            "by_type": by_type,
            "folders": len(self.get_user_folders(user_id)),
        }

    def _sub_to_dict(self, sub: BaseSubscription) -> Dict[str, Any]:
        """Convert subscription to dictionary."""
        return {
            "id": sub.id,
            "type": sub.subscription_type,
            "query_or_topic": sub.query_or_topic,
            "created_at": sub.created_at.isoformat()
            if sub.created_at
            else None,
            "last_refresh": sub.last_refresh.isoformat()
            if sub.last_refresh
            else None,
            "next_refresh": sub.next_refresh.isoformat()
            if sub.next_refresh
            else None,
            "refresh_interval_minutes": sub.refresh_interval_minutes,
            "status": sub.status,
        }
