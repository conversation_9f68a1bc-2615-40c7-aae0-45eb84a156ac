"""
Activity-based news subscription scheduler for per-user encrypted databases.
Tracks user activity and temporarily stores credentials for automatic updates.
"""

import random
import threading
from datetime import datetime, timedelta, UTC
from typing import Any, Dict, List

from loguru import logger
from local_deep_research.settings.logger import log_settings

from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.jobstores.base import JobLookupError


SCHEDULER_AVAILABLE = True  # Always available since it's a required dependency


class NewsScheduler:
    """
    Singleton scheduler that manages news subscriptions for active users.

    This scheduler:
    - Monitors user activity through database access
    - Temporarily stores user credentials in memory
    - Automatically schedules subscription checks
    - Cleans up inactive users after configurable period
    """

    _instance = None
    _lock = threading.Lock()

    def __new__(cls):
        """Ensure singleton instance."""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the scheduler (only runs once due to singleton)."""
        # Skip if already initialized
        if hasattr(self, "_initialized"):
            return

        # User session tracking
        self.user_sessions = {}  # user_id -> {password, last_activity, scheduled_jobs}
        self.lock = threading.Lock()

        # Scheduler instance
        self.scheduler = BackgroundScheduler()

        # Configuration (will be loaded from settings)
        self.config = self._load_default_config()

        # State
        self.is_running = False

        self._initialized = True
        logger.info("News scheduler initialized")

    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration (will be overridden by settings manager)."""
        return {
            "enabled": True,
            "retention_hours": 48,
            "cleanup_interval_hours": 1,
            "max_jitter_seconds": 300,
            "max_concurrent_jobs": 10,
            "subscription_batch_size": 5,
            "activity_check_interval_minutes": 5,
        }

    def initialize_with_settings(self, settings_manager):
        """Initialize configuration from settings manager."""
        try:
            # Load all scheduler settings
            self.settings_manager = settings_manager
            self.config = {
                "enabled": self._get_setting("news.scheduler.enabled", True),
                "retention_hours": self._get_setting(
                    "news.scheduler.retention_hours", 48
                ),
                "cleanup_interval_hours": self._get_setting(
                    "news.scheduler.cleanup_interval_hours", 1
                ),
                "max_jitter_seconds": self._get_setting(
                    "news.scheduler.max_jitter_seconds", 300
                ),
                "max_concurrent_jobs": self._get_setting(
                    "news.scheduler.max_concurrent_jobs", 10
                ),
                "subscription_batch_size": self._get_setting(
                    "news.scheduler.batch_size", 5
                ),
                "activity_check_interval_minutes": self._get_setting(
                    "news.scheduler.activity_check_interval", 5
                ),
            }
            log_settings(self.config, "Scheduler configuration loaded")
        except Exception:
            logger.exception("Error loading scheduler settings")
            # Keep default config

    def _get_setting(self, key: str, default: Any) -> Any:
        """Get setting with fallback to default."""
        if hasattr(self, "settings_manager") and self.settings_manager:
            return self.settings_manager.get_setting(key, default=default)
        return default

    def start(self):
        """Start the scheduler."""
        if not self.config.get("enabled", True):
            logger.info("News scheduler is disabled in settings")
            return

        if self.is_running:
            logger.warning("Scheduler is already running")
            return

        # Schedule cleanup job
        self.scheduler.add_job(
            self._run_cleanup_with_tracking,
            "interval",
            hours=self.config["cleanup_interval_hours"],
            id="cleanup_inactive_users",
            name="Cleanup Inactive User Sessions",
            jitter=60,  # Add some jitter to cleanup
        )

        # Schedule configuration reload
        self.scheduler.add_job(
            self._reload_config,
            "interval",
            minutes=30,
            id="reload_config",
            name="Reload Configuration",
        )

        # Start the scheduler
        self.scheduler.start()
        self.is_running = True

        # Schedule initial cleanup after a delay
        self.scheduler.add_job(
            self._run_cleanup_with_tracking,
            "date",
            run_date=datetime.now(UTC) + timedelta(seconds=30),
            id="initial_cleanup",
        )

        logger.info("News scheduler started")

    def stop(self):
        """Stop the scheduler."""
        if self.is_running:
            self.scheduler.shutdown(wait=True)
            self.is_running = False

            # Clear all user sessions
            with self.lock:
                self.user_sessions.clear()

            logger.info("News scheduler stopped")

    def update_user_info(self, username: str, password: str):
        """
        Update user info in scheduler. Called on every database interaction.

        Args:
            username: User's username
            password: User's password
        """
        logger.info(
            f"update_user_info called for {username}, is_running={self.is_running}"
        )
        if not self.is_running:
            logger.warning(
                f"Scheduler not running, cannot update user {username}"
            )
            return

        with self.lock:
            now = datetime.now(UTC)

            if username not in self.user_sessions:
                # New user - create session info
                logger.info(f"New user in scheduler: {username}")
                self.user_sessions[username] = {
                    "password": password,
                    "last_activity": now,
                    "scheduled_jobs": set(),
                }
                # Schedule their subscriptions
                self._schedule_user_subscriptions(username)
            else:
                # Existing user - update info
                logger.info(
                    f"Updating existing user {username} activity, will reschedule"
                )
                self.user_sessions[username]["password"] = password
                self.user_sessions[username]["last_activity"] = now
                # Reschedule their subscriptions in case they changed
                self._schedule_user_subscriptions(username)

    def unregister_user(self, username: str):
        """
        Unregister a user and clean up their scheduled jobs.
        Called when user logs out.
        """
        with self.lock:
            if username in self.user_sessions:
                logger.info(f"Unregistering user {username}")

                # Remove all scheduled jobs for this user
                session_info = self.user_sessions[username]
                for job_id in session_info["scheduled_jobs"].copy():
                    try:
                        self.scheduler.remove_job(job_id)
                    except JobLookupError:
                        pass

                # Remove user session
                del self.user_sessions[username]
                logger.info(f"User {username} unregistered successfully")

    def _schedule_user_subscriptions(self, username: str):
        """Schedule all active subscriptions for a user."""
        logger.info(f"_schedule_user_subscriptions called for {username}")
        try:
            session_info = self.user_sessions.get(username)
            if not session_info:
                logger.warning(f"No session info found for {username}")
                return

            password = session_info["password"]
            logger.debug(
                f"Got password for {username}, length: {len(password) if password else 0}"
            )

            # Get user's subscriptions from their encrypted database
            from ...database.session_context import get_user_db_session
            from ...database.models.news import NewsSubscription

            with get_user_db_session(username, password) as db:
                subscriptions = (
                    db.query(NewsSubscription).filter_by(is_active=True).all()
                )
                logger.debug(
                    f"Query executed, found {len(subscriptions)} results"
                )

                # Log details of each subscription
                for sub in subscriptions:
                    logger.debug(
                        f"Subscription {sub.id}: name='{sub.name}', is_active={sub.is_active}, status='{sub.status}', refresh_interval={sub.refresh_interval_minutes} minutes"
                    )

            logger.info(
                f"Found {len(subscriptions)} active subscriptions for {username}"
            )

            # Clear old jobs for this user
            for job_id in session_info["scheduled_jobs"].copy():
                try:
                    self.scheduler.remove_job(job_id)
                    session_info["scheduled_jobs"].remove(job_id)
                except JobLookupError:
                    pass

            # Schedule each subscription with jitter
            for sub in subscriptions:
                job_id = f"{username}_{sub.id}"

                # Calculate jitter
                max_jitter = int(self.config.get("max_jitter_seconds", 300))
                jitter = random.randint(0, max_jitter)

                # Determine trigger based on frequency
                refresh_minutes = sub.refresh_interval_minutes

                if refresh_minutes <= 60:  # 60 minutes or less
                    # For hourly or more frequent, use interval trigger
                    trigger = "interval"
                    trigger_args = {
                        "minutes": refresh_minutes,
                        "jitter": jitter,
                        "start_date": datetime.now(UTC),  # Start immediately
                    }
                else:
                    # For less frequent, calculate next run time
                    now = datetime.now(UTC)
                    if sub.next_refresh:
                        # Convert to timezone-aware for comparison
                        next_refresh_aware = sub.next_refresh.replace(
                            tzinfo=None
                        )
                        if next_refresh_aware <= now:
                            # Subscription is overdue - run it immediately with small jitter
                            logger.info(
                                f"Subscription {sub.id} is overdue, scheduling immediate run"
                            )
                            next_run = now + timedelta(seconds=jitter)
                        else:
                            next_run = next_refresh_aware
                    else:
                        next_run = now + timedelta(
                            minutes=refresh_minutes, seconds=jitter
                        )

                    trigger = "date"
                    trigger_args = {"run_date": next_run}

                # Add the job
                job = self.scheduler.add_job(
                    func=self._check_subscription,
                    args=[username, sub.id],
                    trigger=trigger,
                    id=job_id,
                    name=f"Check {sub.name or sub.query_or_topic[:30]}",
                    replace_existing=True,
                    **trigger_args,
                )

                session_info["scheduled_jobs"].add(job_id)
                logger.info(
                    f"Scheduled job {job_id} with {trigger} trigger, next run: {job.next_run_time}"
                )

        except Exception as e:
            logger.exception(
                f"Error scheduling subscriptions for {username}: {e}"
            )

    def _check_user_overdue_subscriptions(self, username: str):
        """Check and immediately run any overdue subscriptions for a user."""
        try:
            session_info = self.user_sessions.get(username)
            if not session_info:
                return

            password = session_info["password"]

            # Get user's overdue subscriptions
            from ...database.session_context import get_user_db_session
            from ...database.models.news import NewsSubscription
            from datetime import timezone

            with get_user_db_session(username, password) as db:
                now = datetime.now(timezone.utc)
                overdue_subs = (
                    db.query(NewsSubscription)
                    .filter(
                        NewsSubscription.is_active.is_(True),
                        NewsSubscription.next_refresh.is_not(None),
                        NewsSubscription.next_refresh <= now,
                    )
                    .all()
                )

            if overdue_subs:
                logger.info(
                    f"Found {len(overdue_subs)} overdue subscriptions for {username}"
                )

                for sub in overdue_subs:
                    # Run immediately with small random delay
                    delay_seconds = random.randint(1, 30)
                    job_id = (
                        f"overdue_{username}_{sub.id}_{int(now.timestamp())}"
                    )

                    self.scheduler.add_job(
                        func=self._check_subscription,
                        args=[username, sub.id],
                        trigger="date",
                        run_date=now + timedelta(seconds=delay_seconds),
                        id=job_id,
                        name=f"Overdue: {sub.name or sub.query_or_topic[:30]}",
                        replace_existing=True,
                    )

                    logger.info(
                        f"Scheduled overdue subscription {sub.id} to run in {delay_seconds} seconds"
                    )

        except Exception as e:
            logger.exception(
                f"Error checking overdue subscriptions for {username}: {e}"
            )

    def _check_subscription(self, username: str, subscription_id: int):
        """Check and refresh a single subscription."""
        logger.info(
            f"_check_subscription called for user {username}, subscription {subscription_id}"
        )
        try:
            session_info = self.user_sessions.get(username)
            if not session_info:
                # User no longer active, cancel job
                job_id = f"{username}_{subscription_id}"
                try:
                    self.scheduler.remove_job(job_id)
                except JobLookupError:
                    pass
                return

            password = session_info["password"]

            # Get subscription details
            from ...database.session_context import get_user_db_session
            from ...database.models.news import NewsSubscription

            with get_user_db_session(username, password) as db:
                sub = db.query(NewsSubscription).get(subscription_id)
                if not sub or not sub.is_active:
                    logger.info(
                        f"Subscription {subscription_id} not active, skipping"
                    )
                    return

                # Prepare query with date replacement
                query = sub.query_or_topic
                if "YYYY-MM-DD" in query:
                    query = query.replace(
                        "YYYY-MM-DD", datetime.now(UTC).date().isoformat()
                    )

                # Update last/next refresh times
                sub.last_refresh = datetime.now(UTC)
                sub.next_refresh = datetime.now(UTC) + timedelta(
                    minutes=sub.refresh_interval_minutes
                )
                db.commit()

                subscription_data = {
                    "id": sub.id,
                    "name": sub.name,
                    "query": query,
                    "original_query": sub.query_or_topic,
                    "model_provider": sub.model_provider,
                    "model": sub.model,
                    "search_strategy": sub.search_strategy,
                    "search_engine": sub.search_engine,
                }

            logger.info(
                f"Refreshing subscription {subscription_id}: {subscription_data['name']}"
            )

            # Trigger research synchronously using requests with proper auth
            self._trigger_subscription_research_sync(
                username, subscription_data
            )

            # Reschedule for next interval if using interval trigger
            job_id = f"{username}_{subscription_id}"
            job = self.scheduler.get_job(job_id)
            if job and job.trigger.__class__.__name__ == "DateTrigger":
                # For date triggers, reschedule
                next_run = datetime.now(UTC) + timedelta(
                    minutes=sub.refresh_interval_minutes,
                    seconds=random.randint(
                        0, int(self.config.get("max_jitter_seconds", 300))
                    ),
                )
                self.scheduler.add_job(
                    func=self._check_subscription,
                    args=[username, subscription_id],
                    trigger="date",
                    run_date=next_run,
                    id=job_id,
                    replace_existing=True,
                )

        except Exception as e:
            logger.exception(
                f"Error checking subscription {subscription_id}: {e}"
            )

    def _trigger_subscription_research_sync(
        self, username: str, subscription: Dict[str, Any]
    ):
        """Trigger research for a subscription using programmatic API."""
        try:
            # Get user's password from session info
            session_info = self.user_sessions.get(username)
            if not session_info:
                logger.error(f"No session info for user {username}")
                return

            password = session_info["password"]

            # Generate research ID
            import uuid

            research_id = str(uuid.uuid4())

            logger.info(
                f"Starting research {research_id} for subscription {subscription['id']}"
            )

            # Get user settings for research
            from ...database.session_context import get_user_db_session
            from ...settings.manager import SettingsManager

            with get_user_db_session(username, password) as db:
                settings_manager = SettingsManager(db)
                settings_snapshot = settings_manager.get_settings_snapshot()

                # Use the search engine from the subscription if specified
                search_engine = subscription.get("search_engine")

                if search_engine:
                    settings_snapshot["search.tool"] = search_engine
                    logger.info(
                        f"Using subscription's search engine: '{search_engine}' for {subscription['id']}"
                    )
                else:
                    # Use the user's default search tool from their settings
                    default_search_tool = settings_snapshot.get(
                        "search.tool", "auto"
                    )
                    logger.info(
                        f"Using user's default search tool: '{default_search_tool}' for {subscription['id']}"
                    )

                logger.debug(
                    f"Settings snapshot has {len(settings_snapshot)} settings"
                )
                # Log a few key settings to verify they're present
                logger.debug(
                    f"Key settings: llm.model={settings_snapshot.get('llm.model')}, llm.provider={settings_snapshot.get('llm.provider')}, search.tool={settings_snapshot.get('search.tool')}"
                )

            # Set up research parameters
            query = subscription["query"]

            # Build metadata for news search
            metadata = {
                "is_news_search": True,
                "search_type": "news_analysis",
                "display_in": "news_feed",
                "subscription_id": subscription["id"],
                "triggered_by": "scheduler",
                "subscription_name": subscription["name"],
                "title": subscription["name"] if subscription["name"] else None,
                "scheduled_at": datetime.now(UTC).isoformat(),
                "original_query": subscription["original_query"],
                "user_id": username,
            }

            # Use programmatic API with settings context
            from ...api.research_functions import quick_summary
            from ...config.thread_settings import set_settings_context

            # Create and set settings context for this thread
            class SettingsContext:
                def __init__(self, snapshot):
                    self.snapshot = snapshot or {}
                    self.values = {}
                    for key, setting in self.snapshot.items():
                        if isinstance(setting, dict) and "value" in setting:
                            self.values[key] = setting["value"]
                        else:
                            self.values[key] = setting

                def get_setting(self, key, default=None):
                    """Get setting from snapshot only"""
                    return self.values.get(key, default)

            # Set the context for this thread
            settings_context = SettingsContext(settings_snapshot)
            set_settings_context(settings_context)

            # Get search strategy from subscription data (for the API call)
            search_strategy = subscription.get(
                "search_strategy", "news_aggregation"
            )

            # Call quick_summary with appropriate parameters
            result = quick_summary(
                query=query,
                research_id=research_id,
                username=username,
                user_password=password,
                settings_snapshot=settings_snapshot,
                search_strategy=search_strategy,
                model_name=subscription.get("model"),
                provider=subscription.get("model_provider"),
                iterations=1,  # Single iteration for news
                metadata=metadata,
                search_original_query=False,  # Don't send long subscription prompts to search engines
            )

            logger.info(
                f"Completed research {research_id} for subscription {subscription['id']}"
            )

            # Store the research result in the database
            self._store_research_result(
                username,
                password,
                research_id,
                subscription["id"],
                result,
                subscription,
            )

        except Exception as e:
            logger.exception(
                f"Error triggering research for subscription {subscription['id']}: {e}"
            )

    def _store_research_result(
        self,
        username: str,
        password: str,
        research_id: str,
        subscription_id: int,
        result: Dict[str, Any],
        subscription: Dict[str, Any],
    ):
        """Store research result in database for news display."""
        try:
            from ...database.session_context import get_user_db_session
            from ...database.models import ResearchHistory
            from ...settings.manager import SettingsManager
            import json

            # Convert result to JSON-serializable format
            def make_serializable(obj):
                """Convert non-serializable objects to dictionaries."""
                if hasattr(obj, "dict"):
                    return obj.dict()
                elif hasattr(obj, "__dict__"):
                    return {
                        k: make_serializable(v)
                        for k, v in obj.__dict__.items()
                        if not k.startswith("_")
                    }
                elif isinstance(obj, (list, tuple)):
                    return [make_serializable(item) for item in obj]
                elif isinstance(obj, dict):
                    return {k: make_serializable(v) for k, v in obj.items()}
                else:
                    return obj

            serializable_result = make_serializable(result)

            with get_user_db_session(username, password) as db:
                # Get user settings to store in metadata
                settings_manager = SettingsManager(db)
                settings_snapshot = settings_manager.get_settings_snapshot()

                # Get the report content - check both 'report' and 'summary' fields
                report_content = serializable_result.get(
                    "report"
                ) or serializable_result.get("summary")
                logger.debug(
                    f"Report content length: {len(report_content) if report_content else 0} chars"
                )

                # Extract sources/links from the result
                sources = serializable_result.get("sources", [])

                # First add the sources/references section if we have sources
                if report_content and sources:
                    # Import utilities for formatting links
                    from ...utilities.search_utilities import (
                        format_links_to_markdown,
                    )

                    # Format the links/citations
                    formatted_links = format_links_to_markdown(sources)

                    # Add references section to the report
                    if formatted_links:
                        report_content = f"{report_content}\n\n## Sources\n\n{formatted_links}"

                # Then format citations in the report content
                if report_content:
                    # Import citation formatter
                    from ...text_optimization.citation_formatter import (
                        CitationFormatter,
                        CitationMode,
                    )
                    from ...config.search_config import (
                        get_setting_from_snapshot,
                    )

                    # Get citation format from settings
                    citation_format = get_setting_from_snapshot(
                        "report.citation_format", "domain_id_hyperlinks"
                    )
                    mode_map = {
                        "number_hyperlinks": CitationMode.NUMBER_HYPERLINKS,
                        "domain_hyperlinks": CitationMode.DOMAIN_HYPERLINKS,
                        "domain_id_hyperlinks": CitationMode.DOMAIN_ID_HYPERLINKS,
                        "domain_id_always_hyperlinks": CitationMode.DOMAIN_ID_ALWAYS_HYPERLINKS,
                        "no_hyperlinks": CitationMode.NO_HYPERLINKS,
                    }
                    mode = mode_map.get(
                        citation_format, CitationMode.DOMAIN_ID_HYPERLINKS
                    )
                    formatter = CitationFormatter(mode=mode)

                    # Format citations within the content
                    report_content = formatter.format_document(report_content)

                if not report_content:
                    # If neither field exists, use the full result as JSON
                    report_content = json.dumps(serializable_result)

                # Generate headline and topics for news searches
                from ...news.utils.headline_generator import generate_headline
                from ...news.utils.topic_generator import generate_topics

                query_text = result.get(
                    "query", subscription.get("query", "News Update")
                )

                # Generate headline from the actual research findings
                logger.info(
                    f"Generating headline for subscription {subscription_id}"
                )
                generated_headline = generate_headline(
                    query=query_text,
                    findings=report_content,
                    max_length=200,  # Allow longer headlines for news
                )

                # Generate topics from the findings
                logger.info(
                    f"Generating topics for subscription {subscription_id}"
                )
                generated_topics = generate_topics(
                    query=query_text,
                    findings=report_content,
                    category=subscription.get("name", "News"),
                    max_topics=6,
                )

                logger.info(
                    f"Generated headline: {generated_headline}, topics: {generated_topics}"
                )

                # Get subscription name for metadata
                subscription_name = subscription.get("name", "")

                # Use generated headline as title, or fallback
                if generated_headline:
                    title = generated_headline
                else:
                    if subscription_name:
                        title = f"{subscription_name} - {datetime.now(UTC).isoformat(timespec='minutes')}"
                    else:
                        title = f"{query_text[:60]}... - {datetime.now(UTC).isoformat(timespec='minutes')}"

                # Create research history entry
                history_entry = ResearchHistory(
                    id=research_id,
                    query=result.get("query", ""),
                    mode="news_subscription",
                    status="completed",
                    created_at=datetime.now(UTC).isoformat(),
                    completed_at=datetime.now(UTC).isoformat(),
                    title=title,
                    research_meta={
                        "subscription_id": subscription_id,
                        "triggered_by": "scheduler",
                        "is_news_search": True,
                        "username": username,
                        "subscription_name": subscription_name,  # Store subscription name for display
                        "settings_snapshot": settings_snapshot,  # Store settings snapshot for later retrieval
                        "generated_headline": generated_headline,  # Store generated headline for news display
                        "generated_topics": generated_topics,  # Store topics for categorization
                    },
                )
                db.add(history_entry)
                db.commit()

                # Store the report content using storage abstraction
                from ...storage import get_report_storage

                # Use storage to save the report (report_content already retrieved above)
                storage = get_report_storage(session=db)
                storage.save_report(
                    research_id=research_id,
                    content=report_content,
                    username=username,
                )

                logger.info(
                    f"Stored research result {research_id} for subscription {subscription_id}"
                )

        except Exception:
            logger.exception("Error storing research result")

    def _run_cleanup_with_tracking(self):
        """Wrapper that tracks cleanup execution."""

        try:
            cleaned_count = self._cleanup_inactive_users()

            logger.info(
                f"Cleanup successful: removed {cleaned_count} inactive users"
            )

        except Exception:
            logger.exception("Cleanup job failed")

    def _cleanup_inactive_users(self) -> int:
        """Remove users inactive for longer than retention period."""
        retention_hours = self.config.get("retention_hours", 48)
        cutoff = datetime.now(UTC) - timedelta(hours=retention_hours)

        cleaned_count = 0

        with self.lock:
            inactive_users = [
                user_id
                for user_id, session in self.user_sessions.items()
                if session["last_activity"] < cutoff
            ]

            for user_id in inactive_users:
                # Remove all scheduled jobs
                for job_id in self.user_sessions[user_id]["scheduled_jobs"]:
                    try:
                        self.scheduler.remove_job(job_id)
                    except JobLookupError:
                        pass

                # Clear password from memory
                del self.user_sessions[user_id]
                cleaned_count += 1
                logger.info(f"Cleaned up inactive user {user_id}")

        return cleaned_count

    def _reload_config(self):
        """Reload configuration from settings manager."""
        if not hasattr(self, "settings_manager") or not self.settings_manager:
            return

        try:
            old_retention = self.config.get("retention_hours", 48)

            # Reload all settings
            for key in self.config:
                if key == "enabled":
                    continue  # Don't change enabled state while running

                full_key = f"news.scheduler.{key}"
                self.config[key] = self._get_setting(full_key, self.config[key])

            # Handle changes that need immediate action
            if old_retention != self.config["retention_hours"]:
                logger.info(
                    f"Retention period changed from {old_retention} "
                    f"to {self.config['retention_hours']} hours"
                )
                # Trigger immediate cleanup with new retention
                self.scheduler.add_job(
                    self._run_cleanup_with_tracking,
                    "date",
                    run_date=datetime.now(UTC) + timedelta(seconds=5),
                    id="immediate_cleanup_config_change",
                )

        except Exception:
            logger.exception("Error reloading configuration")

    def get_status(self) -> Dict[str, Any]:
        """Get scheduler status information."""
        with self.lock:
            active_users = len(self.user_sessions)
            total_jobs = sum(
                len(session["scheduled_jobs"])
                for session in self.user_sessions.values()
            )

        # Get next run time for cleanup job
        next_cleanup = None
        if self.is_running:
            job = self.scheduler.get_job("cleanup_inactive_users")
            if job:
                next_cleanup = job.next_run_time

        return {
            "is_running": self.is_running,
            "config": self.config,
            "active_users": active_users,
            "total_scheduled_jobs": total_jobs,
            "next_cleanup": next_cleanup.isoformat() if next_cleanup else None,
            "memory_usage": self._estimate_memory_usage(),
        }

    def _estimate_memory_usage(self) -> int:
        """Estimate memory usage of user sessions."""

        # Rough estimate: username (50) + password (100) + metadata (200) per user
        per_user_estimate = 350
        return len(self.user_sessions) * per_user_estimate

    def get_user_sessions_summary(self) -> List[Dict[str, Any]]:
        """Get summary of active user sessions (without passwords)."""
        with self.lock:
            summary = []
            for user_id, session in self.user_sessions.items():
                summary.append(
                    {
                        "user_id": user_id,
                        "last_activity": session["last_activity"].isoformat(),
                        "scheduled_jobs": len(session["scheduled_jobs"]),
                        "time_since_activity": str(
                            datetime.now(UTC) - session["last_activity"]
                        ),
                    }
                )
            return summary


# Singleton instance getter
_scheduler_instance = None


def get_news_scheduler() -> NewsScheduler:
    """Get the singleton news scheduler instance."""
    global _scheduler_instance
    if _scheduler_instance is None:
        _scheduler_instance = NewsScheduler()
    return _scheduler_instance
