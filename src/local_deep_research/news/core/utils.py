"""
Core utilities for the news system.
"""

import uuid
from datetime import datetime, timezone


def generate_card_id() -> str:
    """
    Generate a unique ID for a news card using UUID.

    Returns:
        str: A unique UUID string
    """
    return str(uuid.uuid4())


def generate_subscription_id() -> str:
    """
    Generate a unique ID for a subscription.

    Returns:
        str: A unique UUID string
    """
    return str(uuid.uuid4())


def utc_now() -> datetime:
    """
    Get current UTC time with timezone awareness.

    Returns:
        datetime: Current UTC time
    """
    return datetime.now(timezone.utc)


def hours_ago(dt: datetime) -> float:
    """
    Calculate how many hours ago a datetime was.

    Args:
        dt: The datetime to compare

    Returns:
        float: Number of hours ago (negative if in future)
    """
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=timezone.utc)

    delta = utc_now() - dt
    return delta.total_seconds() / 3600
