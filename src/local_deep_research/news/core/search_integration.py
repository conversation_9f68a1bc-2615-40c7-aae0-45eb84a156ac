"""
Integration with LDR's search system to track searches and manage priorities.
"""

import uuid
from typing import Optional, Dict, Any, Callable
from datetime import datetime, timezone
from loguru import logger


class NewsSearchCallback:
    """
    Callback handler for search system integration.
    Tracks searches for news personalization.
    """

    def __init__(self):
        self._tracking_enabled = None

    @property
    def tracking_enabled(self) -> bool:
        """Check if search tracking is enabled."""
        if self._tracking_enabled is None:
            # TODO: Per-user settings will be handled later
            self._tracking_enabled = False  # Default: news.search_tracking
        return self._tracking_enabled

    def __call__(
        self,
        query: str,
        result: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Process a search completion.

        Args:
            query: The search query
            result: The search results
            context: Optional context (includes is_user_search, user_id, etc.)
        """

        # Extract context
        context = context or {}
        is_user_search = context.get("is_user_search", True)
        user_id = context.get("user_id", "anonymous")
        search_id = context.get("search_id", str(uuid.uuid4()))

        # Log the search if tracking is enabled
        if is_user_search and self.tracking_enabled:
            self._track_user_search(
                search_id=search_id, user_id=user_id, query=query, result=result
            )

    def _track_user_search(
        self, search_id: str, user_id: str, query: str, result: Dict[str, Any]
    ) -> None:
        """
        Track a user search for personalization.

        Args:
            search_id: Unique search ID
            user_id: User who performed the search
            query: The search query
            result: The search results
        """
        try:
            # Import here to avoid circular imports
            from ..preference_manager.search_tracker import SearchTracker

            tracker = SearchTracker()
            tracker.track_search(
                user_id=user_id,
                query=query,
                search_id=search_id,
                result_quality=self._calculate_quality(result),
                result_count=len(result.get("findings", [])),
                strategy_used=result.get("strategy", "unknown"),
            )

            logger.debug(f"Tracked user search: {query[:50]}...")

        except Exception:
            logger.exception("Error tracking search")

    def _calculate_quality(self, result: Dict[str, Any]) -> float:
        """
        Calculate quality score for search results.

        Args:
            result: Search results

        Returns:
            Quality score between 0 and 1
        """
        # Simple heuristic based on result count and content
        findings = result.get("findings", [])
        if not findings:
            return 0.0

        # More findings generally means better results
        count_score = min(len(findings) / 10, 1.0)

        # Check if we have actual content
        has_content = any(f.get("content") for f in findings[:5])
        content_score = 1.0 if has_content else 0.5

        return (count_score + content_score) / 2


def create_search_wrapper(original_search_method: Callable) -> Callable:
    """
    Create a wrapper for search methods that integrates news tracking.

    Args:
        original_search_method: The original search method to wrap

    Returns:
        Wrapped method with news tracking
    """
    callback = NewsSearchCallback()

    def wrapped_search(self, query: str, **kwargs) -> Dict[str, Any]:
        """Wrapped search with news tracking."""

        # Determine if this is a user search
        is_user_search = kwargs.pop("is_user_search", True)
        is_news_search = kwargs.pop("is_news_search", False)
        user_id = kwargs.pop("user_id", "anonymous")

        # Generate search ID
        search_id = str(uuid.uuid4())

        # Build context
        context = {
            "is_user_search": is_user_search and not is_news_search,
            "is_news_search": is_news_search,
            "user_id": user_id,
            "search_id": search_id,
            "timestamp": datetime.now(timezone.utc),
        }

        # Perform the search
        result = original_search_method(self, query, **kwargs)

        # Call callback if available
        try:
            callback(query, result, context)
        except Exception:
            logger.exception("Error in news callback")

        return result

    # Preserve method metadata
    wrapped_search.__name__ = original_search_method.__name__
    wrapped_search.__doc__ = original_search_method.__doc__

    return wrapped_search
