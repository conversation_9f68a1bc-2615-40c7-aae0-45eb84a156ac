"""
Headline generation utilities for news items.
Uses LLM to generate concise, meaningful headlines from long queries and findings.
"""

from typing import Optional
from loguru import logger


def generate_headline(
    query: str, findings: str = "", max_length: int = 100
) -> str:
    """
    Generate a concise headline from a query and optional findings.

    Args:
        query: The search query or research question
        findings: Optional findings/content to help generate better headline
        max_length: Maximum length for the headline

    Returns:
        A concise headline string
    """
    # Always try LLM generation first for dynamic headlines based on actual content
    llm_headline = _generate_with_llm(query, findings, max_length)
    if llm_headline:
        return llm_headline

    # No fallback - if LLM fails, indicate failure
    return "[Headline generation failed]"


def _generate_with_llm(
    query: str, findings: str, max_length: int
) -> Optional[str]:
    """Generate headline using LLM."""
    try:
        from ...config.llm_config import get_llm

        # Use the configured model for headline generation
        llm = get_llm(temperature=0.3)

        # Focus only on the findings/report content, not the query
        if not findings:
            logger.debug("No findings provided for headline generation")
            return None

        # Use the COMPLETE findings - no character limit
        findings_preview = findings
        logger.debug(
            f"Generating headline with {len(findings)} chars of findings"
        )

        prompt = f"""Generate a comprehensive news headline that captures the key events from the research report below.

Research Findings:
{findings_preview}

Requirements:
- Include MULTIPLE major events if several important things happened (e.g., "Earthquake Strikes California While Wildfires Rage; Global Markets Tumble Amid Political Tensions")
- Capture as much important information as possible in the headline
- Be specific about locations, impacts, and key details
- Professional news headline style but can be longer to include more information
- Focus on the most impactful findings from the report
- Use semicolons or commas to separate multiple major events
- No quotes or punctuation at start/end
- Base the headline ONLY on the actual findings in the report

Generate only the headline text, nothing else."""

        response = llm.invoke(prompt)
        headline = response.content.strip()

        # Clean up the generated headline
        headline = headline.strip("\"'.,!?")

        # Validate the headline
        if headline:
            logger.debug(f"Generated headline: {headline}")
            return headline

    except Exception as e:
        logger.debug(f"LLM headline generation failed: {e}")

    return None
