"""
Evidence requirements for different constraint types.
"""

from typing import Dict, List

from ..constraints.base_constraint import ConstraintType


class EvidenceRequirements:
    """Define evidence requirements for different constraint types."""

    @staticmethod
    def get_requirements(
        constraint_type: ConstraintType,
    ) -> Dict[str, List[str]]:
        """Get evidence requirements for a constraint type.

        Args:
            constraint_type: The type of constraint

        Returns:
            Dictionary of evidence types and their sources
        """
        requirements = {
            ConstraintType.PROPERTY: {
                "preferred": ["direct_statement", "official_record"],
                "acceptable": ["research_finding", "inference"],
                "sources": [
                    "scientific papers",
                    "official documents",
                    "encyclopedias",
                ],
            },
            ConstraintType.NAME_PATTERN: {
                "preferred": ["direct_statement", "linguistic_analysis"],
                "acceptable": ["correlation", "inference"],
                "sources": [
                    "etymology sources",
                    "naming databases",
                    "historical records",
                ],
            },
            ConstraintType.EVENT: {
                "preferred": ["news_report", "official_record"],
                "acceptable": ["testimonial", "correlation"],
                "sources": [
                    "news archives",
                    "government reports",
                    "witness accounts",
                ],
            },
            ConstraintType.STATISTIC: {
                "preferred": ["statistical_data", "official_record"],
                "acceptable": ["research_finding"],
                "sources": [
                    "government databases",
                    "research papers",
                    "official reports",
                ],
            },
            ConstraintType.TEMPORAL: {
                "preferred": ["official_record", "news_report"],
                "acceptable": ["historical_record", "inference"],
                "sources": ["archives", "newspapers", "official timelines"],
            },
            ConstraintType.LOCATION: {
                "preferred": ["geographical_data", "official_record"],
                "acceptable": ["mapping_data", "inference"],
                "sources": [
                    "geographical surveys",
                    "maps",
                    "location databases",
                ],
            },
            ConstraintType.COMPARISON: {
                "preferred": ["statistical_comparison", "research_finding"],
                "acceptable": ["inference", "correlation"],
                "sources": [
                    "comparative studies",
                    "statistical analyses",
                    "research papers",
                ],
            },
            ConstraintType.EXISTENCE: {
                "preferred": ["direct_statement", "official_record"],
                "acceptable": ["news_report", "inference"],
                "sources": [
                    "official registries",
                    "databases",
                    "authoritative sources",
                ],
            },
        }

        return requirements.get(
            constraint_type,
            {
                "preferred": ["direct_statement"],
                "acceptable": ["inference"],
                "sources": ["general sources"],
            },
        )

    @staticmethod
    def get_minimum_confidence(constraint_type: ConstraintType) -> float:
        """Get minimum confidence required for constraint type.

        Args:
            constraint_type: The type of constraint

        Returns:
            Minimum confidence threshold
        """
        thresholds = {
            ConstraintType.STATISTIC: 0.8,  # High accuracy needed
            ConstraintType.EVENT: 0.7,  # Moderate accuracy
            ConstraintType.PROPERTY: 0.6,  # Some flexibility
            ConstraintType.NAME_PATTERN: 0.5,  # More interpretive
        }

        return thresholds.get(constraint_type, 0.6)
