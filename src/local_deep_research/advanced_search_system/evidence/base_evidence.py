"""
Base evidence classes for the advanced search system.
"""

from dataclasses import dataclass, field
from datetime import datetime, UTC
from enum import Enum
from typing import Any, Dict, Optional


class EvidenceType(Enum):
    """Types of evidence with inherent reliability scores."""

    DIRECT_STATEMENT = "direct_statement"
    OFFICIAL_RECORD = "official_record"
    RESEARCH_FINDING = "research_finding"
    NEWS_REPORT = "news_report"
    STATISTICAL_DATA = "statistical_data"
    INFERENCE = "inference"
    CORRELATION = "correlation"
    SPECULATION = "speculation"

    @property
    def base_confidence(self) -> float:
        """Get base confidence for this evidence type."""
        confidence_map = {
            EvidenceType.DIRECT_STATEMENT: 0.95,
            EvidenceType.OFFICIAL_RECORD: 0.90,
            EvidenceType.RESEARCH_FINDING: 0.85,
            EvidenceType.STATISTICAL_DATA: 0.85,
            EvidenceType.NEWS_REPORT: 0.75,
            EvidenceType.INFERENCE: 0.50,
            EvidenceType.CORRELATION: 0.30,
            EvidenceType.SPECULATION: 0.10,
        }
        return confidence_map.get(self, 0.5)


@dataclass
class Evidence:
    """Evidence supporting or refuting a claim."""

    claim: str
    type: EvidenceType
    source: str
    confidence: float = 0.0
    reasoning: Optional[str] = None
    raw_text: Optional[str] = None
    timestamp: str = field(
        default_factory=lambda: datetime.now(UTC).isoformat()
    )
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Calculate initial confidence if not provided."""
        if self.confidence == 0.0:
            self.confidence = self.type.base_confidence
