"""
Simple concatenation-based follow-up question generator.

This implementation preserves the current behavior of concatenating
the previous research context with the follow-up query without using
an LLM to reformulate.
"""

from loguru import logger
from .base_followup_question import BaseFollowUpQuestionGenerator


class SimpleFollowUpQuestionGenerator(BaseFollowUpQuestionGenerator):
    """
    Simple follow-up question generator that concatenates context.

    This generator creates a contextualized query by directly concatenating
    the previous research findings with the follow-up question, without
    any LLM-based reformulation. This ensures the follow-up query is
    understood in the context of previous research.
    """

    def generate_contextualized_query(
        self,
        follow_up_query: str,
        original_query: str,
        past_findings: str,
        **kwargs,
    ) -> str:
        """
        Generate a contextualized query by simple concatenation.

        This method preserves the exact user query while providing full
        context from previous research. This ensures queries like
        "provide data in a table" are understood as referring to the
        previous findings, not as new searches.

        Args:
            follow_up_query: The user's follow-up question
            original_query: The original research query
            past_findings: The findings from previous research
            **kwargs: Additional context parameters (unused)

        Returns:
            str: A contextualized query with previous research embedded
        """
        # Simply concatenate the context with the query - no LLM interpretation needed
        # Highlight importance at top, actual request at bottom
        contextualized = f"""IMPORTANT: This is a follow-up request. Focus on addressing the specific user request at the bottom of this prompt using the previous research context provided below.

Previous research query: {original_query}

Previous findings:
{past_findings}

---
USER'S FOLLOW-UP REQUEST: {follow_up_query}
Please address this specific request using the context and findings above.
---"""

        logger.info(
            f"Created contextualized query with {len(past_findings)} chars of context"
        )

        return contextualized
