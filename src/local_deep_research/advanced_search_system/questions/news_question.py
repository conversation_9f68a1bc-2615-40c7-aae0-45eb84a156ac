"""
News question generation implementation.
"""

from datetime import datetime, UTC
from typing import List, Dict

from loguru import logger

from .base_question import BaseQuestionGenerator


class NewsQuestionGenerator(BaseQuestionGenerator):
    """News-specific question generator for aggregating current news."""

    def generate_questions(
        self,
        current_knowledge: str,
        query: str,
        questions_per_iteration: int = 8,
        questions_by_iteration: Dict[int, List[str]] = None,
    ) -> List[str]:
        """Generate news-specific search queries."""
        date_str = datetime.now(UTC).strftime("%B %d, %Y")

        logger.info("Generating news search queries...")

        # Build diverse news queries
        base_queries = [
            f"breaking news today {date_str}",
            f"major incidents casualties today {date_str}",
            f"unexpected news surprising today {date_str}",
            "economic news market movement today",
            f"political announcements today {date_str}",
            "technology breakthrough announcement today",
            "natural disaster emergency today",
            "international news global impact today",
        ]

        # If user provided specific focus, add those queries
        if query and query != "latest important news today":
            focus_queries = [
                f"{query} {date_str}",
                f"{query} breaking news today",
                f"{query} latest developments",
            ]
            return focus_queries + base_queries[:5]

        return base_queries[:questions_per_iteration]
