"""
Standard question generation implementation.
"""

from datetime import datetime, UTC
from typing import List

from loguru import logger

from .base_question import BaseQuestionGenerator


class StandardQuestionGenerator(BaseQuestionGenerator):
    """Standard question generator."""

    def generate_questions(
        self,
        current_knowledge: str,
        query: str,
        questions_per_iteration: int = 2,
        questions_by_iteration: dict = None,
    ) -> List[str]:
        """Generate follow-up questions based on current knowledge."""
        now = datetime.now(UTC)
        current_time = now.strftime("%Y-%m-%d")
        questions_by_iteration = questions_by_iteration or {}

        logger.info("Generating follow-up questions...")

        if questions_by_iteration:
            prompt = f"""Critically reflect current knowledge (e.g., timeliness), what {questions_per_iteration} high-quality internet search questions remain unanswered to exactly answer the query?
            Query: {query}
            Today: {current_time}
            Past questions: {questions_by_iteration!s}
            Knowledge: {current_knowledge}
            Include questions that critically reflect current knowledge.
            \n\n\nFormat: One question per line, e.g. \n Q: question1 \n Q: question2\n\n"""
        else:
            prompt = f" You will have follow up questions. First, identify if your knowledge is outdated (high chance). Today: {current_time}. Generate {questions_per_iteration} high-quality internet search questions to exactly answer: {query}\n\n\nFormat: One question per line, e.g. \n Q: question1 \n Q: question2\n\n"

        response = self.model.invoke(prompt)

        # Handle both string responses and responses with .content attribute
        response_text = ""
        if hasattr(response, "content"):
            response_text = response.content
        else:
            # Handle string responses
            response_text = str(response)

        questions = [
            q.replace("Q:", "").strip()
            for q in response_text.split("\n")
            if q.strip().startswith("Q:")
        ][:questions_per_iteration]

        logger.info(f"Generated {len(questions)} follow-up questions")

        return questions

    def generate_sub_questions(
        self, query: str, context: str = ""
    ) -> List[str]:
        """
        Generate sub-questions from a main query.

        Args:
            query: The main query to break down
            context: Additional context for question generation

        Returns:
            List[str]: List of generated sub-questions
        """
        prompt = f"""You are an expert at breaking down complex questions into simpler sub-questions.

Original Question: {query}

{context}

Break down the original question into 2-5 simpler sub-questions that would help answer the original question when answered in sequence.
Follow these guidelines:
1. Each sub-question should be specific and answerable on its own
2. Sub-questions should build towards answering the original question
3. For multi-hop or complex queries, identify the individual facts or entities needed
4. Ensure the sub-questions can be answered with separate searches

Format your response as a numbered list with ONLY the sub-questions, one per line:
1. First sub-question
2. Second sub-question
...

Only provide the numbered sub-questions, nothing else."""

        try:
            response = self.model.invoke(prompt)

            # Handle both string responses and responses with .content attribute
            content = ""
            if hasattr(response, "content"):
                content = response.content
            else:
                # Handle string responses
                content = str(response)

            # Parse sub-questions from the response
            sub_questions = []
            for line in content.strip().split("\n"):
                line = line.strip()
                if line and (line[0].isdigit() or line.startswith("-")):
                    # Extract sub-question from numbered or bulleted list
                    parts = (
                        line.split(".", 1)
                        if "." in line
                        else line.split(" ", 1)
                    )
                    if len(parts) > 1:
                        sub_question = parts[1].strip()
                        sub_questions.append(sub_question)

            # Limit to at most 5 sub-questions
            return sub_questions[:5]
        except Exception as e:
            logger.exception(f"Error generating sub-questions: {e!s}")
            return []
