"""
Base class for all question generators.
Defines the common interface and shared functionality for different question generation approaches.
"""

from abc import ABC, abstractmethod
from typing import Dict, List


class BaseQuestionGenerator(ABC):
    """Abstract base class for all question generators."""

    def __init__(self, model):
        """
        Initialize the question generator.

        Args:
            model: The language model to use for question generation
        """
        self.model = model

    @abstractmethod
    def generate_questions(
        self,
        current_knowledge: str,
        query: str,
        questions_per_iteration: int,
        questions_by_iteration: Dict[int, List[str]],
    ) -> List[str]:
        """
        Generate questions based on the current state of research.

        Args:
            current_knowledge: The accumulated knowledge so far
            query: The original research query
            questions_per_iteration: Number of questions to generate per iteration
            questions_by_iteration: Questions generated in previous iterations

        Returns:
            List[str]: Generated questions
        """
        pass

    def _format_previous_questions(
        self, questions_by_iteration: Dict[int, List[str]]
    ) -> str:
        """
        Format previous questions for context.

        Args:
            questions_by_iteration: Questions generated in previous iterations

        Returns:
            str: Formatted string of previous questions
        """
        formatted = []
        for iteration, questions in questions_by_iteration.items():
            formatted.append(f"Iteration {iteration}:")
            for q in questions:
                formatted.append(f"- {q}")
        return "\n".join(formatted)
