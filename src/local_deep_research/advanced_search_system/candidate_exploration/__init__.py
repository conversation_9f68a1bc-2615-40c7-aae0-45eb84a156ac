"""
Candidate exploration system for discovering and refining candidates.

This module provides inheritance-based components for exploring and discovering
candidates through different search strategies and approaches.
"""

from .adaptive_explorer import AdaptiveExplorer
from .base_explorer import BaseCandidateExplorer, ExplorationResult
from .constraint_guided_explorer import ConstraintGuidedExplorer
from .diversity_explorer import DiversityExplorer
from .parallel_explorer import ParallelExplorer
from .progressive_explorer import ProgressiveExplorer

__all__ = [
    # Base classes
    "BaseCandidateExplorer",
    "ExplorationResult",
    # Concrete implementations
    "ParallelExplorer",
    "AdaptiveExplorer",
    "ConstraintGuidedExplorer",
    "DiversityExplorer",
    "ProgressiveExplorer",
]
