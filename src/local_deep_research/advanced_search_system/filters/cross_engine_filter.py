"""
Cross-engine search result filter implementation.
"""

import json
from typing import Dict, List

from loguru import logger

from ...utilities.search_utilities import remove_think_tags
from .base_filter import BaseFilter


class CrossEngineFilter(BaseFilter):
    """Filter that ranks and filters results from multiple search engines."""

    def __init__(
        self,
        model,
        max_results=None,
        default_reorder=True,
        default_reindex=True,
        settings_snapshot=None,
    ):
        """
        Initialize the cross-engine filter.

        Args:
            model: Language model to use for relevance assessment
            max_results: Maximum number of results to keep after filtering
            default_reorder: Default setting for reordering results by relevance
            default_reindex: Default setting for reindexing results after filtering
            settings_snapshot: Settings snapshot for thread context
        """
        super().__init__(model)
        # Get max_results from database settings if not provided
        if max_results is None:
            # Import from thread_settings to avoid database dependencies
            from ...config.thread_settings import (
                get_setting_from_snapshot,
                NoSettingsContextError,
            )

            try:
                max_results = get_setting_from_snapshot(
                    "search.cross_engine_max_results",
                    default=100,
                    settings_snapshot=settings_snapshot,
                )
                # Ensure we have an integer
                if max_results is not None:
                    max_results = int(max_results)
                else:
                    max_results = 100
            except (NoSettingsContextError, TypeError, ValueError):
                max_results = 100  # Explicit default
        self.max_results = max_results
        self.default_reorder = default_reorder
        self.default_reindex = default_reindex

    def filter_results(
        self,
        results: List[Dict],
        query: str,
        reorder=None,
        reindex=None,
        start_index=0,
        **kwargs,
    ) -> List[Dict]:
        """
        Filter and rank search results from multiple engines by relevance.

        Args:
            results: Combined list of search results from all engines
            query: The original search query
            reorder: Whether to reorder results by relevance (default: use instance default)
            reindex: Whether to update result indices after filtering (default: use instance default)
            start_index: Starting index for the results (used for continuous indexing)
            **kwargs: Additional parameters

        Returns:
            Filtered list of search results
        """
        # Use instance defaults if not specified
        if reorder is None:
            reorder = self.default_reorder
        if reindex is None:
            reindex = self.default_reindex

        if not self.model or len(results) <= 10:  # Don't filter if few results
            # Even if not filtering, update indices if requested
            if reindex:
                for i, result in enumerate(
                    results[: min(self.max_results, len(results))]
                ):
                    result["index"] = str(i + start_index + 1)
            return results[: min(self.max_results, len(results))]

        # Create context for LLM
        preview_context = []
        for i, result in enumerate(results):
            title = result.get("title", "Untitled").strip()
            snippet = result.get("snippet", "").strip()
            engine = result.get("engine", "Unknown engine")

            # Clean up snippet if too long
            if len(snippet) > 200:
                snippet = snippet[:200] + "..."

            preview_context.append(
                f"[{i}] Engine: {engine} | Title: {title}\nSnippet: {snippet}"
            )

        # Set a reasonable limit on context length
        max_context_items = min(30, len(preview_context))
        context = "\n\n".join(preview_context[:max_context_items])

        prompt = f"""You are a search result filter. Your task is to rank search results from multiple engines by relevance to a query.

Query: "{query}"

Search Results:
{context}

Return the search results as a JSON array of indices, ranked from most to least relevant to the query.
Only include indices of results that are actually relevant to the query.
For example: [3, 0, 7, 1]

If no results seem relevant to the query, return an empty array: []"""

        try:
            # Get LLM's evaluation
            response = self.model.invoke(prompt)

            # Extract response text
            if hasattr(response, "content"):
                response_text = remove_think_tags(response.content)
            else:
                response_text = remove_think_tags(str(response))

            # Clean up response
            response_text = response_text.strip()

            # Find JSON array in response
            start_idx = response_text.find("[")
            end_idx = response_text.rfind("]")

            if start_idx >= 0 and end_idx > start_idx:
                array_text = response_text[start_idx : end_idx + 1]
                ranked_indices = json.loads(array_text)

                # If not reordering, just filter based on the indices
                if not reorder:
                    # Just keep the results that were deemed relevant
                    filtered_results = []
                    for idx in sorted(
                        ranked_indices
                    ):  # Sort to maintain original order
                        if idx < len(results):
                            filtered_results.append(results[idx])

                    # Limit results if needed
                    final_results = filtered_results[
                        : min(self.max_results, len(filtered_results))
                    ]

                    # Reindex if requested
                    if reindex:
                        for i, result in enumerate(final_results):
                            result["index"] = str(i + start_index + 1)

                    logger.info(
                        f"Cross-engine filtering kept {len(final_results)} out of {len(results)} results without reordering"
                    )
                    return final_results

                # Create ranked results list (reordering)
                ranked_results = []
                for idx in ranked_indices:
                    if idx < len(results):
                        ranked_results.append(results[idx])

                # If filtering removed everything, return top results
                if not ranked_results and results:
                    logger.info(
                        "Cross-engine filtering removed all results, returning top 10 originals instead"
                    )
                    top_results = results[: min(10, len(results))]
                    # Update indices if requested
                    if reindex:
                        for i, result in enumerate(top_results):
                            result["index"] = str(i + start_index + 1)
                    return top_results

                # Limit results if needed
                max_filtered = min(self.max_results, len(ranked_results))
                final_results = ranked_results[:max_filtered]

                # Update indices if requested
                if reindex:
                    for i, result in enumerate(final_results):
                        result["index"] = str(i + start_index + 1)

                logger.info(
                    f"Cross-engine filtering kept {len(final_results)} out of {len(results)} results with reordering={reorder}, reindex={reindex}"
                )
                return final_results
            else:
                logger.info(
                    "Could not find JSON array in response, returning original results"
                )
                top_results = results[: min(self.max_results, len(results))]
                # Update indices if requested
                if reindex:
                    for i, result in enumerate(top_results):
                        result["index"] = str(i + start_index + 1)
                return top_results

        except Exception:
            logger.exception("Cross-engine filtering error")
            top_results = results[: min(self.max_results, len(results))]
            # Update indices if requested
            if reindex:
                for i, result in enumerate(top_results):
                    result["index"] = str(i + start_index + 1)
            return top_results
