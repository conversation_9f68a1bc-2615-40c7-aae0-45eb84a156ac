"""
Standard knowledge generator implementation.
"""

from loguru import logger
from datetime import datetime, UTC
from typing import List

from .base_knowledge import BaseKnowledgeGenerator


class StandardKnowledge(BaseKnowledgeGenerator):
    """Standard knowledge generator implementation."""

    def generate_knowledge(
        self,
        query: str,
        context: str = "",
        current_knowledge: str = "",
        questions: List[str] = None,
    ) -> str:
        """Generate knowledge based on query and context."""
        now = datetime.now(UTC)
        current_time = now.strftime("%Y-%m-%d")

        logger.info("Generating knowledge...")

        if questions:
            prompt = f"""Based on the following query and questions, generate comprehensive knowledge:

Query: {query}
Current Time: {current_time}
Context: {context}
Current Knowledge: {current_knowledge}
Questions: {questions}

Generate detailed knowledge that:
1. Directly answers the query
2. Addresses each question
3. Includes relevant facts and details
4. Is up-to-date with current information
5. Synthesizes information from multiple sources

Format your response as a well-structured paragraph."""
        else:
            prompt = f"""Based on the following query, generate comprehensive knowledge:

Query: {query}
Current Time: {current_time}
Context: {context}
Current Knowledge: {current_knowledge}

Generate detailed knowledge that:
1. Directly answers the query
2. Includes relevant facts and details
3. Is up-to-date with current information
4. Synthesizes information from multiple sources

Format your response as a well-structured paragraph."""

        response = self.model.invoke(prompt)
        knowledge = response.content

        logger.info("Generated knowledge successfully")
        return knowledge

    def generate_sub_knowledge(self, sub_query: str, context: str = "") -> str:
        """
        Generate knowledge for a sub-question.

        Args:
            sub_query: The sub-question to generate knowledge for
            context: Additional context for knowledge generation

        Returns:
            str: Generated knowledge for the sub-question
        """
        prompt = f"""Generate comprehensive knowledge to answer this sub-question:

Sub-question: {sub_query}

{context}

Generate detailed knowledge that:
1. Directly answers the sub-question
2. Includes relevant facts and details
3. Is up-to-date with current information
4. Synthesizes information from multiple sources

Format your response as a well-structured paragraph."""

        try:
            response = self.model.invoke(prompt)
            return response.content
        except Exception as e:
            logger.exception(f"Error generating sub-knowledge: {e!s}")
            return ""

    def generate(self, query: str, context: str) -> str:
        """Generate knowledge from the given query and context."""
        return self.generate_knowledge(query, context)

    def compress_knowledge(
        self, current_knowledge: str, query: str, section_links: list, **kwargs
    ) -> str:
        """
        Compress and summarize accumulated knowledge.

        Args:
            current_knowledge: The accumulated knowledge to compress
            query: The original research query
            section_links: List of source links
            **kwargs: Additional arguments

        Returns:
            str: Compressed knowledge
        """
        logger.info(
            f"Compressing knowledge for query: {query}. Original length: {len(current_knowledge)}"
        )

        prompt = f"""Compress the following accumulated knowledge relevant to the query '{query}'.
Retain the key facts, findings, and citations. Remove redundancy.

Accumulated Knowledge:
{current_knowledge}

Compressed Knowledge:"""

        try:
            response = self.model.invoke(prompt)
            compressed_knowledge = response.content
            logger.info(
                f"Compressed knowledge length: {len(compressed_knowledge)}"
            )
            return compressed_knowledge
        except Exception as e:
            logger.exception(f"Error compressing knowledge: {e!s}")
            return current_knowledge  # Return original if compression fails

    def format_citations(self, links: List[str]) -> str:
        """
        Format source links into citations using IEEE style.

        Args:
            links: List of source links

        Returns:
            str: Formatted citations in IEEE style
        """
        if not links:
            return ""

        # Format each link as an IEEE citation
        citations = []
        for i, link in enumerate(links, 1):
            citations.append(f"[{i}] {link}")

        return "\n".join(citations)
