"""
Constraint checking and candidate assessment system.

This module provides inheritance-based components for checking candidates
against constraints, with different implementations available.
"""

from .base_constraint_checker import (
    BaseConstraint<PERSON>he<PERSON>,
    ConstraintCheckResult,
)

# Legacy imports for backward compatibility
from .constraint_checker import <PERSON>stra<PERSON><PERSON><PERSON><PERSON>
from .dual_confidence_checker import <PERSON>Confidence<PERSON>he<PERSON>
from .evidence_analyzer import ConstraintEvidence, EvidenceAnalyzer
from .rejection_engine import RejectionEng<PERSON>
from .strict_checker import <PERSON>rictChe<PERSON>
from .threshold_checker import ThresholdChecker

__all__ = [
    # Base classes
    "BaseConstraintChecker",
    "ConstraintCheckResult",
    # Concrete implementations
    "DualConfidenceChe<PERSON>",
    "ThresholdChe<PERSON>",
    "<PERSON>rict<PERSON>he<PERSON>",
    # Supporting components
    "EvidenceAnalyzer",
    "ConstraintEvidence",
    "RejectionEngine",
    # Legacy
    "ConstraintChecker",
]
