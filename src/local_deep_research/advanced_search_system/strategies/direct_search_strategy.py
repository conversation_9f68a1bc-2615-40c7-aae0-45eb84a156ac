"""
Direct search strategy for focused single-query searches.

This strategy is optimized for:
1. Entity identification queries (who, what, which)
2. Single-pass searches without iteration
3. Minimal LLM calls for efficiency
"""

from typing import Dict

from loguru import logger

from ...citation_handler import CitationHandler

# Model and search should be provided by AdvancedSearchSystem
from ..filters.cross_engine_filter import CrossEngineFilter
from ..findings.repository import FindingsRepository
from .base_strategy import BaseSearchStrategy


class DirectSearchStrategy(BaseSearchStrategy):
    """
    Direct search strategy that performs a single focused search and synthesis.

    Key features:
    1. No question generation - uses the original query directly
    2. Single search pass - no iterations
    3. Minimal LLM calls - only for final synthesis
    4. Optimized for entity identification and focused queries
    """

    def __init__(
        self,
        search,
        model,
        citation_handler=None,
        include_text_content: bool = True,
        use_cross_engine_filter: bool = True,
        filter_reorder: bool = True,
        filter_reindex: bool = True,
        cross_engine_max_results: int = None,
        all_links_of_system=None,
    ):
        """Initialize with minimal components for efficiency."""
        super().__init__(all_links_of_system=all_links_of_system)
        self.search = search
        self.model = model
        self.progress_callback = None

        self.include_text_content = include_text_content
        self.use_cross_engine_filter = use_cross_engine_filter
        self.filter_reorder = filter_reorder
        self.filter_reindex = filter_reindex

        # Initialize the cross-engine filter
        self.cross_engine_filter = CrossEngineFilter(
            model=self.model,
            max_results=cross_engine_max_results,
            default_reorder=filter_reorder,
            default_reindex=filter_reindex,
        )

        # Use provided citation_handler or create one
        self.citation_handler = citation_handler or CitationHandler(self.model)
        self.findings_repository = FindingsRepository(self.model)

    def analyze_topic(self, query: str) -> Dict:
        """
        Direct search implementation - single query, single synthesis.
        """
        logger.info(f"Starting direct search on topic: {query}")
        findings = []
        total_citation_count_before_this_search = len(self.all_links_of_system)

        self._update_progress(
            "Initializing direct search",
            5,
            {
                "phase": "init",
                "strategy": "direct",
                "query": query[:100],
            },
        )

        # Check search engine
        if not self._validate_search_engine():
            return {
                "findings": [],
                "iterations": 1,
                "questions_by_iteration": {1: [query]},
                "formatted_findings": "Error: Unable to conduct research without a search engine.",
                "current_knowledge": "",
                "error": "No search engine available",
            }

        try:
            # Single direct search
            self._update_progress(
                f"Searching: {query[:200]}...",
                20,
                {"phase": "searching", "query": query},
            )

            search_results = self.search.run(query)

            if not search_results:
                search_results = []

            self._update_progress(
                f"Found {len(search_results)} results",
                40,
                {
                    "phase": "search_complete",
                    "result_count": len(search_results),
                },
            )

            # Optional: Apply cross-engine filter
            if self.use_cross_engine_filter and search_results:
                self._update_progress(
                    "Filtering search results",
                    50,
                    {"phase": "filtering"},
                )

                filtered_results = self.cross_engine_filter.filter_results(
                    search_results,
                    query,
                    reorder=self.filter_reorder,
                    reindex=self.filter_reindex,
                    start_index=len(self.all_links_of_system),
                )

                self._update_progress(
                    f"Filtered to {len(filtered_results)} results",
                    60,
                    {
                        "phase": "filtering_complete",
                        "filtered_count": len(filtered_results),
                    },
                )
            else:
                filtered_results = search_results

            # Add to all links
            self.all_links_of_system.extend(filtered_results)

            # Final synthesis
            self._update_progress(
                "Generating synthesis",
                80,
                {"phase": "synthesis"},
            )

            final_citation_result = self.citation_handler.analyze_followup(
                query,
                filtered_results,
                previous_knowledge="",
                nr_of_links=total_citation_count_before_this_search,
            )

            if final_citation_result:
                synthesized_content = final_citation_result["content"]
                documents = final_citation_result.get("documents", [])
            else:
                synthesized_content = "No relevant results found."
                documents = []

            # Create finding
            finding = {
                "phase": "Direct Search",
                "content": synthesized_content,
                "question": query,
                "search_results": filtered_results,
                "documents": documents,
            }
            findings.append(finding)

            # Add documents to repository
            self.findings_repository.add_documents(documents)

            # Format findings
            formatted_findings = (
                self.findings_repository.format_findings_to_text(
                    findings, synthesized_content
                )
            )

        except Exception as e:
            error_msg = f"Error in direct search: {e!s}"
            logger.exception(error_msg)
            synthesized_content = f"Error: {e!s}"
            formatted_findings = f"Error: {e!s}"
            finding = {
                "phase": "Error",
                "content": synthesized_content,
                "question": query,
                "search_results": [],
                "documents": [],
            }
            findings.append(finding)

        self._update_progress("Search complete", 100, {"phase": "complete"})

        return {
            "findings": findings,
            "iterations": 1,
            "questions_by_iteration": {1: [query]},
            "formatted_findings": formatted_findings,
            "current_knowledge": synthesized_content,
            "all_links_of_system": self.all_links_of_system,
        }
