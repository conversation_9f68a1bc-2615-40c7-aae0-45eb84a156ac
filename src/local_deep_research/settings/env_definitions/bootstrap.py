"""
Bootstrap environment settings.

These settings are required early in the application lifecycle,
before database initialization and other core systems.
"""

from ..env_settings import (
    BooleanSetting,
    StringSetting,
    PathSetting,
    SecretSetting,
)


# Bootstrap settings required before DB init
BOOTSTRAP_SETTINGS = [
    # Security and encryption
    SecretSetting(
        key="bootstrap.encryption_key",
        description="Database encryption key",
        required=False,  # Not required if allow_unencrypted is True
    ),
    SecretSetting(
        key="bootstrap.secret_key",
        description="Application secret key for session encryption",
    ),
    # Database
    StringSetting(
        key="bootstrap.database_url",
        description="Database connection URL",
    ),
    BooleanSetting(
        key="bootstrap.allow_unencrypted",
        description="Allow unencrypted database (for development)",
        default=False,
    ),
    # System paths
    PathSetting(
        key="bootstrap.data_dir",
        description="Data directory path",
        create_if_missing=True,
    ),
    PathSetting(
        key="bootstrap.config_dir",
        description="Configuration directory path",
        create_if_missing=True,
    ),
    PathSetting(
        key="bootstrap.log_dir",
        description="Log directory path",
        create_if_missing=True,
    ),
    # Logging
    BooleanSetting(
        key="bootstrap.enable_file_logging",
        description="Enable logging to file",
        default=False,
    ),
]
