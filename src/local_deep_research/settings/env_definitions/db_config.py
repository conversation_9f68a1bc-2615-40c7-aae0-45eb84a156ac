"""
Database configuration environment settings.

These settings control SQLite and SQLCipher database parameters
that must be set before opening the database connection.
"""

from ..env_settings import (
    IntegerSetting,
    StringSetting,
    EnumSetting,
)


# Database configuration settings
DB_CONFIG_SETTINGS = [
    # Performance settings
    IntegerSetting(
        key="db_config.cache_size_mb",
        description="SQLite cache size in MB",
        min_value=1,
        max_value=10000,
        default=100,
    ),
    EnumSetting(
        key="db_config.journal_mode",
        description="SQLite journal mode",
        allowed_values={
            "DELETE",
            "TRUNCATE",
            "PERSIST",
            "MEMORY",
            "WAL",
            "OFF",
        },
        default="WAL",
        case_sensitive=False,
    ),
    EnumSetting(
        key="db_config.synchronous",
        description="SQLite synchronous mode",
        allowed_values={"OFF", "NORMAL", "FULL", "EXTRA"},
        default="NORMAL",
        case_sensitive=False,
    ),
    # Storage settings
    IntegerSetting(
        key="db_config.page_size",
        description="SQLite page size (must be power of 2)",
        min_value=512,
        max_value=65536,
        default=4096,
    ),
    # Encryption settings
    IntegerSetting(
        key="db_config.kdf_iterations",
        description="Number of KDF iterations for key derivation",
        min_value=1000,
        max_value=1000000,
        default=256000,
    ),
    StringSetting(
        key="db_config.kdf_algorithm",
        description="Key derivation function algorithm",
        default="PBKDF2_HMAC_SHA512",
    ),
    StringSetting(
        key="db_config.hmac_algorithm",
        description="HMAC algorithm for database integrity",
        default="HMAC_SHA512",
    ),
]
