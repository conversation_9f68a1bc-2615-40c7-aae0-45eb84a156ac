"""
Environment setting definitions organized by category.
"""

from .testing import TESTING_SETTINGS
from .bootstrap import BOOTSTRAP_SETTINGS
from .db_config import DB_CONFIG_SETTINGS

# Combine all settings for easy access
ALL_SETTINGS = {
    "testing": TESTING_SETTINGS,
    "bootstrap": BOOTSTRAP_SETTINGS,
    "db_config": DB_CONFIG_SETTINGS,
}

__all__ = [
    "TESTING_SETTINGS",
    "BOOTSTRAP_SETTINGS",
    "DB_CONFIG_SETTINGS",
    "ALL_SETTINGS",
]
