"""
Registry of all environment-only settings.

This module creates the global registry and registers all environment settings
defined in the env_definitions subfolder.
"""

from typing import Optional, Any

from .env_settings import SettingsRegistry
from .env_definitions import ALL_SETTINGS


def _create_registry() -> SettingsRegistry:
    """Create and initialize the global registry with all defined settings."""
    registry = SettingsRegistry()

    # Register all setting categories
    for category_name, settings_list in ALL_SETTINGS.items():
        registry.register_category(category_name, settings_list)

    return registry


# Global registry instance (singleton)
registry = _create_registry()


# Convenience functions for direct access
def get_env_setting(key: str, default: Optional[Any] = None) -> Any:
    """
    Get an environment setting value.

    Args:
        key: Setting key (e.g., "testing.test_mode")
        default: Default value if not set

    Returns:
        Setting value or default
    """
    return registry.get(key, default)


def is_test_mode() -> bool:
    """Quick check for test mode."""
    return bool(registry.get("testing.test_mode", False))


def use_fallback_llm() -> bool:
    """Quick check for fallback LLM mode."""
    return bool(registry.get("testing.use_fallback_llm", False))


def is_ci_environment() -> bool:
    """Quick check for CI environment."""
    # CI is now an external variable, read it dynamically
    import os

    return os.environ.get("CI", "false").lower() in ("true", "1", "yes")


def is_github_actions() -> bool:
    """Check if running in GitHub Actions."""
    # GITHUB_ACTIONS is now an external variable, read it dynamically
    import os

    return os.environ.get("GITHUB_ACTIONS", "false").lower() in (
        "true",
        "1",
        "yes",
    )


# Export the registry and convenience functions
__all__ = [
    "registry",
    "get_env_setting",
    "is_test_mode",
    "use_fallback_llm",
    "is_ci_environment",
    "is_github_actions",
]
