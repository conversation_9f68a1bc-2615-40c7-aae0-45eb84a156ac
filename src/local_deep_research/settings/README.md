# Environment Settings System

## Quick Start

```python
# Use environment settings
from local_deep_research.settings.env_registry import is_test_mode, use_fallback_llm

if is_test_mode():
    # Running in test mode
    pass
```

## Architecture

- `env_settings.py`: Base classes for all setting types
- `env_registry.py`: Global registry and convenience functions
- `env_definitions/`: Setting definitions by category
  - `testing.py`: Test/CI flags
  - `bootstrap.py`: Pre-database settings
  - `db_config.py`: Database configuration

## Adding New Environment Settings

1. Create setting in `env_definitions/category.py`:
```python
from ..env_settings import BooleanSetting

MY_SETTING = BooleanSetting(
    key="category.my_setting",
    env_var="LDR_MY_SETTING",
    default=False,
    description="My new setting"
)
```

2. Register it:
```python
from ..env_registry import registry
registry.register(MY_SETTING)
```

3. Import module in `env_registry.py`

## Setting Types

- `BooleanSetting`: True/false values
- `IntegerSetting`: Numbers with min/max validation
- `StringSetting`: Text values
- `PathSetting`: File paths with expansion
- `SecretSetting`: Sensitive values (hidden in logs)
- `EnumSetting`: Choice from predefined options

## Environment Variables

### Testing
- `LDR_TEST_MODE`: Enable test mode
- `LDR_USE_FALLBACK_LLM`: Use fallback LLM
- `CI`: Running in CI

### Bootstrap (needed before DB)
- `LDR_ENCRYPTION_KEY`: Master encryption key
- `LDR_DATA_DIR`: Data directory
- `LDR_DB_*`: Database configuration

## Integration

- Automatically works with SettingsManager
- Pre-commit hook recognizes all registered env vars
- Type-safe with automatic validation
