"""Integration module to add dogpile cache to the Flask app.

This module provides a simple function to add to app_factory.py
to enable caching throughout the application.
"""

from flask import Flask
from loguru import logger

from .flask_integration import init_cache, cache_metrics_middleware


def setup_dogpile_cache(app: Flask) -> None:
    """Set up dogpile cache for the Flask application.

    This function should be called in app_factory.py after
    the database and authentication are configured.

    Args:
        app: Flask application instance
    """
    # Initialize cache
    init_cache(app)

    # Add cache metrics middleware if cache is enabled
    if app.cache:
        cache_metrics_middleware(app)
        logger.info("Dogpile cache configured with metrics middleware")
    else:
        logger.warning("Cache is disabled or failed to initialize")

    # Log cache configuration
    if app.cache:
        logger.info("Cache configuration:")
        logger.info("  Simple in-memory cache (1 hour TTL)")
        logger.info("  Used for search results and API response caching")
