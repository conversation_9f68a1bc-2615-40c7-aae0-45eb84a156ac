"""
Utility function to get the appropriate settings manager instance.

This module provides a centralized way to get a settings manager,
ensuring consistency across the application.
"""

from typing import Optional
from sqlalchemy.orm import Session
from loguru import logger

from local_deep_research.settings import SettingsManager


def get_settings_manager(
    db_session: Optional[Session] = None,
    user_id: Optional[str] = None,
    use_cache: bool = True,
) -> SettingsManager:
    """
    Get an appropriate settings manager instance.

    Args:
        db_session: SQLAlchemy session
        user_id: User identifier (required for cached version)
        use_cache: Whether to use the cached version

    Returns:
        SettingsManager instance (cached or regular)
    """
    if use_cache and user_id:
        logger.debug(f"Creating SettingsManager for user {user_id}")
        return SettingsManager(db_session)
    else:
        logger.debug("Creating regular SettingsManager")
        return SettingsManager(db_session)
