"""
Factory for creating search strategies.
This module provides a centralized way to create search strategies
to avoid code duplication.
"""

from loguru import logger
from typing import Optional, Dict, Any, List
from langchain_core.language_models import BaseChatModel


def create_strategy(
    strategy_name: str,
    model: BaseChatModel,
    search: Any,
    all_links_of_system: Optional[List[Dict]] = None,
    settings_snapshot: Optional[Dict] = None,
    research_context: Optional[Dict] = None,
    **kwargs,
):
    """
    Create a search strategy by name.

    Args:
        strategy_name: Name of the strategy to create
        model: Language model to use
        search: Search engine instance
        all_links_of_system: List of existing links
        settings_snapshot: Settings snapshot
        research_context: Research context for special strategies
        **kwargs: Additional strategy-specific parameters

    Returns:
        Strategy instance
    """
    if all_links_of_system is None:
        all_links_of_system = []

    strategy_name_lower = strategy_name.lower()

    # Source-based strategy
    if strategy_name_lower in [
        "source-based",
        "source_based",
        "source_based_search",
    ]:
        from .advanced_search_system.strategies.source_based_strategy import (
            SourceBasedSearchStrategy,
        )

        return SourceBasedSearchStrategy(
            model=model,
            search=search,
            include_text_content=kwargs.get("include_text_content", True),
            use_cross_engine_filter=kwargs.get("use_cross_engine_filter", True),
            all_links_of_system=all_links_of_system,
            use_atomic_facts=kwargs.get("use_atomic_facts", False),
            settings_snapshot=settings_snapshot,
            search_original_query=kwargs.get("search_original_query", True),
        )

    # Focused iteration strategy
    elif strategy_name_lower in ["focused-iteration", "focused_iteration"]:
        from .advanced_search_system.strategies.focused_iteration_strategy import (
            FocusedIterationStrategy,
        )

        return FocusedIterationStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            settings_snapshot=settings_snapshot,
        )

    # Multi-source strategy
    elif strategy_name_lower in [
        "multi-source",
        "multi_source",
        "multi-source_cross_reference",
    ]:
        from .advanced_search_system.strategies.multi_source_strategy import (
            MultiSourceCrossReferenceStrategy,
        )

        return MultiSourceCrossReferenceStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            knowledge_accumulation_mode=kwargs.get(
                "knowledge_accumulation_mode", True
            ),
            settings_snapshot=settings_snapshot,
        )

    # Academic strategy
    elif strategy_name_lower in ["academic", "academic_deep_dive"]:
        from .advanced_search_system.strategies.academic_strategy import (
            AcademicDeepDiveStrategy,
        )

        return AcademicDeepDiveStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            knowledge_accumulation_mode=kwargs.get(
                "knowledge_accumulation_mode", True
            ),
            settings_snapshot=settings_snapshot,
        )

    # Investigative strategy
    elif strategy_name_lower in ["investigative", "investigative_journalism"]:
        from .advanced_search_system.strategies.investigative_strategy import (
            InvestigativeJournalismStrategy,
        )

        return InvestigativeJournalismStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            settings_snapshot=settings_snapshot,
        )

    # Comprehensive strategy
    elif strategy_name_lower in ["comprehensive", "comprehensive_analysis"]:
        from .advanced_search_system.strategies.comprehensive_strategy import (
            ComprehensiveAnalysisStrategy,
        )

        return ComprehensiveAnalysisStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            settings_snapshot=settings_snapshot,
        )

    # Adaptive strategy
    elif strategy_name_lower in ["adaptive", "adaptive_research"]:
        from .advanced_search_system.strategies.adaptive_strategy import (
            AdaptiveResearchStrategy,
        )

        return AdaptiveResearchStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            knowledge_accumulation_mode=kwargs.get(
                "knowledge_accumulation_mode", True
            ),
            initial_strategy=kwargs.get("initial_strategy", "source-based"),
            max_strategy_switches=kwargs.get("max_strategy_switches", 2),
            settings_snapshot=settings_snapshot,
        )

    # Iterative reasoning strategy
    elif strategy_name_lower in [
        "iterative-reasoning",
        "iterative_reasoning",
        "iterative_reasoning_depth",
    ]:
        from .advanced_search_system.strategies.iterative_reasoning_strategy import (
            IterativeReasoningDepthStrategy,
        )

        return IterativeReasoningDepthStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            use_atomic_facts=kwargs.get("use_atomic_facts", True),
            settings_snapshot=settings_snapshot,
        )

    # News aggregation strategy
    elif strategy_name_lower in [
        "news",
        "news_aggregation",
        "news-aggregation",
    ]:
        from .advanced_search_system.strategies.news_strategy import (
            NewsAggregationStrategy,
        )

        return NewsAggregationStrategy()

    # IterDRAG strategy
    elif strategy_name_lower == "iterdrag":
        from .advanced_search_system.strategies.iterdrag_strategy import (
            IterDRAGStrategy,
        )

        return IterDRAGStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            settings_snapshot=settings_snapshot,
        )

    # Parallel strategy
    elif strategy_name_lower == "parallel":
        from .advanced_search_system.strategies.parallel_search_strategy import (
            ParallelSearchStrategy,
        )

        return ParallelSearchStrategy(
            model=model,
            search=search,
            include_text_content=kwargs.get("include_text_content", True),
            use_cross_engine_filter=kwargs.get("use_cross_engine_filter", True),
            all_links_of_system=all_links_of_system,
            settings_snapshot=settings_snapshot,
        )

    # Rapid strategy
    elif strategy_name_lower == "rapid":
        from .advanced_search_system.strategies.rapid_search_strategy import (
            RapidSearchStrategy,
        )

        return RapidSearchStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            settings_snapshot=settings_snapshot,
        )

    # Recursive decomposition strategy
    elif strategy_name_lower in ["recursive", "recursive-decomposition"]:
        from .advanced_search_system.strategies.recursive_decomposition_strategy import (
            RecursiveDecompositionStrategy,
        )

        return RecursiveDecompositionStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            settings_snapshot=settings_snapshot,
        )

    # Iterative reasoning strategy (different from iterative_reasoning_depth)
    elif strategy_name_lower == "iterative":
        from .advanced_search_system.strategies.iterative_reasoning_strategy import (
            IterativeReasoningStrategy,
        )

        # Get iteration settings from kwargs or use defaults
        max_iterations = kwargs.get("max_iterations", 20)
        questions_per_iteration = kwargs.get("questions_per_iteration", 3)
        search_iterations_per_round = kwargs.get(
            "search_iterations_per_round", 1
        )

        return IterativeReasoningStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            max_iterations=max_iterations,
            confidence_threshold=kwargs.get("confidence_threshold", 0.95),
            search_iterations_per_round=search_iterations_per_round,
            questions_per_search=questions_per_iteration,
            settings_snapshot=settings_snapshot,
        )

    # Adaptive decomposition strategy (different from adaptive_research)
    elif strategy_name_lower == "adaptive":
        from .advanced_search_system.strategies.adaptive_decomposition_strategy import (
            AdaptiveDecompositionStrategy,
        )

        return AdaptiveDecompositionStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            max_steps=kwargs.get("max_steps", kwargs.get("max_iterations", 5)),
            min_confidence=kwargs.get("min_confidence", 0.8),
            source_search_iterations=kwargs.get("source_search_iterations", 2),
            source_questions_per_iteration=kwargs.get(
                "source_questions_per_iteration",
                kwargs.get("questions_per_iteration", 3),
            ),
            settings_snapshot=settings_snapshot,
        )

    # Smart decomposition strategy
    elif strategy_name_lower == "smart":
        from .advanced_search_system.strategies.smart_decomposition_strategy import (
            SmartDecompositionStrategy,
        )

        return SmartDecompositionStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            max_iterations=kwargs.get("max_iterations", 5),
            source_search_iterations=kwargs.get("source_search_iterations", 2),
            source_questions_per_iteration=kwargs.get(
                "source_questions_per_iteration",
                kwargs.get("questions_per_iteration", 3),
            ),
            settings_snapshot=settings_snapshot,
        )

    # BrowseComp optimized strategy
    elif strategy_name_lower == "browsecomp":
        from .advanced_search_system.strategies.browsecomp_optimized_strategy import (
            BrowseCompOptimizedStrategy,
        )

        return BrowseCompOptimizedStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            max_browsecomp_iterations=kwargs.get(
                "max_browsecomp_iterations", 15
            ),
            confidence_threshold=kwargs.get("confidence_threshold", 0.9),
            max_iterations=kwargs.get("max_iterations", 5),
            questions_per_iteration=kwargs.get("questions_per_iteration", 3),
            settings_snapshot=settings_snapshot,
        )

    # Enhanced evidence-based strategy
    elif strategy_name_lower == "evidence":
        from .advanced_search_system.strategies.evidence_based_strategy_v2 import (
            EnhancedEvidenceBasedStrategy,
        )

        return EnhancedEvidenceBasedStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            max_iterations=kwargs.get("max_iterations", 20),
            confidence_threshold=kwargs.get("confidence_threshold", 0.95),
            candidate_limit=kwargs.get("candidate_limit", 20),
            evidence_threshold=kwargs.get("evidence_threshold", 0.9),
            max_search_iterations=kwargs.get("max_search_iterations", 5),
            questions_per_iteration=kwargs.get("questions_per_iteration", 3),
            min_candidates_threshold=kwargs.get("min_candidates_threshold", 10),
            enable_pattern_learning=kwargs.get("enable_pattern_learning", True),
            settings_snapshot=settings_snapshot,
        )

    # Constrained search strategy
    elif strategy_name_lower == "constrained":
        from .advanced_search_system.strategies.constrained_search_strategy import (
            ConstrainedSearchStrategy,
        )

        return ConstrainedSearchStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            max_iterations=kwargs.get("max_iterations", 20),
            confidence_threshold=kwargs.get("confidence_threshold", 0.95),
            candidate_limit=kwargs.get("candidate_limit", 100),
            evidence_threshold=kwargs.get("evidence_threshold", 0.9),
            max_search_iterations=kwargs.get("max_search_iterations", 5),
            questions_per_iteration=kwargs.get("questions_per_iteration", 3),
            min_candidates_per_stage=kwargs.get("min_candidates_per_stage", 20),
            settings_snapshot=settings_snapshot,
        )

    # Parallel constrained strategy
    elif strategy_name_lower in [
        "parallel-constrained",
        "parallel_constrained",
    ]:
        from .advanced_search_system.strategies.parallel_constrained_strategy import (
            ParallelConstrainedStrategy,
        )

        return ParallelConstrainedStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            max_iterations=kwargs.get("max_iterations", 20),
            confidence_threshold=kwargs.get("confidence_threshold", 0.95),
            candidate_limit=kwargs.get("candidate_limit", 100),
            evidence_threshold=kwargs.get("evidence_threshold", 0.9),
            max_search_iterations=kwargs.get("max_search_iterations", 5),
            questions_per_iteration=kwargs.get("questions_per_iteration", 3),
            min_candidates_per_stage=kwargs.get("min_candidates_per_stage", 20),
            parallel_workers=kwargs.get("parallel_workers", 100),
            settings_snapshot=settings_snapshot,
        )

    # Early stop constrained strategy
    elif strategy_name_lower in [
        "early-stop-constrained",
        "early_stop_constrained",
    ]:
        from .advanced_search_system.strategies.early_stop_constrained_strategy import (
            EarlyStopConstrainedStrategy,
        )

        return EarlyStopConstrainedStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            max_iterations=kwargs.get("max_iterations", 20),
            confidence_threshold=kwargs.get("confidence_threshold", 0.95),
            candidate_limit=kwargs.get("candidate_limit", 100),
            evidence_threshold=kwargs.get("evidence_threshold", 0.9),
            max_search_iterations=kwargs.get("max_search_iterations", 5),
            questions_per_iteration=kwargs.get("questions_per_iteration", 3),
            min_candidates_per_stage=kwargs.get("min_candidates_per_stage", 20),
            parallel_workers=kwargs.get("parallel_workers", 100),
            early_stop_threshold=kwargs.get("early_stop_threshold", 0.99),
            concurrent_evaluation=kwargs.get("concurrent_evaluation", True),
            settings_snapshot=settings_snapshot,
        )

    # Smart query strategy
    elif strategy_name_lower in ["smart-query", "smart_query"]:
        from .advanced_search_system.strategies.smart_query_strategy import (
            SmartQueryStrategy,
        )

        return SmartQueryStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            max_iterations=kwargs.get("max_iterations", 20),
            confidence_threshold=kwargs.get("confidence_threshold", 0.95),
            candidate_limit=kwargs.get("candidate_limit", 100),
            evidence_threshold=kwargs.get("evidence_threshold", 0.9),
            max_search_iterations=kwargs.get("max_search_iterations", 5),
            questions_per_iteration=kwargs.get("questions_per_iteration", 3),
            min_candidates_per_stage=kwargs.get("min_candidates_per_stage", 20),
            parallel_workers=kwargs.get("parallel_workers", 100),
            early_stop_threshold=kwargs.get("early_stop_threshold", 0.99),
            concurrent_evaluation=kwargs.get("concurrent_evaluation", True),
            use_llm_query_generation=kwargs.get(
                "use_llm_query_generation", True
            ),
            queries_per_combination=kwargs.get("queries_per_combination", 3),
            settings_snapshot=settings_snapshot,
        )

    # Dual confidence strategy
    elif strategy_name_lower in ["dual-confidence", "dual_confidence"]:
        from .advanced_search_system.strategies.dual_confidence_strategy import (
            DualConfidenceStrategy,
        )

        return DualConfidenceStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            max_iterations=kwargs.get("max_iterations", 20),
            confidence_threshold=kwargs.get("confidence_threshold", 0.95),
            candidate_limit=kwargs.get("candidate_limit", 100),
            evidence_threshold=kwargs.get("evidence_threshold", 0.9),
            max_search_iterations=kwargs.get("max_search_iterations", 5),
            questions_per_iteration=kwargs.get("questions_per_iteration", 3),
            min_candidates_per_stage=kwargs.get("min_candidates_per_stage", 20),
            parallel_workers=kwargs.get("parallel_workers", 100),
            early_stop_threshold=kwargs.get("early_stop_threshold", 0.95),
            concurrent_evaluation=kwargs.get("concurrent_evaluation", True),
            use_llm_query_generation=kwargs.get(
                "use_llm_query_generation", True
            ),
            queries_per_combination=kwargs.get("queries_per_combination", 3),
            use_entity_seeding=kwargs.get("use_entity_seeding", True),
            use_direct_property_search=kwargs.get(
                "use_direct_property_search", True
            ),
            uncertainty_penalty=kwargs.get("uncertainty_penalty", 0.2),
            negative_weight=kwargs.get("negative_weight", 0.5),
            settings_snapshot=settings_snapshot,
        )

    # Dual confidence with rejection strategy
    elif strategy_name_lower in [
        "dual-confidence-with-rejection",
        "dual_confidence_with_rejection",
    ]:
        from .advanced_search_system.strategies.dual_confidence_with_rejection import (
            DualConfidenceWithRejectionStrategy,
        )

        return DualConfidenceWithRejectionStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            max_iterations=kwargs.get("max_iterations", 20),
            confidence_threshold=kwargs.get("confidence_threshold", 0.95),
            candidate_limit=kwargs.get("candidate_limit", 100),
            evidence_threshold=kwargs.get("evidence_threshold", 0.9),
            max_search_iterations=kwargs.get("max_search_iterations", 5),
            questions_per_iteration=kwargs.get("questions_per_iteration", 3),
            min_candidates_per_stage=kwargs.get("min_candidates_per_stage", 20),
            parallel_workers=kwargs.get("parallel_workers", 100),
            early_stop_threshold=kwargs.get("early_stop_threshold", 0.95),
            concurrent_evaluation=kwargs.get("concurrent_evaluation", True),
            use_llm_query_generation=kwargs.get(
                "use_llm_query_generation", True
            ),
            queries_per_combination=kwargs.get("queries_per_combination", 3),
            use_entity_seeding=kwargs.get("use_entity_seeding", True),
            use_direct_property_search=kwargs.get(
                "use_direct_property_search", True
            ),
            uncertainty_penalty=kwargs.get("uncertainty_penalty", 0.2),
            negative_weight=kwargs.get("negative_weight", 0.5),
            rejection_threshold=kwargs.get("rejection_threshold", 0.3),
            positive_threshold=kwargs.get("positive_threshold", 0.2),
            critical_constraint_rejection=kwargs.get(
                "critical_constraint_rejection", 0.2
            ),
            settings_snapshot=settings_snapshot,
        )

    # Concurrent dual confidence strategy
    elif strategy_name_lower in [
        "concurrent-dual-confidence",
        "concurrent_dual_confidence",
    ]:
        from .advanced_search_system.strategies.concurrent_dual_confidence_strategy import (
            ConcurrentDualConfidenceStrategy,
        )

        return ConcurrentDualConfidenceStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            max_iterations=kwargs.get("max_iterations", 20),
            confidence_threshold=kwargs.get("confidence_threshold", 0.95),
            candidate_limit=kwargs.get("candidate_limit", 100),
            evidence_threshold=kwargs.get("evidence_threshold", 0.9),
            max_search_iterations=kwargs.get("max_search_iterations", 5),
            questions_per_iteration=kwargs.get("questions_per_iteration", 3),
            min_candidates_per_stage=kwargs.get("min_candidates_per_stage", 20),
            parallel_workers=kwargs.get("parallel_workers", 10),
            early_stop_threshold=kwargs.get("early_stop_threshold", 0.95),
            concurrent_evaluation=kwargs.get("concurrent_evaluation", True),
            use_llm_query_generation=kwargs.get(
                "use_llm_query_generation", True
            ),
            queries_per_combination=kwargs.get("queries_per_combination", 3),
            use_entity_seeding=kwargs.get("use_entity_seeding", True),
            use_direct_property_search=kwargs.get(
                "use_direct_property_search", True
            ),
            uncertainty_penalty=kwargs.get("uncertainty_penalty", 0.2),
            negative_weight=kwargs.get("negative_weight", 0.5),
            rejection_threshold=kwargs.get("rejection_threshold", 0.3),
            positive_threshold=kwargs.get("positive_threshold", 0.2),
            critical_constraint_rejection=kwargs.get(
                "critical_constraint_rejection", 0.2
            ),
            min_good_candidates=kwargs.get("min_good_candidates", 3),
            target_candidates=kwargs.get("target_candidates", 5),
            max_candidates=kwargs.get("max_candidates", 10),
            min_score_threshold=kwargs.get("min_score_threshold", 0.65),
            exceptional_score=kwargs.get("exceptional_score", 0.95),
            quality_plateau_threshold=kwargs.get(
                "quality_plateau_threshold", 0.1
            ),
            max_search_time=kwargs.get("max_search_time", 30.0),
            max_evaluations=kwargs.get("max_evaluations", 30),
            settings_snapshot=settings_snapshot,
        )

    # Modular strategy
    elif strategy_name_lower in ["modular", "modular-strategy"]:
        from .advanced_search_system.strategies.modular_strategy import (
            ModularStrategy,
        )

        return ModularStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            constraint_checker_type=kwargs.get(
                "constraint_checker_type", "dual_confidence"
            ),
            exploration_strategy=kwargs.get("exploration_strategy", "adaptive"),
            early_rejection=kwargs.get("early_rejection", True),
            early_stopping=kwargs.get("early_stopping", True),
            llm_constraint_processing=kwargs.get(
                "llm_constraint_processing", True
            ),
            immediate_evaluation=kwargs.get("immediate_evaluation", True),
            settings_snapshot=settings_snapshot,
        )

    # Modular parallel strategy
    elif strategy_name_lower in ["modular-parallel", "modular_parallel"]:
        from .advanced_search_system.strategies.modular_strategy import (
            ModularStrategy,
        )

        return ModularStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            constraint_checker_type="dual_confidence",
            exploration_strategy="parallel",
            settings_snapshot=settings_snapshot,
        )

    # BrowseComp entity strategy
    elif strategy_name_lower in ["browsecomp-entity", "browsecomp_entity"]:
        from .advanced_search_system.strategies.browsecomp_entity_strategy import (
            BrowseCompEntityStrategy,
        )

        return BrowseCompEntityStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
        )

    # Standard strategy
    elif strategy_name_lower == "standard":
        from .advanced_search_system.strategies.standard_strategy import (
            StandardSearchStrategy,
        )

        return StandardSearchStrategy(
            model=model,
            search=search,
            all_links_of_system=all_links_of_system,
            settings_snapshot=settings_snapshot,
        )

    else:
        # Default to source-based if unknown
        logger.warning(
            f"Unknown strategy: {strategy_name}, defaulting to source-based"
        )
        from .advanced_search_system.strategies.source_based_strategy import (
            SourceBasedSearchStrategy,
        )

        return SourceBasedSearchStrategy(
            model=model,
            search=search,
            include_text_content=True,
            use_cross_engine_filter=True,
            all_links_of_system=all_links_of_system,
            use_atomic_facts=False,
            settings_snapshot=settings_snapshot,
            search_original_query=kwargs.get("search_original_query", True),
        )
