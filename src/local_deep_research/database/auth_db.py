"""
Authentication database initialization and management.
This manages the central ldr_auth.db which only stores usernames.
"""

from pathlib import Path

from loguru import logger
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from ..config.paths import get_data_directory
from .models.auth import User
from .models.base import Base


def get_auth_db_path() -> Path:
    """Get the path to the authentication database."""
    return get_data_directory() / "ldr_auth.db"


def init_auth_database():
    """Initialize the authentication database if it doesn't exist."""
    auth_db_path = get_auth_db_path()

    # Check if database already exists
    if auth_db_path.exists():
        logger.debug(f"Auth database already exists at {auth_db_path}")
        return

    logger.info(f"Creating auth database at {auth_db_path}")

    # Create the database
    engine = create_engine(f"sqlite:///{auth_db_path}")

    # Create tables
    Base.metadata.create_all(engine, tables=[User.__table__])

    logger.info("Auth database initialized successfully")


def get_auth_db_session():
    """Get a session for the auth database."""
    auth_db_path = get_auth_db_path()

    # Ensure database exists
    if not auth_db_path.exists():
        init_auth_database()

    engine = create_engine(f"sqlite:///{auth_db_path}")
    Session = sessionmaker(bind=engine)
    return Session()


# Initialize on import
init_auth_database()
