"""
Temporary authentication storage for handling post-registration flow.
Stores passwords briefly in memory to allow database access after redirect.
Note: Passwords are stored in plain text in memory (encryption removed as it
provided no real security benefit - see issue #593).
"""

import secrets
from typing import Op<PERSON>, <PERSON>ple

from loguru import logger

from .credential_store_base import CredentialStoreBase


class TemporaryAuthStore(CredentialStoreBase):
    """
    Stores authentication temporarily for post-registration/login flow.
    Passwords are stored in plain text in memory and expire after a short time.
    """

    def __init__(self, ttl_seconds: int = 30):
        """
        Initialize the temporary auth store.

        Args:
            ttl_seconds: How long to keep auth data (default 30 seconds)
        """
        super().__init__(ttl_seconds)

    def store_auth(self, username: str, password: str) -> str:
        """
        Store authentication temporarily.

        Args:
            username: Username
            password: Password to store

        Returns:
            Token to retrieve the auth data
        """
        token = secrets.token_urlsafe(32)
        self._store_credentials(
            token, {"username": username, "password": password}
        )
        logger.debug(f"Stored temporary auth for {username}")
        return token

    def retrieve_auth(self, token: str) -> Optional[Tuple[str, str]]:
        """
        Retrieve and remove authentication data.

        Args:
            token: Token from store_auth

        Returns:
            Tuple of (username, password) or None if expired/not found
        """
        result = self._retrieve_credentials(token, remove=True)
        if result:
            logger.debug(f"Retrieved temporary auth for {result[0]}")
        return result

    def peek_auth(self, token: str) -> Optional[Tuple[str, str]]:
        """
        Peek at authentication data without removing it.

        Args:
            token: Token from store_auth

        Returns:
            Tuple of (username, password) or None if expired/not found
        """
        return self._retrieve_credentials(token, remove=False)

    # Implement abstract methods for compatibility
    def store(self, username: str, password: str) -> str:
        """Alias for store_auth."""
        return self.store_auth(username, password)

    def retrieve(self, token: str) -> Optional[Tuple[str, str]]:
        """Alias for retrieve_auth."""
        return self.retrieve_auth(token)


# Global instance
temp_auth_store = TemporaryAuthStore()
