"""
Queue-related database models stored in each user's encrypted database.
"""

from sqlalchemy import Column, Integer, String
from sqlalchemy_utc import UtcDateTime, utcnow

from .base import Base


class QueueStatus(Base):
    """Track queue status for this user."""

    __tablename__ = "queue_status"

    id = Column(Integer, primary_key=True)
    active_tasks = Column(Integer, default=0)
    queued_tasks = Column(Integer, default=0)
    last_checked = Column(UtcDateTime, default=utcnow())
    last_task_id = Column(String, nullable=True)
    updated_at = Column(UtcDateTime, default=utcnow(), onupdate=utcnow())


class TaskMetadata(Base):
    """Store task metadata for background processing."""

    __tablename__ = "task_metadata"

    task_id = Column(String, primary_key=True)
    status = Column(
        String, nullable=False
    )  # queued, processing, completed, failed
    task_type = Column(String, nullable=False)  # research, benchmark, etc
    created_at = Column(UtcDateTime, default=utcnow())
    started_at = Column(UtcDateTime, nullable=True)
    completed_at = Column(UtcDateTime, nullable=True)
    error_message = Column(String, nullable=True)

    # Additional fields for task details
    priority = Column(Integer, default=0)  # Higher number = higher priority
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
