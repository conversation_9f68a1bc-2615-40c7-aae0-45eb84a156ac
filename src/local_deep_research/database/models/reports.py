"""
Report generation and storage models.
"""

from sqlalchemy import (
    JSON,
    Boolean,
    Column,
    Float,
    ForeignKey,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy_utc import UtcDateTime, utcnow

from .base import Base


class Report(Base):
    """
    Generated research reports.
    Can be in various formats and languages.
    """

    __tablename__ = "reports"

    id = Column(Integer, primary_key=True)
    research_task_id = Column(
        Integer, ForeignKey("research_tasks.id", ondelete="CASCADE")
    )

    # Report metadata
    title = Column(String(500))
    subtitle = Column(String(500))
    abstract = Column(Text)  # Summary/abstract
    content = Column(Text)  # Full report content (usually markdown)

    # Format and presentation
    format = Column(String(50), default="markdown")  # markdown, html, pdf, docx
    template = Column(String(100))  # Template used for generation
    style = Column(String(100))  # Style/theme applied
    language = Column(String(10), default="en")

    # Statistics
    word_count = Column(Integer)
    section_count = Column(Integer)
    reference_count = Column(Integer)
    image_count = Column(Integer)

    # Generation metadata
    generation_params = Column(JSON)  # Parameters used for generation
    generation_model = Column(String(100))  # AI model used
    generation_time_seconds = Column(Float)

    # Versioning
    version = Column(Integer, default=1)
    is_draft = Column(Boolean, default=False)

    # Timestamps
    created_at = Column(UtcDateTime, default=utcnow())
    updated_at = Column(UtcDateTime, default=utcnow(), onupdate=utcnow())
    published_at = Column(UtcDateTime)

    # Relationships
    research_task = relationship("ResearchTask", back_populates="reports")
    sections = relationship(
        "ReportSection",
        back_populates="report",
        cascade="all, delete-orphan",
        order_by="ReportSection.section_order",
    )

    def __repr__(self):
        return f"<Report(title='{self.title}', format='{self.format}', draft={self.is_draft})>"


class ReportSection(Base):
    """
    Individual sections within a report.
    Allows for structured document generation.
    """

    __tablename__ = "report_sections"

    id = Column(Integer, primary_key=True)
    report_id = Column(Integer, ForeignKey("reports.id", ondelete="CASCADE"))

    # Section metadata
    title = Column(String(500))
    subtitle = Column(String(500))
    content = Column(Text)

    # Structure
    section_order = Column(Integer, nullable=False)  # Order within report
    section_type = Column(
        String(50)
    )  # introduction, methodology, findings, conclusion, references, appendix
    section_level = Column(Integer, default=1)  # 1=H1, 2=H2, etc.
    parent_section_id = Column(
        Integer, ForeignKey("report_sections.id")
    )  # For nested sections

    # References and citations
    references = Column(JSON)  # List of SearchResult IDs used
    citations = Column(JSON)  # Formatted citations

    # Generation metadata
    auto_generated = Column(Boolean, default=True)
    edited = Column(Boolean, default=False)

    # Timestamps
    created_at = Column(UtcDateTime, default=utcnow())
    updated_at = Column(UtcDateTime, default=utcnow(), onupdate=utcnow())

    # Relationships
    report = relationship("Report", back_populates="sections")
    subsections = relationship(
        "ReportSection", backref="parent_section", remote_side=[id]
    )

    def __repr__(self):
        return f"<ReportSection(title='{self.title}', order={self.section_order}, type='{self.section_type}')>"
