"""
Rate limiting tracking models.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Float, Integer, String
from sqlalchemy_utc import UtcDateTime, utcnow

from .base import Base


class RateLimitAttempt(Base):
    """
    Track individual rate limit retry attempts.
    """

    __tablename__ = "rate_limit_attempts"

    id = Column(Integer, primary_key=True, index=True)
    engine_type = Column(String(100), nullable=False, index=True)
    timestamp = Column(Float, nullable=False, index=True)
    wait_time = Column(Float, nullable=False)
    retry_count = Column(Integer, nullable=False)
    success = Column(Boolean, nullable=False)
    error_type = Column(String(100), nullable=True)
    created_at = Column(UtcDateTime, server_default=utcnow(), nullable=False)

    def __repr__(self):
        return f"<RateLimitAttempt(engine={self.engine_type}, success={self.success}, wait={self.wait_time}s)>"


class RateLimitEstimate(Base):
    """
    Store current rate limit estimates per engine.
    """

    __tablename__ = "rate_limit_estimates"

    id = Column(Integer, primary_key=True, index=True)
    engine_type = Column(String(100), nullable=False, unique=True, index=True)
    base_wait_seconds = Column(Float, nullable=False)
    min_wait_seconds = Column(Float, nullable=False)
    max_wait_seconds = Column(Float, nullable=False)
    last_updated = Column(Float, nullable=False)
    total_attempts = Column(Integer, default=0, nullable=False)
    success_rate = Column(Float, default=0.0, nullable=False)
    created_at = Column(UtcDateTime, server_default=utcnow(), nullable=False)
    updated_at = Column(
        UtcDateTime, server_default=utcnow(), onupdate=utcnow(), nullable=False
    )

    def __repr__(self):
        return f"<RateLimitEstimate(engine={self.engine_type}, wait={self.base_wait_seconds}s, success_rate={self.success_rate:.1%})>"
