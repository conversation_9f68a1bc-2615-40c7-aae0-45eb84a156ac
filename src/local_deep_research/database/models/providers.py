"""
LLM provider and model management.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Integer, String, UniqueConstraint
from sqlalchemy_utc import UtcDateTime, utcnow

from .base import Base


class ProviderModel(Base):
    """
    Cache available models from all LLM providers.
    """

    __tablename__ = "provider_models"

    id = Column(Integer, primary_key=True, index=True)
    provider = Column(String(50), nullable=False, index=True)
    model_key = Column(String(255), nullable=False)
    model_label = Column(String(255), nullable=False)
    model_metadata = Column(JSON, nullable=True)  # For additional model info
    last_updated = Column(UtcDateTime, server_default=utcnow(), nullable=False)

    # Composite unique constraint to prevent duplicates
    __table_args__ = (
        UniqueConstraint("provider", "model_key", name="uix_provider_model"),
    )

    def __repr__(self):
        return (
            f"<ProviderModel(provider={self.provider}, model={self.model_key})>"
        )
