"""
Core research models for tasks, queries, and results.
"""

import enum

from sqlalchemy import (
    JSON,
    Column,
    Enum,
    Float,
    ForeignKey,
    Index,
    Integer,
    String,
    Text,
)
from sqlalchemy.orm import relationship
from sqlalchemy_utc import UtcDateTime, utcnow

from .base import Base


class ResearchTask(Base):
    """
    Main research tasks that users create.
    This is the top-level object that contains all research activities.
    """

    __tablename__ = "research_tasks"

    id = Column(Integer, primary_key=True)
    title = Column(String(500), nullable=False)
    description = Column(Text)
    status = Column(
        String(50), default="pending"
    )  # pending, in_progress, completed, failed
    priority = Column(Integer, default=0)  # Higher number = higher priority
    tags = Column(JSON)  # List of tags for categorization
    research_metadata = Column(JSON)  # Flexible field for additional data

    # Timestamps
    created_at = Column(UtcDateTime, default=utcnow())
    updated_at = Column(UtcDateTime, default=utcnow(), onupdate=utcnow())
    started_at = Column(UtcDateTime)
    completed_at = Column(UtcDateTime)

    # Relationships
    searches = relationship(
        "SearchQuery",
        back_populates="research_task",
        cascade="all, delete-orphan",
    )
    results = relationship(
        "SearchResult",
        back_populates="research_task",
        cascade="all, delete-orphan",
    )
    reports = relationship(
        "Report", back_populates="research_task", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<ResearchTask(title='{self.title}', status='{self.status}')>"


class SearchQuery(Base):
    """
    Individual search queries executed as part of research tasks.
    Tracks what was searched and when.
    """

    __tablename__ = "search_queries"

    id = Column(Integer, primary_key=True)
    research_task_id = Column(
        Integer, ForeignKey("research_tasks.id", ondelete="CASCADE")
    )
    query = Column(Text, nullable=False)
    search_engine = Column(String(50))  # google, bing, duckduckgo, etc.
    search_type = Column(String(50))  # web, academic, news, etc.
    parameters = Column(JSON)  # Additional search parameters

    # Status tracking
    status = Column(
        String(50), default="pending"
    )  # pending, executing, completed, failed
    error_message = Column(Text)
    retry_count = Column(Integer, default=0)

    # Timestamps
    created_at = Column(UtcDateTime, default=utcnow())
    executed_at = Column(UtcDateTime)
    completed_at = Column(UtcDateTime)

    # Relationships
    research_task = relationship("ResearchTask", back_populates="searches")
    results = relationship(
        "SearchResult",
        back_populates="search_query",
        cascade="all, delete-orphan",
    )

    # Indexes for performance
    __table_args__ = (
        Index("idx_research_task_status", "research_task_id", "status"),
        Index("idx_search_engine", "search_engine", "status"),
    )

    def __repr__(self):
        return f"<SearchQuery(query='{self.query[:50]}...', status='{self.status}')>"


class SearchResult(Base):
    """
    Individual search results from queries.
    Stores both the initial result and any fetched content.
    """

    __tablename__ = "search_results"

    id = Column(Integer, primary_key=True)
    research_task_id = Column(
        Integer, ForeignKey("research_tasks.id", ondelete="CASCADE")
    )
    search_query_id = Column(
        Integer, ForeignKey("search_queries.id", ondelete="CASCADE")
    )

    # Basic result information
    title = Column(String(500))
    url = Column(Text, index=True)  # Indexed for deduplication
    snippet = Column(Text)

    # Extended content
    content = Column(Text)  # Full content if fetched
    content_type = Column(String(50))  # html, pdf, text, etc.
    content_hash = Column(String(64))  # For deduplication

    # Metadata
    relevance_score = Column(Float)  # Calculated relevance
    position = Column(Integer)  # Position in search results
    domain = Column(String(255), index=True)
    language = Column(String(10))
    published_date = Column(UtcDateTime)
    author = Column(String(255))

    # Status tracking
    fetch_status = Column(String(50))  # pending, fetched, failed, skipped
    fetch_error = Column(Text)

    # Timestamps
    created_at = Column(UtcDateTime, default=utcnow())
    fetched_at = Column(UtcDateTime)

    # Relationships
    research_task = relationship("ResearchTask", back_populates="results")
    search_query = relationship("SearchQuery", back_populates="results")

    # Indexes for performance
    __table_args__ = (
        Index("idx_task_relevance", "research_task_id", "relevance_score"),
        Index("idx_content_hash", "content_hash"),
        Index("idx_domain_task", "domain", "research_task_id"),
    )

    def __repr__(self):
        return f"<SearchResult(title='{self.title[:50] if self.title else 'No title'}...', score={self.relevance_score})>"


class ResearchMode(enum.Enum):
    """Research modes available."""

    QUICK = "quick"
    DETAILED = "detailed"


class ResearchStatus(enum.Enum):
    """Status of research operations."""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    SUSPENDED = "suspended"


class ResearchResource(Base):
    """Resources associated with research projects."""

    __tablename__ = "research_resources"

    id = Column(Integer, primary_key=True, autoincrement=True)
    research_id = Column(
        String(36),
        ForeignKey("research_history.id", ondelete="CASCADE"),
        nullable=False,
    )
    title = Column(Text)
    url = Column(Text)
    content_preview = Column(Text)
    source_type = Column(Text)
    resource_metadata = Column("metadata", JSON)
    created_at = Column(String, nullable=False)

    # Relationship
    research = relationship("ResearchHistory", back_populates="resources")

    def __repr__(self):
        return f"<ResearchResource(title='{self.title}', url='{self.url}')>"


class ResearchHistory(Base):
    """
    Research history table.
    Tracks research sessions and their progress.
    """

    __tablename__ = "research_history"

    # UUID as primary key
    id = Column(String(36), primary_key=True)
    # The search query.
    query = Column(Text, nullable=False)
    # The mode of research (e.g., 'quick_summary', 'detailed_report').
    mode = Column(Text, nullable=False)
    # Current status of the research.
    status = Column(Text, nullable=False)
    # The timestamp when the research started.
    created_at = Column(Text, nullable=False)
    # The timestamp when the research was completed.
    completed_at = Column(Text)
    # Duration of the research in seconds.
    duration_seconds = Column(Integer)
    # Path to the generated report.
    report_path = Column(Text)
    # Report content stored in database
    report_content = Column(Text)
    # Additional metadata about the research.
    research_meta = Column(JSON)
    # Latest progress log message.
    progress_log = Column(JSON)
    # Current progress of the research (as a percentage).
    progress = Column(Integer)
    # Title of the research report.
    title = Column(Text)

    # Relationships
    resources = relationship(
        "ResearchResource",
        back_populates="research",
        cascade="all, delete-orphan",
    )

    def __repr__(self):
        return f"<ResearchHistory(query='{self.query[:50]}...', status={self.status})>"


class Research(Base):
    """
    Modern research tracking with better type safety.
    """

    __tablename__ = "research"

    id = Column(Integer, primary_key=True, index=True)
    query = Column(String, nullable=False)
    status = Column(
        Enum(ResearchStatus), default=ResearchStatus.PENDING, nullable=False
    )
    mode = Column(
        Enum(ResearchMode), default=ResearchMode.QUICK, nullable=False
    )
    created_at = Column(UtcDateTime, server_default=utcnow(), nullable=False)
    updated_at = Column(
        UtcDateTime, server_default=utcnow(), onupdate=utcnow(), nullable=False
    )
    progress = Column(Float, default=0.0, nullable=False)
    start_time = Column(UtcDateTime, nullable=True)
    end_time = Column(UtcDateTime, nullable=True)
    error_message = Column(Text, nullable=True)

    # Relationship
    strategy = relationship(
        "ResearchStrategy", back_populates="research", uselist=False
    )

    def __repr__(self):
        return f"<Research(query='{self.query[:50]}...', status={self.status.value})>"


class ResearchStrategy(Base):
    """
    Track which search strategy was used for each research.
    """

    __tablename__ = "research_strategies"

    id = Column(Integer, primary_key=True, index=True)
    research_id = Column(
        Integer,
        ForeignKey("research.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
        index=True,
    )
    strategy_name = Column(String(100), nullable=False, index=True)
    created_at = Column(UtcDateTime, server_default=utcnow(), nullable=False)

    # Relationship
    research = relationship("Research", back_populates="strategy")

    def __repr__(self):
        return f"<ResearchStrategy(research_id={self.research_id}, strategy={self.strategy_name})>"
