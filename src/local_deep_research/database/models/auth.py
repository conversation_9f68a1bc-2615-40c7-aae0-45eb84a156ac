"""
Authentication models for managing users.
Only stores username and metadata - passwords are never stored.
Each user gets their own encrypted database file.
"""

from datetime import datetime, UTC
from functools import partial

from sqlalchemy import Column, Integer, String
from sqlalchemy_utc import UtcDateTime

from ...config.paths import get_user_database_filename
from .base import Base


class User(Base):
    """
    User model - stored in a central auth database.
    Passwords are NEVER stored - they are used to decrypt individual user databases.
    """

    __tablename__ = "users"

    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False, index=True)
    created_at = Column(UtcDateTime, default=partial(datetime.now, UTC))
    last_login = Column(UtcDateTime)

    # Metadata only - no sensitive data
    database_version = Column(Integer, default=1)

    def __repr__(self):
        return f"<User {self.username}>"

    @property
    def database_path(self):
        """Path to this user's encrypted database file."""
        return get_user_database_filename(self.username)
