"""
Model for tracking active research processes per user.

As noted by <PERSON><PERSON><PERSON><PERSON>: "Is there a reason this isn't in `models.py`?"
Response: The codebase follows a domain-driven organization where models are
separated into individual files by functionality (auth.py, cache.py, research.py, etc.)
rather than a single models.py file. This provides better maintainability and follows
the established pattern in the models/ directory.
"""

from sqlalchemy import JSON, Boolean, Column, Index, Integer, String
from sqlalchemy_utc import UtcDateTime, utcnow

from .base import Base


class UserActiveResearch(Base):
    """
    Track active research processes for each user.
    Allows multiple concurrent researches per user.
    """

    __tablename__ = "user_active_researches"

    # Composite primary key: username + research_id
    username = Column(String(100), primary_key=True)
    research_id = Column(String(36), primary_key=True)  # UUID

    # Status tracking
    status = Column(String(50), default="in_progress")
    started_at = Column(UtcDateTime, server_default=utcnow(), nullable=False)
    last_heartbeat = Column(
        UtcDateTime, server_default=utcnow(), nullable=False
    )

    # Process information
    thread_id = Column(String(100))  # Thread identifier
    pid = Column(Integer)  # Process ID if using multiprocessing

    # Settings snapshot
    settings_snapshot = Column(JSON)  # Complete settings used for this research

    # Termination flag
    termination_requested = Column(Boolean, default=False)

    # Indexes for performance
    __table_args__ = (
        Index("idx_user_active_research", "username", "status"),
        Index("idx_research_heartbeat", "last_heartbeat"),
    )

    def __repr__(self):
        return f"<UserActiveResearch(user='{self.username}', research='{self.research_id}', status='{self.status}')>"
