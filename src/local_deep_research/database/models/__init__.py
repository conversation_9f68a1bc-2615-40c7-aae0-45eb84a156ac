"""
Database models for Local Deep Research.
All models are organized by domain for better maintainability.
"""

from .active_research import UserActiveResearch
from .auth import User
from .base import Base
from .benchmark import (
    BenchmarkConfig,
    BenchmarkProgress,
    BenchmarkResult,
    BenchmarkRun,
    BenchmarkStatus,
    DatasetType,
)
from .cache import Cache, SearchCache
from .logs import Journal, ResearchLog
from .metrics import ModelUsage, ResearchRating, SearchCall, TokenUsage
from .providers import ProviderModel
from .queue import QueueStatus, TaskMetadata
from .queued_research import QueuedResearch
from .rate_limiting import RateLimitAttempt, RateLimitEstimate
from .reports import Report, ReportSection
from .research import (
    Research,
    ResearchHistory,
    ResearchMode,
    ResearchResource,
    ResearchStatus,
    ResearchStrategy,
    ResearchTask,
    SearchQuery,
    SearchResult,
)
from .settings import APIKey, Setting, SettingType, UserSettings
from .user_news_search_history import UserNewsSearchHistory
from .news import (
    NewsSubscription,
    SubscriptionFolder,
    NewsCard,
    UserRating,
    UserPreference,
    NewsInterest,
    CardType,
    RatingType,
    SubscriptionType,
    SubscriptionStatus,
)

__all__ = [
    # Base
    "Base",
    # Active Research
    "UserActiveResearch",
    # Auth
    "User",
    # Queue
    "QueueStatus",
    "TaskMetadata",
    # Queued Research
    "QueuedResearch",
    # Benchmark
    "BenchmarkStatus",
    "DatasetType",
    "BenchmarkRun",
    "BenchmarkResult",
    "BenchmarkConfig",
    "BenchmarkProgress",
    # Cache
    "Cache",
    "SearchCache",
    # Logs
    "ResearchLog",
    "Journal",
    # Metrics
    "TokenUsage",
    "ModelUsage",
    "ResearchRating",
    "SearchCall",
    # Providers
    "ProviderModel",
    # Rate Limiting
    "RateLimitAttempt",
    "RateLimitEstimate",
    # Reports
    "Report",
    "ReportSection",
    # Research
    "ResearchTask",
    "SearchQuery",
    "SearchResult",
    "ResearchHistory",
    "Research",
    "ResearchStrategy",
    "ResearchMode",
    "ResearchStatus",
    "ResearchResource",
    # Settings
    "UserSettings",
    "APIKey",
    "Setting",
    "SettingType",
    # User News Search History
    "UserNewsSearchHistory",
    # News Models
    "NewsSubscription",
    "SubscriptionFolder",
    "NewsCard",
    "UserRating",
    "UserPreference",
    "NewsInterest",
    "CardType",
    "RatingType",
    "SubscriptionType",
    "SubscriptionStatus",
]
