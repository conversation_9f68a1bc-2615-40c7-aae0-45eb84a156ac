"""Database model for storing user-specific news search history.

This model tracks all news searches performed by users, allowing for
personalized search history that is properly isolated per user account.
"""

from sqlalchemy import Column, Integer, String, Text
from sqlalchemy_utc import UtcDateTime, utcnow

from .base import Base


class UserNewsSearchHistory(Base):
    """Stores news search queries performed by users.

    Each record represents a single search performed on the news page,
    tracking the query, when it was performed, and how many results were found.
    """

    __tablename__ = "user_news_search_history"

    id = Column(Integer, primary_key=True)

    # Search details
    query = Column(Text, nullable=False)
    search_type = Column(
        String(50), nullable=False, default="filter"
    )  # filter, deep, table
    result_count = Column(Integer, default=0)

    # Timestamps
    created_at = Column(
        UtcDateTime, nullable=False, default=utcnow(), index=True
    )

    def __repr__(self):
        return f"<UserNewsSearchHistory(query='{self.query[:30]}...', type={self.search_type})>"

    def to_dict(self):
        """Convert to dictionary for API responses."""
        return {
            "id": self.id,
            "query": self.query,
            "type": self.search_type,
            "timestamp": self.created_at.isoformat(),
            "resultCount": self.result_count,
        }
