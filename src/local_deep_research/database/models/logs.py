"""
Logging models for storing application logs and journal information.
"""

from sqlalchemy import (
    <PERSON>umn,
    Foreign<PERSON>ey,
    Integer,
    Sequence,
    String,
    Text,
)
from sqlalchemy_utc import UtcDateTime, utcnow

from .base import Base


class ResearchLog(Base):
    """
    Logging table for all research operations.

    All logging from research operations, including debug messages,
    errors, and milestones are stored here.
    """

    __tablename__ = "app_logs"

    id = Column(
        Integer, Sequence("reseach_log_id_seq"), primary_key=True, index=True
    )

    timestamp = Column(UtcDateTime, server_default=utcnow(), nullable=False)
    message = Column(Text, nullable=False)
    # Module that the log message came from.
    module = Column(Text, nullable=False)
    # Function that the log message came from.
    function = Column(Text, nullable=False)
    # Line number that the log message came from.
    line_no = Column(Integer, nullable=False)
    # Log level.
    level = Column(String(32), nullable=False)
    research_id = Column(
        String(36),  # UUID as string
        ForeignKey("research_history.id", ondelete="CASCADE"),
        nullable=True,
        index=True,
    )

    def __repr__(self):
        return f"<ResearchLog({self.level}: '{self.message[:50]}...')>"


class Journal(Base):
    """
    Information about academic journals for quality scoring.
    """

    __tablename__ = "journals"

    id = Column(
        Integer, Sequence("journal_id_seq"), primary_key=True, index=True
    )

    # Name of the journal
    name = Column(String(255), nullable=False, unique=True, index=True)
    # Quality score of the journal
    quality = Column(Integer, nullable=True)
    # Model that was used to generate the quality score.
    quality_model = Column(String(255), nullable=True, index=True)
    # Time at which the quality was last analyzed.
    quality_analysis_time = Column(Integer, nullable=False)

    def __repr__(self):
        return f"<Journal(name='{self.name}', quality={self.quality})>"
