"""Queued research model for managing research queue"""

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>n, Integer, String
from sqlalchemy_utc import UtcDateTime, utcnow

from .base import Base


class QueuedResearch(Base):
    """Model to track queued research requests"""

    __tablename__ = "queued_researches"

    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True)

    # User and research identifiers
    username = Column(String(100), nullable=False, index=True)
    research_id = Column(String(36), nullable=False, unique=True)  # UUID

    # Research parameters
    query = Column(String(1000), nullable=False)
    mode = Column(String(50), nullable=False)
    settings_snapshot = Column(JSON)  # All research settings

    # Queue metadata
    position = Column(Integer, nullable=False)  # Position in queue
    created_at = Column(UtcDateTime, server_default=utcnow())

    # Status
    is_processing = Column(<PERSON><PERSON>an, default=False)  # Being processed

    def __repr__(self):
        return f"<QueuedResearch(username={self.username}, research_id={self.research_id}, position={self.position})>"
