"""
Base class for credential storage with TTL.
Provides common functionality for storing credentials with TTL expiration.

NOTE: Credentials are stored in plain text in memory. The encryption
functionality was removed as it provided no real security benefit -
if an attacker can read process memory, they have sufficient privileges
to bypass any protections anyway.
"""

import time
from abc import ABC, abstractmethod
from threading import Lock
from typing import Dict, Optional, Tuple


class CredentialStoreBase(ABC):
    """
    Base class for storing credentials with expiration.
    Credentials are stored in plain text in memory with TTL-based expiration.
    """

    def __init__(self, ttl_seconds: int):
        """
        Initialize the store.

        Args:
            ttl_seconds: How long to keep data before expiration
        """
        self.ttl = ttl_seconds
        self._store: Dict[str, Dict] = {}
        self._lock = Lock()

    def _store_credentials(self, key: str, data: Dict[str, str]) -> None:
        """
        Store credentials with expiration.

        Args:
            key: Storage key
            data: Dictionary with 'username' and 'password'
        """
        with self._lock:
            # Store with expiration (plain text)
            self._store[key] = {
                "username": data["username"],
                "password": data["password"],
                "expires_at": time.time() + self.ttl,
            }

            # Clean up expired entries
            self._cleanup_expired()

    def _retrieve_credentials(
        self, key: str, remove: bool = False
    ) -> Optional[Tuple[str, str]]:
        """
        Retrieve stored credentials.

        Args:
            key: Storage key
            remove: Whether to remove after retrieval

        Returns:
            Tuple of (username, password) or None if not found/expired
        """
        with self._lock:
            if key not in self._store:
                return None

            entry = self._store[key]

            # Check expiration
            if time.time() > entry["expires_at"]:
                del self._store[key]
                return None

            # Get data
            username = entry["username"]
            password = entry["password"]

            # Remove if requested
            if remove:
                del self._store[key]

            return username, password

    def _cleanup_expired(self):
        """Remove expired entries."""
        current_time = time.time()
        expired_keys = [
            key
            for key, entry in self._store.items()
            if current_time > entry["expires_at"]
        ]
        for key in expired_keys:
            del self._store[key]

    def clear_entry(self, key: str) -> None:
        """
        Clear a specific entry.

        Args:
            key: Storage key
        """
        with self._lock:
            if key in self._store:
                del self._store[key]

    @abstractmethod
    def store(self, *args, **kwargs):
        """Store credentials - to be implemented by subclasses."""
        pass

    @abstractmethod
    def retrieve(self, *args, **kwargs):
        """Retrieve credentials - to be implemented by subclasses."""
        pass
