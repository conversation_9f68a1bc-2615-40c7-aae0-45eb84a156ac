"""
Session-based password storage for metrics access.

This module provides a way to temporarily store passwords in memory
for the duration of a user's session, allowing background threads
to access encrypted databases for metrics writing.

SECURITY NOTES:
1. Passwords are only stored in memory, never on disk
2. Passwords are stored in plain text in memory (encryption removed as it
   provided no real security benefit - see issue #593)
3. Passwords are automatically cleared on logout
4. This is only used for metrics/logging, not user data access
"""

from typing import Optional

from loguru import logger

from .credential_store_base import CredentialStoreBase


class SessionPasswordStore(CredentialStoreBase):
    """
    Stores passwords temporarily for active sessions.
    Used to allow background threads to write metrics to encrypted databases.
    """

    def __init__(self, ttl_hours: int = 24):
        """
        Initialize the session password store.

        Args:
            ttl_hours: How long to keep passwords (default 24 hours)
        """
        super().__init__(ttl_hours * 3600)  # Convert to seconds

    def store_session_password(
        self, username: str, session_id: str, password: str
    ) -> None:
        """
        Store a password for an active session.

        Args:
            username: The username
            session_id: The Flask session ID
            password: The password to store
        """
        key = f"{username}:{session_id}"
        self._store_credentials(
            key, {"username": username, "password": password}
        )
        logger.debug(f"Stored session password for {username}")

    def get_session_password(
        self, username: str, session_id: str
    ) -> Optional[str]:
        """
        Retrieve a password for an active session.

        Args:
            username: The username
            session_id: The Flask session ID

        Returns:
            The decrypted password or None if not found/expired
        """
        key = f"{username}:{session_id}"
        result = self._retrieve_credentials(key, remove=False)
        return result[1] if result else None

    def clear_session(self, username: str, session_id: str) -> None:
        """
        Clear password for a specific session (on logout).

        Args:
            username: The username
            session_id: The Flask session ID
        """
        key = f"{username}:{session_id}"
        self.clear_entry(key)
        logger.debug(f"Cleared session password for {username}")

    # Implement abstract methods for compatibility
    def store(self, username: str, session_id: str, password: str) -> None:
        """Alias for store_session_password."""
        self.store_session_password(username, session_id, password)

    def retrieve(self, username: str, session_id: str) -> Optional[str]:
        """Alias for get_session_password."""
        return self.get_session_password(username, session_id)


# Global instance
session_password_store = SessionPasswordStore()
