#!/usr/bin/env python3
"""Quick script to run the subscription folders migration"""

from local_deep_research.news.database import get_news_database
from local_deep_research.news.migrations import (
    apply_subscription_folders_migration,
)
from loguru import logger

if __name__ == "__main__":
    logger.info("Running subscription folders migration...")

    db = get_news_database()
    db.initialize()

    # Run migration
    apply_subscription_folders_migration(db.engine)

    logger.info("Migration completed!")

    # Check the columns
    from sqlalchemy import text

    with db.engine.connect() as conn:
        result = conn.execute(text("PRAGMA table_info(news_subscriptions)"))
        columns = [row[1] for row in result]
        logger.info(f"Current columns in news_subscriptions: {columns}")
