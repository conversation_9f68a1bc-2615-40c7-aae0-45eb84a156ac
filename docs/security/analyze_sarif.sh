#!/bin/bash

# CodeQL SARIF Analysis Script for Unix/Linux
# This script analyzes CodeQL SARIF results using Ollama for human-readable explanations

# Configuration
SARIF_PATH="python-results.sarif"
OUTPUT_PATH="codeql_analysis_results.txt"
OLLAMA_ENDPOINT="http://localhost:11434/api/generate"
MODEL="deepseek-r1:32b"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to check if Ollama is running
check_ollama() {
    echo -n "Checking Ollama connection... "
    if curl -s "$OLLAMA_ENDPOINT" > /dev/null; then
        echo -e "${GREEN}OK${NC}"
        return 0
    else
        echo -e "${RED}FAILED${NC}"
        echo -e "${RED}Error: Ollama is not running. Please start Ollama first.${NC}"
        return 1
    fi
}

# Function to validate SARIF file
validate_sarif() {
    echo -n "Validating SARIF file... "
    if [ ! -f "$SARIF_PATH" ]; then
        echo -e "${RED}FAILED${NC}"
        echo -e "${RED}Error: SARIF file not found at: $SARIF_PATH${NC}"
        return 1
    fi

    if jq empty "$SARIF_PATH" 2>/dev/null; then
        echo -e "${GREEN}OK${NC}"
        return 0
    else
        echo -e "${RED}FAILED${NC}"
        echo -e "${RED}Error: Invalid SARIF file format${NC}"
        return 1
    fi
}

# Main script
echo -e "${CYAN}CodeQL SARIF Analysis Tool${NC}"
echo -e "${CYAN}=========================${NC}"

# Check if required tools are installed
command -v curl >/dev/null 2>&1 || { echo -e "${RED}Error: curl is required but not installed.${NC}"; exit 1; }
command -v jq >/dev/null 2>&1 || { echo -e "${RED}Error: jq is required but not installed.${NC}"; exit 1; }

# Check if Ollama is running
check_ollama || exit 1

# Validate SARIF file
validate_sarif || exit 1

# Read SARIF content
echo -n "Reading SARIF file... "
if SARIF_CONTENT=$(cat "$SARIF_PATH"); then
    echo -e "${GREEN}OK${NC}"
else
    echo -e "${RED}FAILED${NC}"
    echo -e "${RED}Error: Failed to read SARIF file${NC}"
    exit 1
fi

# Prepare the prompt
PROMPT="Please analyze these security findings from CodeQL analysis.
Focus on:
1. Critical vulnerabilities
2. High priority issues
3. Potential impact
4. Recommended fixes

Here is the analysis data:
$SARIF_CONTENT"

# Prepare the request body
REQUEST_BODY=$(jq -n \
    --arg model "$MODEL" \
    --arg prompt "$PROMPT" \
    '{"model": $model, "prompt": $prompt}')

# Make the request to Ollama and process the streaming response
echo -n "Analyzing results with Ollama... "
if curl -s -X POST "$OLLAMA_ENDPOINT" \
    -H "Content-Type: application/json" \
    -d "$REQUEST_BODY" | \
    while IFS= read -r line; do
        if [ ! -z "$line" ]; then
            echo "$line" | jq -r '.response // empty'
        fi
    done > "$OUTPUT_PATH"; then
    echo -e "${GREEN}OK${NC}"
else
    echo -e "${RED}FAILED${NC}"
    echo -e "${RED}Error: Failed to analyze with Ollama${NC}"
    exit 1
fi

echo -e "\n${GREEN}Analysis complete!${NC}"
echo -e "${YELLOW}Results saved to: $OUTPUT_PATH${NC}"
echo -e "${YELLOW}You can view the results by opening: $OUTPUT_PATH${NC}"
