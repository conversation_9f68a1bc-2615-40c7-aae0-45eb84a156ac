# Local Deep Research v0.4.0 Release Notes

We're excited to announce Local Deep Research v0.4.0, bringing significant improvements to search capabilities, model integrations, and overall system performance.

## Major Enhancements

### LLM Improvements
- **Custom OpenAI Endpoint Support**: Added support for custom OpenAI-compatible endpoints
- **Dynamic Model Fetching**: Improved model discovery for both OpenAI and Anthropic using their official packages
- **Increased Context Window**: Enhanced default context window size and maximum limits

### Search Enhancements
- **Journal Quality Assessment**: Added capability to estimate journal reputation and quality for academic sources
- **Enhanced SearXNG Integration**: Fixed API key handling and prioritized SearXNG in auto search
- **Elasticsearch Improvements**: Added English translations to Chinese content in Elasticsearch files

### User Experience
- **Search Engine Visibility**: Added display of selected search engine during research
- **Better API Key Management**: Improved handling of search engine API keys from database settings
- **Custom Context Windows**: Added user-configurable context window size for LLMs

### System Improvements
- **Logging System Upgrade**: Migrated to `loguru` for improved logging capabilities
- **Memory Optimization**: Fixed high memory usage when journal quality filtering is enabled
- **Resumable Benchmarks**: Added support for resuming interrupted benchmark runs

## Bug Fixes
- Fixed broken SearXNG API key setting
- Memory usage optimizations for journal quality filtering
- Cleanup of OpenAI endpoint model loading features
- Various fixes for evaluation scripts
- Improved settings manager reliability

## Development Improvements
- Added test coverage for settings manager
- Cleaner code organization for LLM integration
- Enhanced API key handling from database settings

## New Contributors
- @JayLiu7319 contributed support for Custom OpenAI Endpoint models

## Full Changelog
For complete details of all changes, see the [full changelog](https://github.com/LearningCircuit/local-deep-research/compare/v0.3.12...v0.4.0).

---

## Installation

Download the [Windows Installer](https://github.com/LearningCircuit/local-deep-research/releases/download/v0.4.0/LocalDeepResearch_Setup.exe) or install via pip:

```bash
pip install local-deep-research
```

Requires Ollama or other LLM provider. See the [README](https://github.com/LearningCircuit/local-deep-research/blob/main/README.md) for complete setup instructions.
