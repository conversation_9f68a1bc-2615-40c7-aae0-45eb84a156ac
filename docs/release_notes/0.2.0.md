# Local Deep Research v0.2.0 Release Notes

We're excited to announce Local Deep Research v0.2.0, a major update that brings significant improvements to research capabilities, performance, and user experience.

## Major Enhancements

### New Search Strategies
- **Parallel Search**: Lightning-fast research that processes multiple questions simultaneously
- **Iterative Deep Search**: Enhanced exploration of complex topics with improved follow-up questions
- **Cross-Engine Filtering**: Smart result ranking across multiple search engines for higher quality information

### Improved Search Integrations
- **Enhanced SearxNG Support**: Better integration with self-hosted SearxNG instances
- **Improved GitHub Integration**: More effective search and analysis of code repositories
- **Better Source Selection**: Refined logic for choosing the most appropriate search engines per query

### Technical Improvements
- **Unified Database**: All settings and history now in a single `ldr.db` database
- **Improved Ollama Integration**: Better reliability and error handling with local models
- **Enhanced Error Recovery**: More graceful handling of connectivity issues and API errors

### User Experience
- **Enhanced Logging Panel**: Improved visibility with duplicate detection and better filtering
- **Streamlined Settings UI**: Reorganized settings interface with better organization
- **Research Progress Tracking**: More detailed real-time updates during research

### Development Improvements
- **PDM Support**: Switched to PDM for dependency management
- **Pre-commit Hooks**: Added linting and code quality checks
- **Code Security**: Added CodeQL integration with analysis scripts
- **Improved Documentation**: Better development guides and setup instructions

## API Changes

- Renamed and consolidated some API functions for consistency
- Added support for additional parameters in research configuration
- Improved error handling and response formatting

## Migration Notes

- The application now uses a unified database (`ldr.db`) that will automatically migrate data from older databases
- If upgrading from v0.1.x, your settings and research history will be automatically migrated on first run
- The `llm_config.py` file has been deprecated in favor of direct environment variable configuration

## Bug Fixes

- Fixed issues with settings persistence across sessions
- Resolved UI rendering problems in the history and results pages
- Fixed socket.io event handling and client disconnection issues
- Improved handling of large document collections
- Fixed API endpoint URL inconsistencies

## Contributors
This release represents the combined efforts of multiple contributors :
- @djpetti, @HashedViking, @LearningCircuit (core contributors to this release; sorted alphabetically)
- @dim-tsoukalas, @scottvr (sorted alphabetically)

## Get Involved

- Join our [Discord](https://discord.gg/ttcqQeFcJ3) for support and discussions
- Follow our [Subreddit](https://www.reddit.com/r/LocalDeepResearch/) for announcements and updates
- Report bugs and request features on our [GitHub Issues](https://github.com/LearningCircuit/local-deep-research/issues)

---

## Installation

Download the [Windows Installer](https://github.com/LearningCircuit/local-deep-research/releases/download/v0.2.0/LocalDeepResearch_Setup.exe) or install via pip:

```bash
pip install local-deep-research
```

Requires Ollama or other LLM provider. See the [README](https://github.com/LearningCircuit/local-deep-research/blob/main/README.md) for complete setup instructions.
