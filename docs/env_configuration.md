# Configuring Local Deep Research with Environment Variables

You can override any configuration setting in Local Deep Research using environment variables. This is useful for:

- Setting up multiple environments (development, production)
- Changing settings without modifying configuration files
- Providing sensitive information like API keys securely
- Setting server ports for Docker or cloud deployments

## Environment Variable Format

Local Deep Research uses Dynaconf to manage configuration. The format for environment variables is:

```
LDR_SECTION__SETTING=value
```

Note the **double underscore** (`__`) between the section and setting name.

## Using .env Files in the Config Directory

The easiest way to configure settings is to create a `.env` file in your config directory:

**Config Directory Locations:**
- Windows: `%USERPROFILE%\Documents\LearningCircuit\local-deep-research\config\.env`
- Linux/Mac: `~/.config/local_deep_research/config/.env`

Simply create a text file named `.env` in this directory and add your settings:

```
# Example .env file contents
LDR_WEB__PORT=8080
LDR_SEARCH__TOOL=wikipedia
LDR_GENERAL__ENABLE_FACT_CHECKING=true

# API keys (see important note below)
OPENAI_API_KEY=your-key-here
LDR_OPENAI_API_KEY=your-key-here
```

This file is automatically loaded when Local Deep Research starts, and any settings specified here will override those in the main configuration files.

## Important Note About API Keys

**Known Bug**: Currently, API keys must be set **both with and without** the `LDR_` prefix for search engines to work properly:

```bash
# You need BOTH of these for each API key
export OPENAI_API_KEY=your-key-here
export LDR_OPENAI_API_KEY=your-key-here
```

This applies to all search-related API keys including:
- `OPENAI_API_KEY`
- `ANTHROPIC_API_KEY`
- `SERP_API_KEY`
- `BRAVE_API_KEY`
- `GOOGLE_PSE_API_KEY`
- `GOOGLE_PSE_ENGINE_ID`
- `GUARDIAN_API_KEY`

This issue will be fixed in a future update.

## Examples

| Config in settings.toml | Environment Variable | Example |
|-------------------------|----------------------|---------|
| `[web]` port = 5000 | `LDR_WEB__PORT` | `LDR_WEB__PORT=8080` |
| `[search]` tool = "auto" | `LDR_SEARCH__TOOL` | `LDR_SEARCH__TOOL=wikipedia` |
| `[general]` enable_fact_checking = false | `LDR_GENERAL__ENABLE_FACT_CHECKING` | `LDR_GENERAL__ENABLE_FACT_CHECKING=true` |

## API Keys

API keys are best set using environment variables for security (remember the current requirement for both prefixed and non-prefixed versions):

```bash
# Set both versions for each API key
ANTHROPIC_API_KEY=your-api-key-here
LDR_ANTHROPIC_API_KEY=your-api-key-here

OPENAI_API_KEY=your-openai-key-here
LDR_OPENAI_API_KEY=your-openai-key-here

SERP_API_KEY=your-api-key-here
LDR_SERP_API_KEY=your-api-key-here
```

## Docker Usage

For Docker deployments, you can pass environment variables when starting containers:

```bash
docker run -p 8080:8080 \
  -e LDR_WEB__PORT=8080 \
  -e LDR_SEARCH__TOOL=wikipedia \
  -e OPENAI_API_KEY=your-key-here \
  -e LDR_OPENAI_API_KEY=your-key-here \
  local-deep-research
```

## Common Operations

### Changing the Web Port

```bash
export LDR_WEB__PORT=8080  # Linux/Mac
set LDR_WEB__PORT=8080     # Windows
```

### Setting API Keys (with current dual requirement)

```bash
# Linux/Mac
export ANTHROPIC_API_KEY=your-key-here
export LDR_ANTHROPIC_API_KEY=your-key-here

# Windows
set ANTHROPIC_API_KEY=your-key-here
set LDR_ANTHROPIC_API_KEY=your-key-here
```

### Changing Search Engine

```bash
export LDR_SEARCH__TOOL=wikipedia  # Linux/Mac
set LDR_SEARCH__TOOL=wikipedia     # Windows
```

### Data Directory Location

By default, Local Deep Research stores all data (database, research outputs, cache, logs) in platform-specific user directories. You can override this location using the `LDR_DATA_DIR` environment variable:

```bash
# Linux/Mac
export LDR_DATA_DIR=/path/to/your/data/directory

# Windows
set LDR_DATA_DIR=C:\path\to\your\data\directory
```

All application data will be organized under this directory:
- `$LDR_DATA_DIR/ldr.db` - Application database
- `$LDR_DATA_DIR/research_outputs/` - Research reports
- `$LDR_DATA_DIR/cache/` - Cached data
- `$LDR_DATA_DIR/logs/` - Application logs
