# Frontend Build System Guide

## Overview

Local Deep Research uses a modern frontend build system with **npm** for package management and **Vite** for bundling. This ensures all JavaScript libraries and CSS frameworks are served locally without any external CDN dependencies.

## Quick Start

### Development Mode

1. **Install dependencies** (first time only):
   ```bash
   npm install
   ```

2. **Start the Flask server**:
   ```bash
   cd src
   python -m local_deep_research.web.app
   ```

3. **Start Vite dev server** (in a separate terminal):
   ```bash
   npm run dev
   ```

   Vite will start on http://localhost:5173 with Hot Module Replacement (HMR) for instant updates.

### Production Mode

1. **Build production assets**:
   ```bash
   npm run build
   ```

   This creates optimized bundles in `src/local_deep_research/web/static/dist/`

2. **Start Flask normally**:
   ```bash
   cd src
   python -m local_deep_research.web.app
   ```

   Flask will automatically serve the built assets from the `dist/` folder.

## Architecture

### Why No CDNs?

- **Security**: All code is audited and served from your server
- **Privacy**: No user data leaks to third-party CDNs
- **Reliability**: Works offline, no dependency on external services
- **Performance**: Assets are optimized and cached locally
- **Compliance**: Important for enterprise/government deployments

### Technology Stack

- **npm**: Package manager for JavaScript dependencies
- **Vite**: Fast build tool with instant HMR in development
- **Flask**: Python web framework serving the application

## Dependencies

All frontend dependencies are managed in `package.json`:

| Library | Purpose | License |
|---------|---------|---------|
| `@fortawesome/fontawesome-free` | Icons throughout the UI | Font Awesome Free |
| `bootstrap` | CSS framework for some pages | MIT |
| `bootstrap-icons` | Additional icons | MIT |
| `chart.js` | Analytics charts | MIT |
| `highlight.js` | Code syntax highlighting | BSD-3-Clause |
| `marked` | Markdown rendering | MIT |
| `socket.io-client` | Real-time updates | MIT |
| `jspdf` & `html2canvas` | PDF export | MIT |

## File Structure

```
.
├── package.json                 # npm dependencies and scripts
├── vite.config.js              # Vite configuration
├── node_modules/               # Downloaded packages (git-ignored)
└── src/local_deep_research/web/
    ├── static/
    │   ├── dist/              # Production build output (git-ignored)
    │   │   ├── css/           # Bundled CSS
    │   │   ├── fonts/         # Font files (Font Awesome, Bootstrap Icons)
    │   │   └── js/            # Bundled JavaScript
    │   ├── js/
    │   │   ├── app.js         # Main entry point importing all dependencies
    │   │   └── ...            # Other application JavaScript
    │   └── css/
    │       └── styles.css     # Application styles
    ├── templates/
    │   └── base.html          # Uses Vite helper for asset loading
    └── utils/
        └── vite_helper.py     # Flask-Vite integration

```

## Common Tasks

### Update Dependencies

```bash
# Update all packages to latest versions
npm update

# Check for security vulnerabilities
npm audit

# Auto-fix vulnerabilities (if possible)
npm audit fix

# Rebuild after updates
npm run build
```

### Add a New Library

1. Install the package:
   ```bash
   npm install library-name
   ```

2. Import in `src/local_deep_research/web/static/js/app.js`:
   ```javascript
   import 'library-name/dist/library.css';  // If it has CSS
   import Library from 'library-name';      // Import JS

   // Make available globally if needed
   window.Library = Library;
   ```

3. Rebuild:
   ```bash
   npm run build
   ```

### Debug Build Issues

```bash
# Clean install (removes node_modules and reinstalls)
rm -rf node_modules package-lock.json
npm install

# Verbose build output
npm run build -- --debug

# Check what's included in the bundle
npm run build -- --sourcemap
```

## Security

### Automated Checks

- **Pre-commit Hook**: `.pre-commit-hooks/check-external-resources.py` prevents CDN references
- **GitHub Dependabot**: Enable it to get automated PRs for security updates
- **npm audit**: Run in CI/CD pipeline to catch vulnerabilities

### Manual Security Audit

```bash
# Check for known vulnerabilities
npm audit

# See dependency tree
npm list

# Check outdated packages
npm outdated
```

## Troubleshooting

### Icons Not Showing

**Problem**: Font Awesome or Bootstrap icons appear as squares

**Solution**:
1. Ensure fonts were built: Check `src/local_deep_research/web/static/dist/fonts/`
2. Rebuild if missing: `npm run build`
3. Clear browser cache: Ctrl+F5 (or Cmd+Shift+R on Mac)

### JavaScript Not Loading

**Problem**: Libraries like marked or Chart.js not working

**Solution**:
1. Check browser console for errors
2. Verify npm packages installed: `npm install`
3. Rebuild assets: `npm run build`
4. Check Flask is serving from dist: Look for `.vite/manifest.json`

### Vite Dev Server Issues

**Problem**: HMR not working or connection refused

**Solution**:
1. Ensure Vite is running: `npm run dev`
2. Check port 5173 is free: `lsof -i :5173`
3. Verify Flask debug mode is on for development

### Build Errors

**Problem**: `npm run build` fails

**Solution**:
1. Check Node.js version: `node --version` (should be 16+ or 18+)
2. Clear cache and reinstall:
   ```bash
   rm -rf node_modules package-lock.json
   npm cache clean --force
   npm install
   ```
3. Check for conflicting global packages: `npm list -g --depth=0`

## Development Tips

### Using Vite Dev Server

When developing, Vite provides:
- **Instant updates**: Changes appear immediately without page refresh
- **Better errors**: Build errors shown in browser
- **Fast startup**: No bundling needed during development

### Production Optimization

Vite automatically:
- Minifies JavaScript and CSS
- Tree-shakes unused code
- Splits code into chunks
- Generates sourcemaps for debugging
- Optimizes images and fonts
- Creates cache-busting hashes

### Flask Integration

The `ViteHelper` class (`src/local_deep_research/web/utils/vite_helper.py`) handles:
- Loading from Vite dev server in development
- Loading built assets in production
- Fallback if build hasn't run yet

## CI/CD Integration

Add to your CI pipeline:

```yaml
# Example GitHub Actions
- name: Setup Node.js
  uses: actions/setup-node@v3
  with:
    node-version: '18'
    cache: 'npm'

- name: Install dependencies
  run: npm ci

- name: Security audit
  run: npm audit --audit-level=high

- name: Build assets
  run: npm run build

- name: Check for external resources
  run: python .pre-commit-hooks/check-external-resources.py
```

## Migration from CDNs

If you're updating from an older version that used CDNs:

1. **Pull latest changes**
2. **Install npm dependencies**: `npm install`
3. **Build assets**: `npm run build`
4. **Clear browser cache**: CDN resources may be cached
5. **Test thoroughly**: Ensure all features work offline

## Contributing

When adding frontend features:

1. **No external resources**: All assets must be in npm packages
2. **Update package.json**: Add new dependencies properly
3. **Import in app.js**: Ensure libraries are imported
4. **Test the build**: Run `npm run build` before committing
5. **Document changes**: Update this guide if needed

## Support

- **Build issues**: Check Node.js version and reinstall packages
- **Runtime issues**: Check browser console and Flask logs
- **Security concerns**: Run `npm audit` and update packages
- **Performance**: Vite automatically optimizes; check bundle size with `npm run build`

---

*Last updated: August 2024*
*Vite version: 5.x*
*npm version: 10.x*
