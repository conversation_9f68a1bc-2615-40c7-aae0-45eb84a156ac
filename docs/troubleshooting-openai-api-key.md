# Troubleshooting OpenAI API Key Configuration

This guide helps troubleshoot common issues with OpenAI API key configuration in Local Deep Research v1.0+.

## Quick Test

Run the end-to-end test to verify your configuration:

```bash
# Using command line arguments
python tests/test_openai_api_key_e2e.py \
  --username YOUR_USERNAME \
  --password YOUR_PASSWORD \
  --api-key YOUR_OPENAI_API_KEY

# Using environment variables
export LDR_USERNAME=your_username
export LDR_PASSWORD=your_password
export OPENAI_API_KEY=sk-your-api-key
python tests/test_openai_api_key_e2e.py
```

## Common Issues and Solutions

### 1. "No API key found"

**Symptoms:**
- Error message about missing API key
- Research fails to start

**Solutions:**

1. **Via Web Interface:**
   - Login to LDR web interface
   - Go to Settings
   - Select "OpenAI" as LLM Provider
   - Enter your API key in the "OpenAI API Key" field
   - Click Save

2. **Via Environment Variable:**
   ```bash
   export OPENAI_API_KEY=sk-your-api-key
   python -m local_deep_research.web.app
   ```

3. **Programmatically:**
   ```python
   from local_deep_research.settings import CachedSettingsManager
   from local_deep_research.database.session_context import get_user_db_session

   with get_user_db_session(username="user", password="pass") as session:
       settings_manager = CachedSettingsManager(session, "user")
       settings_manager.set_setting("llm.provider", "openai")
       settings_manager.set_setting("llm.openai.api_key", "sk-your-api-key")
   ```

### 2. "Invalid API key"

**Symptoms:**
- 401 Unauthorized errors
- "Incorrect API key provided" messages

**Solutions:**

1. **Verify API Key Format:**
   - OpenAI keys start with `sk-`
   - Should be around 51 characters long
   - No extra spaces or quotes

2. **Check API Key Validity:**
   ```bash
   # Test directly with curl
   curl https://api.openai.com/v1/models \
     -H "Authorization: Bearer YOUR_API_KEY"
   ```

3. **Regenerate API Key:**
   - Go to https://platform.openai.com/api-keys
   - Create a new API key
   - Update in LDR settings

### 3. "Rate limit exceeded"

**Symptoms:**
- 429 errors
- "You exceeded your current quota" messages

**Solutions:**

1. **Check OpenAI Usage:**
   - Visit https://platform.openai.com/usage
   - Verify you have available credits

2. **Add Payment Method:**
   - OpenAI requires payment info for API access
   - Add at https://platform.openai.com/account/billing

3. **Use Different Model:**
   ```python
   settings_manager.set_setting("llm.model", "gpt-3.5-turbo")  # Cheaper
   # Instead of gpt-4 which is more expensive
   ```

### 4. "Settings not persisting"

**Symptoms:**
- API key needs to be re-entered after restart
- Settings revert to defaults

**Solutions:**

1. **Ensure Proper Shutdown:**
   - Use Ctrl+C to stop server (not kill -9)
   - Wait for "Server stopped" message

2. **Check Database Permissions:**
   ```bash
   ls -la encrypted_databases/
   # Should show your user database with write permissions
   ```

3. **Verify Settings Save:**
   ```python
   # After setting, verify it was saved
   saved_key = settings_manager.get_setting("llm.openai.api_key")
   print(f"Saved key: {'*' * 20 if saved_key else 'Not saved'}")
   ```

### 5. "API key not being used"

**Symptoms:**
- Settings show OpenAI configured but different LLM is used
- API key is saved but not applied

**Solutions:**

1. **Check Provider Setting:**
   ```python
   provider = settings_manager.get_setting("llm.provider")
   print(f"Current provider: {provider}")  # Should be "openai"
   ```

2. **Verify Settings Snapshot:**
   ```python
   settings_snapshot = settings_manager.get_all_settings()
   print("Provider:", settings_snapshot.get("llm.provider", {}).get("value"))
   print("API Key:", "Set" if settings_snapshot.get("llm.openai.api_key", {}).get("value") else "Not set")
   ```

3. **Force Provider Selection:**
   ```python
   # In research call
   result = quick_summary(
       query="Test",
       settings_snapshot=settings_snapshot,
       provider="openai",  # Force OpenAI
       model_name="gpt-3.5-turbo"
   )
   ```

## Testing Your Configuration

### 1. Simple API Test

```python
from local_deep_research.config.llm_config import get_llm
from local_deep_research.settings import CachedSettingsManager
from local_deep_research.database.session_context import get_user_db_session

with get_user_db_session(username="user", password="pass") as session:
    settings_manager = CachedSettingsManager(session, "user")
    settings_snapshot = settings_manager.get_all_settings()

    # Test LLM initialization
    try:
        llm = get_llm(settings_snapshot=settings_snapshot)
        print("✓ LLM initialized successfully")

        # Test response
        from langchain.schema import HumanMessage
        response = llm.invoke([HumanMessage(content="Say hello")])
        print(f"✓ Response: {response.content}")
    except Exception as e:
        print(f"✗ Error: {e}")
```

### 2. Full Research Test

```python
from local_deep_research.api.research_functions import quick_summary

result = quick_summary(
    query="What is OpenAI?",
    settings_snapshot=settings_snapshot,
    iterations=1,
    questions_per_iteration=1
)

print(f"Research ID: {result['research_id']}")
print(f"Summary: {result['summary'][:200]}...")
```

## Advanced Configuration

### Using Azure OpenAI

```python
settings_manager.set_setting("llm.provider", "openai")
settings_manager.set_setting("llm.openai.api_key", "your-azure-key")
settings_manager.set_setting("llm.openai.api_base", "https://your-resource.openai.azure.com/")
settings_manager.set_setting("llm.model", "your-deployment-name")
```

### Using OpenAI-Compatible Endpoints

```python
settings_manager.set_setting("llm.provider", "openai")
settings_manager.set_setting("llm.openai.api_key", "your-api-key")
settings_manager.set_setting("llm.openai.api_base", "https://your-endpoint.com/v1")
```

### Organization ID

```python
settings_manager.set_setting("llm.openai.organization", "org-your-org-id")
```

## Getting Help

1. **Run Diagnostic Test:**
   ```bash
   python tests/test_openai_api_key_e2e.py --verbose
   ```

2. **Check Logs:**
   ```bash
   # Look for OpenAI-related errors
   grep -i "openai\|api.*key" logs/ldr.log
   ```

3. **Community Support:**
   - GitHub Issues: https://github.com/LearningCircuit/local-deep-research/issues
   - Discord: https://discord.gg/ttcqQeFcJ3

4. **API Key Best Practices:**
   - Never commit API keys to version control
   - Use environment variables for production
   - Rotate keys regularly
   - Set usage limits in OpenAI dashboard
