# SQLCipher Installation Guide

Local Deep Research uses SQLCipher to provide encrypted databases for each user. This ensures that all user data, including API keys and research results, are encrypted at rest.

## Installation by Platform

### Ubuntu/Debian Linux

SQLCipher can be easily installed from the package manager:

```bash
sudo apt update
sudo apt install sqlcipher libsqlcipher-dev
```

After installation, you can install the Python binding:
```bash
pdm add pysqlcipher3
# or
pip install pysqlcipher3
```

### macOS

Install using Homebrew:

```bash
brew install sqlcipher
```

You may need to set environment variables for the Python binding:
```bash
export LDFLAGS="-L$(brew --prefix sqlcipher)/lib"
export CPPFLAGS="-I$(brew --prefix sqlcipher)/include"
pdm add pysqlcipher3
```

### Windows

Windows installation is more complex and requires building from source:

1. Install Visual Studio 2015 or later (Community Edition works)
2. Install the "Desktop Development with C++" workload
3. Download SQLCipher source from https://github.com/sqlcipher/sqlcipher
4. Build using Visual Studio Native Tools Command Prompt

For easier installation on Windows, consider using WSL2 with Ubuntu.

## Alternative: Using Docker

If you have difficulty installing SQLCipher, you can run Local Deep Research in a Docker container where SQLCipher is pre-installed:

```dockerfile
FROM python:3.11-slim

# Install SQLCipher
RUN apt-get update && apt-get install -y \
    sqlcipher \
    libsqlcipher-dev \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Local Deep Research
RUN pip install local-deep-research pysqlcipher3

CMD ["ldr", "serve"]
```

## Verifying Installation

You can verify SQLCipher is installed correctly:

```bash
# Check command line tool
sqlcipher --version

# Test Python binding
python -c "import pysqlcipher3; print('SQLCipher is installed!')"
```

## Fallback Mode

If SQLCipher is not available, Local Deep Research will fall back to using regular SQLite databases. However, this means your data will not be encrypted at rest. A warning will be displayed when running without encryption.

## Security Notes

- Each user's database is encrypted with their password
- There is no password recovery mechanism - if a user forgets their password, their data cannot be recovered
- The encryption uses SQLCipher's default settings with AES-256
- API keys and sensitive data are only stored in the encrypted user databases

## Troubleshooting

### Linux: "Package not found"

If your distribution doesn't have SQLCipher in its repositories, you may need to build from source or use a third-party repository.

### macOS: "Library not loaded"

Make sure you've set the LDFLAGS and CPPFLAGS environment variables as shown above.

### Windows: Build errors

Ensure you're using the Visual Studio Native Tools Command Prompt and have all required dependencies installed.

### Python: "No module named pysqlcipher3"

Try using the alternative package:
```bash
pip install sqlcipher3
```

## For Developers

To add SQLCipher to an automated installation script:

```bash
#!/bin/bash
# For Ubuntu/Debian
if command -v apt-get &> /dev/null; then
    sudo apt-get update
    sudo apt-get install -y sqlcipher libsqlcipher-dev
fi

# For macOS with Homebrew
if command -v brew &> /dev/null; then
    brew install sqlcipher
fi

# Install Python package
pip install pysqlcipher3 || pip install sqlcipher3
```
