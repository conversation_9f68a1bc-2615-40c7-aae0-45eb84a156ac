# Frequently Asked Questions (FAQ)

> **Note**: This documentation is maintained by the community and may contain inaccuracies. While we strive to keep it up-to-date, please verify critical information and report any errors via [GitHub Issues](https://github.com/LearningCircuit/local-deep-research/issues).

## Table of Contents

1. [General Questions](#general-questions)
2. [Installation & Setup](#installation--setup)
3. [Configuration](#configuration)
4. [Common Errors](#common-errors)
5. [Search Engines](#search-engines)
6. [LLM Configuration](#llm-configuration)
7. [Local Document Search](#local-document-search)
8. [Performance & Optimization](#performance--optimization)
9. [Docker Issues](#docker-issues)
10. [Platform-Specific Issues](#platform-specific-issues)

## General Questions

### What is Local Deep Research (LDR)?

<PERSON><PERSON> is an open-source AI research assistant that performs systematic research by breaking down complex questions, searching multiple sources in parallel, and creating comprehensive reports with proper citations. It can run entirely locally for complete privacy.

### How is LDR different from ChatGPT or other AI assistants?

LDR focuses specifically on research with real-time information retrieval. Key differences:
- Provides citations and sources for claims
- Searches multiple databases including academic papers
- Can run completely offline with local models
- Open source and customizable
- Searches your own documents

### Is LDR really free?

Yes! LDR is open source (MIT license). Costs only apply if you:
- Use cloud LLM providers (OpenAI, Anthropic)
- Use premium search APIs (Tavily, SerpAPI)
- Need cloud hosting infrastructure

Local models (Ollama) and free search engines have no costs.

### Can I use LDR completely offline?

Partially. You can:
- Use local LLMs (Ollama) offline
- Search local documents offline
- But web search requires internet

For intranet/offline environments, configure LDR to use only local documents and disable web search.

## Installation & Setup

### What are the system requirements?

- **Python**: 3.10 or newer
- **RAM**: 8GB minimum (16GB recommended for larger models)
- **GPU VRAM** (for Ollama):
  - 7B models: 4GB VRAM minimum
  - 13B models: 8GB VRAM minimum
  - 30B models: 16GB VRAM minimum
  - 70B models: 48GB VRAM minimum
- **Disk Space**:
  - 100MB for LDR
  - 1-2GB for SearXNG
  - 5-15GB per Ollama model
- **OS**: Windows, macOS, Linux

### Do I need Docker?

Docker is recommended but not required. You can:
- Use Docker Compose (easiest)
- Use Docker containers individually
- Install via pip without Docker

### Which installation method should I use?

- **Docker Compose**: Best for production use
- **Docker**: Good for quick testing
- **Pip package**: Best for development or Python integration

### How do I set up SearXNG?

SearXNG is a privacy-respecting metasearch engine. Learn more at the [SearXNG repository](https://github.com/searxng/searxng).

```bash
docker pull searxng/searxng
docker run -d -p 8080:8080 --name searxng searxng/searxng
```

Then set the URL to `http://localhost:8080` in LDR settings.

### The cookiecutter command fails on Windows

For Windows users, you can use the generated docker-compose file directly instead of running cookiecutter:
```yaml
services:
  local-deep-research:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "5000:5000"
    environment:
      - SEARXNG_URL=http://searxng:8080
    depends_on:
      - searxng

  searxng:
    image: searxng/searxng:latest
    ports:
      - "8080:8080"
```

## Configuration

### How do I change the LLM model?

1. **Via Web UI**: Settings → LLM Provider → Select model
2. **Via Environment**: Set `LDR_LLM_MODEL` and `LDR_LLM_PROVIDER`
3. **Via API**: Pass model parameters in requests

### Where should I configure settings?

**Important**: The `.env` file method is deprecated. Use the web UI settings instead:
1. Run the web app: `python -m local_deep_research.web.app`
2. Navigate to Settings
3. Configure your preferences
4. Settings are saved to the database

### How do I download Ollama models in Docker?

**Note**: If you use cookiecutter with Ollama, it will automatically download an initial model that you specify during setup.

To manually download additional models:
```bash
# Connect to the Ollama container
docker exec -it ollama ollama pull llama3:8b

# Or if using docker-compose
docker-compose exec ollama ollama pull llama3:8b
```

### Which Ollama model should I use?

Recommended models:
- **Best quality**: `llama3:70b` (requires 48GB+ VRAM)
- **Balanced**: `gemma3:12b` (good quality/speed trade-off)
- **Fastest**: `llama3:8b`, `mistral:7b`, or `gemma:7b`

## Common Errors

### "Error: max_workers must be greater than 0"

This means LDR cannot connect to your LLM. Check:
1. Ollama is running: `ollama list`
2. You have models downloaded: `ollama pull llama3:8b`
3. Correct model name in settings
4. For Docker: Ensure containers can communicate

### "No module named 'local_deep_research'"

Reinstall the package:
```bash
pip uninstall local-deep-research
pip install local-deep-research
```

### "404 Error" when viewing results

This issue should be resolved in versions 0.5.2 and later. If you're still experiencing it:
1. Refresh the page
2. Check if research actually completed in logs
3. Update to the latest version

### Research gets stuck or shows empty headings

Common causes:
- "Search snippets only" disabled (must be enabled for SearXNG)
- Rate limiting from search engines
- LLM connection issues

Solutions:
1. Reset settings to defaults
2. Use fewer iterations (2-3)
3. Limit questions per iteration (3-4)

### "'str' object has no attribute 'items'"

This issue should be fixed in recent versions. If you encounter it, ensure you're using the correct environment variable format. Remove deprecated variables:
- `LDR_SEARCH_ENGINE_WEB`
- `LDR_SEARCH_ENGINE_AUTO`
- `LDR_SEARCH_ENGINE_DEFAULT`

Use `LDR_SEARCH_TOOL` instead if needed.

## Search Engines

### SearXNG connection errors

1. **Verify SearXNG is running**:
   ```bash
   docker ps | grep searxng
   curl http://localhost:8080
   ```

2. **For Docker networking issues**:
   - Use `http://searxng:8080` (container name) not `localhost`
   - Or use `--network host` mode

3. **Check browser access**: Navigate to `http://localhost:8080`

### Rate limit errors

Solutions:
1. Check status: `python -m local_deep_research.web_search_engines.rate_limiting status`
2. Reset limits: `python -m local_deep_research.web_search_engines.rate_limiting reset`
3. Use `auto` search tool for automatic fallbacks
4. Add premium search engines

### "Invalid value" errors from SearXNG

Ensure "Search snippets only" is enabled in settings. This is required for SearXNG.

### Captcha errors

Some search engines detect bot activity. Solutions:
- Use SearXNG instead of direct search engines
- Add delays between searches
- Use premium APIs (Tavily, SerpAPI)

## LLM Configuration

### Cannot connect to Ollama

1. **Verify Ollama installation**:
   ```bash
   ollama --version
   ollama list
   ```

2. **For Docker**: Use correct URL
   - From host: `http://localhost:11434`
   - From container: `http://ollama:11434` or `http://host.docker.internal:11434`

### LM Studio connection issues

For Docker on Mac (#365):
- Use `http://host.docker.internal:1234` instead of `localhost:1234`

### Context length not respected

Known issue with Ollama (#500). Workaround:
- Set context length when pulling model: `ollama pull llama3:8b --context-length 8192`

### Model not in dropdown list

Current limitation (#179). Workarounds:
1. Type the exact model name in the dropdown field
2. Edit database directly
3. Use environment variables

## Local Document Search

### How do I configure local document paths?

1. **In Web UI**:
   - Settings → Search for "local"
   - Edit "Document Collection Paths"
   - Use absolute paths: `["/home/<USER>/documents", "/data/pdfs"]`

2. **For Docker**: Mount volumes
   ```bash
   docker run -v /host/path:/container/path ...
   ```
   Then use container path in settings: `["/container/path"]`

### Local search not finding documents

Common issues:
1. **First search is slow** - Initial indexing takes time
2. **Path format** - Use absolute paths, not relative
3. **File types** - Ensure supported formats (PDF, TXT, MD, DOCX)
4. **Permissions** - Check read permissions

### The @format syntax in settings

This is a UI hint to expand environment variables. Replace with actual paths:
- Change: `"@format ${DOCS_DIR}/personal_notes"`
- To: `"/home/<USER>/documents/personal_notes"`

## Performance & Optimization

### Research is too slow

1. **Reduce complexity**:
   - In the Web UI: Use Settings to reduce iterations and questions per iteration
   - Via API:
   ```python
   quick_summary(
       query="your query",
       iterations=1,  # Start with 1
       questions_per_iteration=2  # Limit sub-questions
   )
   ```

2. **Use faster models**:
   - Local: `mistral:7b`
   - Cloud: `gpt-3.5-turbo`

3. **Enable "Search snippets only"** (required for SearXNG)

### High memory usage

- Use smaller models (7B instead of 70B)
- Limit document collection size
- Use quantized models (GGUF format)

## Docker Issues

### Containers can't communicate

1. **Use Docker Compose** (recommended)
2. **Or use host networking**:
   ```bash
   docker run --network host ...
   ```
3. **Check container names** in URLs

### Port 5000 not accessible on Windows

Windows Docker issue. Modify docker-compose.yml:
```yaml
services:
  local-deep-research:
    # ... other config ...
    extra_hosts:
      - "host.docker.internal:host-gateway"
```

### "Database is locked" errors

Stop all containers and restart:
```bash
docker-compose down
docker-compose up -d
```

## Platform-Specific Issues

### Windows filename errors (#339)

LDR may generate invalid filenames. Fixed in recent versions, update to latest.

### macOS M1/M2/M3 issues

- Build your own Docker image for ARM
- Use native Ollama installation
- Some models may not be optimized for Apple Silicon

### WSL2 networking problems

Common on Windows. Solutions:
1. Use `127.0.0.1` instead of `0.0.0.0`
2. Check WSL2 firewall settings
3. Restart WSL: `wsl --shutdown`

## Getting Help

- **Discord**: [Join our community](https://discord.gg/ttcqQeFcJ3)
- **GitHub Issues**: [Report bugs](https://github.com/LearningCircuit/local-deep-research/issues)
- **Reddit**: [r/LocalDeepResearch](https://www.reddit.com/r/LocalDeepResearch/)

When reporting issues, include:
- Error messages and logs
- Your configuration (OS, Docker/pip, models)
- Steps to reproduce
- What you've already tried

## Related Documentation

- [Installation Guide](https://github.com/LearningCircuit/local-deep-research/wiki/Installation)
- [Search Engines Guide](search-engines.md)
- [Features Documentation](features.md)
- [API Documentation](api-quickstart.md)
- [Configuration Guide](env_configuration.md)
