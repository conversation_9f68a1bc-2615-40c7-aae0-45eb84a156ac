# Developer Guide

## Architecture Documentation

- **[URL Routing Architecture](development/url-routing-architecture.md)** - Comprehensive guide to URL management
- **[URL Quick Reference](development/url-quick-reference.md)** - Quick reference for frontend and backend URLs

## Configuring the Environment

The most convenient way to configure the Python environment is to use
[PDM](https://pdm-project.org/en/latest/). After installing PDM, configure the
environment and install dependencies:

```bash
pdm install --no-self
```

You can run a command in the environment by prefixing it with `pdm run`. You
can also activate the environment with `pdm venv activate`.

## Setting up Pre-Commit Hooks

These hooks will automatically run linting for every commit. You need to
initialize them once after configuring the environment:

```bash
pre-commit install
pre-commit install-hooks
```

# Running the Application

You can run the application directly using Python module syntax:

```bash
# Activate the environment.
pdm venv activate
# You need to be in the src directory if you are not.
cd src

# Run the web interface
python -m local_deep_research.web.app
# Run the CLI version
python -m local_deep_research.main
```

# Building a Package

To build a wheel and source distribution, simply run `pdm build`.
