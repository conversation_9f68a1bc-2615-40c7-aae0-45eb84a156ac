# Release Guide

## 🚀 Automated Release Process

Releases are now **fully automated** when PRs are merged to the `main` branch.

## 📋 How Releases Work

### 1. **Automatic Release Creation**
- **Trigger**: Any merge to `main` branch
- **Version**: Automatically uses version from `src/local_deep_research/__version__.py`
- **Changelog**: Auto-generated from commit history since last release
- **No duplicates**: Skips if release already exists for that version

### 2. **Automatic Publishing** (with approval)
- **GitHub Release** → triggers:
  - **PyPI publishing** (requires `release` environment approval)
  - **Docker publishing** (requires `release` environment approval)

## 👥 Who Can Release

Code owners (defined in `.github/CODEOWNERS`):
- `@LearningCircuit`
- `@hashedviking`
- `@djpetti`

## 📝 Release Workflow

### For Regular Releases:

1. **Create PR** with your changes
2. **Update version** in `src/local_deep_research/__version__.py`
3. **Get approval** from code owners
4. **Merge to main** → Release automatically created
5. **Approve publishing** in GitHub Actions (PyPI/Docker)

### For Hotfixes:

1. **Create hotfix branch** from main
2. **Make minimal fix**
3. **Bump patch version** (e.g., 0.4.3 → 0.4.4)
4. **Fast-track review** by code owners
5. **Merge to main** → Automatic release

## 🔧 Manual Release Options

### Option A: Manual Trigger
- Go to Actions → "Create Release" → "Run workflow"
- Specify version and prerelease flag

### Option B: Version Tags
- `git tag v0.4.3 && git push origin v0.4.3`
- Automatically creates release

## 🛡️ Branch Protection

- **Main branch** is protected
- **Required reviews** from code owners
- **No direct pushes** - only via approved PRs
- **Status checks** must pass (CI tests)

## 📦 Version Numbering

Follow [Semantic Versioning](https://semver.org/):
- **Major** (X.0.0): Breaking changes
- **Minor** (0.X.0): New features, backward compatible
- **Patch** (0.0.X): Bug fixes, backward compatible

## 🚨 Emergency Procedures

If automation fails:
1. **Manual GitHub release** still triggers PyPI/Docker
2. **Contact code owners** for assistance
3. **Check workflow logs** in GitHub Actions

## 📊 Release Checklist

- [ ] Version updated in `__version__.py`
- [ ] Changes tested in PR
- [ ] Code owner approval received
- [ ] CI tests passing
- [ ] Merge to main completed
- [ ] Release automatically created
- [ ] PyPI/Docker publishing approved
