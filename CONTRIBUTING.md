# Contributing to Local Deep Research

Thank you for your interest in contributing to Local Deep Research.

We deeply value every contribution to this project. Open source thrives when developers share their expertise, creativity, and passion. Whether you're fixing a typo, optimizing performance, adding a feature, or helping with documentation - your work directly impacts researchers and developers worldwide. We carefully review all PRs and are genuinely excited to merge contributions that enhance the project. This is truly a community-driven project, and we're honored to have you join us.

## 📚 Developer Resources

For detailed development setup instructions, please see our [Developer Guide](https://github.com/LearningCircuit/local-deep-research/wiki/Developer-Guide) which covers:
- Environment configuration with PDM
- Pre-commit hooks setup
- Building packages
- Running the application

## 🔒 Security Guidelines

As a public repository, we maintain strict file management policies to ensure code quality and prevent unintended data exposure.

### Allowed File Types

Our repository uses a whitelist approach. Only these file types are permitted:
- **Source code**: `.py`, `.js`, `.html`, `.css`
- **Configuration**: `.json`, `.yml`, `.yaml`, `.cfg`
- **Documentation**: `.md`, `.ipynb`
- **Project files**: `LICENSE`, `README`, `Dockerfile`, `pyproject.toml`, etc.
- **Scripts**: `.sh`, `.template`
- **Windows installers** (only in `installers/` directory): `.bat`, `.ps1`, `.iss`, `.ico`

### Blocked File Types

The following are automatically blocked by our CI/CD pipeline:
- Data files (`.csv`, `.xlsx`, `.jsonl`, `.db`, `.sqlite`, `.parquet`, etc.)
- Binary files (`.pickle`, `.pkl`, `.npy`, `.npz`)
- Media files (`.mp4`, `.png`, `.jpg`, `.pdf`, etc.)
- Archive files (`.zip`, `.tar.gz`, `.rar`)
- Sensitive files (`.env`, `.key`, `.pem`, or files containing credentials)
- Files larger than 1MB

## 🚀 Quick Start

1. **Fork and clone the repository**
2. **Set up your development environment** following the [Developer Guide](https://github.com/LearningCircuit/local-deep-research/wiki/Developer-Guide)
3. **Install pre-commit hooks**:
   ```bash
   pre-commit install
   pre-commit install-hooks
   ```
4. **Create a new branch** for your feature or fix

## 💻 Development Workflow

### Configuration

Never commit sensitive information like API keys or passwords. Configuration is typically done through the web UI.

For environment variables and advanced configuration, see the [Installation guide](https://github.com/LearningCircuit/local-deep-research/wiki/Installation#environment-variables) on our wiki.

### Testing

Run tests before submitting PRs:
```bash
pdm run python run_tests.py
```

### Code Quality

Pre-commit hooks will automatically:
- Format code with Black
- Sort imports with isort
- Run flake8 linting
- Check for large files
- Prevent commits to main branch

## 📋 Pull Request Process

1. **Create a focused PR** - One feature/fix per PR
2. **Write clear commit messages** - Explain what and why
3. **Update documentation** - Keep docs in sync with code changes
4. **Add tests** - Include tests for new functionality
5. **Ensure CI passes** - All automated checks must pass

### Security Checks

Every PR automatically runs:
- File whitelist enforcement
- Large file detection (>1MB)
- Security pattern scanning
- Binary file detection

## 🛡️ Additional Security

### GitGuardian

For enhanced security on your fork:
1. Visit [GitGuardian on GitHub Marketplace](https://github.com/marketplace/gitguardian)
2. Install the free plan for public repositories
3. It will scan commits for exposed secrets

### If You Accidentally Commit Sensitive Data

1. **Immediately revoke** any exposed credentials
2. **Clean git history** using BFG Repo-Cleaner or git filter-branch
3. **Force push** the cleaned history
4. **Notify maintainers** if the data was pushed to the main repository

## 🤝 Community

- **Discord**: Join our [Discord server](https://discord.gg/ttcqQeFcJ3) for discussions
- **Issues**: Check existing issues before opening new ones
- **Wiki**: Contribute to our [documentation wiki](https://github.com/LearningCircuit/local-deep-research/wiki)

## 📝 Code of Conduct

- Be respectful and professional
- Welcome newcomers with patience
- Focus on constructive feedback
- Report inappropriate behavior to maintainers

## 🏆 Recognition

All contributors are recognized in:
- Release notes
- GitHub contributors graph
- Special mentions for significant contributions

Thank you for helping improve Local Deep Research! 🎉
