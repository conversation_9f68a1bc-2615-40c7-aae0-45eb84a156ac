{"name": "local-deep-research-web", "version": "1.0.0", "description": "Web frontend for Local Deep Research", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "update": "npm update", "audit": "npm audit", "audit-fix": "npm audit fix"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.5.0", "bootstrap": "^5.3.0", "bootstrap-icons": "^1.11.0", "chart.js": "^4.4.0", "chartjs-adapter-date-fns": "^3.0.0", "chartjs-plugin-annotation": "^3.0.0", "date-fns": "^2.30.0", "highlight.js": "^11.9.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "marked": "^11.0.0", "socket.io-client": "^4.6.0"}, "devDependencies": {"axe-core": "^4.8.0", "vite": "^5.0.0"}}