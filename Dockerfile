####
# Used for building the LDR service dependencies.
####
FROM python:3.12.8-slim AS builder-base

# Install system dependencies for SQLCipher and Node.js for frontend build
RUN apt-get update && apt-get install -y \
    libsqlcipher-dev \
    build-essential \
    curl \
    && curl -fsSL https://deb.nodesource.com/setup_lts.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# Install dependencies and tools
RUN pip3 install --upgrade pip && pip install pdm playwright
# disable update check
ENV PDM_CHECK_UPDATE=false

WORKDIR /install
COPY pyproject.toml pyproject.toml
COPY pdm.lock pdm.lock
COPY src/ src
COPY LICENSE LICENSE
COPY README.md README.md
# Copy frontend build files
COPY package.json package.json
COPY package-lock.json* package-lock.json
COPY vite.config.js vite.config.js

####
# Builds the LDR service dependencies used in production.
####
FROM builder-base AS builder

# Install npm dependencies and build frontend assets
RUN npm install
RUN npm run build

# Install the package using PDM
RUN pdm install --check --prod --no-editable


####
# Container for running tests.
####
FROM builder-base AS ldr-test

# Install runtime dependencies for SQLCipher, Node.js, and testing tools
RUN apt-get update && apt-get install -y \
    sqlcipher \
    libsqlcipher0 \
    curl \
    xauth \
    xvfb \
    # Dependencies for Chromium
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgbm1 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxkbcommon0 \
    libxrandr2 \
    xdg-utils \
    && curl -fsSL https://deb.nodesource.com/setup_lts.x | bash - \
    && apt-get install -y nodejs \
    && rm -rf /var/lib/apt/lists/*

# Set up Puppeteer environment
ENV PUPPETEER_CACHE_DIR=/app/puppeteer-cache
ENV DOCKER_ENV=true
# Don't skip Chrome download - let Puppeteer download its own Chrome as fallback
# ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

# Create puppeteer cache directory with proper permissions
RUN mkdir -p /app/puppeteer-cache && chmod -R 777 /app/puppeteer-cache

# Install Playwright with Chromium first (before npm packages)
RUN playwright install --with-deps chromium || echo "Playwright install failed, will use Puppeteer's Chrome"

# Copy test package files
COPY tests/api_tests_with_login/package.json /install/tests/api_tests_with_login/
COPY tests/ui_tests/package.json /install/tests/ui_tests/

# Install npm packages - Skip Puppeteer Chrome download since we have Playwright's Chrome
WORKDIR /install/tests/api_tests_with_login
ENV PUPPETEER_SKIP_DOWNLOAD=true
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
RUN npm install
WORKDIR /install/tests/ui_tests
RUN npm install

# Set CHROME_BIN to help Puppeteer find Chrome from Playwright
# Try to find and set Chrome binary path from Playwright's installation
RUN CHROME_PATH=$(find /root/.cache/ms-playwright -name chrome -type f 2>/dev/null | head -1) && \
    if [ -n "$CHROME_PATH" ]; then \
        echo "export CHROME_BIN=$CHROME_PATH" >> /etc/profile.d/chrome.sh; \
        echo "export PUPPETEER_EXECUTABLE_PATH=$CHROME_PATH" >> /etc/profile.d/chrome.sh; \
    fi || true

# Set environment variables for Puppeteer to use Playwright's Chrome
ENV PUPPETEER_SKIP_DOWNLOAD=true
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV PUPPETEER_EXECUTABLE_PATH=/root/.cache/ms-playwright/chromium-1181/chrome-linux/chrome

# Copy test files to /app where they will be run from
RUN mkdir -p /app && cp -r /install/tests /app/

# Ensure Chrome binaries have correct permissions
RUN chmod -R 755 /app/puppeteer-cache

WORKDIR /install

# Install the package using PDM
RUN pdm install --check --no-editable
# Configure path to default to the venv python.
ENV PATH="/install/.venv/bin:$PATH"

####
# Runs the LDR service.
###
FROM python:3.12.8-slim AS ldr

# Install runtime dependencies for SQLCipher
RUN apt-get update && apt-get install -y \
    sqlcipher \
    libsqlcipher0 \
    && rm -rf /var/lib/apt/lists/*

# retrieve packages from build stage
COPY --from=builder /install/.venv/ /install/.venv
ENV PATH="/install/.venv/bin:$PATH"

# Install browser automation tools
RUN playwright install

# Create volume for persistent configuration
VOLUME /root/.config/local_deep_research

# Create volume for Ollama start script
VOLUME /scripts/
# Copy the Ollama entrypoint script
ADD scripts/ollama_entrypoint.sh /scripts/ollama_entrypoint.sh
RUN chmod +x /scripts/ollama_entrypoint.sh

EXPOSE 5000
STOPSIGNAL SIGINT

# Use PDM to run the application
CMD [ "ldr-web" ]
