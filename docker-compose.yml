services:
  local-deep-research:
    image: localdeepresearch/local-deep-research

    networks:
      - ldr-network

    ports:
      - "5000:5000"
    environment:
      # Web Interface Settings
      - LDR_WEB_PORT=5000
      - LDR_WEB_HOST=0.0.0.0
      - LDR_LLM_PROVIDER=ollama
      - LDR_LLM_OLLAMA_URL=http://ollama:11434

      - LDR_SEARCH_ENGINE_WEB_SEARXNG_DEFAULT_PARAMS_INSTANCE_URL=http://searxng:8080

      # Data directory configuration
      - LDR_DATA_DIR=/data
    volumes:
      - ldr_data:/data
      - ldr_scripts:/scripts
      - ./local_collections/personal_notes:/local_collections/personal_notes/
      - ./local_collections/project_docs:/local_collections/project_docs/
      - ./local_collections/research_papers:/local_collections/research_papers/
    restart: unless-stopped
    depends_on:
      ollama:
        condition: service_healthy

      searxng:
        condition: service_started


  ollama:
    image: ollama/ollama:latest
    deploy: # Remove this section if you do not have an Nvidia GPU.
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [ gpu ]

    container_name: ollama_service
    entrypoint: "/scripts/ollama_entrypoint.sh ${MODEL:-gemma3:12b}"
    healthcheck:
      test: [ "CMD", "ollama", "show", "${MODEL:-gemma3:12b}" ]
      interval: 10s
      timeout: 5s
      start_period: 10m
      retries: 2
    environment:
      OLLAMA_KEEP_ALIVE: '30m'
    volumes:
      - ollama_data:/root/.ollama
      - ldr_scripts:/scripts
      - ./scripts/:/scripts/
    networks:
      - ldr-network


    restart: unless-stopped

  searxng:
    image: searxng/searxng:latest
    container_name: searxng
    networks:
      - ldr-network

    volumes:
      - searxng_data:/etc/searxng
    restart: unless-stopped


volumes:
  ldr_data:
  ldr_scripts:
  ollama_data:
  searxng_data:
networks:
  ldr-network:
