#!/usr/bin/env python
"""Check if there are any researches in the database."""

import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent.resolve()))

from src.local_deep_research.database.models import ResearchLog
from src.local_deep_research.utilities.db_utils import get_db_session


def check_researches():
    """Check for researches in the database."""
    # Note: This checks the shared database, not per-user databases
    try:
        # Check for the most recently created test user
        # The test creates a user with pattern "simple_" + timestamp

        # Look for any user databases that might have been created
        db_dir = (
            Path.home()
            / ".local"
            / "share"
            / "local-deep-research"
            / "encrypted_databases"
        )
        if db_dir.exists():
            db_files = [
                f.name
                for f in db_dir.iterdir()
                if f.name.endswith(".db")
                and not f.name.endswith("-wal")
                and not f.name.endswith("-shm")
            ]
            print(f"Found {len(db_files)} user database(s)")

        # Try with the most recent test user
        session = get_db_session(username="simple_1751271039631")
        count = session.query(ResearchLog).count()
        if count > 0:
            print(f"✓ Found {count} research(es) in database")
            latest = (
                session.query(ResearchLog)
                .order_by(ResearchLog.created_at.desc())
                .first()
            )
            if latest:
                print(
                    f"  Latest: {latest.title[:50] if latest.title else 'No title'}... (created at {latest.created_at})"
                )
            return 0
        else:
            print("✗ No researches found in database")
            return 1
    except Exception as e:
        print(f"✗ Error checking database: {e}")
        import traceback

        traceback.print_exc()
        return 1
    finally:
        if "session" in locals():
            session.close()


if __name__ == "__main__":
    sys.exit(check_researches())
