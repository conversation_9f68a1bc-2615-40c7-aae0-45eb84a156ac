#!/bin/bash
# <PERSON><PERSON>t to restart the LDR server

echo "Stopping existing LDR server..."
pkill -f "python -m local_deep_research.web.app" 2>/dev/null || echo "No existing server found"

# Wait a moment for the process to stop
sleep 1

echo "Starting LDR server..."
# Change to the script's parent directory (project root)
cd "$(dirname "$0")/../.."

# Start server in background and detach from terminal
(nohup pdm run python -m local_deep_research.web.app > /tmp/ldr_server.log 2>&1 &) &
SERVER_PID=$!

# Give it a moment to start
sleep 2

echo "Server started. PID: $SERVER_PID"
echo "Logs: /tmp/ldr_server.log"
echo "URL: http://127.0.0.1:5000"
echo ""
echo "To check server status: ps aux | grep 'python -m local_deep_research.web.app'"
echo "To view logs: tail -f /tmp/ldr_server.log"
echo "To stop server: pkill -f 'python -m local_deep_research.web.app'"

# Exit immediately - don't wait for background process
exit 0
