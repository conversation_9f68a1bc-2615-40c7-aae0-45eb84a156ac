#!/usr/bin/env python3
"""Debug script to run pytest with CI settings and see what's failing."""

import os
import subprocess
import sys

# Set CI environment to mimic CI behavior
os.environ["CI"] = "true"
os.environ["USE_FALLBACK_LLM"] = "true"

cmd = [
    sys.executable,
    "run_tests.py",
    "-m",
    "not slow",
    "--ignore=tests/searxng/",
    "--ignore=tests/unit/test_config.py",
    "--ignore=tests/api_tests/",
    "--ignore=tests/health_check/test_endpoints_health.py",
    "-v",
    "--tb=short",
    "--timeout=30",
    "-x",  # Stop on first failure to debug
]

print(f"Running: {' '.join(cmd)}")
result = subprocess.run(cmd)
sys.exit(result.returncode)
