#!/usr/bin/env python3
"""Check if benchmark runs exist in the database."""

import sys
from pathlib import Path

# Add the parent directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from local_deep_research.database.session_context import get_user_db_session
from local_deep_research.database.models.benchmark import (
    BenchmarkRun,
    BenchmarkResult,
)
from loguru import logger


def check_benchmark_database():
    """Check for benchmark runs in the database."""
    try:
        # Try to find the most recent benchmark user
        import glob
        from local_deep_research.config.paths import get_data_directory

        data_dir = get_data_directory()
        db_pattern = str(
            Path(data_dir) / "encrypted_databases" / "benchmark_*.db"
        )
        benchmark_dbs = glob.glob(db_pattern)

        if benchmark_dbs:
            # Get the most recent benchmark user
            latest_db = max(
                benchmark_dbs, key=lambda x: Path(x).stat().st_mtime
            )
            username = Path(latest_db).stem
            print(f"Checking database for user: {username}")
        else:
            # Try to find most recent user database
            all_dbs = glob.glob(
                str(Path(data_dir) / "encrypted_databases" / "*.db")
            )
            if all_dbs:
                latest_db = max(all_dbs, key=lambda x: Path(x).stat().st_mtime)
                username = Path(latest_db).stem
                print(
                    f"No benchmark users found, using most recent user: {username}"
                )
            else:
                # Fallback to test user
                username = "test"
                print("No user databases found, using test user")

        # Use the user's database
        with get_user_db_session(username) as session:
            # Count total benchmark runs
            total_runs = session.query(BenchmarkRun).count()
            print(f"\nTotal benchmark runs: {total_runs}")

            if total_runs > 0:
                # Get latest benchmark run
                latest_run = (
                    session.query(BenchmarkRun)
                    .order_by(BenchmarkRun.created_at.desc())
                    .first()
                )

                print("\nLatest benchmark run:")
                print(f"  ID: {latest_run.id}")
                print(f"  Name: {latest_run.run_name}")
                print(f"  Status: {latest_run.status.value}")
                print(f"  Created: {latest_run.created_at}")
                print(f"  Total examples: {latest_run.total_examples}")
                print(f"  Completed examples: {latest_run.completed_examples}")

                # Count results for latest run
                results_count = (
                    session.query(BenchmarkResult)
                    .filter(BenchmarkResult.benchmark_run_id == latest_run.id)
                    .count()
                )
                print(f"  Results: {results_count}")

                # Show benchmark configuration
                print("\nBenchmark configuration:")
                print(f"  Search config: {latest_run.search_config}")
                print(f"  Datasets: {latest_run.datasets_config}")

                # Show first few results if any
                if results_count > 0:
                    print("\nFirst 3 results:")
                    results = (
                        session.query(BenchmarkResult)
                        .filter(
                            BenchmarkResult.benchmark_run_id == latest_run.id
                        )
                        .limit(3)
                        .all()
                    )

                    for i, result in enumerate(results, 1):
                        print(f"\n  Result {i}:")
                        print(f"    Question: {result.question[:100]}...")
                        print(f"    Dataset: {result.dataset_type.value}")
                        print(
                            f"    Processing time: {result.processing_time:.2f}s"
                            if result.processing_time
                            else "    Processing time: N/A"
                        )
                        print(f"    Correct: {result.is_correct}")

                return True
            else:
                print("No benchmark runs found in database")
                return False

    except Exception as e:
        logger.exception("Error checking benchmark database")
        print(f"Error: {e}")
        return False


if __name__ == "__main__":
    success = check_benchmark_database()
    sys.exit(0 if success else 1)
