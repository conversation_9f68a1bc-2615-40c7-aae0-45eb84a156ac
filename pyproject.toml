[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[project]
name = "local-deep-research"
dynamic = ["version"]
description = "AI-powered research assistant with deep, iterative analysis using LLMs and web searches"
readme = "README.md"
requires-python = ">=3.10"
license = {file = "LICENSE"}
authors = [
    {name = "LearningCircuit", email = "<EMAIL>"},
    {name = "HashedViking", email = "<EMAIL>"},
    {name = "djpetti", email = "<EMAIL>"},
]
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "langchain>=0.3.18",
    "langchain-community>=0.3.17",
    "langchain-core>=0.3.34",
    "langchain-ollama>=0.2.3",
    "langchain-openai>=0.3.5",
    "langchain-anthropic>=0.3.13",
    "duckduckgo_search>=7.3.2",
    "python-dateutil>=2.9.0",
    "typing_extensions>=4.12.2",
    "justext",
    "playwright",
    "beautifulsoup4",
    "flask>=3.1.0",
    "flask-cors>=3.0.10",
    "flask-socketio>=5.1.1",
    "sqlalchemy>=1.4.23",
    "sqlalchemy-utc>=0.14.0",
    "wikipedia",
    "arxiv>=1.4.3",
    "pypdf",
    "sentence-transformers",
    "faiss-cpu",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "toml>=0.10.2",
    "platformdirs>=3.0.0",
    "dynaconf",
    "requests>=2.28.0",
    "tiktoken>=0.4.0",
    "xmltodict>=0.13.0",
    "lxml>=4.9.2",
    "pdfplumber>=0.9.0",
    "unstructured>=0.10.0",
    "google-search-results",
    "importlib-resources>=6.5.2",
    "setuptools>=78.1.0",
    "flask-wtf>=1.2.2",
    "optuna>=4.3.0",
    "elasticsearch==8.14.0",
    "methodtools>=0.4.7",
    "loguru>=0.7.3",
    "cachetools>=5.5.2",
    "matplotlib>=3.10.3",
    "pandas>=2.2.3",
    "plotly>=6.0.1",
    "kaleido==0.2.1",
    "aiohttp>=3.9.0",
    "tenacity>=8.0.0",
    "apscheduler>=3.10.0",
    "rich>=13.0.0",
    "click>=8.0.0",
    "flask-login>=0.6.3",
    "dogpile.cache>=1.2.0",
    "redis>=4.0.0",
    "msgpack>=1.0.0",
    "sqlcipher3-binary>=0.5.4; sys_platform == 'linux'",
    "sqlcipher3>=0.5.0; sys_platform != 'linux'",
]

[project.urls]
"Homepage" = "https://github.com/LearningCircuit/local-deep-research"
"Bug Tracker" = "https://github.com/LearningCircuit/local-deep-research/issues"

[project.scripts]
ldr = "local_deep_research.main:main"
ldr-web = "local_deep_research.web.app:main"

[tool.setuptools]
include-package-data = true

[tool.setuptools.package-data]
"local_deep_research.web" = ["templates/*", "static/*", "static/**/*"]
"local_deep_research.defaults" = ["*.toml", "*.py", "*.template", ".env.template"]






[tool.pdm]
distribution = true
version = { source = "file", path = "src/local_deep_research/__version__.py" }
[[tool.pdm.source]]
url = "https://download.pytorch.org/whl/cpu"
name = "torch"
include_packages = ["torch", "torch*"]

[dependency-groups]
dev = [
    "pre-commit>=4.3.0",
    "jupyter>=1.1.1",
    "cookiecutter>=2.6.0",
    "pandas>=2.2.3",
    "optuna>=4.3.0",
    "pytest-mock>=3.14.1",
    "pytest>=8.4.1",
    "pytest-cov>=6.2.1",
    "pytest-timeout>=2.3.1",
    "pytest-asyncio>=1.0.0",
    "ruff>=0.11.12",
    "freezegun>=1.5.2",
]

[tool.pytest.ini_options]
markers = [
    "requires_llm: marks tests that require a real LLM (not fallback)",
    "integration: marks integration tests",
    "slow: marks slow tests",
    "asyncio: marks async tests that use asyncio",
]

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
]

# Same as Black.
line-length = 80
indent-width = 4

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`) codes by default.
select = ["E4", "E7", "E9", "F"]
ignore = ["E722"]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"
# Like Black, indent with spaces, rather than tabs.
indent-style = "space"
# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false
# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
