# Contributing to Local Deep Research

We love your input! We want to make contributing to Local Deep Research as easy and transparent as possible, whether it's:

- Reporting a bug
- Discussing the current state of the code
- Submitting a fix
- Proposing new features
- Becoming a maintainer

## Development Process

We use GitHub to host code, to track issues and feature requests, as well as accept pull requests.

1. Fork the repo and create your branch from `main`
2. If you've added code that should be tested, add tests
3. If you've changed APIs, update the documentation
4. Ensure the test suite passes
5. Make sure your code follows the existing style
6. Issue that pull request!

## Any Contributions You Make Will Be Under the CC BY 4.0 License

When you submit code changes, your submissions are understood to be under the same [CC BY 4.0 License](LICENSE.md) that covers the project. Feel free to contact the maintainers if that's a concern.

## Report Bugs Using GitHub's [Issue Tracker](../../issues)

Report a bug by [opening a new issue](../../issues/new); it's that easy!

**Great Bug Reports** tend to have:

- A quick summary and/or background
- Steps to reproduce
  - Be specific!
  - Give sample code if you can
- What you expected would happen
- What actually happens
- Notes (possibly including why you think this might be happening, or stuff you tried that didn't work)

## Development Setup

1. Clone your fork of the repository:
```bash
git clone https://github.com/YOUR_USERNAME/local-deep-research.git
cd local-deep-research
```

2. Create a virtual environment and activate it:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Install Ollama and set up your preferred model:
```bash
# Follow instructions at https://ollama.ai for installation
ollama pull deepseek-r1:14b  # Or mistral:7b for lighter hardware
```

## Code Style

- Follow PEP 8 guidelines
- Use type hints where possible
- Write descriptive docstrings for functions and classes
- Keep functions focused and modular
- Comment complex logic

## License

By contributing, you agree that your contributions will be licensed under its CC BY 4.0 License.

## References

This document was adapted from the open-source contribution guidelines for [Facebook's Draft](https://github.com/facebook/draft-js/blob/master/CONTRIBUTING.md).
