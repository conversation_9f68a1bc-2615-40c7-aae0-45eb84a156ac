# Configuration for automated main-to-dev sync
sync:
  # Enable/disable automatic syncing
  enabled: true

  # Auto-merge PRs when there are no conflicts
  auto_merge_clean: true

  # Branches to sync from main to
  target_branches:
    - dev

  # Ignore sync if these strings are in the commit message
  ignore_patterns:
    - "Merge pull request"
    - "from LearningCircuit/local-deep-research:dev"
    - "[skip sync]"
    - "[no sync]"

  # Labels to add to sync PRs
  labels:
    clean: ["sync", "auto-merge"]
    conflicts: ["sync", "conflicts", "manual-review-required"]

  # Reviewers to assign for conflict resolution
  conflict_reviewers:
    - "LearningCircuit"
    - "djpetti"
    - "HashedViking"
  # Additional settings
  settings:
    # Delete sync branches after successful merge
    delete_branch_after_merge: true

    # Maximum number of sync attempts per day
    max_daily_syncs: 10
