## Description

Fixes #

## Note on File Types

This project uses a file whitelist for security (configured in `.gitignore`). Currently allowed: `.py`, `.js`, `.html`, `.css`, `.json`, `.md`, `.yml`, `.yaml`, `.sh`, `.cfg`, `.ipynb`, `.template`

Need a different file type? We're happy to discuss it in the comments, and if approved, it can be added to `.gitignore`.

## Pre-commit Hooks

If you haven't already, consider setting up pre-commit hooks for automatic code formatting:
```bash
pre-commit install
pre-commit install-hooks
```

---

Thanks for your first contribution to Local Deep Research! Welcome to the community. We genuinely appreciate the time and effort you've put into this PR 🎉
