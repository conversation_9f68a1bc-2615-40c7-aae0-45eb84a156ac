# Code Owners for Local Deep Research
# https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners

# Global owners - these people are automatically requested for review on all PRs
# and their approval is required for merging to protected branches
* @LearningCircuit @hashedviking @djpetti

# Critical infrastructure and release files require maintainer approval
/.github/ @LearningCircuit @hashedviking @djpetti
/pyproject.toml @LearningCircuit @hashedviking @djpetti
/Dockerfile @LearningCircuit @hashedviking @djpetti
/src/local_deep_research/__version__.py @LearningCircuit @hashedviking @djpetti

# CI/CD workflows and release automation
/.github/workflows/ @LearningCircuit @hashedviking @djpetti

# Core API and configuration changes
/src/local_deep_research/api/ @LearningCircuit @hashedviking @djpetti
/src/local_deep_research/config/ @LearningCircuit @hashedviking @djpetti

# Database migrations and metrics (sensitive for data integrity)
/src/local_deep_research/metrics/ @LearningCircuit @hashedviking @djpetti
/src/local_deep_research/migrate*.py @LearningCircuit @hashedviking @djpetti
