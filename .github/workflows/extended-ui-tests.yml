name: Extended UI Tests

on:
  push:
    branches: [ main, dev ]
  pull_request:
    types: [opened, synchronize, reopened]
  schedule:
    # Run nightly at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:

jobs:
  extended-ui-tests:
    runs-on: ubuntu-latest
    name: Extended UI Test Suite

    steps:
      - uses: actions/checkout@v5

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Set up PDM
        uses: pdm-project/setup-pdm@v4
        with:
          python-version: '3.12'

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libsqlcipher-dev xvfb

      - name: Install dependencies
        run: |
          pdm sync -d
          cd tests/ui_tests && npm install

      - name: Install browser
        run: |
          npx puppeteer browsers install chrome

      - name: Setup directories
        run: |
          mkdir -p data
          mkdir -p tests/ui_tests/screenshots
          mkdir -p tests/ui_tests/results

      - name: Start server
        run: |
          export LDR_USE_FALLBACK_LLM=true
          export PYTHONPATH=$PWD/src:$PYTHONPATH
          pdm run python -m local_deep_research.web.app &
          echo $! > server.pid

          # Wait for server
          for i in {1..30}; do
            if curl -f http://localhost:5000/api/v1/health 2>/dev/null; then
              echo "Server ready"
              break
            fi
            sleep 1
          done

      - name: Run critical UI tests
        run: |
          export DISPLAY=:99
          cd tests/ui_tests

          # Create test runner for extended tests
          cat > run_extended_tests.js << 'EOF'
          const { spawn } = require('child_process');
          const path = require('path');

          const tests = [
              // Critical flows not in main test suite
              { name: 'Research Submit', file: 'test_research_submit.js' },
              { name: 'Research Cancellation', file: 'test_research_cancellation.js' },
              { name: 'Export Functionality', file: 'test_export_functionality.js' },
              { name: 'Complete Workflow', file: 'test_complete_workflow.js' },
              { name: 'Concurrent Limit', file: 'test_concurrent_limit.js' },
              { name: 'Multi Research', file: 'test_multi_research.js' },

              // Additional research tests
              { name: 'Research Simple', file: 'test_research_simple.js' },
              { name: 'Research Form', file: 'test_research_form.js' },
              { name: 'Research API', file: 'test_research_api.js' },

              // History and navigation
              { name: 'History Page', file: 'test_history_page.js' },
              { name: 'Full Navigation', file: 'test_full_navigation.js' },

              // Advanced features
              { name: 'Queue Simple', file: 'test_queue_simple.js' },
              { name: 'Direct Mode', file: 'test_direct_mode.js' }
          ];

          let passed = 0;
          let failed = 0;

          async function runTest(test) {
              console.log(`\nRunning: ${test.name}`);
              return new Promise((resolve) => {
                  const testProcess = spawn('node', [test.file], {
                      stdio: 'inherit',
                      env: { ...process.env, HEADLESS: 'true' }
                  });

                  const timeout = setTimeout(() => {
                      testProcess.kill();
                      console.log(`⏱️ ${test.name} timed out`);
                      failed++;
                      resolve();
                  }, 90000); // 90 second timeout per test

                  testProcess.on('close', (code) => {
                      clearTimeout(timeout);
                      if (code === 0) {
                          console.log(`✅ ${test.name} passed`);
                          passed++;
                      } else {
                          console.log(`❌ ${test.name} failed`);
                          failed++;
                      }
                      resolve();
                  });
              });
          }

          async function runAll() {
              console.log('Starting Extended UI Test Suite\n');

              for (const test of tests) {
                  // Check if file exists before trying to run
                  const fs = require('fs');
                  if (fs.existsSync(test.file)) {
                      await runTest(test);
                  } else {
                      console.log(`⚠️ Skipping ${test.name} - file not found`);
                  }
              }

              console.log(`\n📊 Results: ${passed} passed, ${failed} failed`);
              process.exit(failed > 0 ? 1 : 0);
          }

          runAll();
          EOF

          xvfb-run -a -s "-screen 0 1920x1080x24" node run_extended_tests.js

      - name: Upload screenshots
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: extended-ui-screenshots
          path: tests/ui_tests/screenshots/

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: extended-ui-results
          path: tests/ui_tests/results/

      - name: Stop server
        if: always()
        run: |
          if [ -f server.pid ]; then
            kill $(cat server.pid) || true
          fi
