name: News Feature Tests

on:
  push:
    paths:
      - 'src/local_deep_research/web/static/js/news/**'
      - 'src/local_deep_research/web/templates/news/**'
      - 'src/local_deep_research/news/**'
      - 'tests/ui_tests/test_news*.js'
  pull_request:
    types: [opened, synchronize, reopened]
  workflow_dispatch:

jobs:
  news-tests:
    runs-on: ubuntu-latest
    name: News Feature Test Suite

    steps:
      - uses: actions/checkout@v5

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Set up PDM
        uses: pdm-project/setup-pdm@v4
        with:
          python-version: '3.12'

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libsqlcipher-dev xvfb

      - name: Install dependencies
        run: |
          pdm sync -d
          cd tests/ui_tests && npm install

      - name: Install browser
        run: |
          npx puppeteer browsers install chrome

      - name: Setup directories
        run: |
          mkdir -p data
          mkdir -p tests/ui_tests/screenshots/news

      - name: Start server
        run: |
          export LDR_USE_FALLBACK_LLM=true
          export PYTHONPATH=$PWD/src:$PYTHONPATH
          pdm run python -m local_deep_research.web.app &
          echo $! > server.pid

          # Wait for server
          for i in {1..30}; do
            if curl -f http://localhost:5000/api/v1/health 2>/dev/null; then
              echo "Server ready"
              break
            fi
            sleep 1
          done

      - name: Run news feature tests
        run: |
          export DISPLAY=:99
          cd tests/ui_tests

          # Run all news-related tests
          for test in test_news*.js; do
            if [ -f "$test" ] && [[ ! "$test" == *"debug"* ]]; then
              echo "Running $test..."
              xvfb-run -a -s "-screen 0 1920x1080x24" node "$test" || true
            fi
          done

      - name: Run news API tests
        run: |
          pdm run pytest tests/test_news/ -v --tb=short || true

      - name: Upload news test screenshots
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: news-test-screenshots
          path: tests/ui_tests/screenshots/news/

      - name: Stop server
        if: always()
        run: |
          if [ -f server.pid ]; then
            kill $(cat server.pid) || true
          fi
