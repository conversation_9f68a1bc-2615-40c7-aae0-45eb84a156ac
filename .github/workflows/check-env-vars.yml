name: Check Environment Variables

on:
  pull_request:
    paths:
      - '**.py'
      - 'src/local_deep_research/settings/**'
      - '.github/workflows/check-env-vars.yml'
  push:
    branches:
      - main
      - dev
    paths:
      - '**.py'

jobs:
  check-env-vars:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v5

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          pip install loguru sqlalchemy sqlalchemy-utc platformdirs pydantic

      - name: Run environment variable validation
        run: |
          python tests/settings/env_vars/test_env_var_usage.py
