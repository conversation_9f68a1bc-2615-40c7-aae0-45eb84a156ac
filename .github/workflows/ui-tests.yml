name: UI Tests

on:
  push:
    paths:
      - 'src/local_deep_research/web/static/**'
      - 'src/local_deep_research/web/templates/**'
      - 'tests/ui_tests/**'
  pull_request:
    types: [opened, synchronize, reopened]
  schedule:
    # Run UI tests nightly at 3 AM UTC
    - cron: '0 3 * * *'
  workflow_dispatch:

jobs:
  ui-tests:
    runs-on: ubuntu-latest
    name: UI/Browser Tests
    if: |
      github.event_name == 'push' ||
      github.event_name == 'workflow_dispatch' ||
      github.event_name == 'schedule' ||
      (github.event_name == 'pull_request' && (github.event.pull_request.base.ref == 'main' || github.event.pull_request.base.ref == 'dev'))

    steps:
      - uses: actions/checkout@v5

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Set up PDM
        uses: pdm-project/setup-pdm@v4
        with:
          python-version: '3.12'

      - name: Install system dependencies for SQLCipher
        run: |
          sudo apt-get update
          sudo apt-get install -y libsqlcipher-dev

      - name: Install dependencies
        run: |
          # Install in development mode to ensure all modules are available
          pdm sync -d
          cd tests && npm install

      - name: Install browser dependencies
        run: |
          # Install Chrome for Puppeteer
          npx puppeteer browsers install chrome

      - name: Setup test directories
        run: |
          mkdir -p data
          mkdir -p tests/ui_tests/screenshots
          echo "Created data and screenshots directories for tests"

      - name: Start application server
        run: |
          export LDR_USE_FALLBACK_LLM=true
          export PYTHONPATH=$PWD/src:$PYTHONPATH
          echo "Starting web server..."
          pdm run python -m local_deep_research.web.app 2>&1 | tee server.log &
          echo $! > server.pid

      - name: Wait for server to be ready
        run: |
          # Wait up to 60 seconds for server to start
          for i in {1..60}; do
            if curl -f http://localhost:5000/api/v1/health 2>/dev/null; then
              echo "Server is ready after $i seconds"
              break
            fi
            if ! kill -0 $(cat server.pid) 2>/dev/null; then
              echo "Server process died!"
              echo "Server log:"
              cat server.log
              exit 1
            fi
            echo "Waiting for server... ($i/60)"
            sleep 1
          done

          # Final check
          if ! curl -f http://localhost:5000/api/v1/health 2>/dev/null; then
            echo "Server failed to start after 60 seconds"
            echo "Server log:"
            cat server.log
            exit 1
          fi

      - name: Run UI tests
        run: |
          export LDR_USE_FALLBACK_LLM=true
          export DISPLAY=:99
          export SKIP_FLAKY_TESTS=true
          cd tests/ui_tests && xvfb-run -a -s "-screen 0 1920x1080x24" node run_all_tests.js

      - name: Upload UI test screenshots
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: ui-test-screenshots
          path: tests/ui_tests/screenshots/

      - name: Upload UI test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: ui-test-results
          path: tests/ui_tests/results/

      - name: Stop server
        if: always()
        run: |
          if [ -f server.pid ]; then
            kill $(cat server.pid) || true
            rm server.pid
          fi
