name: Infrastructure Tests

on:
  push:
    paths:
      - 'src/local_deep_research/web/routes/**'
      - 'src/local_deep_research/web/static/js/config/**'
      - 'tests/infrastructure_tests/**'
      - '.github/workflows/infrastructure-tests.yml'
  pull_request:
    paths:
      - 'src/local_deep_research/web/routes/**'
      - 'src/local_deep_research/web/static/js/config/**'
      - 'tests/infrastructure_tests/**'
      - '.github/workflows/infrastructure-tests.yml'

jobs:
  test:
    runs-on: ubuntu-latest
    name: Run Infrastructure Tests

    steps:
      - uses: actions/checkout@v5

      - name: Build Docker image
        run: docker build --target ldr-test -t ldr-test .

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install JavaScript test dependencies
        run: |
          cd tests/infrastructure_tests && npm install

      - name: Run Python infrastructure tests
        run: |
          docker run --rm \
            -v $PWD:/app \
            -w /app \
            ldr-test \
            sh -c "cd /app && python -m pytest tests/infrastructure_tests/test_*.py -v --color=yes --no-header -rN"

      - name: Run JavaScript infrastructure tests
        run: |
          cd tests/infrastructure_tests && npm test

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: infrastructure-test-results
          path: |
            tests/infrastructure_tests/coverage/
            tests/infrastructure_tests/test-results/
