name: Security Tests

on:
  push:
    branches: [ main, dev, develop ]
  pull_request:
    branches: [ main, dev, develop ]
  workflow_dispatch:
  schedule:
    # Run security tests daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  security-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 25

    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_security_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v5

    - name: Set up Python 3.11
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: Cache pip packages
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-security-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-security-
          ${{ runner.os }}-pip-

    - name: Set up PDM
      uses: pdm-project/setup-pdm@v4
      with:
        python-version: '3.11'

    - name: Install system dependencies for SQLCipher
      run: |
        sudo apt-get update
        sudo apt-get install -y libsqlcipher-dev

    - name: Install dependencies
      run: |
        pdm sync -d
        pdm add bandit safety sqlparse pytest pytest-cov --no-sync
        pdm sync

    - name: Run Bandit security linter
      run: |
        bandit -r src/ -f json -o bandit-report.json || true
        if [ -f bandit-report.json ]; then
          echo "Bandit security scan completed"
        fi

    - name: Check for known vulnerabilities with Safety
      run: |
        safety check --json > safety-report.json || true
        echo "Safety vulnerability scan completed"

    - name: Run SQL injection tests
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_security_db
      run: |
        # Run SQL injection specific tests
        python -m pytest tests/security/test_sql_injection.py -v --tb=short || true
        python -m pytest tests/test_sql_injection_prevention.py -v --tb=short || true

    - name: Run XSS prevention tests
      run: |
        python -m pytest tests/security/test_xss_prevention.py -v --tb=short || true
        python -m pytest tests/test_xss_prevention.py -v --tb=short || true

    - name: Run CSRF protection tests
      run: |
        python -m pytest tests/security/test_csrf_protection.py -v --tb=short || true
        python -m pytest tests/test_csrf_protection.py -v --tb=short || true

    - name: Run authentication security tests
      run: |
        python -m pytest tests/security/test_auth_security.py -v --tb=short || true
        python -m pytest tests/test_password_security.py -v --tb=short || true
        python -m pytest tests/test_session_security.py -v --tb=short || true

    - name: Run API security tests
      run: |
        python -m pytest tests/security/test_api_security.py -v --tb=short || true
        python -m pytest tests/test_rate_limiting.py -v --tb=short || true
        python -m pytest tests/test_api_authentication.py -v --tb=short || true

    - name: Run input validation tests
      run: |
        python -m pytest tests/security/test_input_validation.py -v --tb=short || true
        python -m pytest tests/test_input_sanitization.py -v --tb=short || true

    - name: Check for hardcoded secrets
      run: |
        # Check for potential secrets in code
        grep -r -E "(api[_-]?key|secret[_-]?key|password|token)" src/ --include="*.py" | \
          grep -v -E "(os\.environ|getenv|config\[|placeholder|example|test)" | \
          grep -E "=\s*['\"]" || echo "No hardcoded secrets found"

    - name: Generate security report
      if: always()
      run: |
        echo "Security Test Report"
        echo "==================="
        echo ""
        if [ -f bandit-report.json ]; then
          echo "Bandit Security Issues:"
          python -c "import json; data=json.load(open('bandit-report.json')); print(f'  High: {len([i for i in data.get(\"results\", []) if i[\"issue_severity\"] == \"HIGH\"])}'); print(f'  Medium: {len([i for i in data.get(\"results\", []) if i[\"issue_severity\"] == \"MEDIUM\"])}'); print(f'  Low: {len([i for i in data.get(\"results\", []) if i[\"issue_severity\"] == \"LOW\"])}')" || true
        fi
        echo ""
        if [ -f safety-report.json ]; then
          echo "Known Vulnerabilities:"
          python -c "import json; data=json.load(open('safety-report.json')); print(f'  Total: {len(data) if isinstance(data, list) else 0}')" || true
        fi

    - name: Upload security reports
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json
