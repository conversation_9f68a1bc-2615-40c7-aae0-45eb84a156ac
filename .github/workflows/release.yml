name: Create Release

on:
  push:
    branches:
      - main  # Automatically create release when merging to main
    tags:
      - 'v*.*.*'  # Also support manual version tags
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., 0.4.3)'
        required: true
        type: string
      prerelease:
        description: 'Mark as pre-release'
        required: false
        type: boolean
        default: false

jobs:
  create-release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: read

    steps:
      - name: Checkout code
        uses: actions/checkout@v5
        with:
          fetch-depth: 0  # Fetch full history for changelog generation

      - name: Determine version
        id: version
        run: |
          if [ "${{ github.event_name }}" == "push" ] && [[ "${{ github.ref }}" == refs/tags/* ]]; then
            # Extract version from tag (remove 'v' prefix)
            VERSION=${GITHUB_REF#refs/tags/v}
          elif [ "${{ github.event_name }}" == "push" ] && [ "${{ github.ref }}" == "refs/heads/main" ]; then
            # Get version from __version__.py for main branch pushes
            VERSION=$(grep -oP '(?<=__version__ = ")[^"]*' src/local_deep_research/__version__.py)
          else
            # Use manual input
            VERSION="${{ github.event.inputs.version }}"
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "tag=v$VERSION" >> $GITHUB_OUTPUT

      - name: Check if release already exists
        id: check_release
        run: |
          if gh release view "${{ steps.version.outputs.tag }}" >/dev/null 2>&1; then
            echo "Release ${{ steps.version.outputs.tag }} already exists, skipping..."
            echo "exists=true" >> $GITHUB_OUTPUT
          else
            echo "Release ${{ steps.version.outputs.tag }} does not exist, proceeding..."
            echo "exists=false" >> $GITHUB_OUTPUT
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Verify version matches __version__.py
        if: steps.check_release.outputs.exists == 'false'
        run: |
          VERSION_FILE_VERSION=$(grep -oP '(?<=__version__ = ")[^"]*' src/local_deep_research/__version__.py)
          if [ "$VERSION_FILE_VERSION" != "${{ steps.version.outputs.version }}" ]; then
            echo "Error: Version mismatch!"
            echo "Tag/input version: ${{ steps.version.outputs.version }}"
            echo "__version__.py version: $VERSION_FILE_VERSION"
            exit 1
          fi
          echo "Version verified: ${{ steps.version.outputs.version }}"

      - name: Generate changelog
        if: steps.check_release.outputs.exists == 'false'
        id: changelog
        run: |
          # Get the previous tag
          PREVIOUS_TAG=$(git describe --tags --abbrev=0 HEAD^ 2>/dev/null || echo "")

          if [ -z "$PREVIOUS_TAG" ]; then
            echo "No previous tag found, generating changelog from all commits"
            COMMITS=$(git log --pretty=format:"- %s (%h)" --no-merges)
          else
            echo "Generating changelog since $PREVIOUS_TAG"
            COMMITS=$(git log ${PREVIOUS_TAG}..HEAD --pretty=format:"- %s (%h)" --no-merges)
          fi

          # Create changelog content
          cat > changelog.md << EOF
          ## What's Changed

          $COMMITS

          **Full Changelog**: https://github.com/LearningCircuit/local-deep-research/compare/${PREVIOUS_TAG}...${{ steps.version.outputs.tag }}
          EOF

          echo "changelog_file=changelog.md" >> $GITHUB_OUTPUT

      - name: Create GitHub Release
        if: steps.check_release.outputs.exists == 'false'
        uses: actions/create-release@v1
        env:
          # Use PAT instead of GITHUB_TOKEN to trigger downstream workflows
          GITHUB_TOKEN: ${{ secrets.PAT_TOKEN }}
        with:
          tag_name: ${{ steps.version.outputs.tag }}
          release_name: Release ${{ steps.version.outputs.version }}
          body_path: changelog.md
          draft: false
          prerelease: ${{ github.event.inputs.prerelease == 'true' }}

      - name: Summary
        run: |
          echo "🎉 Release ${{ steps.version.outputs.version }} created successfully!"
          echo "📋 This will trigger PyPI and Docker publishing workflows"
          echo "🔍 Check the releases page: https://github.com/LearningCircuit/local-deep-research/releases"
