name: Version Check

on:
  pull_request:
    paths:
      - 'src/**'
    types: [ opened, synchronize, reopened ]
    branches:
      - 'main'

permissions:
  contents: write
  pull-requests: write

jobs:
  version-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v5
        with:
          fetch-depth: 0
          token: ${{ secrets.PAT_TOKEN }}
          ref: ${{ github.head_ref }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Check and auto-bump version
        if: "!contains(github.event.head_commit.message, 'chore: auto-bump version')"
        run: |
          if ! git diff origin/${{ github.base_ref }}..HEAD -G"__version__" | grep -E '\+.*__version__.*='; then
            echo "Version not updated. Auto-bumping patch version..."

            # Get current version
            current_version=$(grep -oP '(?<=__version__ = ")[^"]*' src/local_deep_research/__version__.py)
            echo "Current version: $current_version"

            # Split version into parts
            IFS='.' read -ra VERSION_PARTS <<< "$current_version"
            major=${VERSION_PARTS[0]}
            minor=${VERSION_PARTS[1]}
            patch=${VERSION_PARTS[2]}

            # Increment patch version
            new_patch=$((patch + 1))
            new_version="$major.$minor.$new_patch"
            echo "New version: $new_version"

            # Update version file
            sed -i "s/__version__ = \"$current_version\"/__version__ = \"$new_version\"/" src/local_deep_research/__version__.py

            # Configure git
            git config --local user.email "<EMAIL>"
            git config --local user.name "GitHub Action"

            # Commit the version bump
            git add src/local_deep_research/__version__.py
            git commit -m "chore: auto-bump version to $new_version"

            # Push to the PR branch
            git push origin ${{ github.head_ref }}

            echo "Version automatically bumped to $new_version"
          else
            echo "Version has been updated manually. No auto-bump needed."
          fi
