name: File Whitelist Security Check
# Enhanced security checks with comprehensive file type detection

on:
  pull_request:
    branches: [ main, dev ]
  push:
    branches: [ main, dev ]

jobs:
  whitelist-check:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v5
      with:
        fetch-depth: 0

    - name: Security checks for files
      run: |
        # Define allowed file extensions and specific files
        ALLOWED_PATTERNS=(
          "\.py$"
          "\.js$"
          "\.html$"
          "\.css$"
          "\.json$"
          "\.md$"
          "\.yml$"
          "\.yaml$"
          "\.sh$"
          "\.cfg$"
          "\.flake8$"
          "\.ipynb$"
          "\.template$"
          "^\.gitignore$"
          "^\.gitkeep$"
          ".*\.gitkeep$"
          ".*\.gitignore$"
          "^\.pre-commit-config\.yaml$"
          "^\.isort\.cfg$"
          "^\.coveragerc$"
          "^\.secrets\.baseline$"
          "^pytest\.ini$"
          "^LICENSE$"
          "^README$"
          "^README\.md$"
          "^CONTRIBUTING\.md$"
          "^SECURITY\.md$"
          "^Dockerfile$"
          "^pyproject\.toml$"
          "^pdm\.lock$"
          "^package\.json$"
          "^MANIFEST\.in$"
          "^\.github/CODEOWNERS$"
          "^\.github/.*\.(yml|yaml|md)$"
          "installers/.*\.(bat|ps1|iss|ico)$"
        )

        # Get list of files to check
        echo "🔧 DEBUG: GITHUB_EVENT_NAME=$GITHUB_EVENT_NAME"
        echo "🔧 DEBUG: GITHUB_BASE_REF=$GITHUB_BASE_REF"

        if [ "$GITHUB_EVENT_NAME" = "pull_request" ]; then
          # For PRs: check all files that would be added/modified in the entire PR
          echo "🔍 Checking all files in PR commits from $GITHUB_BASE_REF to HEAD..."

          echo "🔧 DEBUG: Running git diff command..."
          CHANGED_FILES=$(git diff --name-only --diff-filter=AM origin/$GITHUB_BASE_REF..HEAD)
          echo "🔧 DEBUG: git diff found $(echo "$CHANGED_FILES" | wc -l) files"

          echo "🔧 DEBUG: Running git log command..."
          # Also get newly added files across all commits in the PR
          # Use a more robust approach that handles edge cases
          ALL_NEW_FILES=$(git log --name-only --pretty=format: --diff-filter=A origin/$GITHUB_BASE_REF..HEAD 2>/dev/null | grep -v '^$' | sort | uniq || echo "")
          echo "🔧 DEBUG: git log found $(echo "$ALL_NEW_FILES" | wc -w) files"

          # Combine both lists and remove duplicates - handle empty ALL_NEW_FILES
          if [ -n "$ALL_NEW_FILES" ]; then
            CHANGED_FILES=$(echo -e "$CHANGED_FILES\n$ALL_NEW_FILES" | sort | uniq | grep -v '^$')
          fi
          echo "🔧 DEBUG: Combined list has $(echo "$CHANGED_FILES" | wc -l) files"
        else
          # For direct pushes: check files in the current commit
          echo "🔧 DEBUG: Direct push mode - checking HEAD~1..HEAD"
          CHANGED_FILES=$(git diff --name-only --diff-filter=AM HEAD~1..HEAD)
          echo "🔧 DEBUG: Found $(echo "$CHANGED_FILES" | wc -l) files in direct push"
        fi

        echo "🔧 DEBUG: Files to check:"
        echo "$CHANGED_FILES" | head -10
        if [ $(echo "$CHANGED_FILES" | wc -l) -gt 10 ]; then
          echo "🔧 DEBUG: ... and $(echo "$CHANGED_FILES" | wc -l | awk '{print $1-10}') more files"
        fi

        echo "🔍 Running comprehensive security checks..."
        echo ""

        WHITELIST_VIOLATIONS=()
        LARGE_FILES=()
        SECRET_VIOLATIONS=()
        BINARY_FILES=()
        SUSPICIOUS_FILES=()
        RESEARCH_DATA_VIOLATIONS=()
        FLASK_SECRET_VIOLATIONS=()
        ENV_FILE_VIOLATIONS=()
        HIGH_ENTROPY_VIOLATIONS=()
        HARDCODED_PATH_VIOLATIONS=()
        HARDCODED_IP_VIOLATIONS=()
        SUSPICIOUS_FILETYPE_VIOLATIONS=()

        echo "🔧 DEBUG: Starting file processing loop..."
        FILE_COUNT=0
        for file in $CHANGED_FILES; do
          FILE_COUNT=$((FILE_COUNT + 1))
          echo "🔧 DEBUG: Processing file $FILE_COUNT: $file"

          # Skip deleted files
          if [ ! -f "$file" ]; then
            echo "🔧 DEBUG: File $file does not exist, skipping"
            continue
          fi

          echo "Checking: $file"

          # 1. Whitelist check
          ALLOWED=false
          for pattern in "${ALLOWED_PATTERNS[@]}"; do
            if echo "$file" | grep -qE "$pattern"; then
              ALLOWED=true
              break
            fi
          done

          if [ "$ALLOWED" = "false" ]; then
            WHITELIST_VIOLATIONS+=("$file")
          fi

          # 2. Large file check (>1MB)
          if [ -f "$file" ]; then
            FILE_SIZE=$(stat -c%s "$file" 2>/dev/null || echo 0)
            if [ "$FILE_SIZE" -gt 1048576 ]; then
              LARGE_FILES+=("$file ($(echo $FILE_SIZE | awk '{printf "%.1fMB", $1/1024/1024}'))")
            fi
          fi

          # 3. Binary file check
          if file "$file" | grep -q "binary"; then
            BINARY_FILES+=("$file")
          fi

          # 4. Secret pattern check in file content - whitelist approach
          if [ -f "$file" ] && [ -r "$file" ]; then
            # Define whitelist patterns for legitimate files that can contain sensitive-looking keywords
            SAFE_FILE_PATTERNS=(
              "src/local_deep_research/metrics/.*\.py$"
              "src/local_deep_research/web_search_engines/.*\.py$"
              "src/local_deep_research/web/services/.*\.py$"
              "src/local_deep_research/utilities/.*\.py$"
              "src/local_deep_research/config/.*\.py$"
              "src/local_deep_research/.*migrate.*\.py$"
              "src/local_deep_research/web/database/.*migration.*\.py$"
              "src/local_deep_research/advanced_search_system/.*\.py$"
              "src/local_deep_research/benchmarks/.*\.py$"
              "src/local_deep_research/web/static/js/components/.*\.js$"
              "src/local_deep_research/database/.*\.py$"
              "src/local_deep_research/web/queue/.*\.py$"
              "src/local_deep_research/api/.*\.py$"
              "src/local_deep_research/settings/.*\.py$"
              "src/local_deep_research/memory_cache/.*\.py$"
              "src/local_deep_research/web/auth/.*\.py$"
              "src/local_deep_research/news/.*\.py$"
              "src/local_deep_research/defaults/settings/.*\.json$"
              "docs/.*\.md$"
              "tests/.*\.py$"
              ".*test.*\.py$"
              ".*mock.*\.py$"
              ".*example.*\.py$"
              "scripts/audit_.*\.py$"
            )

            # Check if file matches whitelist patterns
            FILE_WHITELISTED=false
            for pattern in "${SAFE_FILE_PATTERNS[@]}"; do
              if echo "$file" | grep -qE "$pattern"; then
                FILE_WHITELISTED=true
                break
              fi
            done

            # Only check for secrets if file is not whitelisted
            if [ "$FILE_WHITELISTED" = "false" ]; then
              # Enhanced secret detection with LLM provider keys
              if grep -iE "(api[_-]?key|secret|password|token|private[_-]?key|sk-[a-zA-Z0-9]{20,}|claude-[a-zA-Z0-9]{20,}|AIzaSy[a-zA-Z0-9_-]{33})" "$file" >/dev/null 2>&1; then
                # Additional check for obvious false positives
                if ! grep -iE "(example|sample|test|mock|placeholder|<.*>|\{\{.*\}\})" "$file" >/dev/null 2>&1; then
                  SECRET_VIOLATIONS+=("$file")
                fi
              fi
            fi
          fi

          # 5. Suspicious filename patterns - whitelist approach
          SAFE_FILENAME_PATTERNS=(
            ".*token_counter.*\.py$"
            ".*migrate.*token.*\.py$"
            ".*enhanced.*token.*\.md$"
            "docs/.*token.*\.md$"
            "tests/.*\.py$"
            ".*test.*\.py$"
            "\.secrets\.baseline$"
            ".*session_passwords\.py$"
            ".*change_password\.html$"
          )

          # Check if filename looks suspicious
          if echo "$file" | grep -iE "(secret|password|token|\.key$|\.pem$|\.p12$|\.pfx$|\.env$)" >/dev/null; then
            # Check if filename matches whitelist patterns
            FILENAME_WHITELISTED=false
            for pattern in "${SAFE_FILENAME_PATTERNS[@]}"; do
              if echo "$file" | grep -qE "$pattern"; then
                FILENAME_WHITELISTED=true
                break
              fi
            done

            if [ "$FILENAME_WHITELISTED" = "false" ]; then
              SUSPICIOUS_FILES+=("$file")
            fi
          fi

          # 6. LDR-specific security checks
          # Check for research data leakage
          if [ -f "$file" ] && [ -r "$file" ]; then
            # Check for hardcoded research queries in non-test files
            if ! echo "$file" | grep -qE "(test|mock|example)"; then
              if grep -E "(research_id|session_id|query_id).*=.*[\"'][0-9a-f]{8,}[\"']" "$file" >/dev/null 2>&1; then
                RESEARCH_DATA_VIOLATIONS+=("$file")
              fi
            fi

            # Check for Flask secret keys
            if grep -E "SECRET_KEY.*=.*[\"'][^\"']{16,}[\"']" "$file" >/dev/null 2>&1; then
              if ! grep -iE "(os\.environ|getenv|config\[|example|placeholder)" "$file" >/dev/null 2>&1; then
                FLASK_SECRET_VIOLATIONS+=("$file")
              fi
            fi

            # Check for environment files
            if echo "$file" | grep -E "\.(env|env\.[a-zA-Z]+)$" >/dev/null; then
              ENV_FILE_VIOLATIONS+=("$file")
            fi

            # Check for high-entropy strings (potential keys/secrets)
            if [ -f "$file" ] && [ -r "$file" ]; then
              # Skip HTML files and other safe file types for entropy checks
              if ! echo "$file" | grep -qE "\.(html|css|js|json|yml|yaml|md)$"; then
                # Skip news_strategy.py which contains example categories in prompts
                if ! echo "$file" | grep -qE "news_strategy\.py$"; then
                  # Look for base64-like strings or hex strings that are suspiciously long
                  if grep -E "[a-zA-Z0-9+/]{40,}={0,2}|[a-f0-9]{40,}" "$file" >/dev/null 2>&1; then
                    # Exclude common false positives
                    if ! grep -iE "(sha256|md5|hash|test|example|fixture|integrity)" "$file" >/dev/null 2>&1; then
                      HIGH_ENTROPY_VIOLATIONS+=("$file")
                    fi
                  fi
                fi
              fi
            fi

            # Check for hardcoded paths (Unix/Windows)
            if ! echo "$file" | grep -qE "(test|mock|example|\.md$|docker|Docker|\.yml$|\.yaml$|config/paths\.py$)"; then
              # Look for absolute paths and user home directories
              if grep -E "(/home/<USER>/Users/<USER>"; then
              # Look for IPv4 addresses (excluding common safe ones)
              if grep -E "\b([0-9]{1,3}\.){3}[0-9]{1,3}\b" "$file" >/dev/null 2>&1; then
                # Exclude localhost, documentation IPs, and common examples
                if ! grep -E "\b(127\.0\.0\.1|0\.0\.0\.0|localhost|192\.168\.|10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|255\.255\.255\.|192\.0\.2\.|198\.51\.100\.|203\.0\.113\.)" "$file" >/dev/null 2>&1; then
                  # Additional check to exclude obvious non-IPs (version numbers, etc)
                  if grep -E "\b([0-9]{1,3}\.){3}[0-9]{1,3}\b" "$file" | grep -vE "(version|v[0-9]+\.|release|tag)" >/dev/null 2>&1; then
                    HARDCODED_IP_VIOLATIONS+=("$file")
                  fi
                fi
              fi
            fi

            # 7. Suspicious file type check - detect potentially dangerous file types
            if [ -f "$file" ]; then
              # Check for suspicious file extensions
              if echo "$file" | grep -iE "\.(exe|dll|so|dylib|bin|deb|rpm|msi|dmg|pkg|app)$" >/dev/null; then
                SUSPICIOUS_FILETYPE_VIOLATIONS+=("$file (executable/binary)")
              elif echo "$file" | grep -iE "\.(zip|tar|gz|rar|7z|tar\.gz|tar\.bz2|tgz)$" >/dev/null; then
                SUSPICIOUS_FILETYPE_VIOLATIONS+=("$file (compressed archive)")
              elif echo "$file" | grep -iE "\.(log|tmp|temp|cache|bak|backup|swp|swo|DS_Store|thumbs\.db|desktop\.ini|~|\.orig|\.rej|\.patch)$" >/dev/null; then
                SUSPICIOUS_FILETYPE_VIOLATIONS+=("$file (temporary/cache)")
              elif echo "$file" | grep -iE "\.(png|jpg|jpeg|gif|bmp|tiff|svg|ico|webp)$" >/dev/null; then
                # Images are suspicious unless in specific directories
                if ! echo "$file" | grep -qE "(docs/|\.github/|installers/.*\.(ico|png)$)"; then
                  SUSPICIOUS_FILETYPE_VIOLATIONS+=("$file (image file)")
                fi
              elif echo "$file" | grep -iE "\.(mp3|mp4|wav|avi|mov|mkv|flv|wmv|webm|m4a|ogg)$" >/dev/null; then
                SUSPICIOUS_FILETYPE_VIOLATIONS+=("$file (media file)")
              elif echo "$file" | grep -iE "\.(csv|xlsx|xls|doc|docx|pdf|ppt|pptx)$" >/dev/null; then
                # Documents are suspicious unless in docs directory
                if ! echo "$file" | grep -qE "docs/"; then
                  SUSPICIOUS_FILETYPE_VIOLATIONS+=("$file (document file)")
                fi
              elif echo "$file" | grep -iE "\.(db|sqlite|sqlite3)$" >/dev/null; then
                SUSPICIOUS_FILETYPE_VIOLATIONS+=("$file (database file)")
              elif echo "$file" | grep -iE "node_modules/|__pycache__/|\.pyc$|\.pyo$|\.egg-info/|dist/|build/|\.cache/" >/dev/null; then
                SUSPICIOUS_FILETYPE_VIOLATIONS+=("$file (build artifact/cache)")
              fi
            fi
          fi
        done

        # Report all violations with detailed explanations
        echo "🔧 DEBUG: File processing completed. Checking violations..."
        echo "🔧 DEBUG: Whitelist violations: ${#WHITELIST_VIOLATIONS[@]}"
        echo "🔧 DEBUG: Large files: ${#LARGE_FILES[@]}"
        echo "🔧 DEBUG: Binary files: ${#BINARY_FILES[@]}"
        echo "🔧 DEBUG: Secret violations: ${#SECRET_VIOLATIONS[@]}"
        echo "🔧 DEBUG: Suspicious files: ${#SUSPICIOUS_FILES[@]}"
        echo "🔧 DEBUG: Research data violations: ${#RESEARCH_DATA_VIOLATIONS[@]}"
        echo "🔧 DEBUG: Flask secret violations: ${#FLASK_SECRET_VIOLATIONS[@]}"
        echo "🔧 DEBUG: Env file violations: ${#ENV_FILE_VIOLATIONS[@]}"
        echo "🔧 DEBUG: High entropy violations: ${#HIGH_ENTROPY_VIOLATIONS[@]}"
        echo "🔧 DEBUG: Hardcoded path violations: ${#HARDCODED_PATH_VIOLATIONS[@]}"
        echo "🔧 DEBUG: Hardcoded IP violations: ${#HARDCODED_IP_VIOLATIONS[@]}"
        echo "🔧 DEBUG: Suspicious filetype violations: ${#SUSPICIOUS_FILETYPE_VIOLATIONS[@]}"

        TOTAL_VIOLATIONS=0

        if [ ${#WHITELIST_VIOLATIONS[@]} -gt 0 ]; then
          echo ""
          echo "❌ WHITELIST VIOLATIONS - File types not allowed in repository:"
          echo "   These files don't match any allowed file extension patterns."
          echo "   If these are legitimate files, add them to ALLOWED_PATTERNS in the workflow."
          echo ""
          for violation in "${WHITELIST_VIOLATIONS[@]}"; do
            echo "  🚫 $violation"

            # Show file type and extension
            FILE_EXT="${violation##*.}"
            if [ -f "$violation" ]; then
              FILE_TYPE=$(file -b "$violation" 2>/dev/null || echo "unknown")
              echo "     → File extension: .$FILE_EXT"
              echo "     → File type: $FILE_TYPE"
              echo "     → First few lines:"
              head -3 "$violation" 2>/dev/null | while read line; do
                echo "       $line"
              done
            fi

            echo "     → Issue: File extension/type not in whitelist"
            echo "     → Fix: Add pattern to ALLOWED_PATTERNS or rename file"
            echo ""
          done
          TOTAL_VIOLATIONS=$((TOTAL_VIOLATIONS + ${#WHITELIST_VIOLATIONS[@]}))
        fi

        if [ ${#LARGE_FILES[@]} -gt 0 ]; then
          echo ""
          echo "❌ LARGE FILES (>1MB) - Files too big for repository:"
          echo "   Large files should typically be stored externally or compressed."
          echo ""
          for violation in "${LARGE_FILES[@]}"; do
            echo "  📏 $violation"
            echo "     → Issue: File size exceeds 1MB limit"
            echo "     → Fix: Use Git LFS, external storage, or compress the file"
            echo ""
          done
          TOTAL_VIOLATIONS=$((TOTAL_VIOLATIONS + ${#LARGE_FILES[@]}))
        fi

        if [ ${#BINARY_FILES[@]} -gt 0 ]; then
          echo ""
          echo "⚠️  BINARY FILES DETECTED - Review these carefully:"
          echo "   Binary files may contain sensitive data and can't be easily reviewed."
          echo ""
          for violation in "${BINARY_FILES[@]}"; do
            echo "  🔒 $violation"
            echo "     → Issue: Binary file detected (contents not reviewable)"
            echo "     → Action: Verify this file doesn't contain sensitive data"
            echo ""
          done
        fi

        if [ ${#SECRET_VIOLATIONS[@]} -gt 0 ]; then
          echo ""
          echo "❌ POTENTIAL SECRETS IN FILE CONTENT - Suspicious patterns found:"
          echo "   These files contain keywords like 'api_key', 'secret', 'password', 'token' but"
          echo "   are not in the whitelisted safe directories. Review them carefully!"
          echo ""
          for violation in "${SECRET_VIOLATIONS[@]}"; do
            echo "  🔍 EXAMINING: $violation"

            # Show the specific lines that triggered the detection
            echo "     → Suspicious content found:"
            grep -n -iE "(api[_-]?key|secret|password|token|private[_-]?key)" "$violation" 2>/dev/null | head -5 | while read line; do
              echo "       $line"
            done

            # Show file type and size for context
            if [ -f "$violation" ]; then
              FILE_TYPE=$(file -b "$violation" 2>/dev/null || echo "unknown")
              FILE_SIZE=$(stat -c%s "$violation" 2>/dev/null || echo "unknown")
              echo "     → File info: $FILE_TYPE (${FILE_SIZE} bytes)"
            fi

            echo "     → Issue: Contains sensitive-looking keywords outside whitelisted areas"
            echo "     → Fix: Either add to SAFE_FILE_PATTERNS whitelist or remove secrets"
            echo ""
          done
          TOTAL_VIOLATIONS=$((TOTAL_VIOLATIONS + ${#SECRET_VIOLATIONS[@]}))
        fi

        if [ ${#SUSPICIOUS_FILES[@]} -gt 0 ]; then
          echo ""
          echo "❌ SUSPICIOUS FILENAMES - Files with security-sensitive names:"
          echo "   These filenames contain words that often indicate sensitive files."
          echo ""
          for violation in "${SUSPICIOUS_FILES[@]}"; do
            echo "  🚨 $violation"

            # Show which keyword triggered the detection
            if echo "$violation" | grep -qi "secret"; then
              echo "     → Triggered by: 'secret' in filename"
            elif echo "$violation" | grep -qi "password"; then
              echo "     → Triggered by: 'password' in filename"
            elif echo "$violation" | grep -qi "token"; then
              echo "     → Triggered by: 'token' in filename"
            elif echo "$violation" | grep -qi "api"; then
              echo "     → Triggered by: 'api' in filename"
            elif echo "$violation" | grep -qi "key"; then
              echo "     → Triggered by: 'key' in filename"
            fi

            # Show file content preview if it exists
            if [ -f "$violation" ]; then
              FILE_TYPE=$(file -b "$violation" 2>/dev/null || echo "unknown")
              FILE_SIZE=$(stat -c%s "$violation" 2>/dev/null || echo "unknown")
              echo "     → File info: $FILE_TYPE (${FILE_SIZE} bytes)"
              echo "     → Content preview:"
              head -3 "$violation" 2>/dev/null | while read line; do
                echo "       $line"
              done
            fi

            echo "     → Issue: Filename contains suspicious keywords (secret/password/token/key)"
            echo "     → Fix: Rename file or add to SAFE_FILENAME_PATTERNS whitelist"
            echo ""
          done
          TOTAL_VIOLATIONS=$((TOTAL_VIOLATIONS + ${#SUSPICIOUS_FILES[@]}))
        fi

        # LDR-specific violation reports
        if [ ${#RESEARCH_DATA_VIOLATIONS[@]} -gt 0 ]; then
          echo ""
          echo "❌ RESEARCH DATA LEAKAGE - Hardcoded research session data found:"
          echo "   Research IDs and session data should never be hardcoded in production code."
          echo ""
          for violation in "${RESEARCH_DATA_VIOLATIONS[@]}"; do
            echo "  📊 $violation"

            # Show the specific lines with research data
            echo "     → Found hardcoded research data:"
            grep -n -E "(research_id|session_id|query_id).*=.*[\"'][0-9a-f]{8,}[\"']" "$violation" 2>/dev/null | head -3 | while read line; do
              echo "       $line"
            done

            echo "     → Issue: Hardcoded research/session IDs in non-test file"
            echo "     → Fix: Use environment variables or configuration files"
            echo ""
          done
          TOTAL_VIOLATIONS=$((TOTAL_VIOLATIONS + ${#RESEARCH_DATA_VIOLATIONS[@]}))
        fi

        if [ ${#FLASK_SECRET_VIOLATIONS[@]} -gt 0 ]; then
          echo ""
          echo "❌ FLASK SECRET KEY - Hardcoded Flask secret keys found:"
          echo "   Flask secret keys must never be hardcoded for security reasons."
          echo ""
          for violation in "${FLASK_SECRET_VIOLATIONS[@]}"; do
            echo "  🔐 $violation"

            # Show the specific lines with secret keys
            echo "     → Found hardcoded Flask secret key:"
            grep -n -E "SECRET_KEY.*=.*[\"'][^\"']{16,}[\"']" "$violation" 2>/dev/null | head -3 | while read line; do
              echo "       $line"
            done

            echo "     → Issue: Hardcoded Flask SECRET_KEY"
            echo "     → Fix: Use os.environ or load from secure config file"
            echo ""
          done
          TOTAL_VIOLATIONS=$((TOTAL_VIOLATIONS + ${#FLASK_SECRET_VIOLATIONS[@]}))
        fi

        if [ ${#ENV_FILE_VIOLATIONS[@]} -gt 0 ]; then
          echo ""
          echo "❌ ENVIRONMENT FILES - .env files detected:"
          echo "   Environment files contain sensitive configuration and should never be committed."
          echo ""
          for violation in "${ENV_FILE_VIOLATIONS[@]}"; do
            echo "  🌍 $violation"
            echo "     → Issue: Environment file in repository"
            echo "     → Fix: Add to .gitignore and use .env.example instead"
            echo ""
          done
          TOTAL_VIOLATIONS=$((TOTAL_VIOLATIONS + ${#ENV_FILE_VIOLATIONS[@]}))
        fi

        if [ ${#HIGH_ENTROPY_VIOLATIONS[@]} -gt 0 ]; then
          echo ""
          echo "❌ HIGH ENTROPY STRINGS - Potential secrets or keys detected:"
          echo "   Long random strings may be API keys, tokens, or other secrets."
          echo ""
          for violation in "${HIGH_ENTROPY_VIOLATIONS[@]}"; do
            echo "  🎲 $violation"

            # Show sample of high entropy strings
            echo "     → Found high-entropy strings:"
            grep -n -E "[a-zA-Z0-9+/]{40,}={0,2}|[a-f0-9]{40,}" "$violation" 2>/dev/null | head -3 | while read line; do
              # Truncate long lines for readability
              echo "       ${line:0:120}..."
            done

            echo "     → Issue: High-entropy strings that could be secrets"
            echo "     → Fix: Review and move to environment variables if sensitive"
            echo ""
          done
          TOTAL_VIOLATIONS=$((TOTAL_VIOLATIONS + ${#HIGH_ENTROPY_VIOLATIONS[@]}))
        fi

        if [ ${#HARDCODED_PATH_VIOLATIONS[@]} -gt 0 ]; then
          echo ""
          echo "❌ HARDCODED PATHS - System-specific paths detected:"
          echo "   Absolute paths can expose system structure and break portability."
          echo ""
          for violation in "${HARDCODED_PATH_VIOLATIONS[@]}"; do
            echo "  📁 $violation"

            # Show the specific hardcoded paths
            echo "     → Found hardcoded paths:"
            grep -n -E "(/home/<USER>/Users/<USER>" "$violation" 2>/dev/null | grep -v -E "(127\.0\.0\.1|0\.0\.0\.0|localhost|192\.168\.|10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|255\.255\.255\.|192\.0\.2\.|198\.51\.100\.|203\.0\.113\.)" | head -5 | while read line; do
              echo "       $line"
            done

            echo "     → Issue: Hardcoded IP addresses (non-private/localhost)"
            echo "     → Fix: Use DNS names, environment variables, or config files"
            echo ""
          done
          TOTAL_VIOLATIONS=$((TOTAL_VIOLATIONS + ${#HARDCODED_IP_VIOLATIONS[@]}))
        fi

        if [ ${#SUSPICIOUS_FILETYPE_VIOLATIONS[@]} -gt 0 ]; then
          echo ""
          echo "❌ SUSPICIOUS FILE TYPES - Potentially dangerous file types detected:"
          echo "   These file types are commonly used for malware, data leaks, or bloat the repository."
          echo ""
          for violation in "${SUSPICIOUS_FILETYPE_VIOLATIONS[@]}"; do
            echo "  🚨 $violation"

            FILE_PATH="${violation%% (*}"
            FILE_CATEGORY="${violation##*\\(}"
            FILE_CATEGORY="${FILE_CATEGORY%\\)}"

            # Provide specific guidance based on file category
            case "$FILE_CATEGORY" in
              "executable/binary")
                echo "     → Issue: Executable/binary files can contain malware"
                echo "     → Fix: Remove executable files, use package managers instead"
                ;;
              "compressed archive")
                echo "     → Issue: Compressed archives hide their contents from review"
                echo "     → Fix: Extract contents and commit individual files instead"
                ;;
              "temporary/cache")
                echo "     → Issue: Temporary/cache files should not be committed"
                echo "     → Fix: Add to .gitignore and remove from repository"
                ;;
              "image file")
                echo "     → Issue: Image files should only be in docs/ or .github/ directories"
                echo "     → Fix: Move to docs/ directory or remove if unnecessary"
                ;;
              "media file")
                echo "     → Issue: Media files are large and rarely needed in code repos"
                echo "     → Fix: Use external hosting or remove if unnecessary"
                ;;
              "document file")
                echo "     → Issue: Office documents should be in docs/ directory if needed"
                echo "     → Fix: Move to docs/ directory or convert to markdown"
                ;;
              "database file")
                echo "     → Issue: Database files contain data that shouldn't be in source control"
                echo "     → Fix: Add to .gitignore and use migrations/seeds instead"
                ;;
              "build artifact/cache")
                echo "     → Issue: Build artifacts and cache files bloat the repository"
                echo "     → Fix: Add to .gitignore and remove from repository"
                ;;
            esac

            # Show file info if available
            if [ -f "$FILE_PATH" ]; then
              FILE_SIZE=$(stat -c%s "$FILE_PATH" 2>/dev/null || echo "unknown")
              if [ "$FILE_SIZE" != "unknown" ]; then
                READABLE_SIZE=$(echo $FILE_SIZE | awk '{if($1>=1048576) printf "%.1fMB", $1/1048576; else if($1>=1024) printf "%.1fKB", $1/1024; else printf "%dB", $1}')
                echo "     → File size: $READABLE_SIZE"
              fi
            fi
            echo ""
          done
          TOTAL_VIOLATIONS=$((TOTAL_VIOLATIONS + ${#SUSPICIOUS_FILETYPE_VIOLATIONS[@]}))
        fi

        # Final result
        if [ $TOTAL_VIOLATIONS -eq 0 ]; then
          echo ""
          echo "✅ All security checks passed!"
          exit 0
        else
          echo ""
          echo "💡 To fix these issues:"
          echo "   - Remove data files and use external storage"
          echo "   - Use environment variables for secrets"
          echo "   - Update whitelist for legitimate file types"
          echo "   - Compress large files or use Git LFS"
          echo "   - Never hardcode research data or session IDs"
          echo "   - Use .env.example files instead of .env"
          echo "   - Replace absolute paths with relative paths or configs"
          echo "   - Use DNS names instead of hardcoded IP addresses"
          echo "   - Remove suspicious file types (executables, archives, temp files)"
          echo "   - Move images/documents to appropriate directories (docs/, .github/)"
          echo ""
          echo "⚠️  SECURITY REMINDER: This is a public repository!"
          echo "   Never commit sensitive data, API keys, or personal information."
          exit 1
        fi
