name: Critical UI Tests

on:
  push:
    branches: [ main, dev, develop ]
  pull_request:
    branches: [ main, dev, develop ]
  workflow_dispatch:

jobs:
  critical-ui-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    # No services needed - using SQLite databases

    steps:
    - uses: actions/checkout@v5

    - name: Set up PDM
      uses: pdm-project/setup-pdm@v4
      with:
        python-version: '3.11'

    - name: Cache pip packages
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y chromium-browser libsqlcipher-dev

    - name: Install Python dependencies
      run: |
        pdm sync -d
        pdm run playwright install chromium

    - name: Set up test directories
      run: |
        mkdir -p ~/.local/share/local-deep-research/encrypted_databases
        mkdir -p ~/.local/share/local-deep-research

    - name: Initialize database
      env:
        TEST_ENV: true
      run: |
        cd src
        cat > init_db.py << 'EOF'
        import os
        from pathlib import Path
        # Ensure data directory exists
        data_dir = Path.home() / '.local' / 'share' / 'local-deep-research'
        data_dir.mkdir(parents=True, exist_ok=True)
        (data_dir / 'encrypted_databases').mkdir(parents=True, exist_ok=True)

        from local_deep_research.database.auth_db import init_auth_database, get_auth_db_session
        from local_deep_research.database.models.auth import User
        from local_deep_research.database.encrypted_db import db_manager

        # Initialize auth database
        init_auth_database()

        # Create test user in auth database (no password stored)
        session = get_auth_db_session()
        user = User(username='test_admin')
        session.add(user)
        session.commit()
        session.close()

        # Create user's encrypted database with password
        engine = db_manager.create_user_database('test_admin', 'testpass123')
        print('Database initialized successfully')
        EOF
        pdm run python init_db.py

    - name: Start test server
      env:
        FLASK_ENV: testing
        TEST_ENV: true
        SECRET_KEY: test-secret-key-for-ci
      run: |
        cd src
        # Start server and get its PID
        pdm run python -m local_deep_research.web.app > server.log 2>&1 &
        SERVER_PID=$!
        echo "Server PID: $SERVER_PID"

        # Wait for server to start
        for i in {1..30}; do
          if curl -f http://127.0.0.1:5000 2>/dev/null; then
            echo "Server is ready after $i seconds"
            break
          fi
          # Check if process is still running
          if ! kill -0 $SERVER_PID 2>/dev/null; then
            echo "Server process died!"
            echo "Server log:"
            cat server.log
            exit 1
          fi
          echo "Waiting for server... ($i/30)"
          sleep 1
        done

        # Final check
        if ! curl -f http://127.0.0.1:5000 2>/dev/null; then
          echo "Server failed to start after 30 seconds"
          echo "Server log:"
          cat server.log
          exit 1
        fi

    - name: Install Node.js dependencies for UI tests
      run: |
        cd tests/ui_tests
        npm install

    - name: Create screenshots directory
      run: |
        mkdir -p tests/ui_tests/screenshots

    - name: Run critical UI tests
      env:
        CI: true
        HEADLESS: true
      run: |
        cd tests/ui_tests

        # Critical authentication and research submission tests
        echo "Running critical authentication test..."
        node test_auth_flow.js || exit 1

        echo "Running critical research submission tests..."
        node test_research_submit.js || exit 1
        node test_research_simple.js || exit 1

        echo "Running critical export test..."
        node test_export_functionality.js || exit 1

        echo "Running critical concurrent limits test..."
        node test_concurrent_limit.js || exit 1

    - name: Upload test artifacts
      if: failure()
      uses: actions/upload-artifact@v4
      with:
        name: critical-ui-test-artifacts
        path: |
          tests/ui_tests/screenshots/
          src/server.log

    - name: Generate test report
      if: always()
      run: |
        echo "Critical UI Tests completed"
        if [ -f tests/ui_tests/test_report.json ]; then
          cat tests/ui_tests/test_report.json
        fi
