name: Pre-commit Checks

on:
  pull_request:
    branches: [ main, dev ]
  push:
    branches: [ main, dev ]

jobs:
  pre-commit:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v5

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.12'

    - name: Install system dependencies for SQLCipher
      run: |
        sudo apt-get update
        sudo apt-get install -y libsqlcipher-dev

    - name: Run pre-commit
      uses: pre-commit/action@v3.0.1
      with:
        extra_args: --all-files
