name: Auto-label Fixed Issues in Dev
on:
  pull_request:
    types: [closed]
    branches: [dev]

jobs:
  label-linked-issues:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: read
    steps:
      - name: Add "fixed in dev" label to linked issues
        uses: actions/github-script@v7
        with:
          script: |
            const pr = context.payload.pull_request;
            const body = pr.body || '';
            const title = pr.title || '';

            // Combine title and body for searching
            const text = `${title} ${body}`;

            // Find issue references with keywords (fixes, closes, resolves, etc.)
            const keywordPattern = /(close[sd]?|fix(es|ed)?|resolve[sd]?)\s+#(\d+)/gi;
            const matches = text.matchAll(keywordPattern);

            // Also find simple #123 references without keywords
            const simplePattern = /#(\d+)/g;
            const simpleMatches = text.matchAll(simplePattern);

            // Collect all issue numbers
            const issueNumbers = new Set();

            // Add issues with keywords (definitely linked)
            for (const match of matches) {
              issueNumbers.add(match[3]);
            }

            // Add simple references (might be linked)
            for (const match of simpleMatches) {
              issueNumbers.add(match[1]);
            }

            // Label each issue
            for (const issueNumber of issueNumbers) {
              try {
                console.log(`Adding "fixed in dev" label to issue #${issueNumber}`);
                await github.rest.issues.addLabels({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: parseInt(issueNumber),
                  labels: ['fixed in dev']
                });
              } catch (error) {
                console.log(`Could not label issue #${issueNumber}: ${error.message}`);
                // Continue with other issues even if one fails
              }
            }

            if (issueNumbers.size === 0) {
              console.log('No linked issues found in PR');
            } else {
              console.log(`Labeled ${issueNumbers.size} issue(s) as "fixed in dev"`);
            }
