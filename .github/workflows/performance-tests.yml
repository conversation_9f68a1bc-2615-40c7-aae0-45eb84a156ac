name: Performance Tests

on:
  push:
    branches: [ main, dev, develop ]
  pull_request:
    branches: [ main, dev, develop ]
  workflow_dispatch:
  schedule:
    # Run performance tests weekly on Sunday at 3 AM UTC
    - cron: '0 3 * * 0'

jobs:
  performance-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    services:
      redis:
        image: redis:alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v5

    - name: Set up PDM
      uses: pdm-project/setup-pdm@v4
      with:
        python-version: '3.11'

    - name: Cache pip packages
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-perf-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-perf-
          ${{ runner.os }}-pip-

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y redis-tools

    - name: Install system dependencies for SQLCipher
      run: |
        sudo apt-get update
        sudo apt-get install -y libsqlcipher-dev

    - name: Install Python dependencies
      run: |
        pdm sync -d

    - name: Set up test directories
      run: |
        mkdir -p ~/.local/share/local-deep-research/encrypted_databases
        mkdir -p ~/.local/share/local-deep-research

    - name: Initialize database with test data
      run: |
        cd src
        cat > init_perf_db.py << 'EOF'
        import os
        from pathlib import Path
        # Ensure data directory exists
        data_dir = Path.home() / '.local' / 'share' / 'local-deep-research'
        data_dir.mkdir(parents=True, exist_ok=True)
        (data_dir / 'encrypted_databases').mkdir(parents=True, exist_ok=True)

        from local_deep_research.database.auth_db import init_auth_database, get_auth_db_session
        from local_deep_research.database.models.auth import User
        from local_deep_research.database.encrypted_db import db_manager

        # Initialize auth database
        init_auth_database()

        # Create test users and their databases
        session = get_auth_db_session()
        for i in range(10):
            username = f'perftest{i}'
            # Create user in auth database (no password stored)
            user = User(username=username)
            session.add(user)
            session.commit()

            # Create user's encrypted database with password
            engine = db_manager.create_user_database(username, 'testpass123')
        session.close()
        print('Databases initialized successfully')
        EOF
        pdm run python init_perf_db.py

    - name: Start application server
      env:
        REDIS_URL: redis://localhost:6379
        FLASK_ENV: testing
        SECRET_KEY: perf-test-secret-key
      run: |
        cd src
        # Start server and get its PID
        pdm run python -m local_deep_research.web.app > server.log 2>&1 &
        SERVER_PID=$!
        echo "Server PID: $SERVER_PID"

        # Wait for server to start
        for i in {1..30}; do
          if curl -f http://127.0.0.1:5000 2>/dev/null; then
            echo "Server is ready after $i seconds"
            break
          fi
          # Check if process is still running
          if ! kill -0 $SERVER_PID 2>/dev/null; then
            echo "Server process died!"
            echo "Server log:"
            cat server.log
            exit 1
          fi
          echo "Waiting for server... ($i/30)"
          sleep 1
        done

        # Final check
        if ! curl -f http://127.0.0.1:5000 2>/dev/null; then
          echo "Server failed to start after 30 seconds"
          echo "Server log:"
          cat server.log
          exit 1
        fi

    - name: Run database performance tests
      run: |
        # Note: No dedicated performance test directory exists
        # Run any available database tests
        pdm run pytest tests/database/test_benchmark_models.py -v || true

    - name: Run API performance tests
      run: |
        # Note: No dedicated API performance tests exist
        echo "Skipping API performance tests - no test files available"

    - name: Run load tests with Locust
      run: |
        # Note: No performance test directory or locustfile exists
        echo "Skipping Locust load tests - no test files available"

    - name: Run memory profiling
      run: |
        # Profile memory usage of key operations
        cat > memory_profile.py << 'EOFSCRIPT'
        import tracemalloc
        import requests
        import json

        tracemalloc.start()

        # Simulate API calls
        base_url = 'http://127.0.0.1:5000'
        session = requests.Session()

        # Login
        session.post(f'{base_url}/auth/login', json={'username': 'perftest0', 'password': 'testpass123'})

        # Make various API calls
        for i in range(10):
            session.get(f'{base_url}/api/research/history')
            session.get(f'{base_url}/api/user/stats')

        current, peak = tracemalloc.get_traced_memory()
        print(f'Current memory usage: {current / 1024 / 1024:.2f} MB')
        print(f'Peak memory usage: {peak / 1024 / 1024:.2f} MB')

        tracemalloc.stop()
        EOFSCRIPT
        pdm run python memory_profile.py

    - name: Run frontend performance tests
      run: |
        cd tests/ui_tests
        npm install

        # Run available metrics tests as performance indicators
        echo "Running metrics tests as performance indicators..."
        node test_metrics.js || true

    - name: Analyze Python performance hotspots
      run: |
        # Profile a typical request flow
        cat > profile_requests.py << 'EOFSCRIPT'
        import requests
        import time

        session = requests.Session()
        base_url = 'http://127.0.0.1:5000'

        # Login
        session.post(f'{base_url}/auth/login', json={'username': 'perftest0', 'password': 'testpass123'})

        # Simulate typical user actions
        for _ in range(5):
            session.get(f'{base_url}/api/research/history')
            session.get(f'{base_url}/api/models')
            time.sleep(0.5)
        EOFSCRIPT
        pdm run py-spy record -d 10 -o profile.svg -- pdm run python profile_requests.py || true

    - name: Generate performance report
      if: always()
      run: |
        echo "Performance Test Report"
        echo "======================"
        echo ""
        echo "Test Categories:"
        echo "- Database query performance"
        echo "- API response times"
        echo "- Memory usage profiling"
        echo "- Load testing results"
        echo "- Frontend rendering performance"
        echo ""

        # Check for performance regression
        if [ -d .benchmarks ]; then
          echo "Benchmark History Available"
          cat > check_benchmarks.py << 'EOFSCRIPT'
          import json
          import os

          if os.path.exists('.benchmarks'):
              benchmark_files = [f for f in os.listdir('.benchmarks') if f.endswith('.json')]
              if benchmark_files:
                  latest = sorted(benchmark_files)[-1]
                  with open(f'.benchmarks/{latest}') as f:
                      data = json.load(f)
                      if 'benchmarks' in data:
                          print('Recent benchmark results:')
                          for bench in data['benchmarks'][:5]:
                              print(f"  {bench.get('name', 'N/A')}: {bench.get('stats', {}).get('mean', 'N/A'):.4f}s")
        EOFSCRIPT
          pdm run python check_benchmarks.py || true
        fi

    - name: Upload performance artifacts
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: performance-test-artifacts
        path: |
          .benchmarks/
          profile.svg
          tests/performance/*.html
          src/server.log
