name: Publish to <PERSON><PERSON><PERSON>

on:
  release:
    types: [created]

jobs:
  # Build frontend if package.json exists (isolated, no secrets)
  build-frontend:
    runs-on: ubuntu-latest
    outputs:
      has-frontend: ${{ steps.check.outputs.has-frontend }}
    container:
      image: node:20-alpine
      # Note: Network is needed for npm ci to work, but no secrets are available
      options: --user 1001
    permissions:
      contents: read

    steps:
    - uses: actions/checkout@v5

    - name: Check for frontend assets
      id: check
      run: |
        if [ -f "package.json" ] && [ -f "package-lock.json" ]; then
          echo "has-frontend=true" >> $GITHUB_OUTPUT
          echo "Found package.json and package-lock.json - will build frontend"
        else
          echo "has-frontend=false" >> $GITHUB_OUTPUT
          echo "No frontend build files found - skipping"
        fi

    - name: Build frontend
      if: steps.check.outputs.has-frontend == 'true'
      run: |
        # Install dependencies from root package.json
        npm ci --ignore-scripts --no-audit --no-fund
        # Build with Vite (outputs to src/local_deep_research/web/static/dist)
        npm run build --ignore-scripts
        # Create build marker
        echo '{"status":"complete","built":"$(date -Iseconds)"}' > src/local_deep_research/web/static/.frontend-built

    - name: Upload frontend artifacts
      if: steps.check.outputs.has-frontend == 'true'
      uses: actions/upload-artifact@v4
      with:
        name: frontend-assets
        path: src/local_deep_research/web/static/
        retention-days: 1

  # Build Python package (isolated, no PyPI access)
  build-package:
    needs: build-frontend
    runs-on: ubuntu-latest
    container:
      image: python:3.12-slim
    permissions:
      contents: read

    steps:
    - uses: actions/checkout@v5

    - name: Install build dependencies
      run: |
        apt-get update && apt-get install -y libsqlcipher-dev build-essential
        pip install pdm

    - name: Download frontend artifacts
      if: needs.build-frontend.outputs.has-frontend == 'true'
      uses: actions/download-artifact@v4
      continue-on-error: true
      with:
        name: frontend-assets
        path: src/local_deep_research/web/static/

    - name: Build Python package
      run: pdm build

    - name: Upload package
      uses: actions/upload-artifact@v4
      with:
        name: python-dist
        path: dist/
        retention-days: 1

  # Publish (ONLY job with PyPI access)
  publish:
    needs: build-package
    runs-on: ubuntu-latest
    environment: release
    permissions:
      id-token: write

    steps:
    - name: Download package
      uses: actions/download-artifact@v4
      with:
        name: python-dist
        path: dist/

    - name: List packages
      run: ls -lh dist/

    - name: Publish to Test PyPI
      if: github.event.release.prerelease == true
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        repository-url: https://test.pypi.org/legacy/

    - name: Publish to PyPI
      if: github.event.release.prerelease == false
      uses: pypa/gh-action-pypi-publish@release/v1
