name: Enhanced Test Framework

on:
  push:
    branches: [ main, dev ]
    paths:
      - 'tests/**'
      - 'src/local_deep_research/web_search_engines/**'
      - '.github/workflows/enhanced-tests.yml'
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [ main, dev ]
    paths:
      - 'tests/**'
      - 'src/local_deep_research/web_search_engines/**'
  workflow_dispatch:

jobs:
  enhanced-tests:
    runs-on: ubuntu-latest
    name: Enhanced Test Framework

    steps:
      - uses: actions/checkout@v5

      - name: Build Docker image
        run: docker build --target ldr-test -t ldr-test .

      - name: Run enhanced test framework tests
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e LDR_USE_FALLBACK_LLM=true \
            -e CI=true \
            -w /app \
            ldr-test \
            sh -c "python -m pytest -v \
              tests/test_wikipedia_url_security.py \
              tests/test_search_engines_enhanced.py \
              tests/test_utils.py \
              -x \
              --tb=short"

      - name: Run fixture tests
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e LDR_USE_FALLBACK_LLM=true \
            -e CI=true \
            -w /app \
            ldr-test \
            sh -c 'python -c "
          from tests.fixtures.search_engine_mocks import SearchEngineMocks, validate_wikipedia_url
          from tests.mock_fixtures import get_mock_search_results
          from tests.mock_modules import create_mock_llm_config
          print(\"✅ All fixture imports successful\")
          # Test URL validation
          assert validate_wikipedia_url(\"https://en.wikipedia.org/wiki/Test\") == True
          assert validate_wikipedia_url(\"https://evil.com/wiki/Test\") == False
          print(\"✅ URL validation tests passed\")
          # Test mock data
          results = get_mock_search_results()
          assert len(results) == 2
          print(\"✅ Mock data tests passed\")
          "'

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: enhanced-test-results
          path: |
            .coverage
            htmlcov/
            tests/screenshots/
