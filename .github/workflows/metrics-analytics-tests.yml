name: Metrics & Analytics Tests

on:
  push:
    paths:
      - 'src/local_deep_research/metrics/**'
      - 'src/local_deep_research/web/static/js/metrics/**'
      - 'src/local_deep_research/web/templates/metrics/**'
      - 'tests/ui_tests/test_metrics*.js'
      - 'tests/ui_tests/test_cost*.js'
  pull_request:
    types: [opened, synchronize, reopened]
  schedule:
    # Run weekly on Sundays at 1 AM UTC
    - cron: '0 1 * * 0'
  workflow_dispatch:

jobs:
  metrics-analytics-tests:
    runs-on: ubuntu-latest
    name: Metrics & Analytics Test Suite

    steps:
      - uses: actions/checkout@v5

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Set up PDM
        uses: pdm-project/setup-pdm@v4
        with:
          python-version: '3.12'

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libsqlcipher-dev xvfb

      - name: Install dependencies
        run: |
          pdm sync -d
          cd tests/ui_tests && npm install

      - name: Install browser
        run: |
          npx puppeteer browsers install chrome

      - name: Setup directories
        run: |
          mkdir -p data
          mkdir -p tests/ui_tests/screenshots/metrics
          mkdir -p tests/ui_tests/results/metrics

      - name: Start server
        run: |
          export LDR_USE_FALLBACK_LLM=true
          export PYTHONPATH=$PWD/src:$PYTHONPATH
          pdm run python -m local_deep_research.web.app &
          echo $! > server.pid

          # Wait for server
          for i in {1..30}; do
            if curl -f http://localhost:5000/api/v1/health 2>/dev/null; then
              echo "Server ready"
              break
            fi
            sleep 1
          done

      - name: Run metrics and analytics tests
        run: |
          export DISPLAY=:99
          cd tests/ui_tests

          # Create test runner for metrics tests
          cat > run_metrics_tests.js << 'EOF'
          const { spawn } = require('child_process');

          const tests = [
              // Star reviews analytics
              { name: 'Star Reviews', file: 'test_star_reviews.js' }

              // TODO: Add these tests as they are implemented:
              // { name: 'Metrics Dashboard', file: 'test_metrics_dashboard.js' },
              // { name: 'Cost Analytics', file: 'test_cost_analytics.js' },
              // { name: 'Metrics Verification', file: 'test_metrics_verification.js' },
              // { name: 'Metrics Full Flow', file: 'test_metrics_full_flow.js' },
              // { name: 'Metrics Display', file: 'test_metrics_display.js' },
              // { name: 'Metrics Browser', file: 'test_metrics_browser.js' },
              // { name: 'Metrics with LLM', file: 'test_metrics_with_llm.js' },
              // { name: 'Simple Metrics', file: 'test_simple_metrics.js' },
              // { name: 'Simple Cost', file: 'test_simple_cost.js' }
          ];

          let passed = 0;
          let failed = 0;

          async function runTest(test) {
              console.log(`\n📊 Running: ${test.name}`);
              return new Promise((resolve) => {
                  const testProcess = spawn('node', [test.file], {
                      stdio: 'inherit',
                      env: { ...process.env, HEADLESS: 'true' }
                  });

                  const timeout = setTimeout(() => {
                      testProcess.kill();
                      console.log(`⏱️ ${test.name} timed out`);
                      failed++;
                      resolve();
                  }, 60000); // 60 second timeout per test

                  testProcess.on('close', (code) => {
                      clearTimeout(timeout);
                      if (code === 0) {
                          console.log(`✅ ${test.name} passed`);
                          passed++;
                      } else {
                          console.log(`❌ ${test.name} failed`);
                          failed++;
                      }
                      resolve();
                  });
              });
          }

          async function runAll() {
              console.log('Starting Metrics & Analytics Test Suite\n');

              for (const test of tests) {
                  // Check if file exists before trying to run
                  const fs = require('fs');
                  if (fs.existsSync(test.file)) {
                      await runTest(test);
                  } else {
                      console.log(`⚠️ Skipping ${test.name} - file not found`);
                  }
              }

              console.log(`\n📈 Results: ${passed} passed, ${failed} failed`);
              process.exit(failed > 0 ? 1 : 0);
          }

          runAll();
          EOF

          xvfb-run -a -s "-screen 0 1920x1080x24" node run_metrics_tests.js

      - name: Upload metrics screenshots
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: metrics-screenshots
          path: tests/ui_tests/screenshots/metrics/

      - name: Upload metrics results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: metrics-test-results
          path: tests/ui_tests/results/metrics/

      - name: Stop server
        if: always()
        run: |
          if [ -f server.pid ]; then
            kill $(cat server.pid) || true
          fi
