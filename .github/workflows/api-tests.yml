name: API Tests

on:
  push:
    paths:
      - 'src/local_deep_research/api/**'
      - 'src/local_deep_research/web/routes/**'
      - 'tests/api_tests/**'
  pull_request:
    types: [opened, synchronize, reopened]
  workflow_dispatch:

jobs:
  api-tests:
    runs-on: ubuntu-latest
    name: REST API Tests
    if: |
      github.event_name == 'push' ||
      github.event_name == 'workflow_dispatch' ||
      (github.event_name == 'pull_request' && (github.event.pull_request.base.ref == 'main' || github.event.pull_request.base.ref == 'dev'))

    services:
      # Add any external services the API needs for testing
      redis:
        image: redis:alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v5

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Set up PDM
        uses: pdm-project/setup-pdm@v4
        with:
          python-version: '3.12'

      - name: Install system dependencies for SQLCipher
        run: |
          sudo apt-get update
          sudo apt-get install -y libsqlcipher-dev

      - name: Install dependencies
        run: |
          # Install in development mode to ensure all modules are available
          pdm sync -d
          # Verify the package is installed correctly
          pdm run python -c "import local_deep_research; print('Package imported:', local_deep_research.__file__)"
          pdm run python -c "import local_deep_research.memory_cache; print('Memory cache module imported successfully')"

      - name: Start server for API unit tests
        run: |
          export LDR_USE_FALLBACK_LLM=true
          export PYTHONPATH=$PWD/src:$PYTHONPATH
          echo "Starting server for API unit tests..."
          pdm run python -m local_deep_research.web.app 2>&1 | tee server.log &
          SERVER_PID=$!

          # Wait for server to start
          for i in {1..30}; do
            if curl -s http://localhost:5000/api/v1/health > /dev/null; then
              echo "Server started successfully after $i seconds"
              break
            fi
            if ! kill -0 $SERVER_PID 2>/dev/null; then
              echo "Server process died!"
              echo "Server log:"
              cat server.log
              exit 1
            fi
            sleep 1
          done

          # Final check
          if ! curl -s http://localhost:5000/api/v1/health > /dev/null; then
            echo "Server failed to start after 30 seconds"
            echo "Server log:"
            cat server.log
            exit 1
          fi

          # Save PID for cleanup
          echo $SERVER_PID > server.pid

      - name: Run API unit tests
        run: |
          export LDR_USE_FALLBACK_LLM=true
          pdm run pytest tests/api_tests/ -v --cov=src/local_deep_research/api --cov-report=term

      - name: Stop server after unit tests
        if: always()
        run: |
          if [ -f server.pid ]; then
            kill $(cat server.pid) || true
            rm server.pid
          fi

      - name: Setup data directory
        run: |
          mkdir -p data
          echo "Created data directory for tests"

      - name: Test server import
        run: |
          echo "Testing if server module can be imported..."
          pdm run python -c "from local_deep_research.web.app import app; print('Import successful')" || echo "Import failed: $?"

      - name: Run API integration tests with health checks
        run: |
          # Start server in background for API testing
          echo "Starting web server..."
          echo "Current directory: $(pwd)"
          echo "Python executable: $(which python)"
          echo "PDM executable: $(which pdm)"

          # Try to start the server with more debugging
          export PYTHONUNBUFFERED=1
          export FLASK_ENV=testing
          export FLASK_DEBUG=1
          export LDR_USE_FALLBACK_LLM=true

          # First check if the command exists
          echo "Checking PDM scripts..."
          pdm run --list || echo "PDM run --list failed"

          # Try running the server directly with Python to see the error
          echo "Attempting direct Python startup..."
          pdm run python -m local_deep_research.web.app 2>&1 | tee server_direct.log &
          DIRECT_PID=$!
          sleep 2

          if kill -0 $DIRECT_PID 2>/dev/null; then
            echo "Direct Python startup succeeded, killing it"
            kill $DIRECT_PID
          else
            echo "Direct Python startup failed, checking log:"
            cat server_direct.log
          fi

          # Now try with pdm run ldr-web
          echo "Starting server with pdm run python..."
          export PYTHONPATH=$PWD/src:$PYTHONPATH
          pdm run python -m local_deep_research.web.app 2>&1 | tee server.log &
          SERVER_PID=$!

          # Give server a moment to start logging
          sleep 2

          # Check if server process is still alive
          if ! kill -0 $SERVER_PID 2>/dev/null; then
            echo "Server process died immediately!"
            echo "Server log:"
            cat server.log
            exit 1
          fi

          # Wait for server to start with health check
          echo "Waiting for server to start..."
          for i in {1..30}; do
            if curl -s http://localhost:5000/api/v1/health > /dev/null; then
              echo "Server started successfully after $i seconds"
              break
            fi
            if ! kill -0 $SERVER_PID 2>/dev/null; then
              echo "Server process died!"
              echo "Server log:"
              cat server.log
              exit 1
            fi
            echo "Waiting for server... ($i/30)"
            echo "Checking port 5000: $(netstat -tuln | grep 5000 || echo 'Not listening')"
            sleep 1
          done

          # Final check
          if ! curl -s http://localhost:5000/api/v1/health > /dev/null; then
            echo "Server failed to start after 30 seconds"
            echo "Server log:"
            cat server.log
            exit 1
          fi

          # Run health checks
          cd tests/health_check && pdm run python test_endpoints_health.py

          # Run API tests
          cd tests/api_tests && pdm run python test_rest_api.py

          # Clean up
          kill $SERVER_PID || true

      - name: Upload API test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: api-test-results
          path: tests/api_tests/results/
