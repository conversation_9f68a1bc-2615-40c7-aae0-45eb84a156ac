name: LLM Integration Tests

on:
  push:
    branches: [ main, dev ]
    paths:
      - 'src/local_deep_research/llm/**'
      - 'src/local_deep_research/config/llm_config.py'
      - 'src/local_deep_research/api/research_functions.py'
      - 'tests/test_llm/**'
      - '.github/workflows/llm-tests.yml'
  pull_request:
    branches: [ main, dev ]
    paths:
      - 'src/local_deep_research/llm/**'
      - 'src/local_deep_research/config/llm_config.py'
      - 'src/local_deep_research/api/research_functions.py'
      - 'tests/test_llm/**'
      - '.github/workflows/llm-tests.yml'
  workflow_dispatch:

jobs:
  llm-unit-tests:
    runs-on: ubuntu-latest
    name: LLM Unit Tests

    steps:
      - uses: actions/checkout@v5

      - name: Build Docker image
        run: docker build --target ldr-test -t ldr-test .

      - name: Run LLM registry tests
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e PYTHONPATH=/app \
            -w /app \
            ldr-test \
            sh -c "python -m pytest tests/test_llm/test_llm_registry.py -v"

      - name: Run LLM integration tests
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e PYTHONPATH=/app \
            -e LDR_USE_FALLBACK_LLM=true \
            -w /app \
            ldr-test \
            sh -c "python -m pytest tests/test_llm/test_llm_integration.py -v"

      - name: Run API LLM integration tests
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e PYTHONPATH=/app \
            -e LDR_USE_FALLBACK_LLM=true \
            -w /app \
            ldr-test \
            sh -c "python -m pytest tests/test_llm/test_api_llm_integration.py -v"

      - name: Run LLM edge case tests
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e PYTHONPATH=/app \
            -w /app \
            ldr-test \
            sh -c "python -m pytest tests/test_llm/test_llm_edge_cases.py -v"

      - name: Run LLM benchmark tests
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e PYTHONPATH=/app \
            -w /app \
            ldr-test \
            sh -c "python -m pytest tests/test_llm/test_llm_benchmarks.py -v"

  llm-example-tests:
    runs-on: ubuntu-latest
    name: LLM Example Tests
    needs: llm-unit-tests

    steps:
      - uses: actions/checkout@v5

      - name: Build Docker image
        run: docker build --target ldr-test -t ldr-test .

      - name: Test basic custom LLM example
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e PYTHONPATH=/app \
            -e LDR_USE_FALLBACK_LLM=true \
            -w /app \
            ldr-test \
            sh -c "timeout 60s python examples/llm_integration/basic_custom_llm.py || true"

      - name: Test mock LLM example
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e PYTHONPATH=/app \
            -e LDR_USE_FALLBACK_LLM=true \
            -w /app \
            ldr-test \
            sh -c "timeout 60s python examples/llm_integration/mock_llm_example.py || true"

      - name: Test provider switching example
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e PYTHONPATH=/app \
            -e LDR_USE_FALLBACK_LLM=true \
            -w /app \
            ldr-test \
            sh -c "timeout 60s python examples/llm_integration/switch_providers.py || true"

      - name: Test custom research example
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e PYTHONPATH=/app \
            -e LDR_USE_FALLBACK_LLM=true \
            -w /app \
            ldr-test \
            sh -c "timeout 60s python examples/llm_integration/custom_research_example.py || true"
