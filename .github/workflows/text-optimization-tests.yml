name: Text Optimization Tests

on:
  push:
    paths:
      - 'src/local_deep_research/text_optimization/**'
      - 'tests/text_optimization/**'
      - '.github/workflows/text-optimization-tests.yml'
  pull_request:
    types: [opened, synchronize, reopened]
    paths:
      - 'src/local_deep_research/text_optimization/**'
      - 'tests/text_optimization/**'
  workflow_dispatch:

jobs:
  test-text-optimization:
    runs-on: ubuntu-latest
    name: Text Optimization Module Tests

    steps:
      - uses: actions/checkout@v5

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Set up PDM
        uses: pdm-project/setup-pdm@v4
        with:
          python-version: '3.12'

      - name: Install system dependencies for SQLCipher
        run: |
          sudo apt-get update
          sudo apt-get install -y libsqlcipher-dev

      - name: Install dependencies
        run: |
          # Install in development mode to ensure all modules are available
          pdm sync -d

      - name: Run text optimization tests
        run: |
          pdm run pytest tests/text_optimization/ -v --tb=short

      - name: Run coverage report
        run: |
          pdm run pytest tests/text_optimization/ --cov=src/local_deep_research/text_optimization --cov-report=xml --cov-report=term

      - name: Upload coverage reports
        uses: codecov/codecov-action@v5
        with:
          file: ./coverage.xml
          flags: text-optimization
          name: text-optimization-coverage
