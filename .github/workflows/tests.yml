name: Tests

on:
  push:
    branches: [ main, dev ]
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [ main, dev ]
  workflow_dispatch:

jobs:
  # Fast unit tests for every push
  unit-tests:
    runs-on: ubuntu-latest
    name: Unit Tests (Fast)

    steps:
      - uses: actions/checkout@v5

      - name: Build Docker image
        run: docker build --target ldr-test -t ldr-test .

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install infrastructure test dependencies
        run: |
          cd tests/infrastructure_tests && npm install

      - name: Run unit tests only
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e LDR_USE_FALLBACK_LLM=true \
            -e LDR_TESTING_WITH_MOCKS=true \
            -w /app \
            ldr-test \
            sh -c "python -m pytest -v \
              tests/test_settings_manager.py \
              tests/test_google_pse.py \
              tests/test_wikipedia_url_security.py \
              tests/test_search_engines_enhanced.py \
              tests/test_utils.py \
              tests/test_database_initialization.py \
              tests/rate_limiting/ \
              tests/retriever_integration/ \
              tests/feature_tests/ \
              tests/fix_tests/"

      - name: Run JavaScript infrastructure tests
        run: |
          cd tests/infrastructure_tests && npm test

  # Infrastructure tests for every push
  infrastructure-tests:
    runs-on: ubuntu-latest
    name: Infrastructure Tests
    needs: unit-tests

    steps:
      - uses: actions/checkout@v5

      - name: Build Docker image
        run: docker build --target ldr-test -t ldr-test .

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install JavaScript test dependencies
        run: |
          cd tests/infrastructure_tests && npm install

      - name: Run Python infrastructure tests
        run: |
          docker run --rm \
            -v $PWD:/app \
            -w /app \
            ldr-test \
            sh -c "pytest tests/infrastructure_tests/test_*.py -v"

      - name: Run JavaScript infrastructure tests
        run: |
          cd tests/infrastructure_tests && npm test

  # Standard tests for PRs and main branch
  integration-tests:
    runs-on: ubuntu-latest
    name: Integration Tests
    needs: [unit-tests, infrastructure-tests]
    if: github.event_name == 'pull_request' || github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev'

    steps:
      - uses: actions/checkout@v5

      - name: Build Docker image
        run: docker build --target ldr-test -t ldr-test .

      - name: Run CI test profile
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e LDR_USE_FALLBACK_LLM=true \
            -e LDR_TESTING_WITH_MOCKS=true \
            -w /app \
            ldr-test \
            sh -c "cd tests && python run_all_tests.py ci --no-server-start"

  # Full tests for PRs to main/dev branches and main branch pushes
  full-tests:
    runs-on: ubuntu-latest
    name: Full Test Suite
    needs: [unit-tests, infrastructure-tests]
    if: |
      github.event_name == 'pull_request' ||
      github.ref == 'refs/heads/main' ||
      github.ref == 'refs/heads/dev'

    steps:
      - uses: actions/checkout@v5

      - name: Build Docker image
        run: docker build --target ldr-test -t ldr-test .

      - name: Install Node.js for UI tests
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install UI test dependencies
        run: |
          export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
          cd tests && npm install

      - name: Install infrastructure test dependencies
        run: |
          cd tests/infrastructure_tests && npm install

      - name: Start application server in Docker
        run: |
          docker run -d \
            --name ldr-server \
            -p 5000:5000 \
            -e LDR_USE_FALLBACK_LLM=true \
            ldr-test ldr-web

          # Wait for server to be ready
          for i in {1..60}; do
            if curl -f http://localhost:5000/api/v1/health 2>/dev/null; then
              echo "Server is ready after $i seconds"
              break
            fi
            if ! docker ps --filter "name=ldr-server" --filter "status=running" -q | grep -q .; then
              echo "Server container died!"
              echo "Server log:"
              docker logs ldr-server
              exit 1
            fi
            echo "Waiting for server... ($i/60)"
            sleep 1
          done

          # Final check
          if ! curl -f http://localhost:5000/api/v1/health 2>/dev/null; then
            echo "Server failed to start after 60 seconds"
            echo "Server log:"
            docker logs ldr-server
            exit 1
          fi

      - name: Run optimized full test suite (including UI tests)
        run: |
          docker run --rm \
            -v $PWD:/app \
            -e LDR_USE_FALLBACK_LLM=true \
            -e LDR_TESTING_WITH_MOCKS=true \
            -e CI=true \
            --network host \
            -w /app \
            ldr-test \
            sh -c "cd tests && python run_all_tests.py full"

      - name: Run JavaScript infrastructure tests
        run: |
          cd tests/infrastructure_tests && npm test

      - name: Stop server
        if: always()
        run: |
          docker stop ldr-server || true
          docker rm ldr-server || true

      - name: Upload test results and screenshots
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: full-test-results
          path: |
            tests/test_results.json
            tests/screenshots/
            tests/ui_tests/screenshots/
# Force CI rebuild
# Force CI rebuild - network issue
