name: Follow-up Research Tests

on:
  push:
    paths:
      - 'src/local_deep_research/followup_research/**'
      - 'src/local_deep_research/advanced_search_system/strategies/contextual_followup_strategy.py'
      - 'src/local_deep_research/search_system.py'
      - 'tests/test_followup_api.py'
      - 'tests/ui_tests/test_followup_research.js'
      - '.github/workflows/followup-research-tests.yml'
  pull_request:
    types: [opened, synchronize, reopened]
    paths:
      - 'src/local_deep_research/followup_research/**'
      - 'src/local_deep_research/advanced_search_system/strategies/contextual_followup_strategy.py'
      - 'tests/test_followup_api.py'
      - 'tests/ui_tests/test_followup_research.js'

jobs:
  test-followup-research:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - uses: actions/checkout@v5

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install PDM
        uses: pdm-project/setup-pdm@v4
        with:
          python-version: '3.11'
          cache: true

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies
        run: |
          pdm sync -d
          cd tests/ui_tests && npm install

      - name: Install browser
        run: |
          npx playwright install chromium

      - name: Create test directories
        run: |
          mkdir -p data
          mkdir -p tests/ui_tests/screenshots/followup
          mkdir -p tests/ui_tests/results/followup

      - name: Start server
        run: |
          export LDR_USE_FALLBACK_LLM=true
          export PYTHONPATH=$PWD/src:$PYTHONPATH
          pdm run python -m local_deep_research.web.app 2>&1 | tee server.log &
          echo $! > server.pid

          # Wait for server
          for i in {1..30}; do
            if curl -f http://localhost:5000/api/v1/health 2>/dev/null; then
              echo "Server ready after $i seconds"
              break
            fi
            echo "Waiting for server... ($i/30)"
            sleep 1
          done

          # Final check
          curl -f http://localhost:5000/api/v1/health || (echo "Server failed to start" && cat server.log && exit 1)

      - name: Run API tests
        run: |
          pdm run pytest tests/test_followup_api.py -v --tb=short

      - name: Run UI tests
        env:
          HEADLESS: true
          DISPLAY: ':99'
        run: |
          export DISPLAY=:99
          cd tests/ui_tests

          # Run follow-up research UI test
          timeout 300 node test_followup_research.js || {
            echo "Follow-up research test failed or timed out"
            exit 1
          }

      - name: Upload screenshots
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: followup-test-screenshots
          path: tests/ui_tests/screenshots/followup/

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: followup-test-results
          path: |
            tests/ui_tests/results/followup/
            server.log

      - name: Stop server
        if: always()
        run: |
          if [ -f server.pid ]; then
            kill $(cat server.pid) || true
          fi

      - name: Test Summary
        if: always()
        run: |
          echo "Follow-up Research Tests completed"
          echo "API tests and UI tests have been executed"
          if [ -f tests/ui_tests/results/followup/test_report.json ]; then
            cat tests/ui_tests/results/followup/test_report.json
          fi
