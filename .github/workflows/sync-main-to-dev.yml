name: Sync Main to Dev

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  sync-main-to-dev:
    runs-on: ubuntu-latest
    # Only run if the push is not from a merge of dev branch
    if: ${{ !contains(github.event.head_commit.message, 'Merge pull request') || !contains(github.event.head_commit.message, 'from LearningCircuit/local-deep-research:dev') }}

    permissions:
      contents: write
      pull-requests: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v5
        with:
          fetch-depth: 0
          token: ${{ secrets.PAT_TOKEN || secrets.GITHUB_TOKEN }}

      - name: Configure Git
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"

          # Configure git to use the token for authentication
          if [ -n "${{ secrets.PAT_TOKEN }}" ]; then
            git config --global url."https://x-access-token:${{ secrets.PAT_TOKEN }}@github.com/".insteadOf "https://github.com/"
          fi

          # Configure git to use the token for authentication
          if [ -n "${{ secrets.PAT_TOKEN }}" ]; then
            git config --global url."https://x-access-token:${{ secrets.PAT_TOKEN }}@github.com/".insteadOf "https://github.com/"
          fi

      - name: Check if sync is needed
        id: check_sync
        run: |
          # Fetch latest changes
          git fetch origin main dev

          # Check if main and dev are already in sync
          MAIN_COMMIT=$(git rev-parse origin/main)
          DEV_COMMIT=$(git rev-parse origin/dev)

          echo "main_commit=$MAIN_COMMIT" >> $GITHUB_OUTPUT
          echo "dev_commit=$DEV_COMMIT" >> $GITHUB_OUTPUT

          if [ "$MAIN_COMMIT" = "$DEV_COMMIT" ]; then
            echo "sync_needed=false" >> $GITHUB_OUTPUT
            echo "Main and dev are already in sync"
          else
            echo "sync_needed=true" >> $GITHUB_OUTPUT
            echo "Sync needed: main ($MAIN_COMMIT) differs from dev ($DEV_COMMIT)"
          fi

      - name: Create or update sync branch
        if: steps.check_sync.outputs.sync_needed == 'true'
        id: create_branch
        run: |
          # Use fixed branch name for updates
          BRANCH_NAME="sync-main-to-dev"
          echo "branch_name=$BRANCH_NAME" >> $GITHUB_OUTPUT

          # Check if branch exists on remote
          if git ls-remote --heads origin $BRANCH_NAME | grep -q $BRANCH_NAME; then
            echo "Branch exists, will update it"
            git fetch origin $BRANCH_NAME
            git checkout -B $BRANCH_NAME origin/main
            # Force push to update the branch
            git push -f origin $BRANCH_NAME
          else
            echo "Creating new branch"
            git checkout -b $BRANCH_NAME origin/main
            git push origin $BRANCH_NAME
          fi

      - name: Attempt merge and handle conflicts
        if: steps.check_sync.outputs.sync_needed == 'true'
        id: merge_attempt
        run: |
          BRANCH_NAME="${{ steps.create_branch.outputs.branch_name }}"

          # Try to merge dev into the sync branch
          if git merge origin/dev --no-edit; then
            echo "merge_conflicts=false" >> $GITHUB_OUTPUT
            echo "Merge successful, no conflicts"
          else
            echo "merge_conflicts=true" >> $GITHUB_OUTPUT
            echo "Merge conflicts detected"

            # Get list of conflicted files
            CONFLICTED_FILES=$(git diff --name-only --diff-filter=U)
            echo "conflicted_files<<EOF" >> $GITHUB_OUTPUT
            echo "$CONFLICTED_FILES" >> $GITHUB_OUTPUT
            echo "EOF" >> $GITHUB_OUTPUT

            # Abort the merge for now
            git merge --abort
          fi

      - name: Push successful merge
        if: steps.check_sync.outputs.sync_needed == 'true' && steps.merge_attempt.outputs.merge_conflicts == 'false'
        run: |
          BRANCH_NAME="${{ steps.create_branch.outputs.branch_name }}"

          # Push the merged changes
          git push origin $BRANCH_NAME

      - name: Create or Update Pull Request
        if: steps.check_sync.outputs.sync_needed == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.PAT_TOKEN || secrets.GITHUB_TOKEN }}
          script: |
            const branchName = '${{ steps.create_branch.outputs.branch_name }}';
            const hasConflicts = '${{ steps.merge_attempt.outputs.merge_conflicts }}' === 'true';
            const conflictedFiles = `${{ steps.merge_attempt.outputs.conflicted_files }}`.split('\n').filter(f => f.trim());
            const timestamp = new Date().toISOString();

            let title, body;

            if (hasConflicts) {
              title = `🚨 Sync main to dev (CONFLICTS)`;
              body = `## ⚠️ Merge Conflicts Detected

            This automated sync from main to dev has **merge conflicts** that require manual resolution.

            ### Conflicted Files:
            ${conflictedFiles.map(file => `- \`${file}\``).join('\n')}

            ### Manual Steps Required:
            1. Check out this branch: \`git checkout ${branchName}\`
            2. Merge dev: \`git merge dev\`
            3. Resolve conflicts in the files listed above
            4. Commit the resolution: \`git commit\`
            5. Push changes: \`git push origin ${branchName}\`

            ### What's Being Synced:
            - Latest changes from main branch
            - Preserving all dev branch improvements
            - Maintaining compatibility between branches

            **⚡ Action Required:** Please resolve conflicts manually and update this PR.

            ---
            *Last updated: ${timestamp}*`;
            } else {
              title = `✅ Sync main to dev`;
              body = `## Summary
            This is an automated sync of main branch changes into dev.

            ### Changes Included:
            - All latest commits from main branch
            - No merge conflicts detected
            - Ready for review and merge

            ### Automated Process:
            - ✅ Created/updated sync branch from main
            - ✅ Successfully merged dev branch
            - ✅ No conflicts detected
            - ✅ Ready for final review

            This ensures main and dev branches stay synchronized automatically.

            ---
            *Last updated: ${timestamp}*`;
            }

            // Check if a PR already exists for this branch
            const { data: existingPRs } = await github.rest.pulls.list({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              head: `${context.repo.owner}:${branchName}`,
              base: 'dev'
            });

            let pr;
            if (existingPRs.length > 0) {
              // Update existing PR
              pr = existingPRs[0];
              console.log(`Updating existing PR #${pr.number}`);

              await github.rest.pulls.update({
                owner: context.repo.owner,
                repo: context.repo.repo,
                pull_number: pr.number,
                title: title,
                body: body
              });

              console.log(`Updated PR #${pr.number}: ${pr.html_url}`);
            } else {
              // Create new PR
              const { data: newPr } = await github.rest.pulls.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: title,
                head: branchName,
                base: 'dev',
                body: body
              });
              pr = newPr;
              console.log(`Created PR #${pr.number}: ${pr.html_url}`);
            }

            // Update labels (remove old ones and add new ones)
            const currentLabels = pr.labels.map(l => l.name);
            const labelsToRemove = currentLabels.filter(l => l === 'sync' || l === 'conflicts' || l === 'auto-merge');

            if (labelsToRemove.length > 0) {
              for (const label of labelsToRemove) {
                try {
                  await github.rest.issues.removeLabel({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    issue_number: pr.number,
                    name: label
                  });
                } catch (e) {
                  console.log(`Could not remove label ${label}: ${e.message}`);
                }
              }
            }

            // Add appropriate labels
            await github.rest.issues.addLabels({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: pr.number,
              labels: hasConflicts ? ['sync', 'conflicts'] : ['sync', 'auto-merge']
            });

      - name: Notify if no sync needed
        if: steps.check_sync.outputs.sync_needed == 'false'
        run: |
          echo "✅ Main and dev branches are already synchronized - no action needed"
