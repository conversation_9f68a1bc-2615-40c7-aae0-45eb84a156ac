name: Auto-merge Clean Syncs

on:
  pull_request:
    types: [opened, synchronize]
    branches:
      - dev

jobs:
  auto-merge-sync:
    runs-on: ubuntu-latest
    # Only run on sync PRs without conflicts
    if: startsWith(github.event.pull_request.title, '✅ Sync main to dev') && contains(github.event.pull_request.labels.*.name, 'auto-merge')

    steps:
      - name: Checkout repository
        uses: actions/checkout@v5

      - name: Enable auto-merge
        uses: actions/github-script@v7
        with:
          script: |
            const prNumber = context.payload.pull_request.number;

            await github.rest.pulls.createReview({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: prNumber,
              event: 'APPROVE',
              body: '✅ Auto-approved: Clean sync with no conflicts detected.'
            });

            await github.rest.pulls.merge({
              owner: context.repo.owner,
              repo: context.repo.repo,
              pull_number: prNumber,
              commit_title: `Automated sync: ${context.payload.pull_request.title}`,
              commit_message: 'Automatically merged clean sync from main to dev',
              merge_method: 'merge'
            });

            console.log(`Auto-merged PR #${prNumber}`);
