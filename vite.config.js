import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  root: 'src/local_deep_research/web/static',
  base: '/static/',

  // Ensure Vite handles Font Awesome fonts correctly
  assetsInclude: ['**/*.woff', '**/*.woff2', '**/*.ttf', '**/*.eot'],

  build: {
    // Output directory relative to root
    outDir: 'dist',

    // Generate manifest for Flask integration
    manifest: true,

    // Single entry point that includes all dependencies
    rollupOptions: {
      input: {
        app: resolve(__dirname, 'src/local_deep_research/web/static/js/app.js'),
      },
      output: {
        // Consistent file naming
        entryFileNames: 'js/[name].[hash].js',
        chunkFileNames: 'js/[name].[hash].js',
        assetFileNames: (assetInfo) => {
          if (assetInfo.name.endsWith('.css')) {
            return 'css/[name].[hash][extname]';
          }
          if (/\.(woff2?|ttf|eot|svg)$/.test(assetInfo.name)) {
            return 'fonts/[name].[hash][extname]';
          }
          return 'assets/[name].[hash][extname]';
        }
      }
    },

    // Optimize chunks
    chunkSizeWarningLimit: 1000,
  },

  server: {
    // Development server settings
    port: 5173,
    strictPort: true,

    // Proxy API requests to Flask
    proxy: {
      '/api': {
        target: 'http://localhost:5000',
        changeOrigin: true,
      },
      '/socket.io': {
        target: 'http://localhost:5000',
        ws: true,
        changeOrigin: true,
      }
    }
  },

  resolve: {
    alias: {
      '@': resolve(__dirname, 'src/local_deep_research/web/static'),
      '@js': resolve(__dirname, 'src/local_deep_research/web/static/js'),
      '@css': resolve(__dirname, 'src/local_deep_research/web/static/css'),
    }
  }
});
